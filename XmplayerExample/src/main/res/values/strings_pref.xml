<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- -->
    <string name="pref_title_general">General</string>

    <string name="pref_key_enable_background_play">pref.enable_background_play</string>
    <string name="pref_title_enable_background_play">Enable background play</string>
    <string name="pref_summary_enable_background_play">need Android 4.0+</string>

    <string name="pref_key_using_android_player">pref.using_android_player</string>
    <string name="pref_title_using_android_player">Using system player</string>
    <string name="pref_summary_using_android_player"></string>

    <string name="pref_key_player">pref.player</string>
    <string name="pref_title_player">Choose Player</string>
    <string-array name="pref_entries_player">
        <item>AndroidMediaPlayer</item>
        <item>IjkMediaPlayer</item>
        <item>IjkExoMediaPlayer</item>
    </string-array>
    <string-array name="pref_entry_values_player">
        <item>1</item>
        <item>2</item>
        <item>3</item>
    </string-array>
    <string-array name="pref_entry_summaries_player">
        <item>AndroidMediaPlayer</item>
        <item>IjkMediaPlayer</item>
        <item>IjkExoMediaPlayer</item>
    </string-array>

    <!-- -->
    <string name="pref_title_ijkplayer_video">Video: ijkplayer</string>

    <string name="pref_key_using_media_codec">pref.using_media_codec</string>
    <string name="pref_title_using_media_codec">Using MediaCodec</string>
    <string name="pref_summary_using_media_codec"></string>

    <string name="pref_key_using_media_codec_auto_rotate">pref.using_media_codec_auto_rotate</string>
    <string name="pref_title_using_media_codec_auto_rotate">Using MediaCodec auto rotate</string>
    <string name="pref_summary_using_media_codec_auto_rotate"></string>

    <string name="pref_key_media_codec_handle_resolution_change">pref.media_codec_handle_resolution_change</string>
    <string name="pref_title_media_codec_handle_resolution_change">MediaCodec handle resolution change</string>
    <string name="pref_summary_media_codec_handle_resolution_change"></string>

    <string name="pref_key_pixel_format">pref.pixel_format</string>
    <string name="pref_title_pixel_format">Pixel Format</string>
    <string-array name="pref_entries_pixel_format">
        <item>Auto Select</item>
        <item>RGB 565</item>
        <item>RGB 888</item>
        <item>RGBX 8888</item>
        <item>YV12</item>
        <item>OpenGL ES2</item>
    </string-array>
    <string-array name="pref_entry_values_pixel_format">
        <item></item>
        <item>fcc-rv16</item>
        <item>fcc-rv24</item>
        <item>fcc-rv32</item>
        <item>fcc-yv12</item>
        <item>fcc-_es2</item>
    </string-array>
    <string-array name="pref_entry_summaries_pixel_format">
        <item>Auto Select</item>
        <item>RGB 565</item>
        <item>RGB 888</item>
        <item>RGBX 8888</item>
        <item>YV12</item>
        <item>OpenGL ES2</item>
    </string-array>

    <!-- -->
    <string name="pref_title_ijkplayer_audio">Audio: ijkplayer</string>

    <string name="pref_key_using_opensl_es">pref.using_opensl_es</string>
    <string name="pref_title_using_opensl_es">Using OpenSL ES</string>
    <string name="pref_summary_using_opensl_es"></string>

    <!-- -->
    <string name="pref_title_render_view">RenderView</string>

    <string name="pref_key_enable_no_view">pref.enable_no_view</string>
    <string name="pref_title_enable_no_view">Enable NoView</string>
    <string name="pref_summary_enable_no_view"></string>

    <string name="pref_key_enable_surface_view">pref.enable_surface_view</string>
    <string name="pref_title_enable_surface_view">Enable SurfaceView</string>
    <string name="pref_summary_enable_surface_view"></string>

    <string name="pref_key_enable_texture_view">pref.enable_texture_view</string>
    <string name="pref_title_enable_texture_view">Enable TextureView</string>
    <string name="pref_summary_enable_texture_view"></string>

    <string name="pref_key_enable_detached_surface_texture">pref.enable_detached_surface_texture</string>
    <string name="pref_title_enable_detached_surface_texture">Enable detached SurfaceTexture</string>
    <string name="pref_summary_enable_detached_surface_texture"></string>

    <!-- -->
    <string name="pref_title_misc">Misc</string>
    <string name="pref_key_using_mediadatasource">pref.using_mediadatasource</string>
    <string name="pref_title_using_mediadatasource">Using MediaDataSource</string>
    <string name="pref_summary_using_mediadatasource"></string>

    <!-- -->
    <string name="pref_key_last_directory"></string>

</resources>
