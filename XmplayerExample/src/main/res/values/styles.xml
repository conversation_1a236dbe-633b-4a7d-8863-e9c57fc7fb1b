<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Widget.App.ActionButton" parent="@style/Widget.AppCompat.ActionButton">
        <item name="textAllCaps">false</item>
    </style>

    <style name="Theme.App.Light.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="preferenceTheme">@style/PreferenceThemeOverlay</item>

        <item name="actionButtonStyle">@style/Widget.App.ActionButton</item>
    </style>

    <style name="Theme.App.NoActionBar" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@null</item>

        <item name="actionButtonStyle">@style/Widget.App.ActionButton</item>
    </style>
</resources>
