<?xml version="1.0" encoding="utf-8"?>
<resources>

    <string name="app_name">XMPlayerdemo</string>

    <string name="N_A">N/A</string>
    <string name="close">Close</string>
    <string name="exit">Exit</string>
    <string name="sample">Sample</string>
    <string name="recent">Recent</string>
    <string name="settings">Settings</string>
    <string name="tracks">Tracks</string>
    <string name="toggle_player">Player</string>
    <string name="toggle_render">Render</string>
    <string name="toggle_ratio">Scale</string>
    <string name="show_info">Info</string>


    <string name="media_information">Media Information</string>
    <string name="mi_player">Player</string>
    <string name="mi_media">Media</string>
    <string name="mi_profile_level">Profile level</string>
    <string name="mi_pixel_format">Pixel format</string>
    <string name="mi_resolution">Resolution</string>
    <string name="mi_length">Length</string>
    <string name="mi_stream_fmt1">Stream #%d</string>
    <string name="mi_type">Type</string>
    <string name="mi_language">Language</string>
    <string name="mi_codec">Codec</string>
    <string name="mi_frame_rate">Frame rate</string>
    <string name="mi_bit_rate">Bit rate</string>
    <string name="mi_sample_rate">Sample rate</string>
    <string name="mi_channels">Channels</string>
    <string name="mi__selected_video_track">*</string>
    <string name="mi__selected_audio_track">*</string>

    <string name="filter_none">无</string>
    <string name="filter_beauty">磨皮美颜</string>

    <string name="TrackType_video">Video</string>
    <string name="TrackType_audio">Audio</string>
    <string name="TrackType_subtitle">Subtitle</string>
    <string name="TrackType_timedtext">Timed text</string>
    <string name="TrackType_metadata">Meta data</string>
    <string name="TrackType_unknown">Unknown</string>

    <string name="VideoView_error_text_invalid_progressive_playback">Invalid progressive playback</string>
    <string name="VideoView_error_text_unknown">Unknown</string>
    <string name="VideoView_error_button">OK</string>



    <string name="VideoView_render_none">Render: None</string>
    <string name="VideoView_render_surface_view">Render: SurfaceView</string>
    <string name="VideoView_render_texture_view">Render: TextureView</string>

    <string name="VideoView_player_none">Player: None</string>
    <string name="VideoView_player_AndroidMediaPlayer">Player: AndroidMediaPlayer</string>
    <string name="VideoView_player_IjkMediaPlayer">Player: IjkMediaPlayer</string>
    <string name="VideoView_player_IjkExoMediaPlayer">Player: IjkExoMediaPlayer</string>

</resources>
