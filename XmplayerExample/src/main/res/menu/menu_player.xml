<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".menu_app">
    <item
        android:id="@+id/action_toggle_player"
        android:title="@string/toggle_player"
        app:showAsAction="ifRoom" />
    <item
        android:id="@+id/action_toggle_render"
        android:title="@string/toggle_render"
        app:showAsAction="ifRoom" />
    <item
        android:id="@+id/action_toggle_ratio"
        android:title="@string/toggle_ratio"
        app:showAsAction="ifRoom" />
    <item
        android:id="@+id/action_show_info"
        android:title="@string/show_info"
        app:showAsAction="ifRoom" />
    <item
        android:id="@+id/action_show_tracks"
        android:title="@string/tracks"
        app:showAsAction="ifRoom" />
    <item
        android:id="@+id/actions_capture"
        android:title="Capture"
        app:showAsAction="ifRoom" />
    <item
        android:id="@+id/live_stream"
        android:title="Live"
        app:showAsAction="ifRoom" />
</menu>