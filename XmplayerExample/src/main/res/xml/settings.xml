<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <PreferenceCategory android:title="@string/pref_title_general">
        <CheckBoxPreference
            android:defaultValue="true"
            android:disableDependentsState="true"
            android:key="@string/pref_key_enable_background_play"
            android:persistent="true"
            android:summary="@string/pref_summary_enable_background_play"
            android:title="@string/pref_title_enable_background_play" />
        <tv.danmaku.ijk.media.example.widget.preference.IjkListPreference
            android:defaultValue="1"
            android:entries="@array/pref_entries_player"
            android:entryValues="@array/pref_entry_values_player"
            android:key="@string/pref_key_player"
            android:persistent="true"
            android:title="@string/pref_title_player"
            app:entrySummaries="@array/pref_entry_summaries_player" />
    </PreferenceCategory>
    <PreferenceCategory android:title="@string/pref_title_ijkplayer_video">
        <CheckBoxPreference
            android:defaultValue="true"
            android:key="@string/pref_key_using_media_codec"
            android:persistent="true"
            android:summary="@string/pref_summary_using_media_codec"
            android:title="@string/pref_title_using_media_codec" />
        <CheckBoxPreference
            android:defaultValue="true"
            android:key="@string/pref_key_using_media_codec_auto_rotate"
            android:persistent="true"
            android:summary="@string/pref_summary_using_media_codec_auto_rotate"
            android:title="@string/pref_title_using_media_codec_auto_rotate" />
        <CheckBoxPreference
            android:defaultValue="true"
            android:key="@string/pref_key_media_codec_handle_resolution_change"
            android:persistent="true"
            android:summary="@string/pref_summary_media_codec_handle_resolution_change"
            android:title="@string/pref_title_media_codec_handle_resolution_change" />
        <tv.danmaku.ijk.media.example.widget.preference.IjkListPreference
            android:defaultValue=""
            android:entries="@array/pref_entries_pixel_format"
            android:entryValues="@array/pref_entry_values_pixel_format"
            android:key="@string/pref_key_pixel_format"
            android:persistent="true"
            android:title="@string/pref_title_pixel_format"
            app:entrySummaries="@array/pref_entry_summaries_pixel_format" />
    </PreferenceCategory>
    <PreferenceCategory android:title="@string/pref_title_ijkplayer_audio">
        <CheckBoxPreference
            android:defaultValue="false"
            android:key="@string/pref_key_using_opensl_es"
            android:persistent="true"
            android:summary="@string/pref_summary_using_opensl_es"
            android:title="@string/pref_title_using_opensl_es" />
    </PreferenceCategory>
    <PreferenceCategory android:title="@string/pref_title_render_view">
        <CheckBoxPreference
            android:defaultValue="false"
            android:key="@string/pref_key_enable_no_view"
            android:persistent="true"
            android:summary="@string/pref_summary_enable_no_view"
            android:title="@string/pref_title_enable_no_view" />
        <CheckBoxPreference
            android:defaultValue="false"
            android:key="@string/pref_key_enable_surface_view"
            android:persistent="true"
            android:summary="@string/pref_summary_enable_surface_view"
            android:title="@string/pref_title_enable_surface_view" />
        <CheckBoxPreference
            android:defaultValue="false"
            android:key="@string/pref_key_enable_texture_view"
            android:persistent="true"
            android:summary="@string/pref_summary_enable_texture_view"
            android:title="@string/pref_title_enable_texture_view" />
    </PreferenceCategory>
    <PreferenceCategory android:title="@string/pref_title_misc">
        <CheckBoxPreference
            android:defaultValue="true"
            android:key="@string/pref_key_using_mediadatasource"
            android:persistent="true"
            android:summary="@string/pref_summary_using_mediadatasource"
            android:title="@string/pref_title_using_mediadatasource" />
    </PreferenceCategory>
</PreferenceScreen>
