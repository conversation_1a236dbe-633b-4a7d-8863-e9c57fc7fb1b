package tv.danmaku.ijk.media.example.activities;

import android.app.Activity;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.KeyEvent;

import com.ximalaya.ting.android.xmplaysdk.video.VideoEnv;
import com.ximalaya.ting.android.xmplaysdk.video.player.VideoPlayer;
import com.ximalaya.ting.android.xmplaysdk.video.player.VideoSource;

import tv.danmaku.ijk.media.example.R;
import tv.danmaku.ijk.media.example.application.Settings;

/**
 * Created by <PERSON> on 2017/8/11 下午4:59.
 *
 * <AUTHOR>
 */

public class VideoActivity1 extends Activity {

    private VideoPlayer mVideoPlayer;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        VideoEnv.init(getApplicationContext());
        setContentView(R.layout.activity_video);
        mVideoPlayer = (VideoPlayer) findViewById(R.id.player);
        Settings settings = new Settings(this);
        mVideoPlayer.setPlayerType(settings.getPlayer());
//        VideoSource videoSource = new VideoSource("TEST", "http://fms.cntv.lxdns.com/live/flv/channel89.flv");
        VideoSource videoSource = new VideoSource("TEST", "http://9890.vod.myqcloud.com/9890_4e292f9a3dd011e6b4078980237cc3d3.f20.mp4");
        mVideoPlayer.setVideoSource(videoSource);
        mVideoPlayer.start();
    }

    @Override
    protected void onResume() {
        super.onResume();
//        if (!mVideoPlayer.isPlaying() && !mVideoPlayer.isPauseByUser()) {
//            mVideoPlayer.start();
//        }
    }

    @Override
    protected void onPause() {
//        mVideoPlayer.pause();
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        mVideoPlayer.stop();
        super.onDestroy();
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (mVideoPlayer.dispatchKeyEvent(event)) {
            return true;
        }
        return super.dispatchKeyEvent(event);
    }
}