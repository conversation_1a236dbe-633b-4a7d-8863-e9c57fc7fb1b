package com.github.faucamp.simplertmp.io.packets;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import com.github.faucamp.simplertmp.Util;
import com.github.faucamp.simplertmp.io.ChunkStreamInfo;

/**
 * A "Abort" RTMP control message, received on chunk stream ID 2 (control channel)
 * 
 * <AUTHOR>
 */
public class Abort extends RtmpPacket {

    private int chunkStreamId;
    
    public Abort(RtmpHeader header) {
        super(header);
    }

    public Abort(int chunkStreamId) {
        super(new RtmpHeader(RtmpHeader.ChunkType.TYPE_1_RELATIVE_LARGE, ChunkStreamInfo.CONTROL_CHANNEL, RtmpHeader.MessageType.SET_CHUNK_SIZE));
        this.chunkStreamId = chunkStreamId;
    }

    /** @return the ID of the chunk stream to be aborted */
    public int getChunkStreamId() {
        return chunkStreamId;
    }

    /** Sets the ID of the chunk stream to be aborted */
    public void setChunkStreamId(int chunkStreamId) {
        this.chunkStreamId = chunkStreamId;
    }

    @Override
    public void readBody(InputStream in) throws IOException {
        // Value is received in the 4 bytes of the body
        chunkStreamId = Util.readUnsignedInt32(in);
    }

    @Override
    protected void writeBody(OutputStream out) throws IOException {
        Util.writeUnsignedInt32(out, chunkStreamId);
    }
}
