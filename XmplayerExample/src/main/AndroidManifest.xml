<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="tv.danmaku.ijk.media.example">

    <uses-sdk
        android:minSdkVersion="9"
        android:targetSdkVersion="22" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- for umeng -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <application
        android:name=".MainApplication"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/AppTheme">
        <activity
            android:name="tv.danmaku.ijk.media.example.activities.FileExplorerActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:screenOrientation="user"
            android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="tv.danmaku.ijk.media.example.activities.LiveActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:screenOrientation="user"
            android:theme="@style/AppTheme">
            <!--<intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>-->
        </activity>
        <activity
            android:name="tv.danmaku.ijk.media.example.activities.SampleMediaActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:screenOrientation="user"
            android:theme="@style/AppTheme">
        </activity>
        <activity
            android:name="tv.danmaku.ijk.media.example.activities.RecentMediaActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:screenOrientation="user"
            android:theme="@style/AppTheme">
        </activity>
        <activity
            android:name="tv.danmaku.ijk.media.example.activities.SettingsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:screenOrientation="user"
            android:theme="@style/AppTheme" />
        <activity
            android:name="tv.danmaku.ijk.media.example.activities.VideoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:screenOrientation="user"
            android:theme="@style/FullscreenTheme">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="video/*" />
                <data android:mimeType="audio/*" />
                <data android:scheme="http" />
                <data android:scheme="file" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="video/*" />
            </intent-filter>
        </activity>

        <activity android:name=".activities.VideoActivity1"
                  android:configChanges="orientation|keyboardHidden|screenSize">

        </activity>
        <activity
            android:name=".activities.CameraPreviewActivity"
            android:screenOrientation="user"
            android:label="@string/app_name"
            android:theme="@style/AppTheme">
        </activity>
        <activity
            android:name=".activities.VideoPostProcessingActivity"
            android:screenOrientation="user"
            android:label="@string/app_name"
            android:theme="@style/AppTheme">
        </activity>

        <service
            android:name="tv.danmaku.ijk.media.example.services.MediaPlayerService"
            android:enabled="false"
            android:exported="false"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name" >
        </service>
    </application>

</manifest>
