apply plugin: 'com.android.application'

android {
    // http://tools.android.com/tech-docs/new-build-system/tips
    //noinspection GroovyAssignabilityCheck
    signingConfigs {
        config {
            keyAlias 'debug.keystore'
            keyPassword '111111'
            storeFile file('/Users/<USER>/Documents/debug.keystore')
            storePassword '111111'
        }
    }
    compileSdkVersion rootProject.ext.compileSdkVersion
    //noinspection GroovyAssignabilityCheck
    buildToolsVersion rootProject.ext.buildToolsVersion
    lintOptions {
        abortOnError false
    }
    defaultConfig {
        applicationId "tv.danmaku.ijk.media.example"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 202
        versionName rootProject.ext.versionName
        flavorDimensions "versionCode"
        ndk {
            abiFilters "armeabi"
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
//            signingConfig signingConfigs.config
        }
    }
    productFlavors {
        all32 { minSdkVersion rootProject.ext.minSdkVersion }
        all64 { minSdkVersion 21 }
        // armv5 {}
        // armv7a {}
        // arm64 { minSdkVersion 21 }
        // x86 {}
//        androids {
//            applicationIdSuffix ".android"
//            buildConfigField "int", "PV_TYPE", "1"
//        }
//        exo {
//            applicationIdSuffix ".exo"
//            buildConfigField "int", "PV_TYPE", "3"
//        }
//        ijk {
//            applicationIdSuffix ".ijk"
//            buildConfigField "int", "PV_TYPE", "2"
//        }
    }
}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    api 'com.android.support:preference-v7:26.1.0'
    api 'com.android.support:support-annotations:26.1.0'
    api 'com.squareup:otto:1.3.8'

    api project(':VideoBundle:Xmplayer4Enterprise')

    // compile 'tv.danmaku.ijk.media:ijkplayer-java:0.6.3'
    // compile 'tv.danmaku.ijk.media:ijkplayer-exo:0.6.3'
    // all32Compile 'tv.danmaku.ijk.media:ijkplayer-armv5:0.6.3'
    // all32Compile 'tv.danmaku.ijk.media:ijkplayer-armv7a:0.6.3'
    // all32Compile 'tv.danmaku.ijk.media:ijkplayer-x86:0.6.3'
    // all64Compile 'tv.danmaku.ijk.media:ijkplayer-armv5:0.6.3'
    // all64Compile 'tv.danmaku.ijk.media:ijkplayer-armv7a:0.6.3'
    // all64Compile 'tv.danmaku.ijk.media:ijkplayer-arm64:0.6.3'
    // all64Compile 'tv.danmaku.ijk.media:ijkplayer-x86:0.6.3'
    // all64Compile 'tv.danmaku.ijk.media:ijkplayer-x86_64:0.6.3'
    // armv5Compile project(':player-armv5')
    // armv7aCompile project(':player-armv7a')
    // arm64Compile project(':player-arm64')
    // x86Compile project(':player-x86')
    // x86_64Compile project(':player-x86_64')
}
