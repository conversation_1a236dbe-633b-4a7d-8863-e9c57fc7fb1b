package com.ximalaya.ting.android.pay.alipay;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.os.AsyncTask;
import android.text.TextUtils;

import com.alipay.sdk.app.PayTask;
import com.ximalaya.ting.android.pay.basepay.PayType;
import com.ximalaya.ting.android.pay.log.PayActionLog;
import com.ximalaya.ting.android.pay.log.PayConstants;
import com.ximalaya.ting.android.routeservice.service.pay.IPayAction;
import com.ximalaya.ting.android.routeservice.service.pay.PayResult;

import java.util.Map;


/**
 * Created by chengyun.wu on 17/6/28.
 *
 * <AUTHOR>
 */

public class AliPayAction implements IPayAction<AliPayRequest> {

    public static final int RESULT_SUCCESS = 0;
    public static final int RESULT_FAIL = -1;
    public static final int RESULT_WAIT_FOR_RESULT = -4;
    public static final int RESULT_USER_CANCEL = -2;

    private Activity mActivity;
    private PayActionLog mActionLog = new PayActionLog(PayConstants.TYPE_ALI_PAY);

    public AliPayAction(Activity activity) {
        mActivity = activity;
    }

    @Override
    public void pay(AliPayRequest payRequest, PayCallBack callBack) {
        if (payRequest != null) {
            String payInfo = payRequest.getPayInfo();
            payInternal(payInfo, callBack);
            if (mActionLog != null) {
                mActionLog.startPay();
            }
        } else {
            if (callBack != null) {
                PayResult payResult = new PayResult();
                payResult.retCode = RESULT_FAIL;
                payResult.errorMsg = "支付宝支付IPayRequest必须是AliPayRequest";
                callBack.onPayResult(payResult);
                if (mActionLog != null) {
                    mActionLog.endPay(payResult);
                }
            }
        }
    }

    @Override
    public boolean isSupported() {
        return true;
    }


    private void payInternal(String payInfo, final PayCallBack callback) {
        @SuppressLint("StaticFieldLeak")
        AsyncTask<String, Void, AliPayResult> asyncTask = new AsyncTask<String, Void, AliPayResult>() {

            @Override
            protected AliPayResult doInBackground(String... params) {
                PayTask alipay = new PayTask(mActivity);
                String payInfoValue = params[0];
                // 使用pay方法返回的字符串可能出现截取异常的情况
                Map<String, String> result = alipay.payV2(payInfoValue, true);
                try {
                    return new AliPayResult(result);
                } catch (Exception e) {
                    e.printStackTrace();
                    return new AliPayResult(e);
                }
            }


            @Override
            protected void onPostExecute(AliPayResult result) {
                PayResult payResult = null;
                if (result == null || result.getException() != null) {
                    String msg = result != null && result.getException() != null ? result.getException().getMessage() : "cause exception when create AliPayResult";
                    payResult = new PayResult();
                    payResult.retCode = RESULT_FAIL;
                    payResult.errorMsg = msg;
                    payResult.payType = PayType.ALI_PAY;
                } else {
                    String resultStr = result.getResult();
                    int retCode;
//                    if (ConstantsOpenSdk.isDebug) {
//                        Toast.makeText(mActivity, "status:" + result.getResultStatus() + ",result:" + resultStr, Toast.LENGTH_SHORT).show();
//                    }
                    if (TextUtils.equals(result.getResultStatus(), "9000")) {
                        retCode = RESULT_SUCCESS;
                    } else if (TextUtils.equals(result.getResultStatus(), "6001")) {
                        //用户取消
                        retCode = RESULT_USER_CANCEL;
                    } else if (TextUtils.equals(result.getResultStatus(), "8000")) {
                        // 支付结果确认中
                        retCode = RESULT_WAIT_FOR_RESULT;
                    } else {
                        // 支付失败
                        retCode = RESULT_FAIL;
                    }
                    payResult = new PayResult();
                    payResult.retCode = retCode;
                    payResult.errorMsg = result.getMemo();
                    payResult.payType = PayType.ALI_PAY;
                }
                if (mActionLog != null) {
                    mActionLog.endPay(payResult);
                }
                if (callback != null) {
                    callback.onPayResult(payResult);
                }
            }
        };
        asyncTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, payInfo);
//        AsyncTaskCompat.executeParallel(asyncTask, payInfo);
    }
}
