package com.ximalaya.ting.android.pay.alipay;

import android.app.Activity;

import com.ximalaya.ting.android.routeservice.service.pay.IPayAction;
import com.ximalaya.ting.android.routeservice.service.pay.IPayActionFactory;


/**
 * Created by chengyun.wu on 17/6/28.
 *
 * <AUTHOR>
 */

public class ReflectAliPayActionFactory implements IPayActionFactory<AliPayRequest> {

    @Override
    public IPayAction<AliPayRequest> createPayAction(Activity activity) {
        return new AliPayAction(activity);
    }

}
