<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="AppBaseTheme" parent="android:Theme.Holo.Light">
        <!--
            Theme customizations available in newer API levels can go in
            res/values-vXX/styles.xml, while customizations related to
            backward-compatibility can go here.
			<item name="android:windowIsTranslucent">true</item>
        -->
    </style>

    <!-- Application theme. -->
    <style name="host_AppTheme" parent="AppBaseTheme">
        <item name="android:windowBackground">@color/host_white</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <style name="WXTransparent" parent="host_AppTheme">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item>
    </style>
</resources>