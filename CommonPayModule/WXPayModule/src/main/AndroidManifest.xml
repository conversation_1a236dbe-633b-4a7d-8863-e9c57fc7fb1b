<?xml version="1.0" encoding="utf-8"?>

<manifest package="com.ximalaya.ting.android.pay.wxpay"
    xmlns:android="http://schemas.android.com/apk/res/android">


    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>

    <application>
        <activity
            android:name=".XMWXPayEntryActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:launchMode="singleTask"
            android:screenOrientation="unspecified"
            android:theme="@style/WXTransparent"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="${WEIXIN_APP_ID_FOR_PAY}" />
            </intent-filter>
        </activity>

        <!-- 微信支付 -->
        <activity-alias
            android:name="com.ximalaya.ting.android.wxapi.WXPayEntryActivity"
            android:exported="true"
            android:targetActivity=".XMWXPayEntryActivity"/>
    </application>



</manifest>