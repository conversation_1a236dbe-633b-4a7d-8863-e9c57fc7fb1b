package com.ximalaya.ting.android.pay.wxpay;


import android.app.Activity;

import com.ximalaya.ting.android.routeservice.service.pay.IPayAction;
import com.ximalaya.ting.android.routeservice.service.pay.IPayActionFactory;


/**
 * Created by chengyun.wu on 17/6/28.
 *
 * <AUTHOR>
 */

public class ReflectWXPayActionFactory implements IPayActionFactory<WxPayRequest> {

    @Override
    public IPayAction<WxPayRequest> createPayAction(Activity activity) {
        return new WxPayAction(activity);
    }
}
