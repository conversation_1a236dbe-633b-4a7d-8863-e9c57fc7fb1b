apply plugin: 'com.android.library'
apply from: project.rootDir.absolutePath + '/util.gradle'
dependencies {
    compileOnly rootProject.ext.xmDependencies.supportV4
    api project(path:':XAndroidFramework:CommonPayModule:BasePayModule',configuration: rootProject.configurationType)
    api rootProject.ext.xmDependencies.wechatSdkVersion
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion Integer.parseInt(rootProject.ext.minSdkVersion)
        targetSdkVersion Integer.parseInt(rootProject.ext.targetSdkVersion)
        versionCode 1
        versionName "1.0"
    }

    lintOptions {
        quiet true
        abortOnError false
        checkReleaseBuilds false
    }

    defaultConfig {
        consumerProguardFiles 'proguard-project.txt'
    }
}
