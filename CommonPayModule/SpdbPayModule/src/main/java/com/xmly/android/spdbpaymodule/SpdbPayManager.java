package com.xmly.android.spdbpaymodule;

import android.content.Context;

import com.ximalaya.ting.android.pay.log.PayActionLog;
import com.ximalaya.ting.android.pay.log.PayActionManager;
import com.ximalaya.ting.android.pay.log.PayConstants;
import com.ximalaya.ting.android.routeservice.service.spdbpay.ISpdbPayManager;

/**
 * Create by {jian.kang} on 9/9/21
 *
 * <AUTHOR>
 */
public class SpdbPayManager implements ISpdbPayManager {
    private  ISpdbCallback iSpdbCallback;

    @Override
    public void registerSpdbCallback(ISpdbCallback callback) {
        // 这里可以认为是农行支付的开始
        PayActionLog log = new PayActionLog(PayConstants.TYPE_SPDB_PAY);
        PayActionManager.getInstance().setCurPayActionLog(log);
        log.startPay();
        iSpdbCallback = callback;
    }

    @Override
    public void unRegisterSpdbCallBack(ISpdbCallback callback) {
        if (iSpdbCallback == callback) {
            iSpdbCallback = null;
        }
    }

    @Override
    public ISpdbCallback getCallback() {
        return iSpdbCallback;
    }

    @Override
    public void init(Context appContext) {

    }
}
