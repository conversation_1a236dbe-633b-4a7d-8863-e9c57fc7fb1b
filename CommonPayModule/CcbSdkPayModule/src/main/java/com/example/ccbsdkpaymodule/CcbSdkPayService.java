package com.example.ccbsdkpaymodule;

import android.content.Context;

import com.ximalaya.ting.android.pay.log.PayActionLog;
import com.ximalaya.ting.android.pay.log.PayActionManager;
import com.ximalaya.ting.android.pay.log.PayConstants;
import com.ximalaya.ting.android.routeservice.service.abcpay.IAbcManager;
import com.ximalaya.ting.android.routeservice.service.ccbsdkpay.ICcbSdkService;

/**
 * Create by {jian.kang} on 3/4/22
 *
 * <AUTHOR>
 */
public class CcbSdkPayService implements ICcbSdkService {
    private ICcbSdkService.ICcbSdkPayCallback mCcbSdkPayCallback;

    @Override
    public void registerCcbCallback(ICcbSdkPayCallback callback) {
        // 这里可以认为是农行支付的开始
        PayActionLog log = new PayActionLog(PayConstants.TYPE_CCB_SDK_PAY);
        PayActionManager.getInstance().setCurPayActionLog(log);
        log.startPay();
        mCcbSdkPayCallback = callback;
    }

    @Override
    public void unRegisterCcbCallBack(ICcbSdkPayCallback callback) {
        if (mCcbSdkPayCallback == callback) {
            mCcbSdkPayCallback = null;
        }
    }

    @Override
    public ICcbSdkPayCallback getCallback() {
        return mCcbSdkPayCallback;
    }

    @Override
    public void init(Context appContext) {

    }
}
