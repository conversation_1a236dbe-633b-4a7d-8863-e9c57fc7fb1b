package com.ximalaya.ting.android.pay.basepay;

import android.app.Activity;
import android.content.Context;
import androidx.collection.ArrayMap;


import com.ximalaya.ting.android.routeservice.service.pay.IPayAction;
import com.ximalaya.ting.android.routeservice.service.pay.IPayActionFactory;
import com.ximalaya.ting.android.routeservice.service.pay.IThirdPayManager;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by chengyun.wu on 17/6/28.
 *
 * <AUTHOR>
 */

public class ThirdPayManager implements IThirdPayManager{

    private static final String WXPAY_MODE_CONTROL_CLASS =
            "com.ximalaya.ting.android.pay.wxpay.ReflectWXPayActionFactory";
    private static final String ALIPAY_MODE_CONTROL_CLASS =
            "com.ximalaya.ting.android.pay.alipay.ReflectAliPayActionFactory";

    private ArrayMap<String, IPayActionFactory> mPayFactoryMap;

    private IPayAction.PayCallBack mCallBack;

    @Override
    public void init(Context appContext) {
        mPayFactoryMap = new ArrayMap<>();
        initPayMode();
    }

    private ThirdPayManager(){
    }

    public void initPayMode() {
        //Wechat
        IPayActionFactory wxPayActionFactory = createPayFactory(WXPAY_MODE_CONTROL_CLASS);
        if(wxPayActionFactory != null){
            addPayActionInternal(PayType.WX_PAY, wxPayActionFactory);
        }

        //AliPay
        IPayActionFactory aliPayActionFactory = createPayFactory(ALIPAY_MODE_CONTROL_CLASS);
        if(aliPayActionFactory != null){
            addPayActionInternal(PayType.ALI_PAY, aliPayActionFactory);
        }
    }

    /**
     * 注册一个监听支付结果的callback
     * @param callBack
     * @return 返回一个当前callbck 的id. 调用支付IPayAction.pay的时候需要传入
     */

    public void registerPayCallBack(IPayAction.PayCallBack callBack){
        mCallBack = callBack;
    }

    /**
     * 接触一个支付结果的回调
     * @param callBack 注册的callback
     */
    public void unRegisterPayCallBack(IPayAction.PayCallBack callBack){
        if(mCallBack == callBack){
            mCallBack = null;
        }
    }

    /**
     * 获取所对应的PayCallBack
     * @return
     */
    public IPayAction.PayCallBack getRegisterPayCallBack(){
        return mCallBack;
    }


    /**
     * 获取目前支持的支付方式
     * @return
     */
    public List<String> getSupportPayTypeList() {
        List<String> list = new ArrayList<>();
        if(mPayFactoryMap != null) {
            return new ArrayList<>(mPayFactoryMap.keySet());
        }
        return list;
    }

    /**
     * 自己添加 实现的支付方式
     * @param payType
     * @param payActionFactory
     */
    public void addPayAction(String payType, IPayActionFactory payActionFactory){
        if (PayType.ALI_PAY.equals(payType) || PayType.WX_PAY.equals(payType)
                ||PayType.GOOGLE_PAY.equals(payType)) {
            throw new RuntimeException("can't add PayActionFactory " +  payType + "is exist");
        }
        addPayActionInternal(payType, payActionFactory);
    }


    private void addPayActionInternal(String payType, IPayActionFactory payActionFactory){
        if(payActionFactory != null && mPayFactoryMap != null) {
            mPayFactoryMap.put(payType, payActionFactory);
        }
    }

    /**
     *
     * @param activity must be a Activity
     * @param payType
     * @return
     */
    public IPayAction getPayActionForType(Activity activity, String payType){
        IPayActionFactory factory = mPayFactoryMap.get(payType);
        if(factory != null){
            return factory.createPayAction(activity);
        }
        return null;

    }

    private IPayActionFactory createPayFactory(String className) {
        Class<?> classType = null;
        IPayActionFactory payModeControl = null;
        try {
            classType = Class.forName(className);
            payModeControl = (IPayActionFactory) classType.newInstance();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e1) {
            e1.printStackTrace();
        }

        return payModeControl;
    }
}
