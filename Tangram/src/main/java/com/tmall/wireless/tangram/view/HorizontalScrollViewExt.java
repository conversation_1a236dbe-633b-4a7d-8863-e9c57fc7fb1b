/*
 * MIT License
 *
 * Copyright (c) 2018 Alibaba Group
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

package com.tmall.wireless.tangram.view;

import android.content.Context;
import android.os.Message;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.HorizontalScrollView;

import com.tmall.wireless.tangram.util.WeakHandler;

/**
 * Provide onScrollChangedListenser by {@link #onScrollChanged(int, int, int, int)} method, in order to be in compatible with API under 23
 * <p/>
 * Created by Kunlun on 9/17/16.
 */
public class HorizontalScrollViewExt extends HorizontalScrollView implements WeakHandler.IMessageHandler {
    private OnScrollChangedListenerExt listener;
    private IOnScrollStopListener stopListener;

    private int lastX = -1;
    private boolean scrollStop;
    private WeakHandler mHandler;

    public HorizontalScrollViewExt(Context context) {
        super(context);
        init();
    }

    public HorizontalScrollViewExt(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public HorizontalScrollViewExt(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (listener != null) {
            listener.onScrollChangedExt(l, t, oldl, oldt);
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            scrollStop = false;
        } else if (ev.getAction() == MotionEvent.ACTION_UP) {
            lastX = getScrollX();
            mHandler.sendMessageDelayed(mHandler.obtainMessage(1), 100);
            scrollStop = true;
        }
        return super.onTouchEvent(ev);
    }

    @Override
    public void handleMessage(Message message) {
        if (scrollStop && message.what == 1) {
            int scrollX = getScrollX();
            if (lastX == scrollX) {
                if (stopListener != null) {
                    stopListener.onScrollStop(this);
                }
            } else {
                lastX = scrollX;
                mHandler.sendMessageDelayed(mHandler.obtainMessage(1), 100);
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        scrollStop = false;
        mHandler.removeMessages(1);
        super.onDetachedFromWindow();
    }

    public void setOnScrollChangedListenerExt(OnScrollChangedListenerExt listener) {
        this.listener = listener;
    }

    public void setOnScrollStopListener(IOnScrollStopListener listener) {
        this.stopListener = listener;
    }

    private void init() {
        mHandler = new WeakHandler(this);
    }

    public interface OnScrollChangedListenerExt {
        void onScrollChangedExt(int l, int t, int oldl, int oldt);
    }

    public interface IOnScrollStopListener {
        void onScrollStop(HorizontalScrollViewExt view);
    }
}
