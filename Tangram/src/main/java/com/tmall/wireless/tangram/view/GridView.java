package com.tmall.wireless.tangram.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import com.tmall.wireless.tangram.core.adapter.BinderViewHolder;
import com.tmall.wireless.tangram.core.adapter.GroupBasicAdapter;
import com.tmall.wireless.tangram.structure.BaseCell;
import com.tmall.wireless.tangram.structure.card.GridCard;
import com.tmall.wireless.tangram.structure.cell.GridCell;
import com.tmall.wireless.tangram.structure.view.ITangramViewLifeCycle;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by <PERSON> on 2022/7/13 18:56.
 *
 * <AUTHOR>
 */

public class GridView extends ConstraintLayout implements ITangramViewLifeCycle {
    private GridCell mCell;
    private int vGap;
    private int hGap;
    private int column;
    private BinderViewHolder headerViewHolder;
    private BinderViewHolder footerViewHolder;
    private List<BinderViewHolder> childrenCellBinders = new ArrayList<>();

    public GridView(@NonNull Context context) {
        super(context);
        init();
    }

    public GridView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public GridView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
//        setLayoutParams(new ViewGroup.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
    }

    @Override
    public void cellInited(BaseCell cell) {
        mCell = (GridCell) cell;
    }

    @Override
    public void postBindView(BaseCell cell) {
        GridCell gridCell = (GridCell) cell;
        column = gridCell.column;
        if (column == 0) {
            column = 1;
        }
        GridCard.GridStyle style = gridCell.mStyle;
        if (style == null) {
            style = new GridCard.GridStyle();
        }
        int[] paddings = style.padding;
        setPadding(paddings[3], paddings[0], paddings[1], paddings[2]);
        setBackgroundColor(style.bgColor);
        vGap = style.vGap;
        hGap = style.hGap;

        recycleView();
        int id = 1;
        if (gridCell.mHeader != null) {
            headerViewHolder = getViewFromRecycler(gridCell.mHeader);
            View headerView = headerViewHolder.itemView;
            headerView.setId(id);
            LayoutParams params = new LayoutParams(0, LayoutParams.WRAP_CONTENT);
            params.leftToLeft = LayoutParams.PARENT_ID;
            params.topToTop = LayoutParams.PARENT_ID;
            params.rightToRight = LayoutParams.PARENT_ID;
            addView(headerView, params);
        }
        if (gridCell.mCells != null && !gridCell.mCells.isEmpty()) {
            for (BaseCell item : gridCell.mCells) {
                if (item == null) {
                    continue;
                }
                BinderViewHolder viewHolder = getViewFromRecycler(item);
                childrenCellBinders.add(viewHolder);
            }
        }
        if (!childrenCellBinders.isEmpty()) {
            int size = childrenCellBinders.size();
            int fullLineCount = size / column;
            int lines = size / column + (size % column == 0 ? 0 : 1);




            for (int i = 0; i < lines * column; i ++) {
                View view;
                if (i < size) {
                    BinderViewHolder viewHolder = childrenCellBinders.get(i);
                    view = viewHolder.itemView;

                } else {
                    view = new View(getContext());
                }
                int line = i / column + 1;
                int index = i % column + 1;
                id = line * 100 + index;
                view.setId(id);
                LayoutParams params = new LayoutParams(0, LayoutParams.WRAP_CONTENT);
                if (index == 1) {
                    params.leftToLeft = LayoutParams.PARENT_ID;
                    if (line == 1 && headerViewHolder == null) {
                        params.topToTop = LayoutParams.PARENT_ID;
                    } else {
                        params.topToBottom = id - 100;
                    }
                    if (line > 1) {
                        params.topMargin = vGap;
                    }
                }
                if (line == 1) {
                    params.horizontalWeight = 1;
                    if (index < column) {
                        params.rightMargin = hGap;
                        params.rightToLeft = id + 1;
                    }
                    if (index > 1) {
                        params.leftToRight = id -1;
                        params.topToTop = id - 1;
                        params.bottomToBottom = id - 1;
                    }
                    if (index == column) {
                        params.rightToRight = LayoutParams.PARENT_ID;
                    }
                } else {
                    params.leftToLeft = id - 100;
                    params.rightToRight = id - 100;
                    if (index == 1) {
                        params.topToBottom = id - 100;
                    }
                    if (index > 1) {
                        params.topToTop = id -1;
                        params.bottomToBottom = id -1;
                    }
                }
                addView(view, params);
            }
        }
        if (gridCell.mFooter != null) {
            id = (id / 100 + 1) * 100 + 1;
            footerViewHolder = getViewFromRecycler(gridCell.mFooter);
            View footerView = footerViewHolder.itemView;
            footerView.setId(id);
            LayoutParams params = new LayoutParams(0, LayoutParams.WRAP_CONTENT);
            params.leftToLeft = LayoutParams.PARENT_ID;
            if (id - 100 == 1 && headerViewHolder == null) {
                params.topToTop = LayoutParams.PARENT_ID;
            } else {
                params.topToBottom = id - 100;
            }
            params.rightToRight = LayoutParams.PARENT_ID;
            addView(footerView, params);
        }
    }

    @Override
    public void postUnBindView(BaseCell cell) {
        recycleView();
    }

    private BinderViewHolder getViewFromRecycler(@NonNull BaseCell cell) {
        GroupBasicAdapter adapter = cell.serviceManager.getService(GroupBasicAdapter.class);
        RecyclerView.RecycledViewPool pool = cell.serviceManager.getService(RecyclerView.RecycledViewPool.class);
        int itemViewType = adapter.getItemType(cell);
        BinderViewHolder holder = (BinderViewHolder) pool.getRecycledView(itemViewType);
        if (holder == null) {
            holder = (BinderViewHolder) adapter.createViewHolder(this, itemViewType);
        }
        holder.bind(cell);
        return holder;
    }

    private void recyclerView(List<BinderViewHolder> cache) {
        if (cache != null && !cache.isEmpty()) {
            RecyclerView.RecycledViewPool pool = mCell.serviceManager.getService(RecyclerView.RecycledViewPool.class);
            for (int i = 0, size = cache.size(); i < size; i++) {
                BinderViewHolder viewHolder = cache.get(i);
                if (viewHolder != null) {
                    viewHolder.unbind();
                    removeView(viewHolder.itemView);
                    pool.putRecycledView(viewHolder);
                }
            }
            cache.clear();
        }
    }

    private void recycleView() {
        removeAllViews();
        childrenCellBinders.add(headerViewHolder);
        childrenCellBinders.add(footerViewHolder);
        recyclerView(childrenCellBinders);
        headerViewHolder = null;
        footerViewHolder = null;
    }
}