# To enable ProGuard in your project, edit project.properties
# to define the proguard.config property as described in that file.
#
# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in ${sdk.dir}/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the ProGuard
# include property in project.properties.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

-ignorewarnings
-optimizationpasses 5
-dontusemixedcaseclassnames #【混淆时不会产生形形色色的类名 】
-dontskipnonpubliclibraryclasses #【指定不去忽略非公共的库类。 】
-dontpreverify #【不预校验】
-verbose
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/* #【优化】
-dontshrink


#--VideoBundle--start
#视频模块相关类
-keep public class com.ximalaya.ting.android.video.VideoApplication {*;}
-keep public class com.ximalaya.ting.android.xmplaysdk.IjkVideoView {*;}
-keep class com.ximalaya.ting.android.xmplaysdk.video.player.**{*;}
-keep class tv.danmaku.ijk.media.player.** {*;}
-keep class tv.danmaku.ijk.media.player.IjkMediaPlayer{*;}
-keep class tv.danmaku.ijk.media.player.ffmpeg.FFmpegApi{*;}
#--VideoBundle--end
