apply plugin: 'com.android.application'
apply plugin: 'com.android.dynamic-feature'
apply from: rootProject.rootDir.absolutePath + "/util.gradle"


//if(!buildFast.toBoolean()) {
//    apply plugin: 'android-aspectjx'
//
//    aspectjx {
//        rootProject.ext.aspectJExclude.each{//请在 config.gradle的aspectJExclude 添加
//            exclude it
//        }
//    }
//}

xmBundle {
    bundleName = "video"
    packageId = "0x67"
    packageName = "com.ximalaya.ting.android.video.application"
    isEncrypt = "true"
    isBuildIn = "true"
    encryptStr = rootProject.hostBundleVersion
}

def readChannel() {
    if (shallReadChannelfromFile.toBoolean()) {
        println "------------------read Channels from file start-------------------------"
        // 渠道号配置文件路径
        def path = "build-files/channels.txt"
        file(path).eachLine { channel ->
            if (!channel.startsWith("//")) { //剔除注释行
                setManifestValue(channel)
            }
        }
        println "------------------read Channels from file end-------------------------"
    } else if (project.hasProperty('pChannels')) {
        println "------------------read Channels from property start---------------"
        project.properties['pChannels'].split(',').each { channel ->
            setManifestValue(channel)
        }
        println "------------------read Channels from property end---------------"
    } else if (project.hasProperty('extChannels')) {
        println "------------------read extChannels from ext property start---------------"
        project.properties['extChannels'].split(',').each { channel ->
            setManifestValue(channel)
        }
        println "------------------read extChannels from ext property end---------------"
    } else {
        println "------------------No Channels-------------------------"
    }
}

// 设置Manifest里面需要替换的字段
private void setManifestValue(channel) {
    println channel
    android.productFlavors.create(channel, {
        if (isProguard.toBoolean() && !(isReleaseDebug.toBoolean())) {
            // 正式包
            manifestPlaceholders = [CHANNEL_VALUE: name, UMENG_APP_KEY: "516f69a256240b561e021be4"]
        } else {
            manifestPlaceholders = [CHANNEL_VALUE: name, UMENG_APP_KEY: "54dad2b2fd98c5a1db0005b8"]
        }
    })
}

def buildTime = new Date().format("yyMMdd")

dependencies {
    api fileTree(include: '*.jar', dir: 'libs')

    api project(':VideoBundle:VideoModule')
    providedhost project(path:':TingMainHost:TingMainApp',configuration: rootProject.configurationType)
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
//    dataBinding {
//        enabled true
//    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
//            java.srcDirs = ['src','src_chat','src_host','src_live','src_main']
//            resources.srcDirs = ['src']
//            aidl.srcDirs = ['src']
//            renderscript.srcDirs = ['src']
//            res.srcDirs = ['res', 'res_chat', 'res_host', 'res_live', 'res_main']
            assets.srcDirs = ['assets']
            jniLibs.srcDirs = ['libs']
        }

        androidTest.setRoot('tests')

        debug.setRoot('build-types/debug')
        release.setRoot('build-types/release')
    }

    defaultConfig {
        applicationId "com.ximalaya.ting.android.video.application"
        minSdkVersion Integer.parseInt(rootProject.property('minSdkVersion'))
        targetSdkVersion Integer.parseInt(rootProject.property('targetSdkVersion'))
        if (rootProject.hasProperty('pVersionCode')) {
            versionCode Integer.parseInt(rootProject.property('pVersionCode'))
        } else {
            versionCode Integer.parseInt(rootProject.property('versionCode'))
        }
        if (rootProject.hasProperty('pVersionName')) {
            versionName rootProject.property('pVersionName')
        } else {
            versionName rootProject.property('versionName')
        }


        //支持multidex 解决65536爆包问题
        multiDexEnabled false

//        ndk {
//            // 设置支持的SO库架构
//            abiFilters 'armeabi'/*, 'x86'*///, 'armeabi-v7a', 'x86_64', 'arm64-v8a'
//        }
    }

    splits {
        abi {
            reset()
            enable true
            universalApk false
//            include "arm64-v8a"
            if (buildFast.toBoolean()) {
                include "arm64-v8a"
            } else {
                include "armeabi-v7a", "arm64-v8a"
            }
        }
    }
    flavorDimensions "default"

    productFlavors {
        readChannel()
    }

//    productFlavors.all { flavor ->
//        flavor.manifestPlaceholders = [CHANNEL_VALUE: name]
//    }

    // 签名
    signingConfigs {
        myConfig {
            storeFile file("build-files/ximalaya.keystore")
            storePassword "ximalaya!1203"
            keyAlias "ximalaya"
            keyPassword "ximalaya!1203"
            v2SigningEnabled false
        }
//        debugConfig {
//            storeFile file("build-files/debug.keystore")
//            storePassword "android"
//            keyAlias "androiddebugkey"
//            keyPassword "android"
//            v2SigningEnabled false
//        }
    }
//    dexOptions {
//        incremental false
//        preDexLibraries = false
//        jumboMode = false
//    }

    //自动移除不用资源
    buildTypes {
//        debugNoUse {
//            signingConfig signingConfigs.debugConfig
//            // 显示Log
//            buildConfigField "boolean", "LOG_DEBUG", "true"
//
//            versionNameSuffix "-debug"
//            minifyEnabled false
//            zipAlignEnabled false
//            shrinkResources false
//            debuggable true
//        }
        release {
            debuggable rootProject.property('isReleaseDebug').toBoolean()
            signingConfig signingConfigs.myConfig
            //是否混淆
//            minifyEnabled rootProject.property('isProguard').toBoolean()
//            zipAlignEnabled rootProject.property('isProguard').toBoolean()
            // 移除无用的resource文件
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-project.txt'
            applicationVariants.all { variant ->
                variant.outputs.all {
                    def abi = it.getFilter(com.android.build.OutputFile.ABI)

                    outputFileName = createPluginApkName(needChannel.toBoolean() ? variant.productFlavors[0].name :"",
                            "${abi}",
                            defaultConfig.versionCode + "",
                            defaultConfig.versionName,
                            buildTime + "",
                            needChannel.toBoolean(),
                            minifyEnabled.toBoolean())
                }
            }
        }

    }

    lintOptions {
        // set to true to turn off analysis progress reporting by lint
        quiet true
        // if true, stop the gradle build if errors are found
        abortOnError false
        // if true, only report errors
        ignoreWarnings false
        checkAllWarnings true
        checkReleaseBuilds false
        lintConfig file("lint.xml")
    }

    compileOptions {
        sourceCompatibility rootProject.javaCompileVersion
        targetCompatibility rootProject.javaCompileVersion
    }

    //为所有的子项目设置一些通用配置
    subprojects {
        afterEvaluate {
            if (getPlugins().hasPlugin('android') ||
                    getPlugins().hasPlugin('android-library')) {
                android {
                    lintOptions {
                        quiet true
                        abortOnError false
                        ignoreWarnings true
                        checkAllWarnings false
                        checkReleaseBuilds false
                    }
                }
            }
        }
    }


    aaptOptions.cruncherEnabled = false

    dexOptions {
        maxProcessCount 4
        javaMaxHeapSize "2048M"
    }

}

android.variantFilter { variant ->
    if (variant.buildType.name.endsWith('debug')) {
        variant.setIgnore(true);
    }
}
