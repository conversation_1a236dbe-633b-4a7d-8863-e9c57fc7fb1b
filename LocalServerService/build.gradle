apply plugin: 'com.android.library'
apply from: project.rootDir.absolutePath + '/util.gradle'

dependencies {
    api project(path: ':XAndroidFramework:XFramework', configuration: rootProject.configurationType)
    api project(path:':XAndroidFramework:TingPhoneOpenSDK',configuration: rootProject.configurationType)
    compileOnly rootProject.ext.xmDependencies.supportV4
    compileOnly rootProject.ext.xmDependencies.okHttp
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion Integer.parseInt(rootProject.ext.minSdkVersion)
        targetSdkVersion Integer.parseInt(rootProject.ext.targetSdkVersion)
        versionCode 1
        versionName "1.0"
    }

    lintOptions {
        quiet true
        abortOnError false
        checkReleaseBuilds false
    }

    defaultConfig {
        consumerProguardFiles 'proguard-project.txt'
    }
}
