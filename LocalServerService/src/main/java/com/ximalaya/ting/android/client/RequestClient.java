package com.ximalaya.ting.android.client;

import android.content.Context;
import android.os.*;
import androidx.annotation.NonNull;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.localserver.FileLog;
import com.ximalaya.ting.android.localserver.HttpProxyServer;
import com.ximalaya.ting.android.localserver.LOG;
import com.ximalaya.ting.android.manager.KeepAliveManager;
import com.ximalaya.ting.android.model.KeepAliveModel;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.util.ToolUtil;
import org.jetbrains.annotations.NotNull;

import java.io.BufferedInputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/17 17:36
 */
public class RequestClient {
    private static final String TAG = "RequestClient";

    private WorkHandler mWorkHandler;
    private static String MOCK_URL = null;
    private HttpProxyServer mHttpProxyServer;
    private static boolean sOpenLocalServer = false;

    private RequestClient() {
        sOpenLocalServer = ConfigureCenter.getInstance().getBool(
                "android", "item_open_local_server", false);
        if (ConstantsOpenSdk.isDebug) {
            String open = ToolUtil.getDebugSystemProperty("debug.zimo.openLocalServer", null);
            if (open != null && open.length() > 0) {
                sOpenLocalServer = "1".equals(open);
            }
        }
        LOG.info(TAG, "local_server-" + sOpenLocalServer);
        FileLog.info(TAG, "local_server-" + sOpenLocalServer);
    }

    private static class RequestClientHolder {
        private static final RequestClient INSTANCE = new RequestClient();
    }

    public static RequestClient getInstance() {
        return RequestClientHolder.INSTANCE;
    }

    public boolean isRunning() {
        if (mWorkHandler == null) {
            return false;
        }
        return mWorkHandler.mIsRunning;
    }

    public void wakeHappen() {
        if (mWorkHandler != null) {
            mWorkHandler.wakeCountHappen();
        }
    }

    public void mockRequest() {
        LOG.info(TAG, "mockRequest");
        if (mWorkHandler != null) {
            mWorkHandler.resetInternal();
        }
        initLocalServer();
        initThread();
        mWorkHandler.sendEmptyMessage(WorkHandler.MSG_START);
        LOG.info(TAG, "mockRequest sendEmptyMessage MSG_START");
        FileLog.info(TAG, "mockRequest sendEmptyMessage MSG_START");
    }

    private void initThread() {
        if (mWorkHandler == null) {
            HandlerThread handlerThread = new HandlerThread("local_server_request_thread");
            handlerThread.start();
            mWorkHandler = new WorkHandler(handlerThread.getLooper());
            LOG.info(TAG, "initThread");
        }
    }

    private void initLocalServer() {
        if (!sOpenLocalServer) {
            return;
        }
        // check local server是否可用，不可用的话还需要做些其他处理
        if (mHttpProxyServer != null && !mHttpProxyServer.isAlive()) {
            LOG.info(TAG, "local server is not alive, restart now");
            mHttpProxyServer.shutdown();
            mHttpProxyServer = null;
        }
        if (mHttpProxyServer == null) {
            Context context = BaseApplication.getMyApplicationContext();
            mHttpProxyServer = new HttpProxyServer.Builder(context).setResponseLen(3_000).build();
            String url = "localMock";
            MOCK_URL = mHttpProxyServer.getProxyUrl(url, false);
            LOG.info(TAG, "initLocalServer " + MOCK_URL);
        }
    }

    // 如果发现一直在循环请求中，这里会上报数据
    public void endRequest() {
        if (mWorkHandler == null) {
            return;
        }
        // 为防止出现多线程同步问题，这里通过发消息来实现
        mWorkHandler.sendEmptyMessage(WorkHandler.MSG_END_REQUEST);
        LOG.info(TAG, "endRequest sendEmptyMessage MSG_END_REQUEST");
    }

    public void reset() {
        if (mWorkHandler != null) {
            mWorkHandler.sendEmptyMessage(WorkHandler.MSG_RESET);
            LOG.info(TAG, "reset sendEmptyMessage MSG_RESET");
        }
        LOG.info(TAG, "reset");
    }

    private static class WorkHandler extends Handler {
        private static final int MSG_START = 1;
        private static final int MSG_REQUEST = 2;
        private static final int MSG_RESET = 3;
        private static final int MSG_END_REQUEST = 4;

        private static final int REQUEST_DEFAULT_DELAY_TIME = 5_000;
        private static int REQUEST_DELAY_TIME = REQUEST_DEFAULT_DELAY_TIME;
        private static int REQUEST_MAX_DELAY_TIME = REQUEST_DEFAULT_DELAY_TIME + 2_000;

        private long mRequestStartTime = -1;
        private long mRequestLastTime = -1;

        private final List<Long> mTimeQueue = new LinkedList<>();
        private final byte[] mBuffer = new byte[1024];

        private boolean mIsRunning = false;
        private int mWakeCount = 0;

        public WorkHandler(@NonNull @NotNull Looper looper) {
            super(looper);
            if (ConstantsOpenSdk.isDebug) {
                String property = ToolUtil.getDebugSystemProperty("debug.zimo.requestDelay", null);
                if (property != null && property.length() > 0) {
                    try {
                        REQUEST_DELAY_TIME = Integer.parseInt(property);
                    } catch (Exception e) {
                        e.printStackTrace();
                        REQUEST_DELAY_TIME = REQUEST_DEFAULT_DELAY_TIME;
                    }
                    REQUEST_MAX_DELAY_TIME = REQUEST_DELAY_TIME + 2_000;
                }
            }
        }

        public long getCurAliveTime() {
            if (mRequestStartTime <= 0 || mRequestLastTime <= 0) {
                return -1;
            }
            long curTime = System.currentTimeMillis();
            // 去除最近的一个小于一个周期的数据，小于一个周期的数据有可能是因为是到了前台触发的
            while (mTimeQueue.size() > 0) {
                long lastTime = mTimeQueue.remove(mTimeQueue.size() - 1);
                if (curTime - lastTime < REQUEST_MAX_DELAY_TIME) {
                    LOG.info(TAG, "remove time " + lastTime);
                    FileLog.info(TAG, "remove time " + lastTime);
                } else {
                    return lastTime - mRequestStartTime;
                }
            }
            return -2;
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            switch (msg.what) {
                case MSG_START:
                    LOG.info(TAG, "WorkThread MSG_START");
                    mIsRunning = true;
                    mWakeCount = 0;
                    mRequestStartTime = System.currentTimeMillis();
                    mRequestLastTime = mRequestStartTime;
                    mockRequestDelay();
                    break;
                case MSG_REQUEST:
                    LOG.info(TAG, "WorkThread MSG_REQUEST");
                    mockRequest();
                    break;
                case MSG_END_REQUEST:
                    LOG.info(TAG, "WorkThread MSG_END_REQUEST");
                    FileLog.info(TAG, "WorkThread MSG_END_REQUEST");
                    endRequest();
                    break;
                case MSG_RESET:
                    resetInternal();
                    break;
                default:
                    break;
            }
        }

        private void wakeCountHappen() {
            mWakeCount++;
        }

        private void resetInternal() {
            mWakeCount = 0;
            mIsRunning = false;
            mRequestStartTime = -1;
            mRequestLastTime = -1;
            mTimeQueue.clear();
            removeMessages(WorkHandler.MSG_REQUEST);
            removeMessages(WorkHandler.MSG_START);
            removeMessages(WorkHandler.MSG_RESET);
        }

        private void mockRequestDelay() {
            removeMessages(MSG_REQUEST);
            // 延时发起请求
            sendEmptyMessageDelayed(MSG_REQUEST, REQUEST_DELAY_TIME);
            LOG.info(TAG, "mockRequestDelay sendEmptyMessageDelayed MSG_REQUEST");
        }

        private void mockRequest() {
            long startTime = System.currentTimeMillis();
            boolean isConnected = ToolUtil.isNetConnected();
            long intervalTime = startTime - mRequestLastTime;
            if (!isConnected) { // 退到后台执行时间是不确定的
                // 这里认为超时了，也就是上一次网络断了
                long aliveTime = mRequestLastTime - mRequestStartTime;
                LOG.info(TAG, "intervalTime " + intervalTime + ", isConnected " + isConnected + ", total alive time " + aliveTime);
                FileLog.info(TAG, "intervalTime " + intervalTime + ", isConnected " + isConnected + ", total alive time " + aliveTime);
                // shut down service
                resetInternal();

                KeepAliveModel aliveModel = new KeepAliveModel();
                aliveModel.setAliveTime(aliveTime);
                aliveModel.setLocalServerOpen(sOpenLocalServer);
                aliveModel.setType(KeepAliveModel.TYPE_DISCONNECT_FROM_BACKGROUND);
                aliveModel.setWakeCount(KeepAliveManager.getInstance().getWakeCount());
                KeepAliveManager.getInstance().uploadModel(aliveModel);
                return;
            } else {
                LOG.info(TAG, "intervalTime: " + intervalTime + ", isConnected " + isConnected + ", totalRunTime=" + (startTime - mRequestStartTime));
                FileLog.info(TAG, "intervalTime: " + intervalTime + ", isConnected " + isConnected + ", totalRunTime=" + (startTime - mRequestStartTime));
            }
            boolean isPlaying = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying();
            if (isPlaying) {
                // 发现现在开始播放了，直接return掉，不做上报
                LOG.info(TAG, "isPlaying=true");
                resetInternal();
                return;
            }
            addTimeToQueue(startTime);
            mRequestLastTime = startTime;
            connectToLocalServer();
            LOG.debug(TAG, "request cost time " + (System.currentTimeMillis() - startTime));
            mockRequestDelay();
        }

        private void addTimeToQueue(long time) {
            mTimeQueue.add(time);
            while (mTimeQueue.size() > 20) {
                mTimeQueue.remove(0);
            }
        }

        private void connectToLocalServer() {
            if (!sOpenLocalServer) {
                return;
            }
            LOG.debug(TAG, "connectToLocalServer " + MOCK_URL);
            HttpURLConnection connection = null;
            try {
                connection = (HttpURLConnection) new URL(MOCK_URL).openConnection();
                connection.setConnectTimeout(5_000);
                connection.setReadTimeout(5_000);
                int resCode = connection.getResponseCode();
                BufferedInputStream bufferedInputStream = new BufferedInputStream(connection.getInputStream());
                int length = -1;
                int totalLength = 0;
                while ((length = bufferedInputStream.read(mBuffer, 0, 1024)) > 0) {
                    totalLength += length;
                }
                LOG.info(TAG, "totalLength=" + totalLength);
            } catch (Exception e) {
                LOG.error(TAG, "mockRequest exception", e);
            } finally {
                if (connection != null) {
                    connection.disconnect();
                }
            }
        }

        private void endRequest() {
            if (mIsRunning && mRequestStartTime > 0) {
                long curTime = System.currentTimeMillis();
                StringBuilder sb = new StringBuilder();
                int size = mTimeQueue.size();
                for (int i = 0; i < size; i++) {
                    sb.append(curTime - mTimeQueue.get(i)).append(", ");
                }
                FileLog.info(TAG, sb.toString());

                long aliveTime = getCurAliveTime();
                LOG.info(TAG, "endRequest " + aliveTime);
                FileLog.info(TAG, "endRequest " + aliveTime);
                if (aliveTime > 0) {
                    KeepAliveModel model = new KeepAliveModel();
                    model.setTotalTime(curTime - mRequestStartTime);
                    model.setAliveTime(aliveTime);
                    model.setType(KeepAliveModel.TYPE_SWITCH_TO_FOREGROUND);
                    model.setWakeCount(KeepAliveManager.getInstance().getWakeCount());
                    model.setLocalServerOpen(sOpenLocalServer);
                    KeepAliveManager.getInstance().uploadModel(model);
                }
            }
            resetInternal();
        }
    }
}
