package com.ximalaya.ting.android.manager;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import com.ximalaya.ting.android.client.RequestClient;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.startup.IOnAppStatusChangedListener;
import com.ximalaya.ting.android.framework.startup.XmStartUpActivityLifecycle;
import com.ximalaya.ting.android.localserver.FileLog;
import com.ximalaya.ting.android.localserver.LOG;
import com.ximalaya.ting.android.model.KeepAliveModel;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.util.ToolUtil;
import com.ximalaya.ting.android.xmlog.XmLogger;

/**
 * <AUTHOR>
 * @date 2023/2/20 17:29
 */
public class KeepAliveManager {
    private static final String TAG = "XmKeepAliveManager";

    private final Handler mHandler;
    private boolean mKeepAliveOpen = false;
    private int mWakeCount = 0;
    private final XmStartUpActivityLifecycle mStartUpActivityLifecycle = new XmStartUpActivityLifecycle();
    private final IOnAppStatusChangedListener mAppStatusChangedListener = new IOnAppStatusChangedListener() {
        @Override
        public void onForeground(Intent it) {
            if (!mKeepAliveOpen) {
                return;
            }
            LOG.info(TAG, "onForeground");
            // 回到前台，则关闭请求
            RequestClient.getInstance().endRequest();
        }

        @Override
        public void onBackground(Intent it, boolean fromPause) {
            if (!mKeepAliveOpen || fromPause) {
                return;
            }
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                return;
            }
//            if (BuildProperties.isHuawei()) {
//                return;
//            }
            if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying()) {
                return;
            }

            // 退到后台，且当前状态是未充电的状态，则开启localServer服务
            LOG.info(TAG, "onBackground");

            mWakeCount = 0;
            mHandler.removeCallbacks(mRequestRunnable);
            mHandler.post(mRequestRunnable);
        }
    };

    private final Runnable mRequestRunnable = new Runnable() {
        @Override
        public void run() {
            mHandler.removeCallbacks(mRequestRunnable);

            long curTime = System.currentTimeMillis();
            boolean isCharging = true;
            try {
                Context context = BaseApplication.getMyApplicationContext();
                Intent batIntent = context.registerReceiver(null, new IntentFilter(Intent.ACTION_BATTERY_CHANGED));
                if (batIntent == null) {
                    return;
                }
                int plugged = batIntent.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1);
                isCharging = plugged == BatteryManager.BATTERY_PLUGGED_AC
                        || plugged == BatteryManager.BATTERY_PLUGGED_USB
                        || plugged == BatteryManager.BATTERY_PLUGGED_WIRELESS;
            } catch (Throwable ignored) {
                return;
            }

            boolean isConnected = ToolUtil.isNetConnected();
            boolean isPlaying = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying();

            LOG.info(TAG, "onBackground isCharging=" + isCharging + ", isConnected=" + isConnected + ", isPlaying=" + isPlaying);

            if (!isCharging && isConnected && !isPlaying) {
                RequestClient.getInstance().mockRequest();

                LOG.info(TAG, "onBackground costTime=" + (System.currentTimeMillis() - curTime));
            }
        }
    };

    private KeepAliveManager() {
        mHandler = new Handler(Looper.getMainLooper());
        BaseApplication.sInstance.realApplication.registerActivityLifecycleCallbacks(mStartUpActivityLifecycle);
        mStartUpActivityLifecycle.addOnAppStatusChangedListener(mAppStatusChangedListener);

        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_SCREEN_ON);
        filter.addAction(Intent.ACTION_SCREEN_OFF);
        try {
            if (BaseApplication.mAppInstance != null) {
                BaseApplication.mAppInstance.registerReceiver(mScreenBroadcastReceiver, filter);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    BroadcastReceiver mScreenBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (!mKeepAliveOpen) {
                return;
            }
            String action = intent.getAction();
            LOG.info(TAG, "ScreenBroadcastReceiver action=" + action);

            if (RequestClient.getInstance().isRunning() && Intent.ACTION_SCREEN_ON.equals(action)) {
                mWakeCount++;
                LOG.info(TAG, "ScreenBroadcastReceiver mWakeCount=" + mWakeCount);
//                RequestClient.getInstance().wakeHappen();
            }
        }
    };

    public int getWakeCount() {
        return mWakeCount;
    }

    public void uploadModel(KeepAliveModel aliveModel) {
        if (aliveModel == null || aliveModel.getTotalTime() < 10_000 || aliveModel.getAliveTime() <= 0) {
            return;
        }
        String jsonString = aliveModel.toJsonString();
        LOG.info(TAG, jsonString);
        FileLog.info(TAG, jsonString);
        XmLogger.log("apm", "keepAlive", jsonString);
    }

    private static class Holder {
        private static final KeepAliveManager INSTANCE = new KeepAliveManager();
    }

    public static KeepAliveManager getInstance() {
        return Holder.INSTANCE;
    }

    public void startKeepAlive() {
        LOG.info(TAG, "startKeepAlive");
        mKeepAliveOpen = true;
    }

}
