package com.ximalaya.ting.android.localserver;

import android.os.Environment;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2023/2/17 14:32
 */
public class LOG {
    private static PrintWriter sPrintWriter = null;
    private static SimpleDateFormat sDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());

    private static void printToFile(String tag, String msg) {
//        if (!ConstantsOpenSdk.isDebug) {
//            return;
//        }
//        if (sPrintWriter == null) {
//            File file = new File(Environment.getExternalStorageDirectory(), "xmly_log.txt");
//            file.delete();
//            if (!file.exists()) {
//                try {
//                    file.createNewFile();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//            try {
//                sPrintWriter = new PrintWriter(file);
//            } catch (FileNotFoundException e) {
//                e.printStackTrace();
//            }
//        }
//
//        if (sPrintWriter == null) {
//            return;
//        }
//
//        sPrintWriter.append(sDateFormat.format(System.currentTimeMillis())).append(":").append(tag).append(":").append(msg).append("\n");
//        sPrintWriter.flush();
//
//        File file = new File(Environment.getExternalStorageDirectory(), "xmly_log.txt");
//        file.setLastModified(System.currentTimeMillis());
    }

    public static void debug(String tag, String msg) {
        Logger.d(tag, msg);
        printToFile(tag, msg);
    }

    public static void info(String tag, String msg) {
        Logger.i(tag, msg);
        printToFile(tag, msg);
    }

    public static void warn(String tag, String msg) {
        Logger.w(tag, msg);
        printToFile(tag, msg);
    }

    public static void warn(String tag, String msg, Throwable throwable) {
        Logger.w(tag, msg, throwable);
        printToFile(tag, msg);
    }

    public static void error(String tag, String msg) {
        Logger.e(tag, msg);
        printToFile(tag, msg);
    }

    public static void error(String tag, String msg, Throwable throwable) {
        Logger.e(tag, msg, throwable);
        printToFile(tag, msg);
    }
}
