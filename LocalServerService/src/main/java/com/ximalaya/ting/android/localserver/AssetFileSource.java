package com.ximalaya.ting.android.localserver;

import android.content.Context;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.localserver.headers.HeaderInjector;
import com.ximalaya.ting.android.localserver.sourcestorage.SourceInfoStorage;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2023/9/6 20:11
 */
public class AssetFileSource extends HttpUrlSource {
    private static final String TAG = "AssetFileSource";

    private Context mContext;

    private String urlPath;

    private InputStream mInputStream;

    public AssetFileSource(HttpUrlSource source) {
        super(source);
        this.urlPath = source.getUrl();
        String filePath = urlPath.substring("android_asset/".length());
        Logger.i(TAG, "urlPath=" + filePath);
        mContext = BaseApplication.getMyApplicationContext();
        try {
            mInputStream = mContext.getAssets().open(filePath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public AssetFileSource(String url, SourceInfoStorage sourceInfoStorage, HeaderInjector headerInjector) {
        super(url, sourceInfoStorage, headerInjector);
    }

    @Override
    public synchronized long length() throws ProxyCacheException {
        try {
            return mInputStream.available();
        } catch (IOException e) {
            return 0;
        }
    }

    @Override
    public void open(long offset) throws ProxyCacheException {
    }

    @Override
    public void close() throws ProxyCacheException {
        try {
            if (mInputStream != null) {
                mInputStream.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public int read(byte[] buffer) throws ProxyCacheException {
        if (mInputStream != null) {
            try {
                return mInputStream.read(buffer);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return -1;
    }

    @Override
    public String toString() {
        return "AssetFileSource{" +
                "urlPath='" + urlPath + '\'' +
                '}';
    }
}
