package com.ximalaya.ting.android.localserver;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.Socket;

/**
 * <AUTHOR>
 * @date 2023/2/17 17:15
 */
public class HttpProxyMockSource {
    private static final String TAG = "HttpProxyMockSource";

    private static final String MOCK_REQUEST = "localMock";
    private static final byte[] MOCK_DATA = new byte[1024];
    private final int RESPONSE_LEN;

    public HttpProxyMockSource(Config config) {
        RESPONSE_LEN = Math.max(config.responseLen, 10);
    }

    boolean isMockRequest(String request) {
        return MOCK_REQUEST.equals(request);
    }

    void processRequest(Socket socket) throws IOException {
        OutputStream out = new BufferedOutputStream(socket.getOutputStream());
        out.write("HTTP/1.1 200 OK\n\n".getBytes());
        for (int i = 0; i < RESPONSE_LEN; i++) {
            out.write(MOCK_DATA);
        }
        out.flush();
        LOG.info(TAG, "processRequest end " + RESPONSE_LEN);
    }
}
