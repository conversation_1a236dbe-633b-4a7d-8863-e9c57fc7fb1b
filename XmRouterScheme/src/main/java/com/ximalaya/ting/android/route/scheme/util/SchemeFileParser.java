package com.ximalaya.ting.android.route.scheme.util;

import com.google.gson.Gson;
import com.google.gson.JsonParseException;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.hybrid.intercept.IFetchCallback;
import com.ximalaya.ting.android.hybrid.intercept.model.WebResource;
import com.ximalaya.ting.android.hybrid.intercept.util.FileUtil;
import com.ximalaya.ting.android.route.scheme.model.RouterSchemeModel;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by chengyun.wu on 2018/11/1.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17621691226
 */
public class SchemeFileParser {
    public static void parseTheConfigFile(File configJson, IFetchCallback<List<RouterSchemeModel>> callback){
        if (!configJson.exists()) {
            if(callback != null){
                callback.onError("scheme.json is not exist in ");
                return;
            }
        }

        String configString = FileUtil.readStrFromFile(configJson.getAbsolutePath());
        try {
            if(configString == null || configString.length() == 0){
                if(callback != null){
                    callback.onError("configString is null or length is 0 ");
                    return;
                }
            }
            Gson gson = new Gson();
            List<RouterSchemeModel> schemeModelList = gson.fromJson(configString,
                    new TypeToken<List<RouterSchemeModel>>(){}.getType());
            if(callback != null){
                callback.onSuccess(schemeModelList);
            }
        } catch (Exception e) {
            if(callback != null){
                callback.onError("scheme.json parse error " + e.getMessage());
                return;
            }
            e.printStackTrace();
        }
    }
}
