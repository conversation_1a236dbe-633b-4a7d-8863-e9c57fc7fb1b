package com.ximalaya.ting.android.route.scheme;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.hybrid.intercept.IFetchCallback;
import com.ximalaya.ting.android.hybrid.intercept.server.IGetHttpURLConnection;
import com.ximalaya.ting.android.hybrid.intercept.server.IWebResourceDownloader;
import com.ximalaya.ting.android.hybrid.intercept.server.WebResourcesDownloader;
import com.ximalaya.ting.android.hybrid.intercept.util.ZipUtil;
import com.ximalaya.ting.android.route.scheme.model.CheckSchemeData;
import com.ximalaya.ting.android.route.scheme.model.RouterSchemeModel;
import com.ximalaya.ting.android.route.scheme.model.SchemeSetting;
import com.ximalaya.ting.android.route.scheme.server.XmRouterSchemeFetcher;
import com.ximalaya.ting.android.route.scheme.util.SchemeFileParser;
import com.ximalaya.ting.android.route.scheme.util.XmRouterSchemeSpUtil;

import java.io.File;
import java.net.HttpURLConnection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;

/**
 * Created by chengyun.wu on 2019-06-10.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17621691226
 */
public class XmRouterSchemeManager {

    public static final String TAG = "RouterScheme";

    private Map<String, SchemeSetting> mRouterSchemeModelMap = new HashMap<>();

    private Context mContext;
    private IXmRouterSchemeConfig mIXmRouterSchemeConfig;
    private final ScheduledExecutorService SCHEDULER;
    private ScheduledFuture mTimer;
    private ExecutorService mRunExecutorService;

    private static class InnerHolder{
        private static XmRouterSchemeManager instance = new XmRouterSchemeManager();
    }

    private XmRouterSchemeManager() {
        SCHEDULER = Executors.newScheduledThreadPool(1, new ThreadFactory() {
            @Override
            public Thread newThread(@NonNull Runnable r) {
                Thread thread = new Thread(r);
                thread.setName("XmRouterSchemeManager-timer-task");
                return thread;
            }
        });
    }

    public static XmRouterSchemeManager getInstance(){
        return InnerHolder.instance;
    }

    public void init(Context context, IXmRouterSchemeConfig xmRouterSchemeConfig){
        mContext = context;
        mIXmRouterSchemeConfig = xmRouterSchemeConfig;
        mRunExecutorService = Executors.newSingleThreadExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(@NonNull Runnable r) {
                Thread thread = new Thread(r, "XmRouterSchemeManager");
                return thread;
            }
        });

        if(mTimer != null){
            mTimer.cancel(true);
        }
//        mTimer = SCHEDULER.scheduleAtFixedRate(new CheckSchemeRunnable(),
//                mIXmRouterSchemeConfig.getCheckServerConfigInternalTime(),
//                mIXmRouterSchemeConfig.getCheckServerConfigInternalTime(), TimeUnit.MINUTES);
    }


    class CheckSchemeRunnable implements Runnable{

        @Override
        public void run() {
            fetchServerConfigUrl();
        }
    }

    public void release(){
        if(mTimer != null && !mTimer.isCancelled()){
            mTimer.cancel(true);
        }
        mTimer = null;
    }

    public void readSchemePathConfig(){
        if (mRunExecutorService == null){
            return;
        }
        mRunExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                readLocalPathConfig(new IFetchCallback<List<RouterSchemeModel>>() {
                    @Override
                    public void onSuccess(List<RouterSchemeModel> routerSchemeModels) {
                        if(routerSchemeModels != null && routerSchemeModels.size() > 0){
                            Map<String, SchemeSetting> map = new HashMap<>(routerSchemeModels.size());
                            for(RouterSchemeModel schemeModel : routerSchemeModels){
                                map.put(schemeModel.getPathView(), schemeModel.getSchemeSetting());
                            }
                            mRouterSchemeModelMap = map;
                        }
                    }

                    @Override
                    public void onError(String message) {
                        statErrorToServer("read local file fail " + message);
                    }
                });
                fetchServerConfigUrl();
            }
        });
    }

    public SchemeSetting getSchemeByPathView(String pathView) {
        if (!TextUtils.isEmpty(pathView) && mRouterSchemeModelMap != null) {
            synchronized (XmRouterSchemeManager.class) {
                if(mRouterSchemeModelMap != null) {
                    return mRouterSchemeModelMap.get(pathView);
                }
            }
        }
        return null;
    }

    public void fetchServerConfigUrl() {
        XmRouterSchemeFetcher xmRouterSchemeFetcher = new XmRouterSchemeFetcher(mContext, mIXmRouterSchemeConfig);
        xmRouterSchemeFetcher.checkServerResource(new IFetchCallback<CheckSchemeData>() {
            @Override
            public void onSuccess(final CheckSchemeData checkSchemeData) {
                if(checkSchemeData == null) {
                    return;
                }
                String localVersion = XmRouterSchemeSpUtil.getLocalVersion(mContext);
                if(!TextUtils.isEmpty(localVersion) && localVersion.equals(checkSchemeData.getVersion())){
                    return;
                }

                if(mIXmRouterSchemeConfig != null && mRunExecutorService != null){
                    mRunExecutorService.execute(new Runnable() {
                        @Override
                        public void run() {
                            WebResourcesDownloader downloader = new WebResourcesDownloader(new IGetHttpURLConnection() {
                                @Override
                                public HttpURLConnection getUrlConnection(String url, long startPos) {
                                    return mIXmRouterSchemeConfig.getUrlConnection(url, startPos);
                                }
                            });
                            startDownloadZipFile(downloader, checkSchemeData);
                        }
                    });
                }
            }

            @Override
            public void onError(String message) {
                statErrorToServer("checkServerResource fail " + message);
            }
        });
    }

    private void startDownloadZipFile(WebResourcesDownloader downloader, final CheckSchemeData checkSchemeData) {
        final String zipSaveFilePath = getSaveTmpZipFilePath(checkSchemeData);
        downloader.download(checkSchemeData.getUrl(), zipSaveFilePath, checkSchemeData.getMd5(),
                new IWebResourceDownloader.DownloadListener() {
            @Override
            public void onSuccess() {
                if(installZipFile(zipSaveFilePath, getSaveUnZipFilePath())){
                    readLocalPathConfig(new IFetchCallback<List<RouterSchemeModel>>() {
                        @Override
                        public void onSuccess(List<RouterSchemeModel> routerSchemeModels) {
                            if(routerSchemeModels != null && routerSchemeModels.size() > 0){
                                Map<String, SchemeSetting> map = new HashMap<>(routerSchemeModels.size());
                                for(RouterSchemeModel schemeModel : routerSchemeModels){
                                    map.put(schemeModel.getPathView(), schemeModel.getSchemeSetting());
                                }
                                mRouterSchemeModelMap = map;
                            }else {
                                if(mRouterSchemeModelMap != null){
                                    synchronized (XmRouterSchemeManager.class){
                                        mRouterSchemeModelMap.clear();
                                        mRouterSchemeModelMap = null;
                                    }
                                }
                            }

                            XmRouterSchemeSpUtil.saveLocalVersion(mContext, checkSchemeData.getVersion());
                            File tempFile = new File(zipSaveFilePath);
                            if(tempFile.exists()){
                                tempFile.delete();
                            }
                        }

                        @Override
                        public void onError(String message) {
                            statErrorToServer("read local file fail " + message);
                        }
                    });
                }
            }

            @Override
            public void onFail(String errorMsg) {
                statErrorToServer("download file fail " + errorMsg);
            }
        });
    }

    /**
     * 安装的时候不能读取，解压会覆盖文件
     * @param zipPath
     * @param destPath
     * @return
     */
    private boolean installZipFile(String zipPath, String destPath){
        File zipFile = new File(zipPath);
        if(!zipFile.exists()){
            return false;
        }

        File destFile = new File(destPath);
        if(!destFile.exists()){
            destFile.mkdirs();
        }
        try {
            ZipUtil.unZip(zipPath, destPath);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private String getSaveTmpZipFilePath(CheckSchemeData checkSchemeData) {
        File file = new File(mContext.getFilesDir().getAbsolutePath() + File.separator + TAG + "Tmp");
        if(!file.exists()){
            file.mkdirs();
        }
        return file.getAbsolutePath()
                + File.separator + checkSchemeData.getMd5() + ".zip";
    }

    private String getSaveUnZipFilePath() {
        File file = new File(mContext.getFilesDir().getAbsolutePath() + File.separator + TAG);
        if(!file.exists()){
            file.mkdirs();
        }
        return file.getAbsolutePath();
    }

    private void statErrorToServer(String message){
        if(mIXmRouterSchemeConfig != null){
            mIXmRouterSchemeConfig.statErrorInfo(message);
        }
    }

    /**
     * 读取文件时不能安装，防止冲突
     * @param callback
     */
    private void readLocalPathConfig(@Nullable final IFetchCallback<List<RouterSchemeModel>> callback) {
        if(mContext == null){
            return;
        }
        File configFile = new File(mContext.getFilesDir().getAbsolutePath() + File.separator + TAG
                + File.separator + "scheme.json");
        if(!configFile.exists()){
            return;
        }

        SchemeFileParser.parseTheConfigFile(configFile, new IFetchCallback<List<RouterSchemeModel>>() {
            @Override
            public void onSuccess(List<RouterSchemeModel> routerSchemeModels) {
                if (callback != null) {
                    callback.onSuccess(routerSchemeModels);
                }
            }

            @Override
            public void onError(String message) {
                if(callback != null){
                    callback.onError(message);
                }
            }
        });
    }
}
