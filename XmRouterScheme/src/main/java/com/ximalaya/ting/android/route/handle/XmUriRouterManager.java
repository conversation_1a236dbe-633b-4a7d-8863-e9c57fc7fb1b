package com.ximalaya.ting.android.route.handle;

import android.content.Context;
import android.net.Uri;

import com.ximalaya.ting.android.route.handle.model.XmInterruptErrorInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by chengyun.wu on 2019/4/10.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17621691226
 */
public class XmUriRouterManager {

    private static final String TAG = "XmUriRouterManager";

    private List<IXmInterceptor> mIInterceptorList;
    private Context mContext;
    private Map<String, BundleRouterHandler> mBundleRouterHandlerMap;

    private static class InnerHolder{
        private static XmUriRouterManager instance = new XmUriRouterManager();
    }

    private XmUriRouterManager() {
        mIInterceptorList = new ArrayList<>();
        mBundleRouterHandlerMap = new HashMap<>();
    }

    public static XmUriRouterManager getInstance(){
        return InnerHolder.instance;
    }

    public void addInterceptor(IXmInterceptor iInterceptor){
        mIInterceptorList.add(iInterceptor);
    }

    public void addBundleRouteHandler(String bundleName, BundleRouterHandler bundleRouterHandler){
        mBundleRouterHandlerMap.put(bundleName, bundleRouterHandler);
    }

    public void routeXmUri(final Uri uri, final IXmRouterCallback navigationCallback) {
        handleRoutePostcard(uri, new IXmRouterInterceptorCallback(){

            @Override
            public void onContinue(Uri uri) {
                if (uri != null) {
                    List<String> pathList = uri.getPathSegments();
                    if (pathList != null && pathList.size() > 0) {
                        BundleRouterHandler bundleRouterHandler = mBundleRouterHandlerMap.get(pathList.get(0));
                        if (bundleRouterHandler != null) {
                            bundleRouterHandler.handleRouteAction(uri, navigationCallback);
                        } else {
                            if (navigationCallback != null) {
                                navigationCallback.onFail(IXmRouterCallback.FAIL_ROUTE_NOT_FOUND, "uri destination not found");
                            }
                        }
                    }else {
                        if (navigationCallback != null) {
                            navigationCallback.onFail(IXmRouterCallback.FAIL_ROUTE_NOT_FOUND, "uri path is empty");
                        }
                    }
                }
            }

            @Override
            public void onInterrupt(XmInterruptErrorInfo exception) {
                if(navigationCallback != null){
                    navigationCallback.onFail(exception.getErrorCode(), exception.getErrorMessage());
                }
            }
        });
    }

    public void handleRoutePostcard(Uri uri, final IXmRouterInterceptorCallback interceptorCallback){
        if(mIInterceptorList.size() == 0){
            interceptorCallback.onContinue(uri);
            return;
        }

        try {
            final int[] count = new int[1];
            count[0] = mIInterceptorList.size();
            final Uri[] uris = new Uri[1];
            final XmInterruptErrorInfo[] errorInfos = new XmInterruptErrorInfo[1];
            uris[0] = uri;
            _excute(0, count, uris, errorInfos, new IInterceptDone() {
                @Override
                public void done() {
                    if (count[0] > 0) {    // Cancel the navigation this time, if it hasn't return anythings.
                        if(errorInfos[0] != null) {
                            interceptorCallback.onInterrupt(errorInfos[0]);
                        }else {
                            interceptorCallback.onInterrupt(new XmInterruptErrorInfo(-1, "interceptor error"));
                        }
                    } else  {
                        interceptorCallback.onContinue(uris[0]);
                    }
                }
            });
        } catch (Exception e) {
            interceptorCallback.onInterrupt(new XmInterruptErrorInfo(-1, e.getMessage()));
        }

    }

    public void init(Context context){
        mContext = context;
    }

    /**
     * Excute interceptor
     *
     * @param index    current interceptor index
     * @param uris routeMeta
     */
    private void _excute(final int index, final int[] count, final Uri[] uris, final XmInterruptErrorInfo[] errorInfos,
                         final IInterceptDone interceptDone) {
        if (index < mIInterceptorList.size()) {
            IXmInterceptor iInterceptor = mIInterceptorList.get(index);
            iInterceptor.process(uris[0], new IXmRouterInterceptorCallback() {
                @Override
                public void onContinue(Uri uri) {
                    count[0]--;
                    uris[0] = uri;
                    _excute(index + 1, count, uris, errorInfos, interceptDone);
                }

                @Override
                public void onInterrupt(XmInterruptErrorInfo exception) {
                    errorInfos[0] = exception;
                    if(interceptDone != null){
                        interceptDone.done();
                    }
                }
            });
        }else {
            if(interceptDone != null){
                interceptDone.done();
            }
        }
    }

    public interface IInterceptDone{
        void done();
    }
}
