package com.ximalaya.ting.android.route.scheme.util;

import android.content.Context;

/**
 * Created by chengyun.wu on 2018/10/30.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17621691226
 */
public class XmRouterSchemeSpUtil {
    public static final String SP_SAVE_KEY = "SP_ROUTER_SCHEME_LOCAL_VERSION";
    public static final String SP_FILE_NAME = "xm_route_scheme_res";

    public static String getLocalVersion(Context context){
        return context.getSharedPreferences(SP_FILE_NAME, Context.MODE_PRIVATE)
                .getString(SP_SAVE_KEY, "");
    }

    public static void saveLocalVersion(Context context, String version){
        context.getSharedPreferences(SP_FILE_NAME, Context.MODE_PRIVATE)
                .edit().putString(SP_SAVE_KEY, version).apply();
    }
}
