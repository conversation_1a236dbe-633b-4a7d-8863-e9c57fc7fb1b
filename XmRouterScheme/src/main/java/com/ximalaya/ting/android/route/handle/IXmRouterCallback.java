package com.ximalaya.ting.android.route.handle;

/**
 * Created by chengyun.wu on 2019-06-12.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17621691226
 */
public interface IXmRouterCallback {

    int FAIL_ROUTE_INTERCEPT = 403;
    int FAIL_ROUTE_PERMISSION = 401;
    int FAIL_ROUTE_NOT_FOUND = 404;
    int FAIL_CONDITION_ERROR = 412;

    void onSuccess();

    void onFail(int code, String message);

}
