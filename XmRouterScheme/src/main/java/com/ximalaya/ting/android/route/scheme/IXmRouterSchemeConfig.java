package com.ximalaya.ting.android.route.scheme;

import com.ximalaya.ting.android.hybrid.intercept.ISignatureGenerator;

import java.net.HttpURLConnection;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * Created by chengyun.wu on 2019-06-10.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17621691226
 */
public interface IXmRouterSchemeConfig {

    HttpURLConnection getUrlConnection(String url, long startPos);

    ISignatureGenerator getISignatureGenerator();

    String getCheckSchemeUrl();

    Map<String, String> getHeader();

    /**
     *
     * @return minute
     */
    int getCheckServerConfigInternalTime();

    void statErrorInfo(String errorInfo);
}
