package com.ximalaya.ting.android.route.scheme.model;

/**
 * Created by chengyun.wu on 2019-06-10.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17621691226
 */
/*
{
	"pathView": "/XMFindTopViewController<*>/XMSegmentTabViewController[0]/XMPageViewController[0]/XMFindFlowViewController[0]/UIView[0]/UITableView[0]/UIView[0]/XMFindOptionScrollView[0]/UIScrollView[0]/XMFindOptionView<*>",
	"schemeSetting": {
		"degradeUrl": "www.baidu.com",
		"forceDegrade": false,
		"grade": 12,
		"method": "get",
		"parmas": [{
			"defaultValue": "",
			"name": "anchor_title"
		}, {
			"defaultValue": "normal",
			"name": "type"
		}, {
			"defaultValue": "music",
			"name": "anchor_category"
		}],
		"schemeName": "xmly://page.xm/main/anchor_list?type=normal&anchor_category=music",
		"template": ""
	}
}
 */
public class RouterSchemeModel {
    private String pathView;

    private SchemeSetting schemeSetting;

    public String getPathView() {
        return pathView;
    }

    public void setPathView(String pathView) {
        this.pathView = pathView;
    }

    public SchemeSetting getSchemeSetting() {
        return schemeSetting;
    }

    public void setSchemeSetting(SchemeSetting schemeSetting) {
        this.schemeSetting = schemeSetting;
    }
}
