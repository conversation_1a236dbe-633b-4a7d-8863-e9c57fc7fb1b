package com.ximalaya.ting.android.route.scheme.server;

import android.content.Context;
import android.text.TextUtils;

import com.ximalaya.ting.android.hybrid.intercept.IFetchCallback;
import com.ximalaya.ting.android.hybrid.intercept.InterceptConfig;
import com.ximalaya.ting.android.hybrid.intercept.model.CheckResourceData;
import com.ximalaya.ting.android.hybrid.intercept.util.AppVersionUtil;
import com.ximalaya.ting.android.hybrid.intercept.util.SpUtil;
import com.ximalaya.ting.android.route.scheme.IXmRouterSchemeConfig;
import com.ximalaya.ting.android.route.scheme.model.CheckSchemeData;
import com.ximalaya.ting.httpclient.HttpCallback;
import com.ximalaya.ting.httpclient.HttpClient;
import com.ximalaya.ting.httpclient.Schedulers;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * Created by chengyun.wu on 2018/10/30.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17621691226
 */
/*
GET请求参数
http://mobile.ximalaya.com/dog-portal/check/scheme/1540619837248
返回结果
{
    "msg":"0",
    "ret":0,
    "data":[
        {
	"pathView": "/XMFindTopViewController<*>/XMSegmentTabViewController[0]/XMPageViewController[0]/XMFindFlowViewController[0]/UIView[0]/UITableView[0]/UIView[0]/XMFindOptionScrollView[0]/UIScrollView[0]/XMFindOptionView<*>",
	"schemeSetting": {
		"degradeUrl": "www.baidu.com",
		"forceDegrade": false,
		"grade": 12,
		"method": "get",
		"parmas": [{
			"defaultValue": "",
			"name": "anchor_title"
		}, {
			"defaultValue": "normal",
			"name": "type"
		}, {
			"defaultValue": "music",
			"name": "anchor_category"
		}],
		"schemeName": "xmly://page.xm/main/anchor_list?type=normal&anchor_category=music",
		"template": ""
	}
}
    ],
    "signature":"9c0b0a6aedc68cc879ca17ca47c26cd9"
}
 */
public class XmRouterSchemeFetcher {

    private IXmRouterSchemeConfig mIXmRouterSchemeConfig;

    private Context mContext;

    public XmRouterSchemeFetcher(Context context, IXmRouterSchemeConfig interceptConfig) {
        mContext = context;
        mIXmRouterSchemeConfig = interceptConfig;
    }

    public void checkServerResource(final IFetchCallback<CheckSchemeData> callback) {
        if (mIXmRouterSchemeConfig == null) {
            return;
        }
        Map<String, String> map = new HashMap<>();
        map.put("packageName", mContext.getPackageName());
        map.put("appVersion", AppVersionUtil.getVersionName(mContext));
        mIXmRouterSchemeConfig.getISignatureGenerator().addSignatureForParams(map);

        HttpClient.getInstance().url(mIXmRouterSchemeConfig.getCheckSchemeUrl() + System.currentTimeMillis())
                .headers(mIXmRouterSchemeConfig.getHeader())
                .callbackOn(Schedulers.io())
                .params(map)
                .post(new HttpCallback() {
                    @Override
                    protected void onSuccess(int i, Object o) {
                        if (o instanceof String) {
                            String content = (String) o;
                            if (!TextUtils.isEmpty(content)) {
                                JSONObject jsonObject = null;
                                try {
                                    jsonObject = new JSONObject(content);
                                    String signature = jsonObject.optString("signature");
                                    String json = (String) jsonObject.opt("data");
                                    Map<String, String> map = new TreeMap<>();
                                    map.put("data", json);
                                    //验证签名
                                    String localCalculateSignature = mIXmRouterSchemeConfig.getISignatureGenerator().getSignatureForParams(map);
                                    if (!localCalculateSignature.equals(signature)) {
                                        if (callback != null) {
                                            callback.onError("signature verify failed ");
                                        }
                                        return;
                                    }
                                    json = json.replace("\\", "");
                                    JSONArray jsonArray = new JSONArray(json);
                                    if (jsonArray == null || jsonArray.length() == 0) {
                                        return;
                                    }
                                    JSONObject data = jsonArray.getJSONObject(0);
                                    String md5 = data.getString("md5");
                                    String url = data.getString("url");
                                    String version = data.getString("version");

                                    if (callback != null) {
                                        callback.onSuccess(new CheckSchemeData(md5, url, version));
                                    }
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                    if (callback != null) {
                                        callback.onError(e.getMessage());
                                    }
                                }
                            }
                        }
                    }

                    @Override
                    protected void onFailure(int i, Object o) {
                        if (callback != null) {
                            callback.onError("onFailure ");
                        }
                    }

                    @Override
                    protected void onError(Exception e) {
                        if (callback != null) {
                            callback.onError(e.getMessage());
                        }
                    }
                });
    }

}
