ext.getVersionName = { String urlStr, String fileName, String savePath->
    try {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setConnectTimeout(3 * 1000);
        InputStream inputStream = conn.getInputStream();
        byte[] getData = readInputStream(inputStream);
        File saveDir = new File(savePath);
        if (!saveDir.exists()) {
            saveDir.mkdir();
        }
        String content = new String(getData)
        String filePath = savePath + File.separator + fileName
        File file = new File(filePath);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(getData);
        if (fos != null) {
            fos.close();
        }
        if (inputStream != null) {
            inputStream.close();
        }
        XmlParser xmlParser = new XmlParser()
        def metadata = xmlParser.parse(file)
        NodeList nodeList = metadata.get("versioning")
        NodeList list = nodeList.getAt("latest")
        Node node = list.get(0)
        println("fjsdoijfs"+node.name()+node.text() )
        return node.text();
    }
    catch (Exception e) {
        return "";
    }
}

public static byte[] readInputStream(InputStream inputStream) throws IOException {
    byte[] buffer = new byte[1024];
    int len = 0;
    ByteArrayOutputStream bos = new ByteArrayOutputStream();
    while ((len = inputStream.read(buffer)) != -1) {
        bos.write(buffer, 0, len);
    }
    bos.close();
    return bos.toByteArray();
}