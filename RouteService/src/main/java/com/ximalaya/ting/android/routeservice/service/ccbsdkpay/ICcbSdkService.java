package com.ximalaya.ting.android.routeservice.service.ccbsdkpay;

import com.ximalaya.ting.android.routeservice.base.IService;

/**
 * Create by {jian.kang} on 3/4/22
 *
 * <AUTHOR>
 */
public interface ICcbSdkService extends IService {
    int CODE_COMMON_ERROR = -1;
    int CODE_PAY_RESULT_ERROR = -2;
    int CODE_PARAM_ERROR = -3;
    int CODE_INNER_ERROR = -4;

    void registerCcbCallback(ICcbSdkService.ICcbSdkPayCallback callback);

    void unRegisterCcbCallBack(ICcbSdkService.ICcbSdkPayCallback callback);

    ICcbSdkService.ICcbSdkPayCallback getCallback();



    public interface ICcbSdkPayCallback{
        void onSuccess(String msg);
        void onError(String code,String msg);
    }
}
