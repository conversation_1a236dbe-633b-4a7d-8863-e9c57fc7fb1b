package com.ximalaya.ting.android.routeservice.service.xdcs;

import com.ximalaya.ting.android.opensdk.model.xdcs.XdcsRecord;
import com.ximalaya.ting.android.routeservice.base.IService;

/**
 * Created by chengyun.wu on 17/9/14.
 *
 * <AUTHOR>
 */

public interface IXdcsPost extends IService{

    void postError(XdcsRecord xdcsRecord);

    void postXdcsJsonData(String url, String xdcsRecordString, IPostCallback<String> callback);

    /**
     *
     * @param xdcsRecord
     * @param foreUpdate 强制上传 不受xdcs enable 开发影响
     */
    void postIting(XdcsRecord xdcsRecord, boolean foreUpdate);

    void postOfflineData(XdcsRecord xdcsRecord);

    void postTraffic(XdcsRecord xdcsRecord);

    void statErrorToXDCS(String module, String detailError);

    boolean getItemSettingValue();

    boolean shouldPostThisXdcsInfo(String url);
}
