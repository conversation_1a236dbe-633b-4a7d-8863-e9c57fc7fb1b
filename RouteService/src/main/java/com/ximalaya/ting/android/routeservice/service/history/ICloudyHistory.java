package com.ximalaya.ting.android.routeservice.service.history;

import com.ximalaya.ting.android.opensdk.model.history.HistoryModel;
import com.ximalaya.ting.android.routeservice.base.IService;

import java.util.List;

/**
 * Created by chengyun.wu on 17/9/11.
 *
 * <AUTHOR>
 */

public interface ICloudyHistory extends IService{
    /**
     * 同步本地历史记录到服务器
     *
     * @param isNeedToPull 同步完成之后是否需要，下载服务器最新记录
     */
    void syncCloudHistory(final boolean isNeedToPull);


    /**
     * 删除所有云端历史记录
     */
    void clearAllPlayHistory(boolean deleteCloudy);


    /**
     * 删除某一条历史记录，包括本地和云端
     *
     * @param historyModel
     */
    void deletePlayHistory(HistoryModel historyModel);


    void batchDeleteHistory(List<HistoryModel> historyModels);

}
