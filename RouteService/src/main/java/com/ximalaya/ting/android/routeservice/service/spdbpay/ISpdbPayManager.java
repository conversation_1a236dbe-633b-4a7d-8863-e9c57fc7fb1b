package com.ximalaya.ting.android.routeservice.service.spdbpay;


import com.ximalaya.ting.android.routeservice.base.IService;

/**
 * Create by {jian.kang} on 9/9/21
 *
 * <AUTHOR>
 */
public interface ISpdbPayManager extends IService {

    void registerSpdbCallback(ISpdbPayManager.ISpdbCallback callback);

    void unRegisterSpdbCallBack(ISpdbPayManager.ISpdbCallback callback);

    ISpdbPayManager.ISpdbCallback getCallback();

     public interface ISpdbCallback{
        void onSuccess(String msg);
        void onError(String code,String msg);
    }
}
