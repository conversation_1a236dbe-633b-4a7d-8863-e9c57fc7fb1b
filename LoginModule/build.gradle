plugins {
    id 'com.google.devtools.ksp' version "${KSP_VERSION}"
}
apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'

apply from: project.rootDir.absolutePath + '/util.gradle'
ext {
    extChannels = "ceshi"
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    aaptOptions.cruncherEnabled = false

    lintOptions {
        // set to true to turn off analysis progress reporting by lint
        quiet true
        // if true, stop the gradle build if errors are found
        abortOnError false
        // if true, only report errors
        ignoreWarnings false
        checkAllWarnings true
        checkReleaseBuilds false
        lintConfig file("lint.xml")
    }
    defaultConfig {
        minSdkVersion Integer.parseInt(rootProject.ext.minSdkVersion)
        targetSdkVersion Integer.parseInt(rootProject.ext.targetSdkVersion)
        versionCode 1
        versionName "1.0"
    }

    sourceSets {
        main {
            java.srcDirs = ['src/main/java']
            aidl.srcDirs = ['src/main/java']
            jniLibs.srcDirs = ['libs']
        }

    }


    buildTypes {
        release {
            // 不显示Log
//            buildConfigField "boolean", "LOG_DEBUG", "true"
            //混淆
            minifyEnabled false
            //Zipalign优化
//            zipAlignEnabled true

            // 移除无用的resource文件
//            shrinkResources true
            //前一部分代表系统默认的android程序的混淆文件，该文件已经包含了基本的混淆声明
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    resourcePrefix "login_"
}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    androidTestImplementation('androidx.test.espresso:espresso-core:3.1.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    testImplementation rootProject.ext.xmDependencies.junit

    api project(':TingMainHost:TingMainApp')

    api 'com.ximalaya.ting.android.loginservicenew:loginmeizu-developer:' + XM_LOGIN_SERVICE_VERSION
    api 'com.ximalaya.ting.android.loginservicenew:loginqq-developer:' + XM_LOGIN_SERVICE_VERSION
    api 'com.ximalaya.ting.android.loginservicenew:loginsinawb-developer:' + XM_LOGIN_SERVICE_VERSION
    api 'com.ximalaya.ting.android.loginservicenew:loginwx-developer:' + XM_LOGIN_SERVICE_VERSION
    api 'com.ximalaya.ting.android.loginservicenew:loginxiaomi-developer:' + XM_LOGIN_SERVICE_VERSION
    api 'com.ximalaya.ting.android.loginservicenew:loginquick_base-developer:' + XM_LOGIN_SERVICE_VERSION
    api 'com.ximalaya.ting.android.loginservicenew:loginquick_mix-developer:' + XM_LOGIN_SERVICE_VERSION
    ksp rootProject.ext.xmDependencies.xmprocessor
}
