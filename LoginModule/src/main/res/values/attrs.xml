<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <declare-styleable name="gridPasswordView" tools:ignore="ResourceName">

        <attr name="gpvTextColor" format="color|reference" />
        <attr name="gpvTextSize" format="dimension" />

        <attr name="gpvLineColor" format="color" />
        <attr name="gpvLineHeightColor" format="color" />
        <attr name="gpvGridColor" format="color" />
        <attr name="gpvLineWidth" format="dimension" />

        <attr name="gpvPasswordLength" format="integer" />
        <attr name="gpvPasswordTransformation" format="string" />

        <attr name="gpvPasswordType" format="enum">
            <enum name="numberPassword" value="0" />
            <enum name="textPassword" value="1" />
            <enum name="textVisiblePassword" value="2" />
            <enum name="textWebPassword" value="3" />
            <enum name="number" value="4" />
        </attr>

    </declare-styleable>

    <declare-styleable name="login_MaxHeightScrollView">
        <attr name="login_maxHeight" format="dimension"/>
    </declare-styleable>
</resources>
