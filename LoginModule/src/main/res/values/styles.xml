<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="login_last_login_hint_style" >
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">-8dp</item>
        <item name="android:text">上次登录</item>
        <item name="android:textColor">#6c6c6c</item>
        <item name="android:background">@drawable/login_last_login_hint_bg</item>
        <item name="android:textSize">9sp</item>
        <item name="android:visibility">invisible</item>
    </style>

    <style name="login_setting_text_title">
        <item name="android:textColor">@color/host_color_030303_cfcfcf</item>
        <item name="android:textSize">15sp</item>
        <item name="android:drawablePadding">10dp</item>
        <item name="android:paddingLeft">15dp</item>
        <item name="android:paddingRight">15dp</item>
        <item name="android:clickable">true</item>
        <item name="android:minHeight">50dp</item>
        <item name="android:ellipsize">middle</item>
        <item name="android:gravity">center_vertical|left</item>
        <item name="android:background">@drawable/host_bg_list_selector</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:drawableRight">@drawable/host_ic_more</item>
        <item name="android:singleLine">true</item>
        <item name="android:maxEms">8</item>
    </style>

    <style name="GridPasswordView" tools:ignore="ResourceName"/>

    <style name="GridPasswordView.TextView" tools:ignore="ResourceName">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:background">@null</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_weight">1</item>
        <item name="android:singleLine">true</item>
    </style>

    <style name="GridPasswordView.EditText" parent="GridPasswordView.TextView" tools:ignore="ResourceName">
        <item name="android:cursorVisible">false</item>
    </style>

</resources>
