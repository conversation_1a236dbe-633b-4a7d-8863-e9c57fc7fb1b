<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/main_iv_bottom_tail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right|top"
            android:layout_marginRight="8dp"
            android:src="@drawable/login_complaint_pop_icon" />

        <LinearLayout
            android:id="@+id/main_ll_container"
            android:layout_width="102dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:layout_marginTop="4.5dp"
            android:background="@drawable/login_bg_complaint_pop"
            android:orientation="vertical" >

            <TextView
                android:id="@+id/main_btn_1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:text="原手机停用"
                android:textColor="@color/login_color_ffffff_333333"
                android:textSize="12sp" />


            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginLeft="13dp"
                android:layout_marginRight="13dp"
                android:background="@color/login_color_33ffffff_333333" />

            <TextView
                android:id="@+id/main_btn_2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:text="忘记密码"
                android:textColor="@color/login_color_ffffff_333333"
                android:textSize="12sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginLeft="13dp"
                android:layout_marginRight="13dp"
                android:background="@color/login_color_33ffffff_333333" />

            <TextView
                android:id="@+id/main_btn_3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:text="其他问题"
                android:textColor="@color/login_color_ffffff_333333"
                android:textSize="12sp" />
        </LinearLayout>
    </FrameLayout>
</RelativeLayout>