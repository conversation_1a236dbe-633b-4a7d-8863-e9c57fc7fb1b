<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="414dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingBottom="50dp">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/login_login_hint_layout"
        android:background="@drawable/host_bg_rect_white_radius_10"
        />

    <com.ximalaya.ting.android.framework.view.image.FlexibleRoundImageView
        android:id="@+id/login_iv_img"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="343:143"
        app:layout_constraintTop_toTopOf="parent"
        app:flexible_round_corner_radius="10dp"
        app:flexible_round_corner="left_top|right_top"
        android:src="@color/host_color_ff4444"
        android:scaleType="centerCrop"
        />

    <ImageView
        android:id="@+id/login_iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/login_new_user_login_dialog_close"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="3dp"
        android:layout_marginEnd="4dp"
        android:padding="10dp"
        />

    <TextView
        android:id="@+id/login_tv_phone_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="28sp"
        android:textColor="@color/host_color_333333_dcdcdc"
        app:layout_constraintTop_toBottomOf="@id/login_iv_img"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/login_tv_change"
        app:layout_constraintHorizontal_chainStyle="packed"
        android:layout_marginTop="26dp"
        tools:text="130****0000"
        />

    <TextView
        android:id="@+id/login_tv_change"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/login_tv_phone_number"
        app:layout_constraintBottom_toBottomOf="@id/login_tv_phone_number"
        app:layout_constraintStart_toEndOf="@id/login_tv_phone_number"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="12dp"
        android:text="更换"
        android:textSize="11sp"
        android:textColor="@color/login_color_444444_8d8d91"
        android:background="@drawable/host_bg_rect_f4f4f4_1b1b1b_corner_100"
        android:padding="4dp"
        android:drawableStart="@drawable/login_ic_new_user_login_switch"
        android:drawablePadding="1dp"
        />

    <TextView
        android:id="@+id/login_tv_login_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="立即登录"
        android:background="@drawable/host_bg_ff4444_radius_50"
        android:paddingVertical="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/login_tv_phone_number"
        android:layout_marginHorizontal="45dp"
        android:gravity="center"
        android:layout_marginTop="15dp"
        android:textColor="@color/host_white"
        android:textSize="16sp"
        />

    <LinearLayout
        android:id="@+id/login_login_hint_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginRight="21dp"
        android:paddingBottom="26dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/login_tv_login_btn">

        <ImageView
            android:id="@+id/login_login_hint_state"
            android:layout_width="41dp"
            android:layout_height="wrap_content"
            android:paddingEnd="3dp"
            android:paddingTop="3dp"
            android:paddingBottom="8dp"
            android:paddingStart="24dp"
            android:contentDescription="@string/login_login_checkbox"
            android:src="@drawable/login_login_aggress_state" />

        <TextView
            android:id="@+id/login_regiset_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:lineSpacingMultiplier="1.2"
            android:gravity="center_vertical"
            android:layout_gravity="center_vertical"
            tools:text="未注册用户登录时将自动创建账号，且代表您已同意《用户服务协议》和《隐私政策》"
            android:textColor="@color/host_color_999999_888888"
            android:textSize="12sp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>