<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="467dp"
    android:background="@drawable/login_bg_rect_ffffff_121212_radius_12_12_0_0"
    android:paddingTop="16dp"
    android:paddingLeft="12dp"
    android:paddingRight="12dp"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <TextView
        android:id="@+id/login_multi_account_title"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:text="请确认开通账号"
        android:textStyle="bold"
        android:textSize="16sp"
        android:textColor="@color/host_color_333333_cfcfcf"
        android:gravity="center"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/login_multi_account_content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="26dp"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/host_color_f6f7f8_121212"
        android:layout_marginTop="10dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="76dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/login_multi_account_btn_1"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:text="取消"
            android:textSize="16sp"
            android:textColor="@color/host_color_666666"
            android:gravity="center"
            android:background="@drawable/login_bg_rect_f3f4f5_radius_22"/>

        <View
            android:layout_width="6dp"
            android:layout_height="match_parent"/>

        <TextView
            android:id="@+id/login_multi_account_btn_2"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:text="切换账号"
            android:textSize="17sp"
            android:textColor="@color/host_color_ffffff"
            android:background="@drawable/login_bg_rect_ff4646_radius_22"
            android:gravity="center"/>

    </LinearLayout>

</LinearLayout>