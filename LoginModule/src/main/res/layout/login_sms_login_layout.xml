<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:gravity="center_horizontal"
              android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content" >

        <TextView
            android:id="@+id/login_sms_login_title"
            android:textSize="16sp"
            android:layout_centerInParent="true"
            android:textColor="@color/host_color_333333_cfcfcf"
            android:text="登录后有新天地，快来探索"
            android:layout_marginTop="15dp"
            android:fontFamily="sans-serif-light"
            android:textStyle="bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/login_sms_help_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:textSize="14sp"
            android:fontFamily="sans-serif-light"
            android:textStyle="bold"
            android:textColor="@color/host_color_111111_cfcfcf"
            android:layout_marginRight="14dp"
            android:text="帮助" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/login_layout_account"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:layout_marginTop="16dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/login_region_number"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginLeft="7dp"
            android:drawablePadding="5dp"
            android:drawableRight="@drawable/login_arrow_gray_down"
            android:gravity="center"
            android:textSize="16sp"
            android:text="+86"
            android:textColor="@color/login_color_333333_cfcfcf"/>

        <View
            android:id="@+id/login_vertical_divider"
            android:layout_width="0.5dp"
            android:layout_height="12dp"
            android:layout_gravity="center"
            android:layout_marginLeft="7dp"
            android:background="@color/login_color_d6d6d6_353535"
            />

        <EditText
            android:id="@+id/login_username"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@null"
            android:drawablePadding="7dp"
            android:gravity="center_vertical"
            android:imeOptions="actionNext"
            android:maxLines="1"
            android:hint="@string/login_hint_input_phone_num"
            android:paddingLeft="7dp"
            android:paddingRight="7dp"
            android:textCursorDrawable="@null"
            android:textColor="@color/login_color_333333_cfcfcf"
            android:textColorHint="@color/login_color_bbbbbb_66666b"
            android:inputType="phone"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/login_iv_clear_accout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:contentDescription="清空账户"
            android:padding="5dp"
            android:src="@drawable/login_icon_clear_input"
            android:visibility="invisible" />
    </LinearLayout>

    <View
        android:id="@+id/login_divider_below_account"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:background="@color/login_color_e5e5e5_353535" />

    <LinearLayout
        android:id="@+id/login_login_hint_layout"
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:paddingLeft="10dp"
        android:paddingRight="50dp"
        android:layout_marginTop="20dp"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/login_login_hint_state"
            android:layout_width="wrap_content"
            android:paddingLeft="40dp"
            android:paddingBottom="6dp"
            android:paddingTop="6dp"
            android:paddingRight="6dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/login_login_aggress_state"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/login_regiset_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="未注册用户登录时将自动创建账号，且代表您已同意《用户服务协议》和《隐私政策》"
            android:textColor="@color/host_color_acacaf_66666b"
            android:lineSpacingMultiplier="1.2"
            android:textSize="12sp" />
    </LinearLayout>

    <Button
        android:id="@+id/login_login"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/login_login_highlight_btn_bg"
        android:enabled="false"
        android:textStyle="bold"
        android:text="@string/login_login"
        android:textColor="@drawable/login_login_button_text"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/main_other_login_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginTop="13dp"
        android:paddingBottom="10dp"
        android:layout_marginBottom="10dp"
        android:text="@string/login_other_login_method"
        android:textColor="@color/login_color_444444_cfcfcf"
        android:textSize="14sp" />

</LinearLayout>