<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true">

    <RelativeLayout
        android:id="@+id/login_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@color/host_color_ffffff_121212"
        android:contentDescription="@string/login_title_bar_contentDescription">

        <ImageView
            android:id="@+id/login_back_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="0dp"
            android:contentDescription="@string/login_back"
            android:padding="10dp"
            android:src="@drawable/host_btn_orange_back_selector" />

        <EditText
            android:id="@+id/login_et_search_input"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_marginRight="10dp"
            android:layout_marginTop="10dp"
            android:layout_toLeftOf="@+id/login_tv_search"
            android:layout_toRightOf="@+id/login_back_btn"
            android:background="@drawable/host_bg_navi_search"
            android:cursorVisible="true"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:hint="@string/login_search_country_hint"
            android:maxLines="1"
            android:paddingLeft="10dp"
            android:singleLine="true"
            android:textColor="@color/host_color_333333"
            android:textColorHint="#B3B2B2"
            android:textCursorDrawable="@drawable/login_cursor_edittext_orange"
            android:textSize="14sp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/login_choose_country_title"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toLeftOf="@+id/login_iv_search"
            android:layout_toRightOf="@+id/login_back_btn"
            android:gravity="center"
            android:text="选择国家和地区"
            android:textColor="@color/host_color_black"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/login_tv_search"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_marginRight="15dp"
            android:gravity="center"
            android:text="搜索"
            android:textColor="@color/host_color_black"
            android:textSize="16sp"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/login_iv_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginRight="5dp"
            android:layout_marginTop="4dp"
            android:contentDescription="搜索"
            android:padding="10dp"
            android:src="@drawable/ic_search_n_n_line_regular_24"
            android:tint="@color/host_color_131313_ffffff" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_alignParentBottom="true"
            android:background="@color/login_color_e8e8e8_272727" />
    </RelativeLayout>

    <ListView
        android:id="@+id/login_list_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/login_title_bar"
        android:clipToPadding="false"
        android:divider="@null"
        android:dividerHeight="0dp"
        android:listSelector="@color/host_transparent"
        android:overScrollMode="never"
        android:scrollbars="none"
        android:focusable="false"
        android:focusableInTouchMode="false" />

<!--    <View-->
<!--        android:id="@+id/login_index_border"-->
<!--        android:layout_width="4dp"-->
<!--        android:layout_height="match_parent"-->
<!--        android:layout_toLeftOf="@+id/login_side_bar"-->
<!--        android:layout_below="@+id/login_title_bar"-->
<!--        android:background="@drawable/login_bg_vertical_gradient" />-->

    <com.ximalaya.ting.android.host.view.bar.indexsidebar.IndexSideBar
        android:id="@+id/login_side_bar"
        android:layout_width="30dp"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:layout_below="@id/login_title_bar"
        android:layout_centerVertical="true"
        android:layout_marginBottom="70dp"
        android:layout_marginTop="70dp"
        android:paddingRight="10dp"
        app:indexBackgroundColor="#00000000"
        app:indexGap="1dp"
        app:isBold="true"
        app:indexTextSize="12sp" />

    <TextView
        android:id="@+id/login_tv_show_index"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_centerInParent="true"
        android:background="@drawable/login_country_indexbar_showtv_bg"
        android:gravity="center"
        android:textColor="@color/host_white"
        android:textSize="22sp"
        android:textStyle="normal"
        android:visibility="gone"
        tools:text="A" />

</merge>