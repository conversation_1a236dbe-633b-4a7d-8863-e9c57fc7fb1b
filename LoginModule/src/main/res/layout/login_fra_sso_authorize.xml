<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/framework_bg_color"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <!--<include-->
    <!--android:id="@+id/login_head_layout"-->
    <!--layout="@layhost_titlebar_top_top" />-->

    <RelativeLayout
        android:id="@+id/login_head_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:id="@+id/login_horizontal_divider"
                android:layout_width="match_parent"
                android:layout_height="1px" />

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp" />

            <LinearLayout
                android:id="@+id/login_ly_app_icons"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/host_color_ffffff_121212"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                android:paddingBottom="28dp"
                android:paddingTop="40dp"
                tools:visibility="visible"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <com.ximalaya.ting.android.framework.view.image.RoundImageView
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:layout_marginLeft="15dp"
                        android:layout_marginRight="15dp"
                        android:adjustViewBounds="false"
                        android:saveEnabled="true"
                        android:scaleType="centerCrop"
                        android:src="@mipmap/ic_launcher_ting"
                        app:corner_radius="5dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:maxLines="1"
                        android:text="@string/host_app_name"
                        android:textColor="@color/login_sso_authorize_permissions_title_text_color"
                        android:textSize="15sp" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/login_iv_arrow"
                    android:layout_width="35dp"
                    android:layout_height="20dp"
                    android:layout_marginTop="18dp"
                    android:scaleType="fitXY"
                    android:contentDescription="@string/login_content_description_arrow"
                    android:src="@drawable/login_sso_icon_arrow" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <com.ximalaya.ting.android.framework.view.image.RoundImageView
                        android:id="@+id/login_iv_third_app_icon"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:layout_marginLeft="15dp"
                        android:layout_marginRight="15dp"
                        android:adjustViewBounds="false"
                        android:saveEnabled="true"
                        android:scaleType="centerCrop"
                        android:src="@drawable/login_sso_third_app_logo_default"
                        app:corner_radius="5dp" />

                    <TextView
                        android:id="@+id/login_tv_third_app_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:maxLines="1"
                        android:textColor="@color/login_sso_authorize_permissions_title_text_color"
                        android:textSize="15sp" />

                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="@color/host_color_e8e8e8_2a2a2a"
                android:visibility="gone" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="1dp"
                android:background="@color/host_color_ffffff_121212"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                tools:visibility="visible"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/login_iv_user_icon"
                    android:layout_width="30dp"
                    android:layout_height="match_parent"
                    android:layout_alignParentLeft="true"
                    android:layout_marginBottom="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_marginTop="10dp"
                    android:scaleType="centerCrop"
                    android:contentDescription="@string/login_content_description_user_icon"
                    android:src="@mipmap/ic_launcher_ting" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_toRightOf="@id/login_iv_user_icon"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/login_tv_change_account"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:layout_marginLeft="2dp"
                        android:gravity="center_vertical"
                        android:text="@string/login_sso_authorize_change_account"
                        android:textColor="@color/login_sso_authorize_change_account_text_color"
                        android:textSize="15sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_alignParentLeft="true"
                        android:layout_marginRight="2dp"
                        android:layout_toLeftOf="@id/login_tv_change_account"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:textSize="14sp" />
                </RelativeLayout>
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/login_ll_permissions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                tools:visibility="visible"
                android:visibility="gone">

                <TextView
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:maxLines="1"
                    android:text="@string/login_sso_authorize_permissions"
                    android:textColor="@color/login_sso_authorize_permissions_title_text_color"
                    android:textSize="13sp" />

                <LinearLayout
                    android:id="@+id/login_ll_permissions_content"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="vertical" />

            </LinearLayout>

            <Button
                android:id="@+id/login_btn_sso_authorize"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginBottom="50dp"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:layout_marginTop="30dp"
                android:background="@drawable/login_login_highlight_btn_bg"
                android:text="@string/login_sso_btn_authorize"
                android:textColor="@color/host_white"
                android:textSize="16sp"
                tools:visibility="visible"
                android:visibility="gone" />

        </LinearLayout>

    </ScrollView>


</LinearLayout>