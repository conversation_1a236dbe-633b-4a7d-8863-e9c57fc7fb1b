<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/login_bg_rect_f9f9f9_000000_corner_8"
    android:layout_height="wrap_content">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/login_icon"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginLeft="9dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        tools:src="@drawable/host_album_default_1_145"
        app:corner_radius="50dp" />

    <TextView
        android:id="@+id/login_auth_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/login_icon"
        android:fontFamily="sans-serif-light"
        android:textStyle="bold"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="10dp"
        android:layout_toRightOf="@id/login_icon"
        android:textColor="@color/host_color_333333_cfcfcf"
        android:textSize="13sp"
        tools:text="王哈哈" />

    <TextView
        android:id="@+id/login_autho_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@id/login_icon"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="10dp"
        android:layout_toRightOf="@id/login_icon"
        android:textColor="@color/host_color_666666_888888"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="11sp"
        tools:text="无账号权益丨微信;QQ登录" />


    <TextView
        android:id="@+id/login_auth_curr_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:textSize="10sp"
        android:paddingLeft="6dp"
        android:paddingRight="6dp"
        android:paddingTop="2dp"
        android:paddingBottom="2dp"
        android:textColor="@color/host_color_ffffff"
        android:background="@drawable/login_auth_curr_login_bg"
        android:text="当前登录" />

</RelativeLayout>