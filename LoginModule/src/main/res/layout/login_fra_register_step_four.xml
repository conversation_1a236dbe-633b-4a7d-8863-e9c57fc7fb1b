<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/login_color_ffffff_121212">


    <FrameLayout
        android:id="@+id/login_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height" />

    <LinearLayout
        android:id="@+id/login_input_area"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_below="@id/login_top"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:layout_marginTop="30dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/login_nickname"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:gravity="center_vertical"
            android:hint="@string/login_hint_input_username"
            android:maxLines="1"
            android:textColor="@color/host_color_black"
            android:textColorHint="@color/login_color_c8c8c8_999999"
            android:textSize="15sp" />

        <ImageButton
            android:id="@+id/login_clear_input"
            style="@style/host_edit_text_cursor_drawable"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:src="@drawable/login_ic_clear_search"
            android:contentDescription="@string/login_content_description_clear_input"
            android:visibility="invisible" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@+id/login_input_area"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:background="@color/login_color_e5e5e5_272727" />

    <Button
        android:id="@+id/login_done"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_below="@id/login_input_area"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/login_login_highlight_btn_bg"
        android:gravity="center"
        android:text="@string/login_complete"
        android:textColor="@drawable/login_login_button_text"
        android:textSize="16sp" />

    <ProgressBar
        android:id="@+id/login_progress_bar"
        style="?android:attr/progressBarStyleLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="invisible" />

</RelativeLayout>