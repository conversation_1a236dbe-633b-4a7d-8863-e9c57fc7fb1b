<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/login_border_top"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginRight="34dp"
        android:background="@color/login_color_e9e9e9_2a2a2a" />

    <TextView
        android:id="@+id/login_tv_index"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/login_border_top"
        android:paddingBottom="5dp"
        android:paddingLeft="15dp"
        android:paddingTop="6dp"
        android:text="A"
        android:textColor="@color/login_color_9B9B9B"
        android:textSize="13sp" />

    <View
        android:id="@+id/login_border_bottom"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_below="@+id/login_tv_index"
        android:layout_marginRight="34dp"
        android:background="@color/login_color_e9e9e9_2a2a2a" />

    <TextView
        android:id="@+id/login_tv_country_name"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:layout_below="@+id/login_border_bottom"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:text="中国"
        android:textColor="@color/host_color_black"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/login_tv_country_num"
        android:layout_width="wrap_content"
        android:layout_height="70dp"
        android:layout_alignParentRight="true"
        android:layout_below="@+id/login_border_bottom"
        android:layout_marginRight="60dp"
        android:gravity="center_vertical"
        android:text="+86"
        android:textColor="@color/host_color_666666_888888"
        android:textSize="16sp" />
</RelativeLayout>