<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginLeft="30dp"
    android:layout_marginRight="30dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/host_bg_rect_ffffff_1e1e1e_radius_8">

    <ImageView
        android:id="@+id/login_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="25dp"
        android:src="@drawable/login_unlock_tips" />

    <TextView
        android:id="@+id/login_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/login_img"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="38dp"
        android:layout_marginTop="14dp"
        android:layout_marginRight="38dp"
        android:gravity="center"
        android:lineSpacingMultiplier="1.2"
        android:textColor="@color/host_color_666666_888888"
        android:textSize="15sp"
        tools:text="抱歉，您的账户因【封号原因封号原因】已被" />

    <TextView
        android:id="@+id/login_ok"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_below="@id/login_title"
        android:layout_marginLeft="37dp"
        android:layout_marginTop="24dp"
        android:layout_marginRight="37dp"
        android:layout_marginBottom="24dp"
        android:background="@drawable/login_login_highlight_btn_bg"
        android:gravity="center"
        android:text="申请解除限制"
        android:textColor="@color/host_white"
        android:textSize="16sp" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/login_cancel"
        android:layout_alignParentRight="true"
        app:srcCompat="@drawable/host_dialog_update_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</RelativeLayout>