<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/login_root_lay"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/login_head_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@color/host_color_ffffff_121212" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/login_tv_other_login"
        android:layout_below="@+id/login_head_layout"
        android:paddingBottom="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_height="wrap_content">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_marginTop="24dp"
                android:layout_marginRight="40dp"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/login_title"
                    android:layout_width="match_parent"
                    android:text="登录即可参与评论"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    tools:text="登录即可参与评论登录即可参与评论登录即可参与评论"
                    android:layout_marginLeft="40dp"
                    android:textSize="24sp"
                    android:fontFamily="sans-serif-light"
                    android:textStyle="bold"
                    android:layout_toLeftOf="@id/login_sms_verification"
                    android:textColor="@color/host_color_333333_cfcfcf"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/login_sms_verification"
                    android:layout_centerVertical="true"
                    android:layout_alignParentRight="true"
                    android:text="验证码登录"
                    android:drawablePadding="4dp"
                    android:textColor="@color/host_color_666666_cfcfcf"
                    android:drawableRight="@drawable/login_right_other_login_channel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/login_layout_account"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_alignWithParentIfMissing="true"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="40dp"
                android:layout_marginRight="40dp"
                android:layout_marginTop="40dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/login_region_number"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_marginLeft="7dp"
                    android:drawablePadding="5dp"
                    android:drawableRight="@drawable/login_arrow_gray_down"
                    android:gravity="center"
                    android:text="+86"
                    android:textColor="@color/login_color_333333_cfcfcf"
                    tools:visibility="visible"
                    android:visibility="gone" />

                <View
                    android:id="@+id/login_vertical_divider"
                    android:layout_width="0.5dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="7dp"
                    android:background="@color/login_color_d6d6d6_353535"
                    tools:visibility="visible"
                    android:visibility="gone" />

                <EditText
                    android:id="@+id/login_username"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@null"
                    android:drawablePadding="7dp"
                    android:gravity="center_vertical"
                    android:imeOptions="actionNext"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:maxLines="1"
                    android:paddingLeft="7dp"
                    android:paddingRight="7dp"
                    android:textCursorDrawable="@drawable/login_edit_cursor_bg"
                    android:textColor="@color/login_color_333333_cfcfcf"
                    tools:text="1232313424"
                    android:textColorHint="@color/login_color_bbbbbb_414141"
                    android:textSize="14sp" />

                <ImageView
                    android:id="@+id/login_iv_clear_accout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:contentDescription="清空账户"
                    android:padding="5dp"
                    android:src="@drawable/login_icon_clear_input"
                    android:visibility="visible" />
            </LinearLayout>

            <View
                android:id="@+id/login_divider_below_account"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_below="@+id/login_layout_account"
                android:layout_marginLeft="40dp"
                android:layout_marginRight="40dp"
                android:background="@color/login_color_e5e5e5_272727" />

            <LinearLayout
                android:id="@+id/login_layout_password"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_below="@+id/login_divider_below_account"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="40dp"
                android:layout_marginRight="40dp"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/login_password"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="2"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:paddingLeft="7dp"
                    android:paddingRight="7dp"
                    android:inputType="textPassword"
                    android:textCursorDrawable="@drawable/login_edit_cursor_bg"
                    android:textColor="@color/login_color_333333_cfcfcf"
                    android:textColorHint="@color/login_color_bbbbbb_414141"
                    android:textSize="14sp" />

                <ImageView
                    android:id="@+id/login_iv_clear_pwd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:contentDescription="清空密码"
                    android:padding="5dp"
                    android:src="@drawable/login_icon_clear_input"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/login_psw_status_switch"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_gravity="center"
                    android:padding="2dp"
                    android:contentDescription="密码明文显示"
                    android:src="@drawable/login_eye_close" />


                <View
                    android:id="@+id/login_divider_verifycode"
                    android:layout_width="0.5dp"
                    android:layout_height="22dp"
                    android:layout_gravity="center"
                    tools:visibility="visible"
                    android:background="@color/host_item_border_color"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/login_tv_check_code"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:enabled="false"
                    android:gravity="center"
                    android:text="@string/login_get_check_code"
                    android:textColor="@color/login_text_333_999"
                    android:textSize="13sp"
                    tools:visibility="visible"
                    android:visibility="gone" />

            </LinearLayout>

            <View
                android:id="@+id/login_divider_below_psw"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_below="@+id/login_layout_password"
                android:layout_marginLeft="40dp"
                android:layout_marginRight="40dp"
                android:background="@color/login_color_e5e5e5_272727" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/login_login"
                android:layout_alignLeft="@id/login_login"
                android:layout_alignRight="@id/login_login"
                android:layout_marginLeft="30dp"
                android:layout_marginRight="30dp"
                android:layout_marginTop="22dp"
                android:orientation="vertical">


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/login_assistant_left"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="10dp"
                        android:text="邮箱登录"
                        android:textColor="@color/login_color_444444_cfcfcf"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/login_forget_password"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:padding="10dp"
                        android:text="@string/login_forget_password"
                        android:textColor="@color/login_color_444444_cfcfcf"
                        android:textSize="12sp" />
                </RelativeLayout>


            </LinearLayout>

            <Button
                android:id="@+id/login_login"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_below="@id/login_layout_password"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="40dp"
                android:layout_marginRight="40dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/login_login_highlight_btn_bg"
                android:enabled="false"
                android:text="@string/login_login"
                android:textStyle="bold"
                android:textColor="@drawable/login_login_button_text"
                android:textSize="16sp" />

            <LinearLayout
                android:id="@+id/login_login_hint_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="40dp"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/login_login_hint_state"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingRight="6dp"
                    android:paddingTop="6dp"
                    android:paddingBottom="6dp"
                    android:paddingLeft="30dp"
                    android:contentDescription="@string/login_login_checkbox"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/host_sync_dynamic_check_selector" />

                <TextView
                    android:id="@+id/login_regiset_hint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:lineSpacingMultiplier="1.2"
                    android:gravity="center_vertical"
                    android:layout_gravity="center_vertical"
                    tools:text="未注册用户登录时将自动创建账号，且代表您已同意《用户服务协议》和《隐私政策》"
                    android:textColor="@color/host_color_999999_888888"
                    android:textSize="12sp" />
            </LinearLayout>


        </LinearLayout>

    </ScrollView>

    <include layout="@layout/login_login_bttom_btn" />


</RelativeLayout>




