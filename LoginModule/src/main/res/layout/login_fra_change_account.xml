<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:makeramen="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/login_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height" />

    <View
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@color/framework_bg_color"/>

    <RelativeLayout
        android:id="@+id/login_rl_account1"
        android:layout_width="match_parent"
        android:layout_height="51dp"
        android:background="@drawable/host_bg_list_selector"
        android:paddingLeft="16dp"
        android:paddingRight="16dp">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/login_iv_avatar1"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:scaleType="centerCrop"
            android:layout_centerVertical="true"
            android:src="@drawable/host_default_avatar_88"
            makeramen:border_width="0px"
            makeramen:corner_radius="80dp"
            makeramen:pressdown_shade="false"
            makeramen:round_background="true"/>

        <TextView
            android:id="@+id/login_tv_name1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/login_iv_avatar1"
            android:layout_marginLeft="8dp"
            android:layout_centerVertical="true"
            android:textSize="15sp"
            android:textColor="@color/host_color_333333_cfcfcf"
            tools:text="郭德纲"/>

        <ImageView
            android:id="@+id/login_iv_login_status1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/login_settings_ic_chosen"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"/>

    </RelativeLayout>

    <View
        android:id="@+id/login_divider"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginLeft="16dp"
        android:background="@color/login_color_eeeeee_2a2a2a"/>

    <RelativeLayout
        android:id="@+id/login_rl_account2"
        android:layout_width="match_parent"
        android:layout_height="51dp"
        android:background="@drawable/host_bg_list_selector"
        android:paddingLeft="16dp"
        android:paddingRight="16dp">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/login_iv_avatar2"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:scaleType="centerCrop"
            android:layout_centerVertical="true"
            android:src="@drawable/host_default_avatar_88"
            makeramen:border_width="0px"
            makeramen:corner_radius="80dp"
            makeramen:pressdown_shade="false"
            makeramen:round_background="true"/>

        <TextView
            android:id="@+id/login_tv_name2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/login_iv_avatar2"
            android:layout_marginLeft="8dp"
            android:layout_centerVertical="true"
            android:textSize="15sp"
            android:textColor="@color/host_color_333333_cfcfcf"
            tools:text="郭德纲"/>

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@color/framework_bg_color"/>

    <TextView
        android:id="@+id/login_tv_change_new_account"
        android:layout_width="match_parent"
        android:layout_height="51dp"
        android:gravity="center_vertical"
        android:textSize="15sp"
        android:textColor="@color/host_color_333333_cfcfcf"
        android:text="@string/login_login_by_changing_new_account"
        android:drawableRight="@drawable/host_ic_more"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/framework_bg_color"/>

</LinearLayout>