<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/login_bg_rect_fafafa_121212_radius_12_12_0_0"
    android:paddingTop="16dp"
    android:paddingBottom="20dp"
    android:paddingLeft="12dp"
    android:paddingRight="12dp"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <TextView
        android:id="@+id/login_multi_account_title"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:text="当前账号不是您的常用账号,请确认是否支付"
        android:textStyle="bold"
        android:textSize="16sp"
        android:textColor="@color/login_color_333333_a0a0a0"
        android:gravity="center"/>
    
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/login_multi_account_content"
        android:layout_width="match_parent"
        android:layout_height="224dp"
        android:layout_marginTop="16dp"/>
    
    <TextView
        android:id="@+id/login_multi_account_btn_1"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:text="确认充值"
        android:textSize="17sp"
        android:textColor="@color/host_color_ffffff"
        android:gravity="center"
        android:layout_marginTop="10dp"
        android:background="@drawable/login_bg_rect_e82b24_ff3722_radius_22"/>

    <TextView
        android:id="@+id/login_multi_account_btn_2"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:layout_marginTop="16dp"
        android:text="切换账号"
        android:textSize="17sp"
        android:textColor="@color/host_color_666666"
        android:gravity="center"/>
    
</LinearLayout>