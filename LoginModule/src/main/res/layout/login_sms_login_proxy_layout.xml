<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.login.view.CatchGetPrivateFlagsCrachFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
             android:layout_width="match_parent"
             android:layout_height="match_parent">

    <View
        android:id="@+id/login_sms_login_click_view"
        android:layout_width="match_parent"
        android:background="@color/host_transparent"
        android:layout_height="match_parent" />

    <com.ximalaya.ting.android.host.view.layout.VerticalSlideRelativeLayout
        android:id="@+id/login_sms_login_proxy_slide"
        android:layout_gravity="bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/login_bg_sms_login"
            android:clickable="true"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="10dp"
                android:contentDescription="@string/login_iv_cd_pull"
                android:src="@drawable/login_ic_track_intro_pull"/>

            <FrameLayout
                android:id="@+id/login_sms_login_proxy_fra"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

            </FrameLayout>
        </LinearLayout>

    </com.ximalaya.ting.android.host.view.layout.VerticalSlideRelativeLayout>
</com.ximalaya.ting.android.login.view.CatchGetPrivateFlagsCrachFrameLayout>
