<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/login_buy_dialog_shape"
    android:minWidth="280dp"
    android:orientation="vertical"
    android:paddingBottom="61dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/login_iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:contentDescription="关闭"
        app:srcCompat="@drawable/host_dialog_update_close" />

    <ImageView
        android:id="@+id/login_iv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="-10dp"
        android:contentDescription=" "
        android:src="@drawable/host_icon_alert" />

    <TextView
        android:id="@+id/login_dialog_unbind_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="30dp"
        android:gravity="center"
        android:lineSpacingExtra="5dp"
        android:textColor="@color/host_color_black"
        android:textSize="16sp" />

    <LinearLayout
        android:id="@+id/login_sub_btn_layout"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="15dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:visibility="visible">

        <Button
            android:id="@+id/login_btn"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:background="@drawable/login_bg_btn_oval_orange_selector"
            android:gravity="center"
            android:text="知道了"
            android:textColor="@color/host_white"
            android:textSize="15sp" />
    </LinearLayout>


</LinearLayout>