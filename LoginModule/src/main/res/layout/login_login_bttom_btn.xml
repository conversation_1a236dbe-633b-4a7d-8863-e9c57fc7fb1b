<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="@layout/login_fra_login">

    <LinearLayout
        android:id="@+id/login_tv_other_login"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/login_third_login"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <View
            android:layout_width="0dp"
            android:layout_height="1px"
            android:layout_weight="1"
            android:background="@color/login_login_divider_gray" />


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:text="@string/login_other_login_method"
            android:textColor="@color/login_color_cccccc_999999"
            android:textSize="14sp" />

        <View
            android:layout_width="0dp"
            android:layout_height="1px"
            android:layout_weight="1"
            android:background="@color/login_login_divider_gray" />
    </LinearLayout>


    <LinearLayout
        android:id="@+id/login_third_login"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="30dp"
        android:layout_marginTop="15dp"
        android:orientation="horizontal"
        android:baselineAligned="false"
        android:paddingLeft="30dp"
        android:paddingRight="30dp">


        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <com.ximalaya.ting.android.host.view.image.TouchableImageView
                android:id="@+id/login_login_weixin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:contentDescription="微信登录"
                android:src="@drawable/login_login_weixin" />

            <TextView
                android:id="@+id/login_latest_login_wechat"
                style="@style/login_last_login_hint_style"
                tools:visibility="visible" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <com.ximalaya.ting.android.host.view.image.TouchableImageView
                android:id="@+id/login_login_qq"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:contentDescription="qq登录"
                android:src="@drawable/login_login_qq" />

            <TextView
                android:id="@+id/login_latest_login_qq"
                style="@style/login_last_login_hint_style"
                tools:visibility="visible" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/login_weibo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <com.ximalaya.ting.android.host.view.image.TouchableImageView
                android:id="@+id/login_login_weibo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:contentDescription="微博登录"
                android:src="@drawable/login_login_weibo" />

            <TextView
                android:id="@+id/login_latest_login_sina"
                style="@style/login_last_login_hint_style"
                tools:visibility="visible" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/login_xiaomi"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <com.ximalaya.ting.android.host.view.image.TouchableImageView
                android:id="@+id/login_login_xiaomi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:contentDescription="小米登录"
                android:src="@drawable/login_login_xiaomi" />

            <TextView
                android:id="@+id/login_latest_login_xiaomi"
                style="@style/login_last_login_hint_style"
                tools:visibility="visible" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/login_meizu"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <com.ximalaya.ting.android.host.view.image.TouchableImageView
                android:id="@+id/login_login_meizu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:contentDescription="魅族登录"
                android:src="@drawable/login_login_meizu" />

            <TextView
                android:id="@+id/login_latest_login_meizu"
                style="@style/login_last_login_hint_style"
                tools:visibility="visible" />
        </LinearLayout>
    </LinearLayout>
</merge>