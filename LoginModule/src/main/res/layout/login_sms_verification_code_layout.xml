<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:app="http://schemas.android.com/apk/res-auto"
                xmlns:tools="http://schemas.android.com/tools"
                android:id="@+id/login_sms_verification_root_lay"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/login_color_ffffff_121212">

    <FrameLayout
        android:id="@+id/login_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@color/host_color_ffffff_121212" />

    <TextView
        android:id="@+id/login_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/login_top"
        android:fontFamily="sans-serif-light"
        android:textColor="@color/host_color_333333_cfcfcf"
        android:text="请输入验证码"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_marginTop="24dp"
        android:layout_marginLeft="40dp"
        android:textSize="24sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/login_phone_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/login_title"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_marginLeft="40dp"
        android:layout_marginTop="4dp"
        tools:text="验证码已通过短信发送至 +86 188*"
        android:textColor="@color/host_color_888888"
        android:textSize="14sp" />


    <com.ximalaya.ting.android.login.view.gridedittext.GridPasswordView
        android:id="@+id/login_verification_code1"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_below="@+id/login_phone_number"
        android:layout_marginLeft="38dp"
        android:layout_marginRight="38dp"
        android:layout_marginTop="50dp"
        app:gpvLineColor="@color/login_color_e8e8e8_272727"
        app:gpvLineWidth="1dp"
        app:gpvPasswordLength="6"
        app:gpvPasswordType="number"
        app:gpvTextColor="@color/host_color_black"
        app:gpvTextSize="25sp" />


    <TextView
        android:id="@+id/login_tv_get_voice_code"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/login_verification_code1"
        android:layout_below="@+id/login_verification_code1"
        android:layout_marginTop="20dp"
        android:text="接收语音验证码"
        android:textColor="@color/host_color_black"
        android:textSize="12sp"
        tools:visibility="visible"
        android:visibility="gone" />

    <TextView
        android:id="@+id/login_timing"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:layout_alignRight="@+id/login_verification_code1"
        android:layout_below="@+id/login_verification_code1"
        android:layout_marginTop="20dp"
        tools:text="重新发送"
        android:textColor="@color/host_color_999999_888888"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/login_regiset_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/login_timing"
        android:layout_alignLeft="@id/login_verification_code1"
        android:layout_alignRight="@id/login_verification_code1"
        android:text="未注册用户登录时将自动创建账号，且代表您已同意《用户服务协议》和《隐私政策》"
        android:textColor="@color/host_color_acacaf_66666b"
        android:textSize="12sp" />

    <ProgressBar
        android:id="@+id/login_progress_bar"
        style="?android:attr/progressBarStyleLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="invisible" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_below="@id/login_regiset_hint"
        android:layout_alignParentBottom="true">

        <TextView
            android:id="@+id/login_enable_false_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="手机号已停用？"
            android:textSize="12sp"
            android:textColor="@color/host_color_666666_cfcfcf"
            android:padding="5dp"
            android:layout_marginBottom="25dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true" />

    </RelativeLayout>

</RelativeLayout>