<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/login_head_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height" />


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">


            <View
                android:id="@+id/login_horizontal_divider"
                android:layout_width="1px"
                android:layout_height="1px" />

            <LinearLayout
                android:id="@+id/login_layout_account"
                android:layout_width="300dp"
                android:layout_height="40dp"
                android:layout_marginTop="30dp"
                android:background="@drawable/login_input_bg"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="7dp"
                    android:src="@drawable/login_ic_login_username"
                    android:contentDescription="@string/login_iv_cd_icon"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/login_region_number"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_marginLeft="7dp"
                    android:gravity="center"
                    android:text="+86"
                    android:textColor="@color/login_color_4a4a4a_cfcfcf" />

                <View
                    android:id="@+id/login_vertical_divider"
                    android:layout_width="1px"
                    android:layout_height="22dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="7dp"
                    android:background="@color/host_item_border_color" />

                <EditText
                    android:id="@+id/login_username"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:background="@null"
                    android:drawablePadding="7dp"
                    android:gravity="center_vertical"
                    android:hint="@string/login_hint_input_phone_num"
                    android:imeOptions="actionNext"
                    android:maxLines="1"
                    android:paddingLeft="7dp"
                    android:paddingRight="7dp"
                    android:textColor="@color/host_color_black"
                    android:textColorHint="@color/host_color_c8c8c8_888888"
                    android:textSize="14sp" />

                <ImageView
                    android:id="@+id/login_iv_clear_accout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:padding="5dp"
                    android:src="@drawable/login_icon_clear_input"
                    android:visibility="invisible"
                    android:contentDescription="@string/login_content_description_clear_account"
                    />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/login_layout_password"
                android:layout_width="300dp"
                android:layout_height="40dp"
                android:layout_marginTop="15dp"
                android:background="@drawable/login_input_bg"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="7dp"
                    android:src="@drawable/login_ic_password"
                    android:contentDescription="@string/login_iv_cd_icon"
                    android:visibility="gone" />

                <EditText
                    android:id="@+id/login_password"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="2"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:paddingLeft="7dp"
                    android:paddingRight="7dp"
                    android:inputType="textPassword"
                    android:textColor="@color/host_color_black"
                    android:textColorHint="@color/host_color_c8c8c8_888888"
                    android:textSize="14sp" />

                <ImageView
                    android:id="@+id/login_iv_clear_pwd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:padding="5dp"
                    android:src="@drawable/login_icon_clear_input"
                    android:contentDescription="@string/login_content_description_clear_pwd"
                    android:visibility="invisible" />

                <TextView
                    android:id="@+id/login_tv_check_code"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:background="@color/host_color_d8d8d8_2a2a2a"
                    android:gravity="center"
                    android:text="@string/login_get_check_code"
                    android:textColor="@color/host_color_ffffff_4d4d4d"
                    android:textSize="13sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="300dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="5dp"
                    android:drawableLeft="@drawable/login_ic_argeement"
                    android:drawablePadding="5dp"
                    android:text="@string/login_my_agree"
                    android:textColor="@color/host_color_666666_888888"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/login_agreement"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/login_register_term"
                    android:textColor="@color/host_orange"
                    android:textSize="12sp" />
            </LinearLayout>

            <Button
                android:id="@+id/login_login"
                android:layout_width="300dp"
                android:layout_height="40dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/login_login_highlight_btn_bg"
                android:gravity="center"
                android:text="@string/login_sso_quick_login"
                android:textColor="#ffffff"
                android:textSize="16sp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>