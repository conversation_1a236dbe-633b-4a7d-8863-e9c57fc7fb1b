<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/host_color_ffffff_121212"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/login_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@drawable/login_gray_underline_white_bg" />


    <ViewStub
        android:id="@+id/login_get_code_stub"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:inflatedId="@+id/login_get_verify_code_group"
        android:layout="@layout/login_fra_get_sms_code" />


    <ViewStub
        android:id="@+id/login_verify_code_stub"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:inflatedId="@+id/login_verify_code_group"
        android:layout="@layout/login_fra_verify_sms_code" />


</LinearLayout>




