<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
       xmlns:tools="http://schemas.android.com/tools"
       tools:parentTag="android.widget.RelativeLayout"
                android:orientation="vertical">

    <FrameLayout
        android:id="@+id/login_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@drawable/login_gray_underline_white_bg"/>

    <ImageView
        android:id="@+id/login_verify_identy_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/login_title_bar"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        android:contentDescription="@string/login_iv_cd_icon"
        android:src="@drawable/login_verify_check_img"/>

    <TextView
        android:id="@+id/login_verify_identy_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/login_verify_identy_img"
        android:layout_centerHorizontal="true"
        android:text="为了账号安全，请验证身份"
        android:textColor="@color/host_color_black"
        android:textSize="14sp" />


    <EditText
        android:id="@+id/login_phone"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_below="@id/login_verify_identy_title"
        android:layout_marginTop="25dp"
        android:background="@null"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:enabled="false"
        android:focusable="false"
        android:paddingLeft="7dp"
        android:paddingRight="7dp"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:textColor="@color/host_color_black"
        android:textColorHint="@color/login_color_c8c8c8_999999"
        android:textSize="14sp" />

    <View
        android:id="@+id/login_divider_below_psw"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@+id/login_phone"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:background="@color/login_color_e5e5e5_272727" />

    <Button
        android:id="@+id/login_login"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_below="@id/login_divider_below_psw"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/login_login_highlight_btn_bg"
        android:enabled="true"
        android:text="下一步"
        android:textStyle="bold"
        android:textColor="@drawable/login_login_button_text"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/login_enable_false_check"
        android:text="手机号已停用，收不到验证码？"
        android:textColor="@color/host_color_999999_888888"
        android:layout_centerHorizontal="true"
        android:textSize="12sp"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="25dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <FrameLayout
        android:id="@+id/login_fragment_container"
        android:layout_height="match_parent"
        android:layout_width="match_parent">
    </FrameLayout>
</merge>