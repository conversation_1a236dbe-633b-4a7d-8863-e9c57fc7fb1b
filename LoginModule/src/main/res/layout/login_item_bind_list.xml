<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/host_bg_list_selector"
    android:minHeight="50dp"
    android:orientation="vertical">


    <TextView
        android:id="@+id/login_setting_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="20dp"
        android:ellipsize="middle"
        android:gravity="center"
        android:maxEms="8"
        android:text="@string/login_find_friend"
        android:textColor="@color/login_color_575757"
        android:textSize="16sp"
        android:singleLine="true" />

    <TextView
        android:id="@+id/login_setting_otherinfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginRight="10dp"
        android:layout_toLeftOf="@+id/login_right"
        android:gravity="center"
        android:text="@string/login_go_bind"
        android:textColor="@color/host_text_light"
        android:textSize="13sp" />

    <ImageView
        android:id="@+id/login_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="10dp"
        android:contentDescription="@string/login_content_description_more"
        android:src="@drawable/host_ic_more" />

    <View
        android:id="@+id/login_bindlist_divider"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="20dp"
        android:background="@color/host_border_gray"
        android:visibility="visible" />

</RelativeLayout>