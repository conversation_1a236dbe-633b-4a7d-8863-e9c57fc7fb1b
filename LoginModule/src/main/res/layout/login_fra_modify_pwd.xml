<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/login_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@drawable/login_gray_underline_white_bg" />

    <View
        android:id="@+id/login_divider"
        android:layout_width="match_parent"
        android:layout_height="15dp"
        android:layout_below="@id/login_title_bar"
        android:background="@color/framework_bg_color" />

    <LinearLayout
        android:id="@+id/login_layout_old_pwd"
        android:layout_width="match_parent"
        android:layout_height="50dip"
        android:layout_below="@+id/login_divider"
        android:background="@color/login_color_ffffff_121212"
        android:gravity="left|center"
        android:orientation="horizontal">

        <TextView
            style="@style/login_setting_text_title"
            android:layout_width="wrap_content"
            android:layout_marginLeft="10dip"
            android:drawableRight="@null"
            android:minWidth="95dp"
            android:text="@string/login_old_pwd_title"
            android:textSize="16sp" />

        <EditText
            android:id="@+id/login_et_old_pwd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dip"
            android:background="@null"
            android:hint="@string/login_old_pwd_content"
            android:inputType="textPassword"
            android:textSize="16sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/login_divide1"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_below="@+id/login_layout_old_pwd"
        android:background="@color/host_border_gray" />

    <LinearLayout
        android:id="@+id/login_layout_new_pwd"
        android:layout_width="match_parent"
        android:layout_height="50dip"
        android:layout_below="@+id/login_divide1"
        android:background="@color/login_color_ffffff_121212"
        android:gravity="left|center"
        android:orientation="horizontal">

        <TextView
            style="@style/login_setting_text_title"
            android:layout_width="wrap_content"
            android:layout_marginLeft="10dip"
            android:drawableRight="@null"
            android:minWidth="95dp"
            android:text="@string/login_new_pwd_title"
            android:textSize="16sp" />

        <EditText
            android:id="@+id/login_et_new_pwd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dip"
            android:background="@null"
            android:hint="@string/login_new_pwd_content"
            android:inputType="textPassword"
            android:textSize="16sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/login_divide2"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_below="@+id/login_layout_new_pwd"
        android:background="@color/host_border_gray" />

    <LinearLayout
        android:id="@+id/login_layout_confirm_pwd"
        android:layout_width="match_parent"
        android:layout_height="50dip"
        android:layout_below="@+id/login_divide2"
        android:background="@color/login_color_ffffff_121212"
        android:gravity="left|center"
        android:orientation="horizontal">

        <TextView
            style="@style/login_setting_text_title"
            android:layout_width="wrap_content"
            android:layout_marginLeft="10dip"
            android:drawableRight="@null"
            android:ellipsize="none"
            android:minWidth="95dp"
            android:text="@string/login_new_pwd_verify_title"
            android:textSize="16sp" />

        <EditText
            android:id="@+id/login_et_verify_pwd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dip"
            android:background="@null"
            android:hint="@string/login_new_pwd_verify_content"
            android:inputType="textPassword"
            android:textSize="16sp" />
    </LinearLayout>

<!--    <TextView-->
<!--        android:id="@+id/login_tv_change_password_hint"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_below="@+id/login_layout_confirm_pwd"-->
<!--        android:paddingLeft="25dp"-->
<!--        android:paddingRight="25dp"-->
<!--        android:paddingTop="10dp"-->
<!--        android:gravity="center"-->
<!--        android:textSize="12sp"-->
<!--        android:text="@string/login_password_safe_tip"-->
<!--        android:textColor="@color/host_color_999999_888888"-->
<!--        android:background="@color/framework_bg_color" />-->

    <TextView
        android:id="@+id/login_tv_forget_password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/login_layout_confirm_pwd"
        android:layout_centerHorizontal="true"
        android:paddingTop="20dp"
        android:text="@string/login_forget_password"
        android:textColor="@color/host_color_xmRed"
        android:textSize="12sp"
        android:gravity="center"
        android:background="@color/framework_bg_color"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/login_tv_forget_password"
        android:background="@color/framework_bg_color" />

</RelativeLayout>