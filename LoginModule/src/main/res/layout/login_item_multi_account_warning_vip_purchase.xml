<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="108dp"
    android:background="@drawable/login_bg_rect_ffffff_1e1e1e_stroke_eeeeee_radius_12"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <View
        android:layout_width="match_parent"
        android:layout_height="108dp"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/login_item_multi_account_mask"
        android:layout_width="match_parent"
        android:layout_height="108dp"
        android:background="@drawable/login_bg_rect_19f49e6c_stroke_f49e6c_width_1_radius_12"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/login_item_multi_account_cover_area"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="12dp"
        app:cardCornerRadius="21dp"
        app:cardBackgroundColor="@color/host_transparent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/login_item_multi_account_cover"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:scaleType="fitXY"
            android:src="@drawable/host_default_album" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/login_item_multi_account_label"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:textSize="10dp"
        android:textColor="@color/host_color_ffffff"
        android:paddingLeft="7dp"
        android:paddingRight="10dp"
        android:background="@drawable/login_bg_rect_ffeee9_radius_0_12_0_10"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="当前登录"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/login_item_multi_account_name"
        android:layout_width="wrap_content"
        android:layout_height="22dp"
        android:textSize="16sp"
        android:textColor="@color/login_color_000000_a0a0a0"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginLeft="13dp"
        android:layout_marginRight="15dp"
        android:gravity="center_vertical"
        app:layout_constraintTop_toTopOf="@id/login_item_multi_account_cover_area"
        app:layout_constraintBottom_toBottomOf="@id/login_item_multi_account_cover_area"
        app:layout_constraintLeft_toRightOf="@id/login_item_multi_account_cover_area"
        tools:text="132****9527"/>

    <ImageView
        android:id="@+id/login_item_multi_account_icon"
        android:layout_width="wrap_content"
        android:layout_height="14dp"
        android:layout_marginLeft="8dp"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="@id/login_item_multi_account_cover_area"
        app:layout_constraintBottom_toBottomOf="@id/login_item_multi_account_cover_area"
        app:layout_constraintLeft_toRightOf="@id/login_item_multi_account_name"/>

    <View
        android:id="@+id/login_item_multi_account_divider"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:background="@color/login_color_eeeeee_666666"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="41dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/login_item_multi_account_access"
        android:layout_width="wrap_content"
        android:layout_height="17dp"
        android:textSize="12sp"
        android:textColor="@color/host_color_666666"
        android:layout_marginBottom="12dp"
        android:layout_marginLeft="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:text="登录方式：微信登录"/>

    <TextView
        android:id="@+id/login_item_multi_account_time"
        android:layout_width="wrap_content"
        android:layout_height="17dp"
        android:textSize="12sp"
        android:textColor="@color/host_color_666666"
        android:layout_marginBottom="12dp"
        android:layout_marginRight="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="注册时间：2021-2-2"/>

</androidx.constraintlayout.widget.ConstraintLayout>