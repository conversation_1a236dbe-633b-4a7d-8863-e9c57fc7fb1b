<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/host_color_ffffff_121212"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/login_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@drawable/host_gray_underline_white_bg" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="40dp"
        android:layout_marginTop="60dp"
        android:scaleType="fitXY"
        android:contentDescription="@string/login_content_description_bind_phone_bg"
        android:src="@drawable/login_image_change_phone" />

    <TextView
        android:id="@+id/login_tv_phone_number"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="25dp"
        android:gravity="center_horizontal"
        android:textColor="@color/host_color_null_cfcfcf"
        android:text="绑定的手机号：137****5678	"
        android:textSize="18sp" />

    <Button
        android:id="@+id/login_btn_change_phone"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:background="@drawable/login_login_highlight_btn_bg"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:text="@string/login_change_phone_number"
        android:textColor="@drawable/login_login_button_text"
        android:textSize="18sp" />

</LinearLayout>