package com.ximalaya.ting.android.login.fragment;


import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.tencent.smtt.sdk.WebView;
import com.tencent.tauth.Tencent;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.SlideView.IOnFinishListener;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.SmsLoginDialogActivity;
import com.ximalaya.ting.android.host.activity.login.LoginActivity;
import com.ximalaya.ting.android.host.activity.login.SsoAuthorizeActivity;
import com.ximalaya.ting.android.host.activity.web.WebActivityDuiBaMall;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog;
import com.ximalaya.ting.android.host.manager.account.LoginUtil;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.request.ExecutorDeliveryM;
import com.ximalaya.ting.android.host.model.sso.SsoAuthInfo;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.login.manager.LoginManager;
import com.ximalaya.ting.android.loginservice.BaseResponse;
import com.ximalaya.ting.android.loginservice.IHandleRequestCode;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.loginservice.LoginService;
import com.ximalaya.ting.android.loginservice.XMLoginCallBack;
import com.ximalaya.ting.android.loginservice.XmLoginInfo;
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin;
import com.ximalaya.ting.android.loginservice.base.ILogin;
import com.ximalaya.ting.android.loginservice.base.ILogin.LoginStrategies;
import com.ximalaya.ting.android.loginservice.base.ILoginStrategy;
import com.ximalaya.ting.android.loginservice.base.IThirdLoginStrategyFactory;
import com.ximalaya.ting.android.loginservice.base.LoginFailMsg;
import com.ximalaya.ting.android.loginservice.loginstrategy.AbLoginStrategy;
import com.ximalaya.ting.android.loginservice.loginstrategy.MeiZuLogin;
import com.ximalaya.ting.android.loginservice.loginstrategy.QQLogin;
import com.ximalaya.ting.android.loginservice.loginstrategy.SinaWbLogin;
import com.ximalaya.ting.android.loginservice.loginstrategy.WXLogin;
import com.ximalaya.ting.android.loginservice.loginstrategy.XMWBAccessManager;
import com.ximalaya.ting.android.loginservice.loginstrategy.XiaoMiLogin;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.wxcallback.wxsharelogin.WXAuthCodeObservable;
import com.ximalaya.ting.android.wxcallback.wxsharelogin.XMWXEntryActivity;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentTransaction;

import static com.ximalaya.ting.android.loginservice.base.ILogin.LOGIN_FLAG_PHONE;

//import android.webkit.WebView;


/**
 * <AUTHOR> on 2017/6/29.
 */
public class BaseLoginFragment extends BaseFragment2 {
    public static Handler mHandler = new Handler(Looper.getMainLooper());
    protected static ExecutorDeliveryM delivery = new ExecutorDeliveryM(mHandler);

    private static final String TAG = BaseLoginFragment.class.getSimpleName();
    protected static final int ERROR_CODE_SEAL_NUMBER = 47; // 账号被封号
    public static Boolean mIsFromOneKeyLogin = false;
    public static String packName;// 第三方游戏的包名，用于第三方游戏通过喜马拉雅登录后，跳转到游戏界面
    @Nullable
    public static WeakReference<Activity> mAuthSDKSoftReference = null;
    public static boolean mAuthSDKLoginFlag = false;
    @Nullable
    public static SsoAuthInfo mSsoAuthInfo = null;
    private LoginInfoModelNew loginInfoModel;
    private Activity activity;

    protected String mCountryCode = "86";
    protected Bundle loginParamsBundle; // 记录用户登录源传递的参数
    @LoginStrategies
    private int loginStrategy;

    private String name, passWd, countryCode;

    private boolean mFinishActivity = false;

    @Nullable
    private ILoginStrategy mThirdStrategy;

    private IHandleRequestCode mHandleRequestCode;

    private boolean isFormOAuth2SDK;

    public BaseLoginFragment() {
    }

    @SuppressLint("ValidFragment")
    @SuppressWarnings("NullAway")
    public BaseLoginFragment(boolean canSiled, IOnFinishListener onFinishListener) {
        super(canSiled, onFinishListener);
    }

    @Override
    protected String getPageLogicName() {
        return "baseLogin";
    }

    private static void loginForDBMall(final Activity act, String url) {
        final WebView webView = new WebActivityDuiBaMall().getWebView();
        Map<String, String> map = new HashMap<>();
        if (!TextUtils.isEmpty(url)) {
            map.put("currentUrl", url);
        }

        CommonRequestM.getoDuiBaMall(map, new IDataCallBack<String>() {

            @Override
            public void onSuccess(@Nullable String object) {
                if (object == null) {
                    showFailToast(act, com.ximalaya.ting.android.host.R.string.host_network_error);
                    return;
                }
                JSONObject jsonObject;
                try {
                    jsonObject = new JSONObject(object);
                } catch (JSONException e) {
                    e.printStackTrace();
                    jsonObject = null;
                }
                if (jsonObject == null) {
                    showFailToast(act, com.ximalaya.ting.android.host.R.string.host_network_error);
                    return;
                }
                String re_url = jsonObject.optString("url");
                if (!TextUtils.isEmpty(re_url)) {
                    if (webView != null) {
                        webView.loadUrl(re_url);
                    } else {
                        showFailToast(act, com.ximalaya.ting.android.host.R.string.host_network_error);
                    }
                } else {
                    showFailToast(act, com.ximalaya.ting.android.host.R.string.host_network_error);
                }
            }

            @Override
            public void onError(int code, String message) {
                showFailToast(act, com.ximalaya.ting.android.host.R.string.host_network_error);
            }
        });
    }

    @Nullable
    private LoginService getLoginService() {
        return LoginService.getInstance();
    }


    public void doLoginWithSina(final Activity activity, final Bundle bundle) {
        if(!ToolUtil.isInstalledByPackageName(activity ,"com.sina.weibo")) {
            showFailToast("请安装微博");
            return;
        }
        this.loginParamsBundle = bundle;
        this.loginStrategy = ILogin.LOGIN_FLAG_WEIBO;
        Activity topActivity = BaseApplication.getTopActivity();
        if (topActivity == null)
            return;

        SharedPreferencesUtil.getInstance(getmActivity()).saveBoolean(
                PreferenceConstantsInHost.TINGMAIN_KEY_LOGIN_FROM_XMLY, false);

        if (getLoginService() != null) {
            getLoginService().loginWithThirdSdk(ILogin.LOGIN_FLAG_WEIBO, getLoginStrategyFactory(), getmActivity(),
                    xmLoginCallBack);
        }
    }

    private void initCallback() {
        xmLoginCallBack = new XMLoginCallBack() {
            @Override
            public void onXMLoginSuccess(LoginInfoModelNew loginInfoModel, XmLoginInfo xmLoginInfo) {
                dismissLoginProgress();
                //登录成功后处理
                handleLoginSuccess(loginInfoModel);
            }

            @Override
            public void onLoginBegin() {
                if (!(loginStrategy == ILogin.LOGIN_FLAG_WEIBO
                        || loginStrategy == ILogin.LOGIN_FLAG_QQ
                        || loginStrategy == ILogin.LOGIN_FLAG_WEIXIN
                        || loginStrategy == ILogin.LOGIN_FLAG_MEIZU
                        || loginStrategy == ILogin.LOGIN_FLAG_XIAOMI)) {
                    showLoginProgress(getmActivity());
                }
            }

            @Override
            public void onLoginSuccess(XmLoginInfo xmLoginInfo) {

            }

            @Override
            public void onLoginFailed(LoginFailMsg msg) {
                dismissLoginProgress();

                handleLoginFail(msg);
            }
        };
    }

    private LoadingDialog loginProgressDialog;
    private void showLoginProgress(Activity activity) {
        if (activity == null || activity.isFinishing())
            return;
        if (loginProgressDialog == null) {
            loginProgressDialog = new LoadingDialog(activity);
        } else {
            loginProgressDialog.cancel();
        }
        loginProgressDialog.setTitle("正在登录...");
        loginProgressDialog.showIcon(true);
        loginProgressDialog.showBg(true);
        loginProgressDialog.show();
    }

    private void dismissLoginProgress() {
        if (loginProgressDialog != null) {
            loginProgressDialog.dismiss();
            loginProgressDialog = null;
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
    }

    private void releaseLoginData() {
        if(mThirdStrategy != null) {
            mThirdStrategy.release();
            mThirdStrategy = null;
        }

        WXAuthCodeObservable.getInstance().unRegisterObserver(XMWXEntryActivity.TRANSACTION_LOGIN);
        XMWBAccessManager.getInstance().release();
        if (getLoginService() != null) {
            getLoginService().release();
        }
    }

    @Override
    public void onDestroy() {
        releaseLoginData();
        super.onDestroy();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (getLoginService() != null) {
            if (loginStrategy == ILogin.LOGIN_FLAG_QQ && mThirdStrategy instanceof QQLogin) {
                Tencent.onActivityResultData(requestCode, resultCode, data, ((QQLogin) mThirdStrategy).getIUiListener());
            } else if (loginStrategy == ILogin.LOGIN_FLAG_WEIBO && mThirdStrategy instanceof SinaWbLogin) {
                if (((SinaWbLogin) mThirdStrategy).getSsoHandler() != null) {
                    ((SinaWbLogin) mThirdStrategy).getSsoHandler().authorizeCallback(requestCode, resultCode, data);
                }
            }
        }
    }

    protected void doLoginWithQQ() {
        final Activity topActivity = BaseApplication.getTopActivity();
        if (topActivity == null)
            return;

        if (getmActivity() == null || !canUpdateUi())
            return;

        SharedPreferencesUtil.getInstance(getmActivity()).saveBoolean(
                PreferenceConstantsInHost.TINGMAIN_KEY_LOGIN_FROM_XMLY, false);

        this.loginParamsBundle = getArguments();
        this.loginStrategy = ILogin.LOGIN_FLAG_QQ;
        if (getLoginService() != null) {
            getLoginService().loginWithThirdSdk(ILogin.LOGIN_FLAG_QQ, getLoginStrategyFactory(), getmActivity(),
                    xmLoginCallBack);
        }
    }

    public interface ILoginStatueCallbackAndFailMsg extends ILoginStatueCallBack {
        void onFail(LoginFailMsg failMsg);
    }

    public interface ILoginStatueCallBack {
        void onSuccess();

        void onFail();
    }

    protected void doLoginWithXMLY(String name, String passwd) {
        doLoginWithXMLY(name, passwd, "", null);
    }

    public static String getCountryCodePhoneNum(String countryCode ,String phoneNum) {
        // 非国内手机号 需要加上国家识别码
        return (TextUtils.equals("86", countryCode) || TextUtils.isEmpty(countryCode))
                ? phoneNum : countryCode + "-" + phoneNum;
    }

    protected void doLoginWithXMLY(String name, String passwd, String countryCode, @Nullable ILoginStatueCallBack loginStatueCallBack) {
        this.loginParamsBundle = getArguments();
        this.loginStrategy = ILogin.LOGIN_FLAG_XIMALAYA;
        this.name = name;
        this.passWd = passwd;
        this.countryCode = countryCode;
        SharedPreferencesUtil.getInstance(getmActivity()).saveBoolean(PreferenceConstantsInHost.TINGMAIN_KEY_LOGIN_FROM_XMLY, true);
        MmkvCommonUtil.getInstance(getmActivity()).saveStringUseEncrypt(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_ACCOUNT, name);
        if (!TextUtils.isEmpty(countryCode)) {
            SharedPreferencesUtil.getInstance(getmActivity().getApplicationContext())
                    .saveString(PreferenceConstantsInOpenSdk.TINGMAIN_KEY_SHARED_PRE_COUNTRY_CODE, countryCode);
            name = getCountryCodePhoneNum(countryCode ,name);
        }

        if (getLoginService() != null) {
            if (TextUtils.isEmpty(countryCode)) {
                getLoginService().loginWithEmail(getmActivity(), name, passwd, new
                        XMLoginCallBackWrapper(xmLoginCallBack, loginStatueCallBack));
            } else {
                getLoginService().loginWithPswd(getmActivity(), name, passwd, new
                        XMLoginCallBackWrapper(xmLoginCallBack, loginStatueCallBack));
            }
        }
    }

    private class XMLoginCallBackWrapper extends XMLoginCallBack {
        private XMLoginCallBack mLoginCallBack;
        private ILoginStatueCallBack mLoginStatueCallBack;

        public XMLoginCallBackWrapper(XMLoginCallBack loginCallBack, ILoginStatueCallBack loginStatueCallBack) {
            mLoginCallBack = loginCallBack;
            mLoginStatueCallBack = loginStatueCallBack;
        }

        @Override
        public void onXMLoginSuccess(LoginInfoModelNew loginInfoModel, XmLoginInfo xmLoginInfo) {
            if (mLoginCallBack != null) {
                mLoginCallBack.onXMLoginSuccess(loginInfoModel, xmLoginInfo);
            }
            if (mLoginStatueCallBack != null) {
                mLoginStatueCallBack.onSuccess();
            }
        }

        @Override
        public void onLoginBegin() {
            if(mLoginCallBack != null) {
                mLoginCallBack.onLoginBegin();
            }
        }

        @Override
        public void onLoginSuccess(XmLoginInfo xmLoginInfo) {

        }

        @Override
        public void onLoginFailed(LoginFailMsg msg) {
            if (mLoginCallBack != null) {
                mLoginCallBack.onLoginFailed(msg);
            }
            if (mLoginStatueCallBack instanceof ILoginStatueCallbackAndFailMsg) {
                ((ILoginStatueCallbackAndFailMsg) mLoginStatueCallBack).onFail(msg);
            } else {
                if (mLoginStatueCallBack != null) {
                    mLoginStatueCallBack.onFail();
                }
            }

        }
    }

    ;

    /**
     * 免密登陆
     *
     * @param name
     * @param checkCode
     */
    protected void doLoginWithoutPwd(String name, String checkCode) {
        doLoginWithoutPwd(name, checkCode, "");
    }

    /**
     * 免密登陆
     *
     * @param name
     * @param checkCode
     */
    protected void doLoginWithoutPwd(String name, String checkCode, String countryCode) {
        this.loginParamsBundle = getArguments();
        this.loginStrategy = LOGIN_FLAG_PHONE;

        this.name = name;
        this.passWd = checkCode;
        this.countryCode = countryCode;

        SharedPreferencesUtil.getInstance(getmActivity()).saveBoolean(PreferenceConstantsInHost.TINGMAIN_KEY_LOGIN_FROM_XMLY, false);
        MmkvCommonUtil.getInstance(getmActivity()).saveStringUseEncrypt(
                PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_ACCOUNT, name);
        if (!TextUtils.isEmpty(countryCode)) {
            SharedPreferencesUtil.getInstance(getmActivity().getApplicationContext())
                    .saveString(PreferenceConstantsInOpenSdk.TINGMAIN_KEY_SHARED_PRE_COUNTRY_CODE, countryCode);
            name = getCountryCodePhoneNum(countryCode ,name);
        }
        if (getLoginService() != null) {
            getLoginService().loginQuick(getmActivity(), name, checkCode, xmLoginCallBack);
        }
    }

    protected void doLoginWithWeiXin() {
        Activity topActivity = BaseApplication.getTopActivity();
        if (topActivity == null)
            return;

        Bundle bundle = new Bundle();
        bundle.putBoolean(BundleKeyConstants.KEY_JUMP_MAIN, true);
        bundle.putBoolean("isWeiXinLogin", true);  // 原来的逻辑有针对 wxentryactivity的处理，目前看不懂，这里标记下，只是把原来的逻辑移动过去，测试时观察效果
        this.loginParamsBundle = bundle;
        this.loginStrategy = ILogin.LOGIN_FLAG_WEIXIN;

        SharedPreferencesUtil.getInstance(getmActivity()).saveBoolean(
                PreferenceConstantsInHost.TINGMAIN_KEY_LOGIN_FROM_XMLY, false);

        if (getLoginService() != null) {
            getLoginService().loginWithThirdSdk(ILogin.LOGIN_FLAG_WEIXIN,
                    getLoginStrategyFactory(),
                    getmActivity(),
                    xmLoginCallBack);
        }
    }


    /**
     * 魅族第三方账号接入
     * 1.使用魅族账号接入SDK提供的API，通过AuthCode的方式进行登陆、授权、获取token
     * 2.登陆授权的核心管理类为MzAuthenticator，通过该类调用授权登陆接口，获得应用授权的authCode，
     * 3.根据authCode在MzAuthCodeCallBack回调中 (再次请求魅族服务器，通过authcode再获取 access_token,open_id,expires_in等，这点和其他登录不同，
     * 其他登录此步骤都有对应的api，魅族目前只能独立发起请求去获取数据)
     * 4.在获取  access_token,open_id,expires_in等数据后，通过本页已经封装的 登录任务 LoginTask来进行登录操作
     */
    protected void doLoginWithMeizu() {

        this.loginParamsBundle = getArguments();
        this.loginStrategy = ILogin.LOGIN_FLAG_MEIZU;

        SharedPreferencesUtil.getInstance(getmActivity()).saveBoolean(
                PreferenceConstantsInHost.TINGMAIN_KEY_LOGIN_FROM_XMLY, false);

        if (getLoginService() != null) {
            getLoginService().loginWithThirdSdk(ILogin.LOGIN_FLAG_MEIZU, getLoginStrategyFactory() ,getmActivity(), xmLoginCallBack);
        }
    }

    private IThirdLoginStrategyFactory mThirdLoginStrategyFactory;
    private IThirdLoginStrategyFactory getLoginStrategyFactory() {
        if(mThirdLoginStrategyFactory == null) {
            mThirdLoginStrategyFactory = new IThirdLoginStrategyFactory() {
                @Override
                public AbLoginStrategy getLoginStrategyByType(int type) {
                    AbLoginStrategy abLoginStrategy;
                    switch (type) {
                        case ILogin.LOGIN_FLAG_MEIZU:
                            abLoginStrategy = new MeiZuLogin();
                            break;
                        case ILogin.LOGIN_FLAG_QQ:
                            abLoginStrategy = new QQLogin();
                            break;
                        case ILogin.LOGIN_FLAG_WEIBO:
                            abLoginStrategy = new SinaWbLogin();
                            break;
                        case ILogin.LOGIN_FLAG_WEIXIN:
                            abLoginStrategy = new WXLogin();
                            break;
                        case ILogin.LOGIN_FLAG_XIAOMI:
                            abLoginStrategy = new XiaoMiLogin();
                            break;
                            default:
                                if(ConstantsOpenSdk.isDebug) {
                                    throw new RuntimeException("没有可以选择的第三方登录方式");
                                }
                                return null;
                    }

                    mThirdStrategy = abLoginStrategy;
                    return abLoginStrategy;
                }
            };
        }
        return mThirdLoginStrategyFactory;
    }

    XMLoginCallBack xmLoginCallBack;

    protected void doLoginWithXiaomi() {
        this.loginStrategy = ILogin.LOGIN_FLAG_XIAOMI;
        this.loginParamsBundle = getArguments();

        Activity topActivity = BaseApplication.getTopActivity();
        if (topActivity == null || topActivity.isFinishing()) {
            return;
        }
        SharedPreferencesUtil.getInstance(getmActivity()).saveBoolean(
                PreferenceConstantsInHost.TINGMAIN_KEY_LOGIN_FROM_XMLY, false);
        if (getLoginService() != null) {
            getLoginService().loginWithThirdSdk(ILogin.LOGIN_FLAG_XIAOMI, getLoginStrategyFactory(), getmActivity(),
                    xmLoginCallBack);
        }
    }


    private void handleLoginSuccess(LoginInfoModelNew loginInfoModel) {
        handleLoginSuccess(loginInfoModel ,true);
    }

    protected void handleLoginSuccess(LoginInfoModelNew loginInfoModel ,boolean showSuccessToast) {
        releaseLoginData();
        requestBack(loginInfoModel ,showSuccessToast);
    }

    /**
     * 登录失败后的信息封装
     *
     * @param msg
     */
    private void handleLoginFail(LoginFailMsg msg) {
        releaseLoginData();
        LoginInfoModelNew mLoginInfo = new LoginInfoModelNew();
        mLoginInfo.setRet(msg.getErrorCode());
        mLoginInfo.setMsg("" + msg.getErrorMsg());
        requestBack(mLoginInfo ,true);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
    }

    @Override
    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {

        return 0;

    }

    @Override
    protected boolean setNetworkErrorButtonVisiblity() {
        return false;
    }

    @Override
    protected boolean onPrepareNoContentView() {
        return false;
    }

    @Override
    protected void onNoContentButtonClick(View view) {

    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        this.activity = activity;
        if (xmLoginCallBack == null) {
            initCallback();
        }

        Bundle arguments = getArguments();
        if(arguments != null) {
            isFormOAuth2SDK = arguments.getBoolean(BundleKeyConstants.KEY_LOGIN_FROM_OAUTH_SDK, false);
        }

        mHandleRequestCode = LoginManager.getHandlerRequestCode(activity, new LoginManager.ILoginHandler() {
            @Override
            public int getLoginStrategy() {
                return loginStrategy;
            }

            @Override
            public void onHandlerRequestCode() {
                try {
                    dismissLoginProgress();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }, isFormOAuth2SDK);
        LoginRequest.setHandleRequestCode(new WeakReference<IHandleRequestCode>(mHandleRequestCode));
    }

    public FragmentActivity getmActivity() {
        return (FragmentActivity) activity;
    }

    private void requestBack(LoginInfoModelNew result ,boolean showSuccessToast) {
        this.loginInfoModel = result;
        if (getmActivity() == null || getmActivity().isFinishing()) {
            return;
        }
        if (result != null) {

            if (result.getRet() == 0) {// 登录成功
                String srcPage = "引导用户登录全屏";
                if (mActivity instanceof SmsLoginDialogActivity) {
                    srcPage = "引导用户登录半屏";
                }

                if (getActivity() != null && getActivity().getIntent() != null) {
                    int loginBy = getActivity().getIntent().getIntExtra(BundleKeyConstants.KEY_LOGIN_BY , LoginByConstants.LOGIN_BY_FULL_SCREEN);
                    if (loginBy == LoginByConstants.LOGIN_BY_CHANGE_USER) {
                        UserInfoMannage.clearLogStatus(mContext);
                    }
                }

                Bundle bundle = null;
                if (getActivity() != null) {
                    Intent intent = getActivity().getIntent();
                    if(intent != null) {
                        bundle = intent.getExtras();
                    }
                }

                LoginUtil.loginSuccess(getmActivity() , result ,loginStrategy ,srcPage ,bundle);

                boolean needJumpToMySpace = false;
                if (getActivity() != null) {
                    Intent intent = getActivity().getIntent();
                    if (intent != null) {
                        boolean isFromGuide = intent.getBooleanExtra(BundleKeyConstants.KEY_LOGIN_FROM_GUIDE, false);
                        needJumpToMySpace = intent.getBooleanExtra(BundleKeyConstants.KEY_JUMP_TO_MYSPACE, false);
                        if (isFromGuide) {
                            userTrackOnLoginFromGuide(result, result.getUid());
                        }
                    }
                }

                Intent intent = MainActivity.getMainActivityIntent(getmActivity());
                String url = null;
                boolean isLoginFromDuiBa = false;
                boolean isLoginFromHotline = false;
                boolean isLoginFromOAuthSDK = false;
                boolean isLoginFromXmAth = false;
                boolean isLoginFromXmAuthorizeActivity = false;
                boolean isLoginFromGuide = false;
                SsoAuthInfo ssoAuthInfo = null;

                boolean needJumpToMainActivity = true;
                String currentUrl = "";
                Logger.d(TAG, "bundle:" + loginParamsBundle);
                if (loginParamsBundle != null) {
                    if (loginParamsBundle.containsKey(BundleKeyConstants.KEY_EXTRA_URL)) {
                        url = loginParamsBundle.getString(BundleKeyConstants.KEY_EXTRA_URL);
                    }
                    isLoginFromDuiBa = loginParamsBundle.getBoolean(WebActivityDuiBaMall.LOGIN_FROM_DUIBA, false);

                    isLoginFromOAuthSDK = loginParamsBundle.getBoolean(BundleKeyConstants.KEY_LOGIN_FROM_OAUTH_SDK);
                    isLoginFromXmAth = loginParamsBundle.getBoolean(BundleKeyConstants.KEY_LOGIN_FROM_XM_AUTH);
                    ssoAuthInfo = loginParamsBundle.getParcelable(BundleKeyConstants.KEY_OAUTH_SDK_OAUTH_INFO);

                    needJumpToMainActivity = loginParamsBundle.getBoolean(BundleKeyConstants.KEY_JUMP_MAIN, true);

                    isLoginFromHotline = loginParamsBundle.getBoolean(AppConstants.LOGIN_FROM_HOTLINE, false);
                    Logger.d(TAG, "needJumpToMainActivity:" + needJumpToMainActivity);
                    currentUrl = loginParamsBundle.getString("currentUrl");

                    // OAuth2SDK授权登录来自微信
                    if (loginParamsBundle.getBoolean("isWeiXinLogin") && mAuthSDKLoginFlag) {
                        if (mAuthSDKSoftReference != null) {
                            Activity authActivity = mAuthSDKSoftReference.get();
                            if (authActivity != null && authActivity instanceof LoginActivity) {
                                Intent ssoAuthIntent = new Intent(authActivity, SsoAuthorizeActivity.class);
                                if (mSsoAuthInfo != null) {
                                    ssoAuthIntent.putExtra(BundleKeyConstants.KEY_OAUTH_SDK_OAUTH_INFO, mSsoAuthInfo);
                                }
                                authActivity.startActivityForResult(ssoAuthIntent, LoginActivity.REQUEST_OAUTH_SSO_AUTHORIZE);
                            }
                            mAuthSDKLoginFlag = false;
                            mSsoAuthInfo = null;
                            mAuthSDKSoftReference.clear();
                            mAuthSDKSoftReference = null;
                        }
                        return;
                    }
                }

                if (getArguments() != null) {
                    Bundle arguments = getArguments();
                    isLoginFromXmAuthorizeActivity = arguments.getBoolean(BundleKeyConstants.KEY_LOGIN_FROM_XM_AUTH_ACTIVITY, false);
                }

                if (loginStrategy == LOGIN_FLAG_PHONE && result.getToSetPwd()) {
                    if (getmActivity() != null) {
                        if (isLoginFromOAuthSDK) { // OAuth2SDK：是否自授权SDK
                            if(isLoginFromXmAth) {
                                getmActivity().finish();
                                return;
                            }

                            // (1) YES 免密码登录成功后直接跳转至授权页面，
                            // (2) NO执行正常免密码登录流程
                            Intent ssoAuthIntent = new Intent(getmActivity(), SsoAuthorizeActivity.class);
                            if (ssoAuthInfo != null) {
                                ssoAuthIntent.putExtra(BundleKeyConstants.KEY_OAUTH_SDK_OAUTH_INFO, ssoAuthInfo);
                            }
                            getmActivity().startActivityForResult(ssoAuthIntent, LoginActivity.REQUEST_OAUTH_SSO_AUTHORIZE);
                            // 如果从登录页面，跳转到注册并登录页面(即快捷登录页面)，执行完授权逻辑，跳转至授权页面时，清空回退栈
                            if (((LoginActivity) getmActivity()).getSupportFragmentManager().getBackStackEntryCount() > 0) {
                                ((LoginActivity) getmActivity()).getSupportFragmentManager().popBackStack();
                            }
                            return;
                        }
                    }
                }
                if (isLoginFromDuiBa) {
                    // 如果从兑吧页面跳转到登陆页面,在登陆成功后重新回到之前兑吧跳转页面并带uid刷新
                    loginForDBMall(getmActivity(), currentUrl);
                    if (getmActivity() != null) {
                        getmActivity().finish();
                    }
                    return;

                } else if (isLoginFromOAuthSDK) {  // OAuth2SDK：登录成功后跳转至授权页面

                    if(isLoginFromXmAth) {
                        getmActivity().finish();
                        return;
                    }

                    if (getmActivity() != null) {
                        Intent ssoAuthIntent = new Intent(getmActivity(), SsoAuthorizeActivity.class);
                        ssoAuthIntent.putExtra(BundleKeyConstants.KEY_OAUTH_SDK_OAUTH_INFO, ssoAuthInfo);
                        getmActivity().startActivityForResult(ssoAuthIntent, LoginActivity.REQUEST_OAUTH_SSO_AUTHORIZE);
                        // 如果从登录页面，跳转到注册并登录页面(即快捷登录页面)，执行完授权逻辑，跳转至授权页面时，清空回退栈
                        if (((LoginActivity) getmActivity()).getSupportFragmentManager().getBackStackEntryCount() > 0) {
                            ((LoginActivity) getmActivity()).getSupportFragmentManager().popBackStack();
                        }
                        Activity activity = BaseApplication.getTopActivity();
                        if (activity != null && !(activity instanceof LoginActivity)) {
                            activity.finish();
                        }
                    }
                    return;
                } else if (!TextUtils.isEmpty(url)) {
                    if (getmActivity() != null) {
                        getmActivity().finish();
                    }
                    return;
                } else if (isLoginFromHotline) {
                    if (getmActivity() != null) {
                        getmActivity().finish();
                    }
                    return;
                } else if (isLoginFromXmAuthorizeActivity) {
                    if (getmActivity() != null) {
                        getmActivity().finish();
                    }
                    return;
                } else {
                    intent.putExtra(BundleKeyConstants.KEY_IS_LOGIN, true);
                }
                MmkvCommonUtil.getInstance(getmActivity()).saveStringUseEncrypt(
                        PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_ACCOUNT, name);

                if (needJumpToMainActivity) {
                    if (null != mIsFromOneKeyLogin && !mIsFromOneKeyLogin && BaseApplication.getMainActivity() == null)
                        getmActivity().startActivity(intent);
                    getmActivity().finish();
                    if (needJumpToMySpace && BaseApplication.getMainActivity() instanceof MainActivity) {
                        ((MainActivity) BaseApplication.getMainActivity()).gotoMySpacePage(null);
                    }
                } else {
                    mFinishActivity = true;
                }

                // 登录时回调给Datasupport
//                getmActivity().getContentResolver().notifyChange(
//                        DataContentObserver.getUriByType(DataContentObserver.TypeLoginIn), null);
            } else {
                Logger.e("login", result.getMsg());
                // 如果是在登录页并且是手机登录不弹出该toast,因为会再外面弹出dialog提示用户使用验证码登录
                if (!((getClass() == LoginFragment.class)
                        && loginStrategy == ILogin.LOGIN_FLAG_XIMALAYA
                        && !TextUtils.isEmpty(countryCode))) {
                    if(!TextUtils.isEmpty(result.getMsg())) {
                        showFailToast(result.getMsg());
                    }
                }
            }
        } else {
            showFailToast("网络超时，请稍候再试！");
        }
        if (mFinishActivity && getmActivity() != null) {
            getmActivity().finish();
        }
        if (mIsFromOneKeyLogin)         //判断是否来自一键登录，跳转回游戏
        {
            try {
                mIsFromOneKeyLogin = false;
                Intent intent = new Intent();
                intent.setAction("android.LoginSDK.action.ONEKEYLOGIN_BACK." + packName);
                intent.putExtra("fromThirdLogin", true);
                getmActivity().startActivity(intent);
                Log.e("packName________", packName);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    private void userTrackOnLoginFromGuide(LoginInfoModelNew model, long uid) {
        if (model == null) {
            return;
        }
        String from = model.getLoginType();

        new UserTracking()
                .setSrcPage("新用户强制登录页")
                .setItem("user")
                .setItemId(uid)
                .setLoginType(from)
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, "login");
    }

    public void startFragment(FragmentActivity activity, Fragment fragment) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed())
            return;
        if (fragment == null) return;
        FragmentTransaction ft = activity.getSupportFragmentManager().beginTransaction();
        ft.add(android.R.id.content, fragment);
        ft.addToBackStack(null);
        ft.commitAllowingStateLoss();
    }

    public void removeFragmentAndAddFragment(FragmentActivity activity, Fragment removeFragment , Fragment addFragment) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed())
            return;
        if (addFragment == null) return;
        FragmentTransaction ft = activity.getSupportFragmentManager().beginTransaction();
        if (removeFragment != null) {
           ft.remove(removeFragment);
        }
        ft.add(android.R.id.content, addFragment);
        ft.addToBackStack(null);
        ft.commitAllowingStateLoss();
    }

    // 因为SmsLoginDialogActivity 里面弹出了一个Dialog ,所以里面显示toast的会被dialog背景覆盖
    void showFailToast(int toastId) {
        CustomToast.showFailToast(toastId);
    }

    void showFailToast(String toast) {
        CustomToast.showFailToast(toast);
    }

    public static void showFailToast(Activity activity, int stringId) {
        CustomToast.showFailToast(stringId);
    }

    public static void showFailToast(Activity activity, String string) {
        CustomToast.showFailToast(string);
    }

    void showSuccessToast(String string) {
        CustomToast.showToast(string);
    }

    private LoadingDialog progressDialog;

    protected void getPhoneCheckCode(@LoginRequest.SendSmsType final int sendSmsType ,
                                     final String phoneNumber, final String countryCode,
                                     final WeakReference<? extends BaseLoginFragment> softReference) {
        getPhoneCheckCode(sendSmsType ,phoneNumber ,countryCode ,softReference ,null);
    }

    protected void getPhoneCheckCode(@LoginRequest.SendSmsType final int sendSmsType ,
                                     final String phoneNumber, final String countryCode,
                                     final WeakReference<? extends BaseLoginFragment> softReference ,
                                     final IHandleOk onDataCallBack) {
        final BaseLoginFragment fragment = softReference.get();
        if (fragment == null)
            return;

        this.loginStrategy = LOGIN_FLAG_PHONE;
        MmkvCommonUtil.getInstance(getmActivity()).saveStringUseEncrypt(
                PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_ACCOUNT, name);

        this.name = phoneNumber;
        if (!TextUtils.isEmpty(countryCode)) {
            SharedPreferencesUtil.getInstance(getmActivity().getApplicationContext())
                    .saveString(PreferenceConstantsInOpenSdk.TINGMAIN_KEY_SHARED_PRE_COUNTRY_CODE, countryCode);
        }

        if (fragment.progressDialog == null) {
            fragment.progressDialog = new LoadingDialog(fragment.getActivity());
        } else {
            fragment.progressDialog.cancel();
        }
        fragment.progressDialog.showIcon(true);
        fragment.progressDialog.showBg(true);
        fragment.progressDialog.show();
        sendSms(sendSmsType ,phoneNumber, countryCode, onDataCallBack, fragment);
    }

    private void sendSms(@LoginRequest.SendSmsType final int sendSmsType ,
                         final String phoneNumber, final String countryCode,
                         final IHandleOk onDataCallBack, final BaseLoginFragment fragment) {

        Map<String ,String> maps = new HashMap<>();
        maps.put("mobile", getCountryCodePhoneNum(countryCode ,phoneNumber));
        maps.put("sendType" ,"1");
        LoginRequest.sendSms(getActivity() ,sendSmsType ,LoginService.getInstance().getRquestData() , maps,
                new IDataCallBackUseLogin<BaseResponse>() {
            @Override
            public void onSuccess(@androidx.annotation.Nullable BaseResponse object) {
                if (fragment.progressDialog != null)
                    fragment.progressDialog.cancel();

                if (fragment.canUpdateUi() && object != null) {
                    // 点击登录到验证码接收页面
                    if(onDataCallBack != null) {
                        onDataCallBack.onReady();
                    } else {
                        Bundle bundle = new Bundle();
                        Bundle arguments = fragment.getArguments();
                        if(arguments != null) {
                            bundle.putAll(arguments);
                        }
                        bundle.putString(BundleKeyConstants.KEY_PHONE_NUMBER, phoneNumber);
                        bundle.putString("countryCode", countryCode);
                        SmsVerificationCodeFragment smsVerificationCodeFragment = SmsVerificationCodeFragment.newInstance(bundle);
                        fragment.startFragment(smsVerificationCodeFragment);
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                if (fragment.progressDialog != null)
                    fragment.progressDialog.cancel();

                if (fragment.canUpdateUi()) {
                    CustomToast.showFailToast(message);
                }
            }
        });
    }

    public void setLoginStrategy(int loginStrategy) {
        this.loginStrategy = loginStrategy;
    }
}
