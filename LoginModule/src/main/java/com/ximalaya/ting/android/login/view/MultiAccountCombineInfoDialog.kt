package com.ximalaya.ting.android.login.view

import android.content.Context
import android.os.Bundle
import android.os.CountDownTimer
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.constants.LoginByConstants
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.login.R
import com.ximalaya.ting.android.login.model.MultiAccountCombineResult
import com.ximalaya.ting.android.login.model.MultiAccountInfo
import com.ximalaya.ting.android.login.request.LoginCommonRequest
import com.ximalaya.ting.android.login.view.gridedittext.GridPasswordView
import com.ximalaya.ting.android.login.view.gridedittext.GridPasswordView.OnPasswordChangedListener
import com.ximalaya.ting.android.loginservice.BaseResponse
import com.ximalaya.ting.android.loginservice.ConstantsForLogin
import com.ximalaya.ting.android.loginservice.LoginEncryptUtil
import com.ximalaya.ting.android.loginservice.LoginRequest
import com.ximalaya.ting.android.loginservice.LoginRequest.SEND_SMS_TYPE_FOR_LOGIN
import com.ximalaya.ting.android.loginservice.LoginService
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin
import com.ximalaya.ting.android.loginservice.base.ILogin
import com.ximalaya.ting.android.loginservice.model.VerifySmsResponse
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.SystemServiceManager
import java.util.HashMap

/**
 * Created by nali on 2022/4/27.
 * <AUTHOR>
 */
class MultiAccountCombineInfoDialog(val mulitAccountInfo: MultiAccountInfo) :
    BaseDialogFragment<MultiAccountCombineInfoDialog>(), View.OnClickListener {

    private lateinit var mClose: ImageView
    private lateinit var mTitle: TextView
    private lateinit var mUserListLay: MaxHeightScrollView
    private lateinit var mUserList: LinearLayout
    private lateinit var mSubTitle: TextView
    private lateinit var mOkBtn: TextView
    private lateinit var mPasswordView: GridPasswordView
    private lateinit var mTiming: TextView
    private lateinit var mLoadDialog: MyProgressDialog

    // 当前的状态
    private var mCurState: Int = 0
    private var mCombineResult: MultiAccountCombineResult? = null
    private var mElapsedTime = 0
    private var mCountDownTimer: CountDownTimer? = null;

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog?.apply {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            window?.let {
                it.decorView?.setPadding(0, 0, 0, 0)
                val lp = it.attributes
                lp.width = WindowManager.LayoutParams.MATCH_PARENT
                lp.height = WindowManager.LayoutParams.WRAP_CONTENT
                lp.gravity = Gravity.CENTER
                it.attributes = lp

                it.setGravity(Gravity.CENTER)
                it.setBackgroundDrawableResource(com.ximalaya.ting.android.host.R.color.host_transparent)
                it.setDimAmount(0.8f)
            }

            setCancelable(false)
            setCanceledOnTouchOutside(false)
        }

        return inflater.inflate(R.layout.login_mult_account_combine_info_dialog, container, false)
    }


    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        initUI()

        mCurState = 0
        updateState()
    }

    private fun initUI() {
        mClose = findViewById(R.id.login_close) as ImageView
        mTitle = findViewById(R.id.login_title) as TextView
        mUserListLay = findViewById(R.id.login_user_list_lay) as MaxHeightScrollView
        mUserList = findViewById(R.id.login_user_list) as LinearLayout
        mOkBtn = findViewById(R.id.login_ok_btn) as TextView
        mSubTitle = findViewById(R.id.login_sub_title) as TextView
        mPasswordView = findViewById(R.id.login_verification_code1) as GridPasswordView
        mTiming = findViewById(R.id.login_timing) as TextView

        mClose.setOnClickListener(this)
        mOkBtn.setOnClickListener(this)
    }

    private fun updateState() {
        if (!isVisible && mCurState != 0) {
            return
        }
        when(mCurState) {
            0 -> initState()
            1 -> inputSmsCodeState()
            2 -> checkSmsSuccess()
        }
    }

    private fun clickBottomBtn() {
        when(mCurState) {
            0 -> {
                XMTraceApi.Trace()
                    .setMetaId(43476)
                    .setServiceId("dialogClick")
                    .createTrace()

                val time = System.currentTimeMillis() - LoginRequest.mLastSendSmsTime
                if (time > 60 * 1000) {
                    sendSms()
                } else {
                    mCurState = 1
                    updateState()
                    HandlerManager.removeCallbacks(sendSmsRunnable)
                    HandlerManager.postOnUIThreadDelay(sendSmsRunnable, time + 2000)
                }
            }
            1 -> {
                // 多账号合并校验弹窗-操作  弹框控件点击
                XMTraceApi.Trace()
                    .setMetaId(43479)
                    .setServiceId("dialogClick")
                    .put("item", mOkBtn.text.toString())
                    .put("dialogTitle", mTitle.text.toString())
                    .createTrace()

                try {
                    val imm = SystemServiceManager.getSystemService(context, Context.INPUT_METHOD_SERVICE) as InputMethodManager
                    imm.hideSoftInputFromWindow(mPasswordView.windowToken, 0)
                } catch (e: Exception) {
                    e.printStackTrace()
                }

                verificationSms(mPasswordView.passWord)
            }
            2 -> {
                if (mCombineResult?.needLogin == true) {
                    UserInfoMannage.logOut(ToolUtil.getCtx())
                    UserInfoMannage.gotoLogin(ToolUtil.getCtx(), LoginByConstants.LOGIN_BY_FULL_SCREEN);
                }
                dismiss()
            }
        }
    }

    // 初始化状态
    private fun initState() {
        mTitle.text = "检测到手机${mulitAccountInfo.mobile}绑定了多个账号，可进行合并"
        mulitAccountInfo.multiAccountCombineAccountInfos?.forEachIndexed { index, accountInfo ->
            val itemView = LayoutInflater.from(context)
                .inflate(R.layout.login_mult_account_item_info, null, false)
            val avaterView = itemView.findViewById<ImageView>(R.id.login_icon)
            val title = itemView.findViewById<TextView>(R.id.login_auth_title)
            val subTitle = itemView.findViewById<TextView>(R.id.login_autho_sub_title)
            val isCurLogin = itemView.findViewById<TextView>(R.id.login_auth_curr_login)

            ImageManager.from(context).displayImage(avaterView, accountInfo.avatar, com.ximalaya.ting.android.host.R.drawable.host_default_avatar_88)
            title.text = accountInfo.nickname
            var subTitleContent = if(accountInfo.haveAssets) "有权益账号" else "无权益账号"
            subTitleContent += " | "
            accountInfo.multiAccountLoginTypes?.forEach {
                subTitleContent += it.cnName
                subTitleContent += ";"
            }
            subTitleContent = subTitleContent.substring(0, subTitleContent.length - 1)
            subTitleContent += "登录"

            subTitle.text = subTitleContent
            if (accountInfo.loginUid || !accountInfo.combine) {
                if (accountInfo.loginUid && !accountInfo.combine) {
                    isCurLogin.text = "当前登录|合并至该账号"
                } else if (!accountInfo.combine) {
                    isCurLogin.text = "合并至该账号"
                } else {
                    isCurLogin.text = "当前登录"
                }
                isCurLogin.visibility = View.VISIBLE
            } else {
                isCurLogin.visibility = View.GONE
            }
            val layoutParams = LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT)
            layoutParams.bottomMargin = if(index == mulitAccountInfo.multiAccountCombineAccountInfos.size) 0 else BaseUtil.dp2px(ToolUtil.getCtx(), 10f)
            mUserList.addView(itemView, layoutParams)
        }
        
    }

    override fun onClick(v: View?) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return
        }
        when(v) {
            mClose -> {
                if (mCurState == 0) {
                    // 多账号合并确认弹窗-关闭  弹框控件点击
                    XMTraceApi.Trace()
                        .setMetaId(43475)
                        .setServiceId("dialogClick")
                        .createTrace()
                } else if(mCurState == 1 || mCurState == 2) {
                    // 多账号合并校验弹窗-关闭  弹框控件点击
                    XMTraceApi.Trace()
                        .setMetaId(43478)
                        .setServiceId("dialogClick")
                        .put("dialogTitle", mTitle.text.toString())
                        .createTrace()
                }
                dismiss()
            }
            mOkBtn -> clickBottomBtn()
            mTiming -> sendSms()
        }
    }

    private val sendSmsRunnable = Runnable {
        sendSms()
    }

    private fun sendSms() {
        mPasswordView.clearPassword()
        val requestParam = mutableMapOf<String, String>()
        requestParam[ILogin.ENCRYPTED_MOBILE] = mulitAccountInfo.cipherMobile
        requestParam["sendType"] = "1"
        LoginRequest.sendSms(MainApplication.getTopActivity() as? FragmentActivity,
            SEND_SMS_TYPE_FOR_LOGIN,
            LoginService.getInstance().rquestData,
            requestParam,
            object : IDataCallBackUseLogin<BaseResponse> {
                override fun onSuccess(data: BaseResponse?) {
                    if (data?.ret == 0) {
                        mCurState = 1
                        updateState()
                    }
                }

                override fun onError(code: Int, message: String?) {
                    showFail(message)
                }
            })
    }

    // 短信验证码输入状态
    private fun inputSmsCodeState() {
        startTimingResend()

        mOkBtn.alpha = 0.4f
        mOkBtn.isEnabled = false
        mOkBtn.text = "下一步"
        mUserListLay.visibility = View.GONE
        mTitle.text = "输入验证码"
        mSubTitle.text = "您正在进行账号合并操作，为了您的账号安全，验证码已发送至 ${mulitAccountInfo.mobile}，请在下方输入验证码"
        mSubTitle.visibility = View.VISIBLE
        mPasswordView.visibility = View.VISIBLE
        mTiming.visibility = View.VISIBLE
        mTiming.setOnClickListener(this)

        traceCurrState()

        mPasswordView.requestFocus()
        mPasswordView.requestFocusFromTouch()
        mPasswordView.setOnPasswordChangedListener(
            object : OnPasswordChangedListener {
                override fun onTextChanged(psw: String) {
                    if (psw.length < 6) {
                        mOkBtn.alpha = 0.4f
                        mOkBtn.isEnabled = false
                    }
                }

                override fun onInputFinish(psw: String) {
                    if (TextUtils.isEmpty(psw)) {
                        return
                    }

                    mOkBtn.alpha = 1.0f
                    mOkBtn.isEnabled = true
                }
            })
        mPasswordView.forceInputViewGetFocus()
    }

    private fun checkSmsSuccess() {
        mPasswordView.visibility = View.GONE
        mTiming.visibility = View.GONE
        mTitle.text = "合并成功"

        var subTitleContent = "您可以使用"

        val loginTypeSet = linkedSetOf<String>()

        mulitAccountInfo.multiAccountCombineAccountInfos?.forEach {
            it.multiAccountLoginTypes?.forEach {
                if (it.cnName != null) {
                    loginTypeSet.add(it.cnName)
                }
            }
        }

        loginTypeSet.forEach{
            subTitleContent += "$it,"
        }
        if (subTitleContent.endsWith(",")) {
            subTitleContent = subTitleContent.substring(0, subTitleContent.length - 1)
        }
        subTitleContent += "任一方式登录该账号"

        mSubTitle.text = subTitleContent
        mOkBtn.text = if (mCombineResult?.needLogin == true) "去登录" else "我知道了"
        mOkBtn.alpha = 1.0f
        mOkBtn.isEnabled = true
    }

    private fun verificationSms(checkKey: String) {
        showLoadingDialog("正在校验验证码")
        val map: MutableMap<String, String> = mutableMapOf()
        // 这里不需要添加城市编号
        map["mobile"] = mulitAccountInfo.cipherMobile
        map["code"] = checkKey
        LoginRequest.verifySms(LoginService.getInstance().rquestData, map,
            object : IDataCallBackUseLogin<VerifySmsResponse?> {
                override fun onSuccess(verifySmsResponse: VerifySmsResponse?) {
                    LoginRequest.getLoginNone(LoginService.getInstance().rquestData, object : IDataCallBackUseLogin<String?> {
                        override fun onSuccess(nonceData: String?) {
                            dismissLoadingDialog()

                            if (TextUtils.isEmpty(nonceData)) {
                                showFail(null)
                                return
                            }

                            val maps = HashMap<String, String?>()
                            maps["smsKey"] = verifySmsResponse?.bizKey
                            maps["combineKey"] = mulitAccountInfo.combineKey
                            maps["nonce"] = nonceData
                            maps["signature"] = LoginEncryptUtil.getInstance().createLoginParamSign(context,
                                ConstantsForLogin.ENVIRONMENT_ON_LINE != ConstantsForLogin.environmentId, maps)
                            CustomToast.showToast("正在合并请稍后")

                            LoginCommonRequest.confirmMultiAccountCombine(maps, object : IDataCallBack<MultiAccountCombineResult> {
                                override fun onSuccess(data: MultiAccountCombineResult?) {
                                    mCombineResult = data
                                    mCombineResult?.takeIf { it.ret == 0 }?.let {
                                        mCurState = 2;
                                        updateState()
                                    }
                                }

                                override fun onError(code: Int, message: String?) {
                                    showFail(message)
                                }
                            })
                        }

                        override fun onError(code: Int, message: String?) {
                            dismissLoadingDialog()

                            showFail(message)
                        }
                    })
                }

                override fun onError(code: Int, message: String?) {
                    dismissLoadingDialog()

                    showFail(message)
                }
            })
    }

    private fun showFail(message: String?) {
        CustomToast.showFailToast(message ?: "请稍后再试")
    }

    private fun showLoadingDialog(content: String?) {
        mLoadDialog = MyProgressDialog(context)
        mLoadDialog.setMessage(content)
        mLoadDialog.show()
    }

    private fun dismissLoadingDialog() {
        if (mLoadDialog?.isShowing == true) {
            mLoadDialog.dismissNoCheckIsShow()
        }
    }

    private fun startTimingResend() {
        mElapsedTime = 0
        if (mCountDownTimer != null) {
            mCountDownTimer?.cancel()
            mCountDownTimer = null
        }

        mCountDownTimer = object : CountDownTimer(70_000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                mElapsedTime++
                if (mElapsedTime >= 60) {
                    stopTiming()
                    mTiming.text = "重新发送"
                    mTiming.isEnabled = true
                    mTiming.setTextColor(
                        ContextCompat.getColor(
                            ToolUtil.getCtx(),
                            com.ximalaya.ting.android.host.R.color.host_color_f86442
                        )
                    )
                } else {
                    val leftTime = 60 - mElapsedTime
                    mTiming.isEnabled = false
                    mTiming.text = leftTime.toString() + "s后再次发送"
                    mTiming.setTextColor(
                        ContextCompat.getColor(
                            ToolUtil.getCtx(),
                            com.ximalaya.ting.android.host.R.color.host_color_999999_888888
                        )
                    )
                }

            }

            override fun onFinish() {}
        }

        mCountDownTimer?.start()
    }

    private fun stopTiming() {
        mCountDownTimer?.cancel()
    }

    override fun dismiss() {
        HandlerManager.removeCallbacks(sendSmsRunnable)
        stopTiming()
        super.dismiss()
    }

    override fun onResume() {
        super.onResume()

        traceCurrState()
    }

    private fun traceCurrState() {
        when(mCurState) {
            0 -> {
                // 多账号合并确认弹窗  弹框展示
                XMTraceApi.Trace()
                    .setMetaId(43474)
                    .setServiceId("dialogView")
                    .createTrace()
            }
            1, 2 -> {
                // 多账号合并校验弹窗  弹框展示
                XMTraceApi.Trace()
                    .setMetaId(43477)
                    .setServiceId("dialogView")
                    .put("dialogTitle", mTitle.text.toString())
                    .createTrace()
            }
        }

    }
}