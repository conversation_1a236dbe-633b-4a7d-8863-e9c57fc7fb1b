package com.ximalaya.ting.android.login.fragment.register;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.Editable;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.view.Gravity;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.ViewGroup.LayoutParams;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.login.LoginActivity;
import com.ximalaya.ting.android.host.activity.login.SsoAuthorizeActivity;
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog;
import com.ximalaya.ting.android.host.manager.account.ScoreManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.login.LoginHelper;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.model.account.LoginInfoModel;
import com.ximalaya.ting.android.host.model.sso.SsoAuthInfo;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.login.model.VerifyNicknameModel;
import com.ximalaya.ting.android.login.request.LoginCommonRequest;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 注册第四步——设置昵称
 *
 * <AUTHOR> on 2017/6/29.
 */
public class RegisterStepFourFragment extends BaseRegisterFragment implements OnClickListener {

    private String mUuid;
    private ImageButton mClearInput;
    private EditText mNickname;
    private Button mDoneBtn;
    @Nullable
    private LoadingDialog mProgressDialog;
    private boolean isChecking;
    private boolean isSetting;

    public static Fragment newInstance(Bundle bundle) {
        RegisterStepFourFragment fragment = new RegisterStepFourFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected String getPageLogicName() {
        return "registerStepFore";
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.login_top;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        if (getArguments() != null) {
            mUuid = getArguments().getString("uuid");
        }
        mClearInput = (ImageButton) findViewById(R.id.login_clear_input);
        mNickname = (EditText) findViewById(R.id.login_nickname);
        mDoneBtn = (Button) findViewById(R.id.login_done);
        mProgressDialog = new LoadingDialog(getActivity());
        setTitle(R.string.login_set_nickname);
        mNickname.requestFocus();
        mDoneBtn.setEnabled(false);
        mClearInput.setOnClickListener(this);
        mDoneBtn.setOnClickListener(this);
        AutoTraceHelper.bindData(mClearInput,"");
        AutoTraceHelper.bindData(mDoneBtn,"");

        mNickname.addTextChangedListener(new TextWatcher() {

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (TextUtils.isEmpty(s)) {
                    mClearInput.setVisibility(View.INVISIBLE);
                    mDoneBtn.setEnabled(false);
                } else {
                    mDoneBtn.setEnabled(true);
                    mClearInput.setVisibility(View.VISIBLE);

                    verifyNickname();
                }
            }

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count,
                                          int after) {

            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
    }

    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.login_fra_register_step_four;
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.login_clear_input) {
            mNickname.setText("");
            mClearInput.setVisibility(View.INVISIBLE);

        } else if (i == R.id.login_done) {
            setNickname();

        } else {
        }
    }

    private void verifyNickname() {
        if (isChecking) {
            return;
        }
        isChecking = true;
        String nickname = mNickname.getText().toString();
        if (TextUtils.isEmpty(nickname)) {
            CustomToast.showFailToast("昵称不能为空");
            return;
        }
        Map<String, String> params = new HashMap<>();
        params.put("nickname", nickname);


//		View view [] = new View[0];
//		if(getView() != null) {
//			view = new View[]{ getView().findViewById(R.id.login_input_area)};
//		}

        LoginCommonRequest.checkNickname(params, new IDataCallBack<VerifyNicknameModel>() {

            @Override
            public void onSuccess(@Nullable VerifyNicknameModel object) {
                isChecking = false;
                if (object != null) {
                    showSuggestNickname(object);
                }
            }

            @Override
            public void onError(int code, String message) {
                isChecking = false;
                CustomToast.showFailToast(message);
            }
        });
    }

    private void setNickname() {
        if (isSetting) {
            return;
        }
        isSetting = true;
        String nickname = mNickname.getText().toString();
        if (!TextUtils.isEmpty(nickname)) {
            Map<String, String> params = new HashMap<>();
            params.put("uuid", mUuid);
            params.put("nickname", nickname);
            if (mProgressDialog != null) {
                mProgressDialog.show();
            }
            if (getView() != null) {
                getView().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (canUpdateUi() && mProgressDialog != null && mProgressDialog.isShowing()) {
                            if (mProgressDialog != null && mProgressDialog.isShowing()) {
                                mProgressDialog.dismiss();
                            }
                        }
                    }
                }, 1200);
            }

            LoginCommonRequest.setNickname(params, new IDataCallBack<JSONObject>() {

                @Override
                public void onSuccess(@Nullable JSONObject object) {
                    if (mProgressDialog != null && mProgressDialog.isShowing()) {
                        mProgressDialog.dismiss();
                    }
                    isSetting = false;
                    String msgString = "";
                    if (object != null) {
                        try {
                            int ret = object.getInt("ret");
                            msgString = object.getString("msg");
                            if (ret == 0) {
                                try {
                                    completeLogin((LoginInfoModel) new Gson().fromJson(object.toString(), new TypeToken<LoginInfoModel>() {
                                    }.getType()));
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                return;
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                    CustomToast.showToast(msgString);
                }

                @Override
                public void onError(int code, String message) {
                    if (mProgressDialog != null && mProgressDialog.isShowing()) {
                        mProgressDialog.dismiss();
                    }
                    isSetting = false;
                    CustomToast.showFailToast(message);
                }
            });
        } else {
            CustomToast.showFailToast("昵称不能为空");
        }
    }

    /**
     * 完成登录
     *
     * @param result
     */
    private void completeLogin(LoginInfoModel result) {

        if (!canUpdateUi() || getActivity() == null)
            return;

        // 下面这段代码只是为了可以编译通过 ,实际应该不会有注册步骤到这里
        LoginInfoModelNew loginInfoModelNew = LoginHelper.fromOldLoginInfoModel(result);
        UserInfoMannage userInforMannage = UserInfoMannage.getInstance();
        userInforMannage.setUser(loginInfoModelNew);

        ScoreManage scoreManage = ScoreManage.getInstance(getActivity().getApplicationContext());
        if (scoreManage != null) {
            scoreManage.initBehaviorScore();
            scoreManage.updateScore();
        }

        // OAuth2SDK：是否来自OAuth2SDK，是：跳转至授权页面 否：跳转至主界面
        boolean isFormOAuth2SDK = isFormOAuth2SDK();

        boolean isFromXmAuth = isFormXmAuth();

        if (isFromXmAuth) {
            getActivity().finish();
        } else if (isFormOAuth2SDK) {
            Intent authIntent = new Intent(getActivity(), SsoAuthorizeActivity.class);
            SsoAuthInfo ssoAuthInfo = parseSsoAuthInfoFormBundle();
            if (ssoAuthInfo != null) {
                authIntent.putExtra(BundleKeyConstants.KEY_OAUTH_SDK_OAUTH_INFO, ssoAuthInfo);
            }
            if (mActivity != null) {
                mActivity.startActivityForResult(authIntent, LoginActivity.REQUEST_OAUTH_SSO_AUTHORIZE);
            }
            if (getActivity() != null && getActivity() instanceof LoginActivity) {
                FragmentManager fragmentManager = getActivity().getSupportFragmentManager();
                while (fragmentManager != null && fragmentManager.getBackStackEntryCount() > 0) {
                    fragmentManager.popBackStackImmediate();
                }
            }
        } else {
            Intent intent = MainActivity.getMainActivityIntent(getActivity());
            intent.putExtra("isShowFreshGift", result.isCoupons());
            getActivity().finish();
            getActivity().startActivity(intent);
        }
    }

    /**
     * 显示建议昵称
     *
     * @param data
     */
    private void showSuggestNickname(final VerifyNicknameModel data) {
        if (data.recommand != null && data.recommand.length > 0 && getActivity() != null) {
            final PopupWindow pw = new PopupWindow(getActivity());
            pw.setHeight(LayoutParams.WRAP_CONTENT);
            pw.setWidth(getView().findViewById(R.id.login_input_area).getWidth());
            pw.setBackgroundDrawable(new BitmapDrawable(getResourcesSafe(), (Bitmap) null));
            pw.setOutsideTouchable(true);
            pw.setTouchable(true);
            final ListView listView = new ListView(mContext);
            listView.setDivider(new ColorDrawable(Color.parseColor("#dedede")));
            LocalImageUtil.setBackgroundDrawable(listView, ContextCompat.getDrawable(mContext, R.drawable.login_register_nickname_suggestion_bg));
            listView.setDividerHeight(1);
            pw.setContentView(listView);

            TextView tv1 = new TextView(getActivity());
            tv1.setTextColor(ContextCompat.getColor(mContext, R.color.login_color_575757_888888));
            tv1.setTextSize(17);
            tv1.setGravity(Gravity.CENTER);
            tv1.setText("昵称已存在");
            tv1.setWidth(LayoutParams.MATCH_PARENT);
            tv1.setHeight(BaseUtil.dp2px(mContext, 40));

            TextView tv2 = new TextView(getActivity());
            tv2.setTextColor(ContextCompat.getColor(mContext,
                    com.ximalaya.ting.android.host.R.color.host_color_999999_888888));
            tv2.setTextSize(15);
            tv2.setGravity(Gravity.CENTER_VERTICAL);
            tv2.setBackgroundColor(ContextCompat.getColor(mContext, R.color.login_color_efefef_1e1e1e));
            tv2.setPadding(BaseUtil.dp2px(mContext, 10), 0, 0, 0);
            tv2.setText("推荐昵称");
            tv2.setWidth(LayoutParams.MATCH_PARENT);
            tv2.setHeight(BaseUtil.dp2px(mContext, 30));

            listView.addHeaderView(tv1, null, false);
            listView.addHeaderView(tv2, null, false);
            listView.setHeaderDividersEnabled(true);
            NicknameSuggestionAdapter adapter = new NicknameSuggestionAdapter(getActivity(), R.layout.login_item_register_nickname_suggest, data.recommand);
            adapter.setKeyword(mNickname.getText().toString());
            listView.setAdapter(adapter);

            listView.setOnItemClickListener(new OnItemClickListener() {

                @Override
                public void onItemClick(AdapterView<?> parent, View view,
                                        int position, long id) {
                    pw.dismiss();
                    int index = position - listView.getHeaderViewsCount();
                    if (data.recommand == null || index < 0 || index >= data.recommand.length) {
                        return;
                    }
                    mNickname.setText(data.recommand[index]);
                }
            });
            pw.showAsDropDown(getView().findViewById(R.id.login_input_area), 0, BaseUtil.dp2px(getActivity(), 5));
        }
    }

    @Override
    protected boolean setNetworkErrorButtonVisiblity() {
        // TODO Auto-generated method stub
        return false;
    }

    @Override
    protected boolean onPrepareNoContentView() {
        // TODO Auto-generated method stub
        return false;
    }

    @Override
    protected void onNoContentButtonClick(View view) {
        // TODO Auto-generated method stub

    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38564;
        super.onMyResume();
    }

    private static class NicknameSuggestionAdapter extends ArrayAdapter<String> {

        private String mKeyword;
        private String[] mData;

        public NicknameSuggestionAdapter(Context context, int resource, String[] objects) {
            super(context, resource, objects);
            mData = objects;
        }

        public void setKeyword(String keyword) {
            mKeyword = keyword;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            View v = super.getView(position, convertView, parent);
            if (v instanceof TextView && !TextUtils.isEmpty(mKeyword)) {
                Pattern pattern = Pattern.compile(mKeyword);
                String str = mData[position];
                Spannable spannable = new SpannableString(str);
                Matcher matcher = pattern.matcher(str);
                while (matcher.find()) {
                    spannable.setSpan(new ForegroundColorSpan(Color.parseColor("#ff4444")), matcher.start(), matcher.end(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                }
                ((TextView) v).setText(spannable);
            }
            return v;
        }

    }
}
