package com.ximalaya.ting.android.login.util;

import android.app.Activity;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.host.IBindCallBack;
import com.ximalaya.ting.android.login.request.LoginCommonRequest;
import com.ximalaya.ting.android.loginservice.ConstantsForLogin;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.loginservice.LoginService;
import com.ximalaya.ting.android.loginservice.XmLoginInfo;
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin;
import com.ximalaya.ting.android.loginservice.base.ILoginStrategy;
import com.ximalaya.ting.android.loginservice.base.LoginFailMsg;
import com.ximalaya.ting.android.loginservice.bindstrategy.AuthorizationInfo;
import com.ximalaya.ting.android.loginservice.loginstrategy.QQLogin;
import com.ximalaya.ting.android.loginservice.loginstrategy.WXLogin;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by dekai.liu on 6/4/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18392566603
 */
public class SyncThirdPartyInfoUtil {
    // 同com.ximalaya.ting.android.loginservice.base.ILogin中的值
    private static final int LOGIN_FLAG_QQ = 2;
    private static final int LOGIN_FLAG_WEIXIN = 4;

    private static class HolderClass {
        private final static SyncThirdPartyInfoUtil instance = new SyncThirdPartyInfoUtil();
    }

    public static SyncThirdPartyInfoUtil getInstance() {
        return SyncThirdPartyInfoUtil.HolderClass.instance;
    }

    private SyncThirdPartyInfoUtil() {
    }

    public void syncWXInfo(Activity activity, IBindCallBack callBack) {
        syncWXInfo(activity, callBack, false);
    }

    public void syncWXInfoAndBind(Activity activity, IBindCallBack callBack) {
        syncWXInfo(activity, callBack, true);
    }

    private void syncWXInfo(Activity activity, IBindCallBack callBack, boolean bind) {
        syncInfo(LOGIN_FLAG_WEIXIN, new WXLogin(), activity, callBack, bind);
    }

    public void syncQQInfo(Activity activity, IBindCallBack callBack) {
        syncInfo(LOGIN_FLAG_QQ, new QQLogin(), activity, callBack, false);
    }

    private void syncInfo(final int loginStrategy, ILoginStrategy strategy,
                          Activity activity, final IBindCallBack callBack, final boolean bind) {
        if (strategy == null || activity == null) {
            return;
        }
        strategy.login(activity, null, new ILoginStrategy.AuthCodeCallBack() {
            @Override
            public void onFail(LoginFailMsg msg) {
                if (callBack != null) {
                    callBack.onBindFail(msg.getErrorMsg());
                }
            }

            @Override
            public void onSuccess(final XmLoginInfo loginInfo) {
                if (bind) {
                    getSyncInfoToken(loginStrategy, loginInfo, callBack);
                } else {
                    onGetLoginInfoSuccess(loginStrategy, loginInfo, callBack, null);
                }
            }
        });
    }

    private void getSyncInfoToken(final int loginStrategy, final XmLoginInfo loginInfo,
                                  final IBindCallBack callBack) {
        LoginCommonRequest.getSyncInfoToken(ConstantsForLogin.getThirdIdByLoginFlag(loginStrategy), new IDataCallBack<String>() {
            @Override
            public void onSuccess(@Nullable String nonce) {
                if (TextUtils.isEmpty(nonce)) {
                    if (callBack != null) {
                        callBack.onBindFail("获取token失败，请重试");
                    }
                } else {
                    onGetLoginInfoSuccess(loginStrategy, loginInfo, callBack, nonce);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (callBack != null) {
                    callBack.onBindFail(message);
                }
            }
        });
    }

    private void onGetLoginInfoSuccess(final int loginStrategy, final XmLoginInfo loginInfo,
                                       final IBindCallBack callBack, String nonce) {
        if (loginInfo == null) {
            return;
        }
        if (loginStrategy == LOGIN_FLAG_WEIXIN) {
            Map<String, String> maps = new HashMap<>();
            maps.put("thirdpartyId", ConstantsForLogin.getThirdIdByLoginFlag(loginStrategy) + "");
            maps.put("code", loginInfo.authInfo.getBackCode());
            if (!TextUtils.isEmpty(nonce)) {
                maps.put("state", nonce);
            }

            LoginRequest.requestTokenByCode(LoginService.getInstance().getRquestData(), loginStrategy, maps,
                    new IDataCallBackUseLogin<AuthorizationInfo>() {
                        @Override
                        public void onSuccess(@Nullable AuthorizationInfo object) {
                            if (object != null && object.getCode() == 0) {
                                if (callBack != null) {
                                    callBack.onBindSuccess(object);
                                }
                            } else {
                                if (callBack != null) {
                                    if (object == null) {
                                        callBack.onBindFail("请求异常");
                                    } else {
                                        callBack.onBindFail(object.getMsg());
                                    }
                                }
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            if (callBack != null) {
                                callBack.onBindFail(message);
                            }
                        }
                    });
        } else {
            accessByToken(loginStrategy, loginInfo, callBack);
        }
    }

    private void accessByToken(int thirdPartyId, final XmLoginInfo loginInfo, final IBindCallBack callBack) {
        if (loginInfo == null) {
            return;
        }
        Map<String, String> requestParams = new HashMap<>();
        requestParams.put("thirdpartyId", ConstantsForLogin.getThirdIdByLoginFlag(thirdPartyId) + "");
        if (loginInfo.authInfo != null) {
            requestParams.put("accessToken", loginInfo.authInfo.getAccess_token());
            if (!TextUtils.isEmpty(loginInfo.authInfo.getRefreshToken())) {
                requestParams.put("refreshToken", loginInfo.authInfo.getRefreshToken());
            }

            if (!TextUtils.isEmpty(loginInfo.authInfo.getExpires_in())) {
                requestParams.put("expireIn", loginInfo.authInfo.getExpires_in());
            }

            if (!TextUtils.isEmpty(loginInfo.authInfo.getOpenid())) {
                requestParams.put("openId", loginInfo.authInfo.getOpenid());
            }
        }

        LoginRequest.accessByToken(LoginService.getInstance().getRquestData(), thirdPartyId,
                requestParams, new IDataCallBackUseLogin<AuthorizationInfo>() {
                    @Override
                    public void onSuccess(@Nullable AuthorizationInfo object) {
                        if (object != null && object.getCode() == 0) {
                            if (callBack != null) {
                                callBack.onBindSuccess(object);
                            }
                        } else {
                            if (callBack != null) {
                                if (object == null) {
                                    callBack.onBindFail("请求异常");
                                } else {
                                    callBack.onBindFail(object.getMsg());
                                }
                            }
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        if (callBack != null) {
                            callBack.onBindFail(message);
                        }
                    }
                });
    }
}
