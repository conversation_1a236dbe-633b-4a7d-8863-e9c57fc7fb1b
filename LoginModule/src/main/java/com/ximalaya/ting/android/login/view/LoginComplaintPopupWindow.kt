package com.ximalaya.ting.android.login.view

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.widget.PopupWindow
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.view.BubbleTextView
import com.ximalaya.ting.android.login.R
import com.ximalaya.ting.android.login.constants.LoginUrlConstants
import java.lang.Exception


/**
 * Created by nali on 2022/3/25.
 * <AUTHOR>
 */
class LoginComplaintPopupWindow constructor(context: Context?) : PopupWindow(context) {

    private val mContentView: View =
        LayoutInflater.from(context).inflate(R.layout.login_complaint_pop_lay, null)
    private var mBtn1: TextView? = null
    private var mBtn2: TextView? = null
    private var mBtn3: TextView? = null

    init {
        mBtn1 = mContentView?.findViewById(R.id.main_btn_1)
        mBtn2 = mContentView?.findViewById(R.id.main_btn_2)
        mBtn3 = mContentView?.findViewById(R.id.main_btn_3)

        mBtn1?.setOnClickListener{ gotoClick(1) }
        mBtn2?.setOnClickListener{ gotoClick(2) }
        mBtn3?.setOnClickListener{ gotoClick(3) }

        setBackgroundDrawable(null)
        isOutsideTouchable = true
        isTouchable = true
        isFocusable = true
        contentView = mContentView
        contentView?.setOnClickListener { dismiss() }
    }

    private fun gotoClick(index: Int) {
        val url = when(index) {
            1 -> "https://pages.ximalaya.com/mkt/act/e75f333538a17e1d"
            2 -> "iting://open?msg_type=94&bundle=account&pageName=forgetPassword"
            else -> "https://m.ximalaya.com/cs-bridge-web/page/contact-cs?systemNum=FMSP9hUpEtz7MJoz0UgSMA&_fix_keyboard=1"
        }

        val intent2 = Intent()
        intent2.action = Intent.ACTION_VIEW
        if (url.startsWith("iting")) {
            intent2.data = Uri.parse(url)
        } else {
            intent2.data = Uri.parse("iting://open?msg_type=14&url=$url")
        }
        intent2.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        ToolUtil.checkIntentAndStartActivity(ToolUtil.getCtx(), intent2)
    }
}