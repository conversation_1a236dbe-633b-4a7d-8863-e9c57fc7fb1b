package com.ximalaya.ting.android.login.fragment;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.Html;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.login.IChooseCountryListener;
import com.ximalaya.ting.android.host.activity.login.LoginActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog;
import com.ximalaya.ting.android.host.manager.account.LoginDeferredActionHelper;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.login.ILoginFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.login.LoginHelper;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.model.account.InternationalCodeModel;
import com.ximalaya.ting.android.host.util.common.JsonUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.login.manager.LoginManager;
import com.ximalaya.ting.android.login.view.gridedittext.GridPasswordView;
import com.ximalaya.ting.android.loginservice.BaseResponse;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.loginservice.LoginService;
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin;
import com.ximalaya.ting.android.loginservice.model.BindLoginInfoModel;
import com.ximalaya.ting.android.loginservice.model.VerifySmsResponse;
import com.ximalaya.ting.android.loginservice.verify.VerifyManager;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * 这个页面有两个功能
 * 1 获取验证码页面,
 * 2 校验验证码页面
 *
 * 此页面有个入口
 * 1 第三方登录没有绑定过手机
 * 2 在账号安全的账号绑定页面绑定手机(这种情况基本可以忽略了)
 * <AUTHOR> on 2017/6/29.
 */
public class GetAndVerifySmsCodeFragment extends BaseLoginFragment
        implements IChooseCountryListener,
        View.OnClickListener {
    private final int VERIFY_ERROR = -1;
    private final int VERIFY_SEND = 1;

    private ViewGroup vGetCodeGroup;
    private TextView mRegionNumber;
    private TextView mLoginBindTip;
    private Button mGetVCButton;
    private EditText mIphoneEditText;
    private ImageView mClearAccout;
    private TextView mToastTextView;

    private TextView mPhoneNumberLabel;
    private GridPasswordView vPasswordGrid;
    private TextView mTiming;
    private TextView mGetVoiceCode;

    private ImageView mLoginHint;
    private View mLoginHintLay;


    private LoadingDialog mLoadDialog;

    private String mCountryCode = "86";
    private String mMobileStr;
    private String mCheckCode;
    private boolean mIsGetCodePage = true;//当前页面类型,true 当前页面为获取验证码页面,false校验验证码页面
    private int type;//进入绑定页面情景,主动解绑(不存在了),登录被动绑定,主动绑定(也不存在了)
    private long uid;//被动绑定请求参数
    private boolean isLoginByEmail = false;
    private InputMethodManager inputManager;

    private int mPhoneNumberViewStatus;
    private ScheduledExecutorService mService;
    private int mElapsedTime;
    private boolean isRequestCode = false;

    private String bizKey;
    private boolean isFormOAuth2SDK;

    @SuppressWarnings("NullAway")
    public GetAndVerifySmsCodeFragment() {
        super(false, null);
    }

    public static GetAndVerifySmsCodeFragment newInstanceForLogin(long uid, String bizKey ,
                                                                  boolean getCodePage,
                                                                  boolean isLoginByEmail,
                                                                  boolean isFormOAuth2SDK) {
        Bundle bundle = new Bundle();
        bundle.putBoolean("mIsGetCodePage", getCodePage);
        bundle.putInt("type", AppConstants.FROM_LOGIN_PAGE);
        bundle.putBoolean("loginByEmail", isLoginByEmail);
        bundle.putLong("uid", uid);
        bundle.putString("bizKey" ,bizKey);
        bundle.putBoolean(BundleKeyConstants.KEY_LOGIN_FROM_OAUTH_SDK, isFormOAuth2SDK);
        GetAndVerifySmsCodeFragment fra = new GetAndVerifySmsCodeFragment();
        fra.setArguments(bundle);
        return fra;
    }

    /**
     * @param verifyCode 是否是验证短信验证码页面
     * @param type       类型
     * @return
     */
    public static GetAndVerifySmsCodeFragment newInstance(boolean verifyCode, int type) {
        Bundle bundle = new Bundle();
        bundle.putBoolean("mIsGetCodePage", !verifyCode);
        bundle.putInt("type", type);
        GetAndVerifySmsCodeFragment fra = new GetAndVerifySmsCodeFragment();
        fra.setArguments(bundle);
        return fra;
    }

    @Override
    protected String getPageLogicName() {
        return ILoginFragmentAction.PAGE_LOGIC_GET_AND_VERIFY_SMS_CODE;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        parseBundle();

        if(mIsGetCodePage){
            new UserTracking().setItem("强制绑定手机号").statIting(XDCSCollectUtil.APP_NAME_EVENT,XDCSCollectUtil.SERVICE_VIEW_ITEM);
        }
    }

    private void parseBundle() {
        if (getArguments() != null) {
            mIsGetCodePage = getArguments().getBoolean("mIsGetCodePage");
            type = getArguments().getInt("type");
            uid = getArguments().getLong("uid");
            bizKey = getArguments().getString("bizKey");
            isFormOAuth2SDK = getArguments().getBoolean(BundleKeyConstants.KEY_LOGIN_FROM_OAUTH_SDK);
        }
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.login_top;
    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        super.setTitleBar(titleBar);
    }


    @Override
    protected void initUi(Bundle savedInstanceState) {
        if (mIsGetCodePage) {
            showGetCodeViews();
        } else {
            showVerifyCodeViews();
        }
    }

    @Override
    public void onResume() {
        super.onResume();

        new XMTraceApi.Trace()
                .pageView(32324, "forcedBindingPhoneNum")
                .put("currPage", "forcedBindingPhoneNum")
                .createTrace();
    }

    @Override
    public void onPause() {
        super.onPause();

        new XMTraceApi.Trace()
                .pageExit2(32325)
                .put("currPage", "forcedBindingPhoneNum")
                .createTrace();
    }

    private void showGetCodeViews() {
        setTitle("绑定手机号");
        ViewStub vGetCodeStub = (ViewStub) findViewById(R.id.login_get_code_stub);
        if (vGetCodeStub != null) {
            vGetCodeStub.inflate();
            vGetCodeGroup = (ViewGroup) findViewById(R.id.login_get_verify_code_group);
            mLoginBindTip = (TextView) findViewById(R.id.login_bind_phone_toast_from_login);
            mIphoneEditText = (EditText) findViewById(R.id.login_iphone_text);
            mClearAccout = (ImageView) findViewById(R.id.login_iv_clear_accout);
            mGetVCButton = (Button) findViewById(R.id.login_reg_getvc_btn);
            mRegionNumber = (TextView) findViewById(R.id.login_region_number);
            mToastTextView = (TextView) findViewById(R.id.login_bind_iphone_toast);

            mIphoneEditText.addTextChangedListener(new PhoneNumberWatcher());
            mClearAccout.setOnClickListener(this);
            mGetVCButton.setOnClickListener(this);
            mRegionNumber.setOnClickListener(this);
            AutoTraceHelper.bindData(mClearAccout,"");
            AutoTraceHelper.bindData(mGetVCButton,"");
            AutoTraceHelper.bindData(mRegionNumber,"");

            mLoginHint = findViewById(R.id.login_login_hint_state);
            mLoginHint.setOnClickListener(this);
            AutoTraceHelper.bindData(mLoginHint,"");
            mLoginHintLay = findViewById(R.id.login_login_hint_layout);
            mLoginHintLay.setVisibility(View.GONE);
//            if(AppConstants.FROM_LOGIN_PAGE == type) {
//                SpannableStringBuilder spannableStringBuilder = LoginUtil.createLoginSpannableStr(mContext, true, 0);
//                TextView mRegister = findViewById(R.id.login_regiset_hint);
//                mRegister.setText(spannableStringBuilder);
//                mRegister.setMovementMethod(LinkMovementMethod.getInstance());
//                mRegister.setHighlightColor(Color.TRANSPARENT);
//
//                mLoginHintLay.setVisibility(View.VISIBLE);
//            }
        }
        mRegionNumber.setText("+" + mCountryCode);
        initToastView();
    }


    private void showVerifyCodeViews() {
        if (TextUtils.isEmpty(mMobileStr)) {
            BindLoginInfoModel model = ToolUtil.getLastBindPhoneInfo();
            if (model != null) {
                mMobileStr = model.getMobile();
                mCountryCode = model.getCountryCode();
            }
        }

        if (vGetCodeGroup != null)
            vGetCodeGroup.setVisibility(View.GONE);
        setTitle("输入验证码");
        ViewStub vVerifyCodeStub = (ViewStub) findViewById(R.id.login_verify_code_stub);
        if (vVerifyCodeStub != null) {
            vVerifyCodeStub.inflate();
            mPhoneNumberLabel = (TextView) findViewById(R.id.login_phone_number);
            vPasswordGrid = (GridPasswordView) findViewById(R.id.login_verification_code1);
            vPasswordGrid.requestFocus();
            vPasswordGrid.requestFocusFromTouch();
            mTiming = (TextView) findViewById(R.id.login_timing);
            mGetVoiceCode = (TextView) findViewById(R.id.login_tv_get_voice_code);


            mTiming.setOnClickListener(this);
            mGetVoiceCode.setOnClickListener(this);
            vPasswordGrid.setOnPasswordChangedListener(
                    new GridPasswordView.OnPasswordChangedListener() {
                        @Override
                        public void onTextChanged(String psw) {
                            setPhoneNumberView(VERIFY_SEND);
                        }

                        @Override
                        public void onInputFinish(String psw) {
                            if (TextUtils.isEmpty(psw)) {
                                CustomToast.showFailToast("验证码不能为空");
                                return;
                            }
                            mCheckCode = psw;
                            handleVerifyCode();
                        }
                    });

            AutoTraceHelper.bindData(mTiming,"");
            AutoTraceHelper.bindData(mGetVoiceCode,"");
        }
        setPhoneNumberView(VERIFY_SEND);
        startTimingResend();
    }

    private void setPhoneNumberView(int type) {
        if (type == VERIFY_SEND && mPhoneNumberViewStatus != VERIFY_SEND) {
            mPhoneNumberViewStatus = VERIFY_SEND;
            mPhoneNumberLabel.setTextColor(getResourcesSafe().getColor(
                    com.ximalaya.ting.android.host.R.color.host_color_666666_888888));
            mPhoneNumberLabel.setText("验证码已通过短信发送至" + " + " + mCountryCode + " " + mMobileStr);
        } else if (type == VERIFY_ERROR && mPhoneNumberViewStatus != VERIFY_ERROR) {
            mPhoneNumberViewStatus = VERIFY_ERROR;
            mPhoneNumberLabel.setTextColor(Color.parseColor("#f43530"));
            mPhoneNumberLabel.setText("验证码错误，请重新填写");
        }
    }

    private void startTimingResend() {
        mElapsedTime = 0;
        if (mService == null || mService.isShutdown()) {
            mService = new ScheduledThreadPoolExecutor(1, new ThreadFactory() {
                @Override
                public Thread newThread(@NonNull Runnable r) {
                    return new Thread(r, "GetAndVerifySmsCodeFragment#thread");
                }
            });
        }
        mService.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                mElapsedTime++;
                timingResend();
            }
        }, 0, 1000, TimeUnit.MILLISECONDS);
    }

    private void stopTiming() {
        if (mService != null && !mService.isShutdown()) {
            mService.shutdown();
        }
    }

    private void timingResend() {
        mTiming.post(new Runnable() {
            @Override
            public void run() {

                if (!canUpdateUi())
                    return;

                if (mElapsedTime >= 60) {
                    stopTiming();
                    mTiming.setText("重新发送");
                    mTiming.setEnabled(true);
                    mTiming.setTextColor(getResourcesSafe().getColor(
                            com.ximalaya.ting.android.host.R.color.host_color_f86442));
                } else {
                    int leftTime = 60 - mElapsedTime;
                    if (leftTime <= 40) {
                        if (TextUtils.equals(mCountryCode, "86"))
                            mGetVoiceCode.setVisibility(View.VISIBLE);
                    }

                    mTiming.setEnabled(false);
                    mTiming.setText(leftTime + "s后再次发送");
                    mTiming.setTextColor(getResourcesSafe().getColor(
                            com.ximalaya.ting.android.host.R.color.host_color_999999_888888));
                }
            }
        });
    }


    private void initToastView() {
        if (mToastTextView == null) return;

        switch (type) {
            case AppConstants.FROM_BIND_PAGE:
                //主动绑定
                if (!mIsGetCodePage && !TextUtils.isEmpty(mMobileStr)) {
                    mToastTextView.setText(Html.fromHtml("已向手机号<font color='#fc8542'>" + (TextUtils.equals("86", mCountryCode) ? mMobileStr : mCountryCode + "-" + mMobileStr) + "</font>发送短信验证码"));
                } else {
                    mToastTextView.setVisibility(View.GONE);
                }
                break;
            case AppConstants.FROM_LOGIN_PAGE:
                if (mIsGetCodePage) {
                    mToastTextView.setVisibility(View.GONE);
                    mLoginBindTip.setVisibility(View.VISIBLE);
                    String s = ConfigureCenter.getInstance().getString("account", "announcementMessage",null);
                    if (!TextUtils.isEmpty(s)) {
                        mLoginBindTip.setText(s);
                    } else {
                        mLoginBindTip.setText(getStringSafe(R.string.login_bind_tip_content));
                    }
                } else {
                    mLoginBindTip.setVisibility(View.GONE);
                    mToastTextView.setVisibility(View.VISIBLE);
                    if (TextUtils.isEmpty(mMobileStr)) {
                        BindLoginInfoModel model = ToolUtil.getLastBindPhoneInfo();
                        if (model != null) {
                            mMobileStr = model.getMobile();
                            mCountryCode = model.getCountryCode();
                        }
                    }
                    mToastTextView.setText(!mIsGetCodePage ? Html.fromHtml("已向手机号<font color='#fc8542'>" + (TextUtils.equals("86", mCountryCode) ? mMobileStr : mCountryCode + "-" + mMobileStr) + "</font>发送短信验证码") : "为了您的账户安全,请绑定手机号");
                }//被动绑定
                break;
            default:
                break;
        }
    }


    @Override
    protected void loadData() {
        VerifyManager.preloadGTCaptchClient(getActivity());
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.login_fra_bindiphone;
    }

    @Override
    public void finish() {
        super.finish();
    }

    @Override
    protected void finishFragment() {
        hideSoftInput();
        if (type == AppConstants.FROM_LOGIN_PAGE) {
            new DialogBuilder(getActivity())
                    .setTitleVisibility(false)
                    .setMessage("确定退出登录?")
                    .setOkBtn("确定退出", new DialogBuilder.DialogCallback() {
                        @Override
                        public void onExecute() {
                            toMainActivity(false);
                        }
                    })
                    .setCancelBtn("取消")
                    .showConfirm();
        } else {
            super.finishFragment();
        }
    }

    @Override
    protected boolean setNetworkErrorButtonVisiblity() {
        return false;
    }

    @Override
    protected boolean onPrepareNoContentView() {
        return false;
    }

    @Override
    protected void onNoContentButtonClick(View view) {
    }

    @Override
    public void onDetach() {
        super.onDetach();

        stopTiming();
    }

    private void handleVerifyCode() {
        verifyCommonSmsCode();
    }

    private void handleGetCode() {
        if (isRequestCode) {
            return;
        }

        getCommonBindSmsCode();
    }

    // 绑定时的是短信验证码
    private void getCommonBindSmsCode() {
        if (!StringUtil.verifyGlobalPhone(mCountryCode, mMobileStr)) {
            CustomToast.showFailToast("手机号格式错误");
            return;
        }
        showLoadingDialog("正在获取验证码...");
        isRequestCode = true;

        Map<String ,String> maps = new HashMap<>();
        maps.put("mobile", getCountryCodePhoneNum(mCountryCode,mMobileStr));
        maps.put("sendType" ,"1");
        LoginRequest.sendSms(getmActivity() ,isLoginByEmail
                ? LoginRequest.SEND_SMS_TYPE_FOR_EMAIL_BIND_PHONE
                : LoginRequest.SEND_SMS_TYPE_FOR_PHONE_BIND_PHONE ,
                LoginService.getInstance().getRquestData() ,maps, new IDataCallBackUseLogin<BaseResponse>() {
            @Override
            public void onSuccess(@Nullable BaseResponse object) {
                isRequestCode = false;
                dismissLoadingDialog();
                if (canUpdateUi() && object != null && object.getRet() == 0) {
                    showVerifyCodeViews();

                    BindLoginInfoModel tempInfoModule = new BindLoginInfoModel();
                    tempInfoModule.setMobile(mMobileStr);
                    tempInfoModule.setCountryCode(mCountryCode);
                    tempInfoModule.setCurTimeStamp(System.currentTimeMillis());
                    tempInfoModule.isLoginByEmail(false);
                    JsonUtil.toJson(tempInfoModule, new JsonUtil.IResult() {
                        @Override
                        public void execute(String result) {
                            if (mContext != null && !TextUtils.isEmpty(result)) {
                                SharedPreferencesUtil.getInstance(mContext.getApplicationContext()).
                                        saveString(PreferenceConstantsInHost.TINGMAIN_KEY_BINDPHONE_JSON_NEW, result);
                            }
                        }
                    });
                }
            }

            @Override
            public void onError(int code, String message) {
                isRequestCode = false;
                dismissLoadingDialog();
                CustomToast.showFailToast(message);
            }
        });
    }

    private void showContactServerDialog() {
        new DialogBuilder(getActivity())
                .setTitle("联系客服")
                .setMessage("收不到验证码？联系客服")
                .setOkBtn("联系客服", new DialogBuilder.DialogCallback() {
                    @Override
                    public void onExecute() {
                        if (getActivity() instanceof MainActivity) {
                            try {
                                BaseFragment2 feedBackMainFragment =
                                        Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().getFeedBackMainFragment();
                                startFragment(feedBackMainFragment);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                        } else if (getActivity() instanceof LoginActivity) {
                            try {
                                BaseFragment2 feedBackMainFragment =
                                        Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().getFeedBackMainFragment();
                                ((LoginActivity) getActivity()).startFragment(feedBackMainFragment);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                        }
                    }
                })
                .showConfirm();
    }

    // 校验短信验证码
    private void verifyCommonSmsCode() {
        ToolUtil.removeLastBindPhoneInfo();
        showLoadingDialog("正在为您校验验证码...");
        Map<String, String> map = new HashMap<>();
        map.put("mobile", getCountryCodePhoneNum(mCountryCode ,mMobileStr));
        map.put("code" ,mCheckCode);
        LoginRequest.verifySms(LoginService.getInstance().getRquestData() , map, new IDataCallBackUseLogin<VerifySmsResponse>() {
            @Override
            public void onSuccess(@Nullable VerifySmsResponse object) {
                dismissLoadingDialog();
                if(canUpdateUi()) {
                    if (object != null && object.getRet() == 0 && !TextUtils.isEmpty(object.getBizKey())) {
                        Map<String ,String> requestParams = new HashMap<>();
                        requestParams.put("bizKey" ,bizKey);
                        requestParams.put("smsKey" ,object.getBizKey());
                        requestParams.put("mobile" ,getCountryCodePhoneNum(mCountryCode ,mMobileStr));
                        LoginRequest.bindPhone(LoginService.getInstance().getRquestData() ,requestParams,
                                new IDataCallBackUseLogin<LoginInfoModelNew>() {
                            @Override
                            public void onSuccess(@Nullable LoginInfoModelNew user) {
                                if (user != null) {
                                    UserInfoMannage.getInstance().setUser(user);
                                    if (type == AppConstants.FROM_BIND_PAGE) {
                                        CustomToast.showSuccessToast("绑定成功!");
                                        GetAndVerifySmsCodeFragment.this.finishFragment();
                                    } else if (type == AppConstants.FROM_LOGIN_PAGE) {
                                        if (!LoginDeferredActionHelper.getInstance().isRunningDeferredAction()) {
                                            CustomToast.showSuccessToast("登录成功!");
                                        }


                                        String string = LoginManager.getOnlyUseMainProcessSharePre(mContext).getString(
                                                PreferenceConstantsInHost.TIMGMAIN_KEY_WILL_GOTO_BIND_WAY);
                                        if(!TextUtils.isEmpty(string)) {
                                            String[] split =
                                                    string.split(PreferenceConstantsInHost.WILL_GOTO_BIND_KEY_SPILT);
                                            if(split.length == 2) {
                                                try {
                                                    long l = Long.parseLong(split[0]);
                                                    if(System.currentTimeMillis() - l < 60 * 60 * 1000) {
                                                        setLoginStrategy(Integer.parseInt(split[1]));
                                                    }
                                                } catch (NumberFormatException e) {
                                                    e.printStackTrace();
                                                }
                                            }
                                        }
                                        handleLoginSuccess(user ,false);

                                        if(!isFormOAuth2SDK) {
                                            toMainActivity(false);
                                        } else {
                                            finishFragment();
                                        }
                                    }
                                } else {
                                    CustomToast.showFailToast("操作失败,请稍后再试!");
                                }
                            }

                            @Override
                            public void onError(int code, String message) {
                                CustomToast.showFailToast(TextUtils.isEmpty(message) ? "请稍候再试" : message);
                            }
                        });
                    } else {
                        String errMsg = "请稍候再试";
                        if(object != null && !TextUtils.isEmpty(object.getMsg())) {
                            errMsg = object.getMsg();
                        }
                        CustomToast.showFailToast(errMsg);
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                dismissLoadingDialog();
                CustomToast.showFailToast(TextUtils.isEmpty(message) ? "请稍候再试" : message);
            }
        });
    }

    private void toMainActivity(boolean loginSuccess) {
        if (getActivity() != null) {
            hideSoftInput();
            if (getActivity() instanceof MainActivity) {
                ((MainActivity) getActivity()).goHome();
            } else {
                Intent intent = MainActivity.getMainActivityIntent(getActivity());
                getActivity().finish();
                getActivity().startActivity(intent);
            }
            if (loginSuccess) {
                // 登录时回调给Datasupport
//                getActivity().getContentResolver()
//                        .notifyChange(
//                                DataContentObserver
//                                        .getUriByType(DataContentObserver.TypeLoginIn),
//                                null);
            }
            ToolUtil.removeLastBindPhoneInfo();
        }
    }

    @Override
    public boolean onBackPressed() {
        if (type == AppConstants.FROM_LOGIN_PAGE) {
            finishFragment();
            return true;
        } else {
            return super.onBackPressed();
        }
    }

    void showLoadingDialog(String content) {
        mLoadDialog = new LoadingDialog(getActivity());
        mLoadDialog.setTitle(content);
        mLoadDialog.showIcon(true);
        mLoadDialog.showBg(true);
        mLoadDialog.show();
    }

    public void dismissLoadingDialog() {
        if (mLoadDialog != null && mLoadDialog.isShowing()) {
            mLoadDialog.dismiss();
        }
    }

    void hideSoftInput() {
        if (mIphoneEditText != null) {
            inputManager = SystemServiceManager.getInputMethodManager(mIphoneEditText.getContext());
            inputManager.hideSoftInputFromWindow(mIphoneEditText.getWindowToken(), 0);
        }
    }

    void showSoftInput() {
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (mIphoneEditText != null) {
                    inputManager = SystemServiceManager.getInputMethodManager(mIphoneEditText.getContext());
                    inputManager.showSoftInput(mIphoneEditText, 0);
                }

            }
        });
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38395;
        super.onMyResume();
    }

    @Override
    public void onCountryChosenListener(InternationalCodeModel model) {
        if (model != null && mRegionNumber != null) {
            mCountryCode = model.countryCode;
            mRegionNumber.setText("+" + model.countryCode);
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if(type == AppConstants.FROM_LOGIN_PAGE && id == R.id.login_reg_getvc_btn) {
//            if(!mLoginHint.isSelected()) {
//                FragmentActivity activity = getActivity();
//                if(ToolUtil.activityIsValid(activity)) {
//                    LoginHintChooseRuleDialog loginHintChooseRuleDialog =
//                            new LoginHintChooseRuleDialog(getActivity());
//                    loginHintChooseRuleDialog.setOkHandle(new IHandleOk() {
//                        @Override
//                        public void onReady() {
//                            mLoginHint.setSelected(true);
//                        }
//                    });
//                    loginHintChooseRuleDialog.show();
//                }
//                return;
//            }
        }

        if (id == R.id.login_reg_getvc_btn) {
            mMobileStr = mIphoneEditText.getText().toString().trim();
            if (!TextUtils.isEmpty(mMobileStr)) {
                if (StringUtil.verifyGlobalPhone(mCountryCode, mMobileStr)) {
                    // 获取验证码
                    new XMTraceApi.Trace()
                            .click(32327)
                            .put("currPage", "forcedBindingPhoneNum")
                            .createTrace();

                    handleGetCode();
                } else {
                    CustomToast.showFailToast("请输入正确的手机号");
                }
            } else {
                CustomToast.showFailToast("请输入手机号");
            }

            if(mIsGetCodePage){
                new UserTracking("强制绑定手机号","button").setItemId("获取验证码")
                        .statIting(XDCSCollectUtil.APP_NAME_EVENT,XDCSCollectUtil.SERVICE_PAGE_CLICK);
            }
        } else if (id == R.id.login_region_number) {
            ChooseCountryFragment fragment = new ChooseCountryFragment();
            fragment.setIChooseCountryListener(GetAndVerifySmsCodeFragment.this);
            startFragment(fragment);
        } else if (id == R.id.login_iv_clear_accout) {
            if (mIphoneEditText != null)
                mIphoneEditText.setText(null);
        } else if (id == R.id.login_tv_get_voice_code) {
            if (OneClickHelper.getInstance().onLongTimeGapClick(v)) {
                if (StringUtil.verifyGlobalPhone(mCountryCode, mMobileStr)) {
                    LoginHelper.sendVoiceSmsCode(getmActivity() ,
                            getCountryCodePhoneNum(mCountryCode ,mMobileStr) ,
                            isLoginByEmail
                                ? LoginRequest.SEND_SMS_TYPE_FOR_EMAIL_BIND_PHONE
                                : LoginRequest.SEND_SMS_TYPE_FOR_PHONE_BIND_PHONE);
                } else {
                    CustomToast.showFailToast("用户手机号输入有误");
                }
            } else {
                CustomToast.showFailToast(R.string.login_click_voice_code_too_fast);
            }
        } else if (id == R.id.login_timing) {
            handleGetCode();
        } else if(id == R.id.login_login_hint_state) {
            new XMTraceApi.Trace()
                    .click(32328)
                    .put("status", mLoginHint.isSelected() ? "勾选" : "未勾选")
                    .put("currPage", "forcedBindingPhoneNum")
                    .createTrace();

            mLoginHint.setSelected(!mLoginHint.isSelected());
        }
    }

    class PhoneNumberWatcher implements TextWatcher {

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }

        @Override
        public void afterTextChanged(Editable s) {
            if (s != null && !TextUtils.isEmpty(s.toString())) {
                if (mClearAccout != null) {
                    mClearAccout.setVisibility(View.VISIBLE);
                }
            } else {
                if (mClearAccout != null) {
                    mClearAccout.setVisibility(View.INVISIBLE);
                }
            }

            if (s == null) return;
            boolean enable = (mIsGetCodePage && !TextUtils.isEmpty(s.toString()))
                    || (!mIsGetCodePage && StringUtil.verifySmsCode(s.toString()));
            mGetVCButton.setEnabled(enable);
        }
    }
}
