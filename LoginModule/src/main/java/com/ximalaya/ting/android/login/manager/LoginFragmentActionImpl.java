package com.ximalaya.ting.android.login.manager;

import android.os.Bundle;

import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.host.activity.login.IChooseCountryListener;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleException;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.login.ILoginFragmentAction;
import com.ximalaya.ting.android.login.fragment.BindFragment;
import com.ximalaya.ting.android.login.fragment.ChangeAccountFragment;
import com.ximalaya.ting.android.login.fragment.ChooseCountryFragment;
import com.ximalaya.ting.android.login.fragment.FreePasswordFragment;
import com.ximalaya.ting.android.login.fragment.GameOneKeyLoginFragment;
import com.ximalaya.ting.android.login.fragment.GetAndVerifySmsCodeFragment;
import com.ximalaya.ting.android.login.fragment.GuideLoginFragment;
import com.ximalaya.ting.android.login.fragment.ModifyPwdFragment;
import com.ximalaya.ting.android.login.fragment.OneKeyQuickLoginFragment;
import com.ximalaya.ting.android.login.fragment.SmsLoginProxyFragment;
import com.ximalaya.ting.android.login.fragment.SsoAuthorizeFragment;
import com.ximalaya.ting.android.login.fragment.SsoQuickLoginFragment;
import com.ximalaya.ting.android.login.fragment.ThirdLoginTranslucentFragment;
import com.ximalaya.ting.android.login.fragment.multiAccount.BaseMultiAccountWarningDialog;
import com.ximalaya.ting.android.login.fragment.register.RegisterStepThreeFragment;
import com.ximalaya.ting.android.login.util.LoginFragmentUtil;

import androidx.fragment.app.Fragment;

/**
 * Created by le.xin on 2020/3/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class LoginFragmentActionImpl implements ILoginFragmentAction {

    @Override
    public BaseFragment getChooseCoutryFragment(
            IChooseCountryListener chooseCountry) throws BundleException {
        ChooseCountryFragment chooseCountryFragment = new ChooseCountryFragment();
        chooseCountryFragment.setIChooseCountryListener(chooseCountry);
        return chooseCountryFragment;
    }

    @Override
    public BaseFragment2 getLoginFragment(Bundle bundle) throws BundleException {
        return LoginFragmentUtil.getLoginFragment(bundle);
    }

    @Override
    public BaseFragment2 getOneKeyQuickLoginFragment(Bundle bundle) throws BundleException {
        return OneKeyQuickLoginFragment.Companion.newInstance(bundle);
    }

    @Override
    public BaseFragment2 getFreePasswordLoginFragment(Bundle bundle) throws BundleException {
        return FreePasswordFragment.Companion.newInstance(bundle);
    }

    @Override
    public BaseFragment2 getGuideLoginFragment(Bundle bundle) throws BundleException {
        BaseFragment2 guideLoginFragment = new GuideLoginFragment();
        guideLoginFragment.setArguments(bundle);
        return guideLoginFragment;
    }

    @Override
    public BaseFragment2 getSsoAuthorizeFragment(Bundle bundle) throws BundleException {
        return SsoAuthorizeFragment.newInstance(bundle);
    }

    @Override
    public BaseFragment2 getSsoQuickLoginFragment(Bundle bundle) throws BundleException {
        return SsoQuickLoginFragment.newInstance(bundle);
    }

    @Override
    public BaseFragment2 getGameOneKeyLoginFragment(String packId) throws BundleException {
        return GameOneKeyLoginFragment.newInstance(packId);
    }

    @Override
    public BaseFragment2 getGetAndVerifySmsCodeFragment(long uid, String bizKey,
                                                        boolean getCodePage,
                                                        boolean isLoginByEmail,
                                                        boolean isFormOAuth2SDK) throws BundleException {
        return GetAndVerifySmsCodeFragment.newInstanceForLogin(uid, bizKey, getCodePage,
                isLoginByEmail,isFormOAuth2SDK);
    }

    @Override
    public BaseFragment2 getThirdLoginTranslucentFragment(int loginStrategy) {
        ThirdLoginTranslucentFragment thirdLoginTranslucentFragment = new ThirdLoginTranslucentFragment();
        Bundle args = new Bundle();
        args.putInt(ThirdLoginTranslucentFragment.BUNDLE_KEY_LOGINSTRATEGY ,loginStrategy);
        thirdLoginTranslucentFragment.setArguments(args);
        return thirdLoginTranslucentFragment;
    }

    @Override
    public BaseDialogFragment newSmsLoginProxyFragment() {
        return new SmsLoginProxyFragment();
    }

    @Override
    public BaseFragment newModifyPwdFragment() {
        return new ModifyPwdFragment();
    }

    @Override
    public BaseFragment newRegisterStepThreeFragment(Bundle bundle) {
        return RegisterStepThreeFragment.newInstance(bundle);
    }

    @Override
    public BaseFragment newRegisterFragment() {
        // app内已经没有注册入口在使用手机号登录后自动注册
        return LoginFragmentUtil.getLoginFragment(null);
    }

    @Override
    public BaseFragment2 getBindFragment() {
        return new BindFragment();
    }
    @Override
    public Fragment newGetAndVerifySmsCodeFragment(boolean b, int i) {
        return GetAndVerifySmsCodeFragment.newInstance(b, i);
    }

    @Override
    public BaseFragment2 getChangeAccountFragment() {
        return new ChangeAccountFragment();
    }

    @Override
    public BaseDialogFragment getMultiAccountWarningDialogForRecharge(String jsonString, IFragmentFinish callBack) {
        return BaseMultiAccountWarningDialog.Companion.getWarningDialog(jsonString, BaseMultiAccountWarningDialog.TYPE_FOR_RECHARGE, callBack);
    }

    @Override
    public BaseDialogFragment getMultiAccountWarningDialogForVipPurchase(String jsonString, IFragmentFinish callBack) {
        return BaseMultiAccountWarningDialog.Companion.getWarningDialog(jsonString, BaseMultiAccountWarningDialog.TYPE_FOR_VIP_PURCHASE, callBack);
    }
}
