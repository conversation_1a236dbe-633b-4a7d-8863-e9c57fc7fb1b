package com.ximalaya.ting.android.login.model

import android.os.Parcel
import android.os.Parcelable

/**
 * Created by WolfXu on 2023/5/12.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
data class NewUserLoginActivityInfo(
    val coverPath: String? = null
) : Parcelable {
    constructor(parcel: Parcel) : this(parcel.readString()) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(coverPath)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<NewUserLoginActivityInfo> {
        override fun createFromParcel(parcel: Parcel): NewUserLoginActivityInfo {
            return NewUserLoginActivityInfo(parcel)
        }

        override fun newArray(size: Int): Array<NewUserLoginActivityInfo?> {
            return arrayOfNulls(size)
        }
    }
}
