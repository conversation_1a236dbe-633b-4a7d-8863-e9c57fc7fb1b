package com.ximalaya.ting.android.login.fragment

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.ximalaya.ting.android.basequicklogin.IPreVerifyResultCallback
import com.ximalaya.ting.android.basequicklogin.PreVerifyResult
import com.ximalaya.ting.android.basequicklogin.QuickLoginUtil
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.view.image.RoundImageView
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.activity.login.LoginActivity
import com.ximalaya.ting.android.host.constants.LoginByConstants
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog
import com.ximalaya.ting.android.host.manager.account.FreePasswordManager
import com.ximalaya.ting.android.host.manager.account.LoginUtil
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.login.ILoginFragmentAction
import com.ximalaya.ting.android.host.manager.login.LoginHelper
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.host.util.view.TitleBar
import com.ximalaya.ting.android.login.R
import com.ximalaya.ting.android.login.util.LoginFragmentUtil
import com.ximalaya.ting.android.login.view.LoginComplaintPopupWindow
import com.ximalaya.ting.android.login.view.LoginHintChooseRuleDialog
import com.ximalaya.ting.android.loginservice.LoginRequest
import com.ximalaya.ting.android.loginservice.LoginService
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin
import com.ximalaya.ting.android.loginservice.model.LogoutModel
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger

/**
 * Created by nali on 2024/4/19.
 * <AUTHOR>
 */
class FreePasswordFragment : BaseLoginFragment(true, null), View.OnClickListener {

    companion object {
        fun newInstance(bundle: Bundle) : FreePasswordFragment {
            val fragment = FreePasswordFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    private var mLoginExcitationTitle: String? = null
    private lateinit var mLoginTitle : TextView
    private lateinit var mLoginNickName : TextView
    private lateinit var mLoginAuthCover : RoundImageView
    private lateinit var mLoginBtn : TextView
    private lateinit var mRegisterHintState : ImageView
    private lateinit var mRegisterHint : TextView
    private lateinit var mGotoOtherLoginBtn : Button
    private lateinit var mHintLay : ViewGroup

    private var mBindStrategy : Int = 0
    private var mLoginBy : Int = 0
    private var mFormOAuth2SDK : Boolean = false

    private var mVerifyResult : LogoutModel? = null
    private var mProgressDialog : LoadingDialog? = null

    private var mLastLoadVerifyData = 0L

    override fun initUi(savedInstanceState: Bundle?) {
        super.initUi(savedInstanceState)

        parseBundle()

        initView()


    }

    override fun loadData() {
        super.loadData()

        verifyData()
    }

    private fun verifyData() {
        mLastLoadVerifyData = System.currentTimeMillis()

        FreePasswordManager.checkAuthCode(object : IDataCallBack<LogoutModel> {
            override fun onSuccess(result: LogoutModel?) {
                mVerifyResult = result
                dismissLoginProgress()
                mLoginBtn?.isEnabled = true
                bindViewData()
            }

            override fun onError(code: Int, message: String?) {
                dismissLoginProgress()
                verifyError(false)
            }
        })
    }

    private fun bindViewData() {
        mVerifyResult?.let {
            this.mLoginNickName.text = it.nickname
            ImageManager.from(ToolUtil.getCtx()).displayImage(this.mLoginAuthCover, it.avatar, R.drawable.host_default_avatar_132)
        }
    }

    override fun onMyResume() {
        super.onMyResume()

        if (mLastLoadVerifyData != 0L && (System.currentTimeMillis() - mLastLoadVerifyData > 3 * 60 * 1000)) {
            loadData()
        }

        // 免密授信登录页  页面展示
        XMTraceApi.Trace()
            .pageView(62806, "withoutCodeLoginPage") // 页面出现在用户视野时上报一条埋点，包括离开页面后返回、息屏后亮屏等
            .put("currPage", "withoutCodeLoginPage")
            .createTrace()
    }

    override fun onPause() {
        super.onPause()

        // 免密授信登录页  页面离开
        XMTraceApi.Trace()
            .pageExit2(62807) // 页面离开在用户视野时上报一条埋点，包括锁屏、回到主页面等
            .put("currPage", "withoutCodeLoginPage")
            .createTrace()
    }

    private fun verifyError(showToast: Boolean = true) {
        gotoNormalLogin(showToast)
    }

    private fun gotoOtherLoginType() {
        gotoNormalLogin(false)
    }

    private fun gotoNormalLogin(showToast : Boolean = true) {
        if (showToast) {
            CustomToast.showFailToast(R.string.login_free_password_login_fail)
        }

        if (LoginHelper.canGotoOneKeyLoginPage(mLoginBy)) {
            showLoginProgress(activity)
            QuickLoginUtil.getInstance().preload(ToolUtil.getCtx(), object : IPreVerifyResultCallback {
                override fun onSuccess(result: PreVerifyResult?) {
                    dismissLoginProgress()
                    if (result != null) {
                        val tempBundle: Bundle = arguments ?: Bundle()
                        tempBundle.putParcelable(BundleKeyConstants.KEY_ONE_KEY_QUICK_PRE_VERIFY, result)
                        removeFragmentAndAddFragment(getmActivity(), this@FreePasswordFragment, OneKeyQuickLoginFragment.newInstance(tempBundle))

                    } else {
                        removeFragmentAndAddFragment(getmActivity(), this@FreePasswordFragment, LoginFragmentUtil.getLoginFragment(arguments))
                    }
                }

                override fun onFailure(code: Int, message: String) {
                    dismissLoginProgress()
                    removeFragmentAndAddFragment(getmActivity(), this@FreePasswordFragment, LoginFragmentUtil.getLoginFragment(arguments))
                }
            })
        } else {
            removeFragmentAndAddFragment(getmActivity(), this, LoginFragmentUtil.getLoginFragment(arguments))
        }
    }

    override fun onBackPressed(): Boolean {
        startMainActivity()
        return true
    }

    private fun initView() {
        mLoginTitle = findViewById(R.id.login_title)
        mLoginAuthCover = findViewById(R.id.login_av_icon)
        mLoginNickName = findViewById(R.id.login_av_title)
        mLoginBtn = findViewById(R.id.login_login)
        mLoginBtn.isEnabled = false
        mLoginBtn.setOnClickListener(this)
        mRegisterHintState = findViewById(R.id.login_login_hint_state)
        mRegisterHintState.setOnClickListener(this)
        mRegisterHint = findViewById(R.id.login_regiset_hint)
        mGotoOtherLoginBtn = findViewById(R.id.login_goto_other_login_btn)
        mGotoOtherLoginBtn.setOnClickListener(this)

        mHintLay = findViewById(R.id.login_login_hint_layout)

        //手机号账号短信验证码
        mLoginTitle.text = mLoginExcitationTitle

        val spannableStringBuilder = LoginUtil.createLoginSpannableStr(mContext, false, 0)
        mRegisterHint.text = spannableStringBuilder
        mRegisterHint.movementMethod = LinkMovementMethod.getInstance()
        mRegisterHint.highlightColor = Color.TRANSPARENT

        bindViewData()
    }


    override fun getPageLogicName(): String {
        return ILoginFragmentAction.PAGE_LOGIC_LOGIN_QUICK
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.login_fra_free_password_page
    }

    override fun getTitleBarResourceId(): Int {
        return R.id.login_head_layout
    }

    override fun setTitleBar(titleBar: TitleBar?) {
        super.setTitleBar(titleBar)

        titleBar?.title?.visibility = View.GONE

        val tagCancel = "tagCancel"
        val cancelAction = TitleBar.ActionType(
            tagCancel,
            TitleBar.LEFT,
            0,
            0,
            com.ximalaya.ting.android.host.R.color.host_orange,
            TextView::class.java)
        cancelAction.setFontSize(14)
        titleBar?.addAction(cancelAction) {
            if (activity != null && activity is LoginActivity) {
                (activity as LoginActivity).toMainActivity()
            }
        }
        if (titleBar != null) {
            AutoTraceHelper.bindData(titleBar.getActionView(tagCancel), "")
        }

        val helpAction = TitleBar.ActionType(
            "tagHelper", TitleBar.RIGHT, com.ximalaya.ting.android.host.R.string.host_helper, 0,
            com.ximalaya.ting.android.host.R.color.host_color_111111_cfcfcf,
            TextView::class.java
        )
        helpAction.setFontSize(14)
        helpAction.space = 20
        titleBar?.addAction(helpAction, View.OnClickListener {
            val activityTemp = activity
            if (activity == null) {
                return@OnClickListener
            }
            val titleBarReal = getTitleBar()
            if (titleBarReal == null || titleBarReal.getActionView("tagHelper") == null) {
                return@OnClickListener
            }
            try {
                val anchor = titleBarReal.getActionView("tagHelper")
                val location = IntArray(2) // 记录anchor在屏幕中的位置
                anchor.getLocationOnScreen(location)
                val offsetY = location[1] + anchor.height
                val loginComplaintPopupWindow = LoginComplaintPopupWindow(activityTemp)
                loginComplaintPopupWindow.showAtLocation(
                    anchor,
                    Gravity.RIGHT or Gravity.TOP,
                    BaseUtil.dp2px(ToolUtil.getCtx(), 22f),
                    offsetY
                )
            } catch (e: Exception) {
                e.printStackTrace()
            }
        })

        titleBar?.update()

        titleBar?.back?.contentDescription = "返回"
        titleBar?.back?.setOnClickListener {
            startMainActivity()
        }
    }

    private fun parseBundle() {
        mLoginBy = LoginByConstants.LOGIN_BY_DEFUALT

        val arguments = arguments
        if (arguments != null) {
            mLoginBy = arguments.getInt(
                BundleKeyConstants.KEY_LOGIN_BY,
                LoginByConstants.LOGIN_BY_DEFUALT
            )

            mBindStrategy = arguments.getInt(BundleKeyConstants.KEY_ONE_KEY_QUICK_LOGIN_STRATEGY)
            mFormOAuth2SDK = arguments.getBoolean(BundleKeyConstants.KEY_ONE_KEY_QUICK_LOGIN_FORM_OAUTH2SDK)
        }
        mLoginExcitationTitle = LoginByConstants.getHintStrByLoginBy(mLoginBy)
    }

    override fun onClick(v: View?) {
        when(v) {
            mRegisterHintState -> {
                mRegisterHintState.isSelected = !mRegisterHintState.isSelected

                // 免密授信登录页-协议勾选  点击事件
                XMTraceApi.Trace().click(62866) // 用户点击时上报
                    .put("currPage", "withoutCodeLoginPage")
                    .createTrace()
            }

            mGotoOtherLoginBtn -> {
                trace62865(mGotoOtherLoginBtn.text.toString())
                gotoOtherLoginType()
            }

            mLoginBtn -> {
                trace62865(mLoginBtn.text.toString())
                loginBtnClick()
            }
        }
    }

    private fun loginBtnClick() {
        if (!mRegisterHintState.isSelected) {
            if (ToolUtil.activityIsValid(activity)) {
                activity?.let {
                    val loginHintChooseRuleDialog = LoginHintChooseRuleDialog(it)
                    loginHintChooseRuleDialog.setOkHandle {
                        mRegisterHintState.isSelected = true
                        loginBtnClick()
                    }
                    loginHintChooseRuleDialog.show()
                }
            }
            return
        }

        if (mVerifyResult != null) {
            showLoginProgress(mActivity)
            gotoLogin()
        } else {
            showLoginProgress(mActivity)
            verifyData()
        }
    }

    private fun gotoLogin() {
        this.mVerifyResult?.let {
            val map = mapOf<String, String>("authCode" to it.authCode)
            LoginRequest.freePassportLogin(LoginService.getInstance().rquestData, map, object: IDataCallBackUseLogin<LoginInfoModelNew> {
                override fun onSuccess(data: LoginInfoModelNew?) {
                    if (canUpdateUi()) {
                        dismissLoginProgress()
                        if (data != null && data.ret == 0) {
                            handleLoginSuccess(data, false)
                        } else {
                            verifyError()
                        }
                    }
                }

                override fun onError(code: Int, message: String?) {
                    if (canUpdateUi()) {
                        dismissLoginProgress()
                        verifyError()
                    }
                }
            })
        }
    }

    private fun startMainActivity() {
        if (MainApplication.getMainActivity() == null) {
            try {
                val intent = Intent(mActivity, MainActivity::class.java)
                ToolUtil.checkIntentAndStartActivity(mActivity, intent)
            } catch (e: Exception) {
                Logger.e(e)
            }
        }
        finish()
    }

    private fun showLoginProgress(activity: Activity?) {
        if (activity == null || activity.isFinishing)
            return
        if (mProgressDialog == null) {
            mProgressDialog = LoadingDialog(activity);
        } else {
            mProgressDialog?.cancel();
        }

        mProgressDialog?.showIcon(true)
        mProgressDialog?.showBg(true)
        mProgressDialog?.show()
    }

    private fun dismissLoginProgress() {
        mProgressDialog?.dismiss();
        mProgressDialog = null;
    }

    override fun finish() {
        super.finish()
        mActivity?.finish()
        mActivity?.overridePendingTransition(0, 0)
    }

    private fun trace62865(item: String) {
        // 免密授信登录页-按钮  点击事件
        XMTraceApi.Trace().click(62865) // 用户点击时上报
            .put("currPage", "withoutCodeLoginPage")
            .put("Item", item) // 传按钮文案
            .createTrace()
    }

}