package com.ximalaya.ting.android.login.fragment;

import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.model.account.HomePageModel;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.util.HashMap;
import java.util.Map;

/**
 * 手机解绑界面
 * <AUTHOR> on 2017/6/29.
 */
public class UnBindFragment extends BaseFragment2 implements OnClickListener {
    private static final String TAG_UNBIND = "tagUnbind";

    private String mPhoneNumber;
    private boolean isFirstLoad = true;

    @SuppressWarnings("NullAway")
    public UnBindFragment() {
        super(true, null);
    }

    public static UnBindFragment newInstance(String number) {
        Bundle bundle = new Bundle();
        bundle.putString(BundleKeyConstants.KEY_PHONE_NUMBER, number);
        UnBindFragment fragment = new UnBindFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected boolean setNetworkErrorButtonVisiblity() {
        return false;
    }

    @Override
    protected boolean onPrepareNoContentView() {
        return false;
    }

    @Override
    protected void onNoContentButtonClick(View view) {

    }

    @Override
    protected String getPageLogicName() {
        return "unBind";
    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        super.setTitleBar(titleBar);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        if (null != getArguments()) {
            mPhoneNumber = getArguments().getString(
                    BundleKeyConstants.KEY_PHONE_NUMBER);
        }
        setTitle("手机号绑定");
        ((TextView) findViewById(R.id.login_tv_phone_number)).setText("绑定的手机号：" + mPhoneNumber);
        findViewById(R.id.login_btn_change_phone).setOnClickListener(this);
        AutoTraceHelper.bindData(findViewById(R.id.login_btn_change_phone),"");
    }

    @Override
    protected void loadData() {
        if (!UserInfoMannage.hasLogined())
            return;

        Map<String, String> homeParams = new HashMap<String, String>();
        homeParams.put("uid", UserInfoMannage.getUid() + "");
        homeParams.put("device", "android");

        try {
            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().getHomePage(homeParams, new IDataCallBack<HomePageModel>() {
                @Override
                public void onSuccess(@Nullable final HomePageModel object) {
                    if (!canUpdateUi()) {
                        return;
                    }
                    doAfterAnimation(new IHandleOk() {

                        @Override
                        public void onReady() {
                            if (object != null) {
                                mPhoneNumber = object.getMobile();
                                if (TextUtils.isEmpty(mPhoneNumber)) {
                                    mPhoneNumber = object.getPhone();
                                }
                                if (!TextUtils.isEmpty(mPhoneNumber)) {
                                    if (mPhoneNumber.length() > 7) {
                                        mPhoneNumber = StringUtil.getGonePhoneNum(mPhoneNumber);
                                    }
                                    ((TextView) findViewById(R.id.login_tv_phone_number)).setText("绑定的手机号："
                                            + mPhoneNumber + (object.isMobileLoginable() ? "" : "    非登录手机"));
                                } else {
                                    finishFragment();
                                }
                            }
                        }
                    });
                }

                @Override
                public void onError(int code, String message) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.login_fra_unbind;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.login_title_bar;
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.login_btn_change_phone) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().handleITing(getActivity(), Uri.parse
                        ("iting://open?msg_type=94&bundle=account&pageName=binding"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38614;
        super.onMyResume();
        boolean needFinish = !isFirstLoad && (!UserInfoMannage.hasLogined()
                || (UserInfoMannage.getInstance().getUser() != null
                && TextUtils.isEmpty(UserInfoMannage.getInstance().getUser().getMobile())));
        if (needFinish) {
            setFinishCallBackData(true);
            finishFragment();
            return;
        }
        if (isFirstLoad) {
            isFirstLoad = false;
        } else {
            loadData();
        }
    }
}
