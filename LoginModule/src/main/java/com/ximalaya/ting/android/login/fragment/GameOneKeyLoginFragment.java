package com.ximalaya.ting.android.login.fragment;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.View.OnTouchListener;
import android.widget.ImageView;

import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import androidx.annotation.Nullable;



/**
 * <AUTHOR> on 2017/6/29.
 */
public class GameOneKeyLoginFragment extends BaseLoginFragment {

    private ImageView mIvGameBanner;
    private View mRlWeixin;
    private View mRlQQ;
    private View mRlWeibo;
    private View mRlXiaomi;

    public GameOneKeyLoginFragment() {
        super(true, null);
    }

    public static GameOneKeyLoginFragment newInstance(String thirdGameAppPackageName) {
        packName = thirdGameAppPackageName;
        mIsFromOneKeyLogin = true;
        return new GameOneKeyLoginFragment();
    }

    @Override
    protected String getPageLogicName() {
        return "gameOneKeyLogin";
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.login_one_key_login_title;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        SharedPreferencesUtil util = SharedPreferencesUtil.getInstance(mContext);   //退出登录
        util.removeByKey(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT);
        util.removeByKey(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT_NEW);
        util.removeByKey(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_ACCOUNT);
        util.removeByKey(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_PASSWORD);
        MmkvCommonUtil.getInstance(getmActivity()).removeKey(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_ACCOUNT);

        if (UserInfoMannage.hasLogined())
            CommonRequestM.logout();
        UserInfoMannage.getInstance().setUser(null);

        mIsFromOneKeyLogin = true;
        setTitle("一键登录");

        mRlXiaomi = findViewById(R.id.login_xiaomi);
        if (!(DeviceUtil.isMIUI())) {
            mRlXiaomi.setVisibility(View.GONE);
        }

        mRlWeixin = findViewById(R.id.login_rl_weixin);
        mRlWeixin.setOnTouchListener(new OnTouchListener() {

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    mRlWeixin.setBackgroundResource(R.drawable.login_akey_weixin_login);
                } else if (event.getAction() == MotionEvent.ACTION_UP) {
                    mRlWeixin.setBackgroundResource(R.drawable.login_akey_weixin_login2);
                }
                return false;
            }
        });
        mRlWeixin.setOnClickListener(new OnClickListener() {

            public void onClick(View v) {
                doLoginWithWeiXin();
            }
        });
        AutoTraceHelper.bindData(mRlWeixin,"");

        mRlQQ = findViewById(R.id.login_rl_QQ);
        mRlQQ.setOnTouchListener(new OnTouchListener() {

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    mRlQQ.setBackgroundResource(R.drawable.login_akey_qq_login);
                } else if (event.getAction() == MotionEvent.ACTION_UP) {
                    mRlQQ.setBackgroundResource(R.drawable.login_akey_qq_login2);
                }
                return false;
            }
        });
        mRlQQ.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                doLoginWithQQ();
            }
        });
        AutoTraceHelper.bindData(mRlQQ,"");

        mRlWeibo = findViewById(R.id.login_weibo);
        mRlWeibo.setOnTouchListener(new OnTouchListener() {

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    mRlWeibo.setBackgroundResource(R.drawable.login_akey_weibo_login);
                } else if (event.getAction() == MotionEvent.ACTION_UP) {
                    mRlWeibo.setBackgroundResource(R.drawable.login_akey_weibo_login2);
                }
                return false;
            }
        });
        mRlWeibo.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                doLoginWithSina(mActivity, getArguments());
            }
        });
        AutoTraceHelper.bindData(mRlWeibo,"");

        mRlXiaomi.setOnTouchListener(new OnTouchListener() {

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    mRlXiaomi.setBackgroundResource(R.drawable.login_akey_xiaomi_login);
                } else if (event.getAction() == MotionEvent.ACTION_UP) {
                    mRlXiaomi.setBackgroundResource(R.drawable.login_akey_xiaomi_login2);
                }
                return false;
            }
        });

        findViewById(R.id.login_xiaomi).setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                doLoginWithXiaomi();
            }
        });
        AutoTraceHelper.bindData(findViewById(R.id.login_xiaomi),"");

        // 游戏 LOGO 图等比例铺满屏幕宽度
        mIvGameBanner = (ImageView) findViewById(R.id.login_iv_game_banner);
        Bitmap bitmap = BitmapFactory.decodeResource(getResourcesSafe(), R.drawable.login_game_banner);
        int screenWidth = com.ximalaya.ting.android.framework.util.BaseUtil.getScreenWidth(getActivity());
        Bitmap newBitmap = getBitmap(bitmap, screenWidth);
        if (newBitmap != null) {
            Context context = getActivity();
            if (context == null) {
                context = MainApplication.getMyApplicationContext();
            }
            mIvGameBanner.setImageDrawable(new BitmapDrawable(context.getResources(), newBitmap));
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.login_fra_one_key_login;
    }


    @Override
    public void finish() {
        if (null != getActivity())
            getActivity().finish();
    }

    /**
     * 缩放至屏幕宽度
     *
     * @param bitmap
     * @param screenWidth
     * @return
     */
    @Nullable
    private Bitmap getBitmap(Bitmap bitmap, int screenWidth) {
        if (bitmap == null || screenWidth <= 0) {
            return null;
        }
        int bWidth = bitmap.getWidth();
        int bHeight = bitmap.getHeight();
        float scaleWidth = ((float) screenWidth) / bWidth;
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleWidth);
        Bitmap newBitmap = Bitmap.createBitmap(bitmap, 0, 0, bWidth, bHeight, matrix, true);
        if (bitmap != null && !bitmap.equals(newBitmap) && !bitmap.isRecycled()) {
            bitmap.recycle();
        }

        return newBitmap;
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38404;
        super.onMyResume();
    }
}
