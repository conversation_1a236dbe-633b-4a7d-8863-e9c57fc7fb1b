package com.ximalaya.ting.android.login;

import android.content.Context;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.host.manager.bundleframework.listener.IApplication;
import com.ximalaya.ting.android.host.manager.bundleframework.route.RouterConstant;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LoginActionRouter;
import com.ximalaya.ting.android.login.manager.LoginActivityActionImpl;
import com.ximalaya.ting.android.login.manager.LoginFragmentActionImpl;
import com.ximalaya.ting.android.login.manager.LoginFunctionActionImpl;

/**
 * Created by le.xin on 2020/3/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class LoginApplication implements IApplication<LoginActionRouter> {
    private Context mContext;
    @Override
    public void attachBaseContext(Context context) {
        mContext = context;
    }

    @NonNull
    @Override
    public Class<LoginActionRouter> onCreateAction() {
        return LoginActionRouter.class;
    }

    @Override
    public void onCreate(LoginActionRouter actionRouter) {
        actionRouter.addLoginAction(RouterConstant.FUNCTION_ACTION ,new LoginFunctionActionImpl());
        actionRouter.addLoginAction(RouterConstant.ACTIVITY_ACTION ,new LoginActivityActionImpl());
        actionRouter.addLoginAction(RouterConstant.FRAGMENT_ACTION ,new LoginFragmentActionImpl());
    }


    @Override
    public void initApp() {

    }

    @Override
    public void exitApp() {

    }
}
