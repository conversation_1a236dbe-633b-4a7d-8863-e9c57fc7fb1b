package com.ximalaya.ting.android.login.fragment;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.LinkMovementMethod;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.activity.login.ChooseCountryActivity;
import com.ximalaya.ting.android.host.activity.login.IChooseCountryListener;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.manager.account.LoginConfig;
import com.ximalaya.ting.android.host.manager.account.LoginDeferredActionHelper;
import com.ximalaya.ting.android.host.manager.account.LoginUtil;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.model.account.InternationalCodeModel;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.login.view.LoginComplaintPopupWindow;
import com.ximalaya.ting.android.login.view.LoginHintChooseRuleDialog;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.loginservice.verify.VerifyManager;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import java.io.Serializable;
import java.lang.ref.WeakReference;

import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import static com.ximalaya.ting.android.host.constants.LoginByConstants.LOGIN_BY_COMMENT;
import static com.ximalaya.ting.android.host.constants.LoginByConstants.LOGIN_BY_DEFUALT;
import static com.ximalaya.ting.android.host.constants.LoginByConstants.LOGIN_BY_DOWNLOAD_MORE;
import static com.ximalaya.ting.android.host.constants.LoginByConstants.LOGIN_BY_LISTEN_MORE;
import static com.ximalaya.ting.android.host.constants.LoginByConstants.LOGIN_BY_SUB_OVER_10;
import static com.ximalaya.ting.android.host.constants.LoginByConstants.LOGIN_BY_SUB_OVER_4;

/**
 * Created by nali on 2018/5/3.
 * 短信登录fragment
 *
 * <AUTHOR>
 */
public class SmsLoginFragment extends BaseLoginFragment implements View.OnClickListener, IChooseCountryListener {
    public static final int REQUEST_CODE = 1002;
    private TextView mTitle, mRegionNum;
    private EditText mPhoneNum;
    private Button mLoginBtn;

    private int loginBy;

    private ImageView mLoginHint;
    private TextView mRegisterHint;
    private TextView mOtherLoginBtn;
    private ImageView mClearAccout;
    private TextView mHelperBtn;
    private boolean isSelectd;

    private View mHintCheckLayout;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.login_sms_login_layout, null);

        mTitle = view.findViewById(R.id.login_sms_login_title);
        // 设置标题
        setTitleByLoginBy();
        mRegionNum = view.findViewById(R.id.login_region_number);
        mRegionNum.setOnClickListener(this);
        mPhoneNum = view.findViewById(R.id.login_username);
        mLoginBtn = view.findViewById(R.id.login_login);
        mHintCheckLayout = view.findViewById(R.id.login_login_hint_layout);
        mLoginBtn.setOnClickListener(this);
        LoginUtil.removePhoneNumSpace(mPhoneNum);
        mPhoneNum.addTextChangedListener(mTextWatcher);
        mPhoneNum.requestFocus();
        mOtherLoginBtn = view.findViewById(R.id.main_other_login_btn);
        mOtherLoginBtn.setOnClickListener(this);
        AutoTraceHelper.bindData(mOtherLoginBtn, null);
        mClearAccout = view.findViewById(R.id.login_iv_clear_accout);
        mClearAccout.setOnClickListener(this);
        AutoTraceHelper.bindData(mClearAccout, null);

        mHelperBtn = view.findViewById(R.id.login_sms_help_btn);
        mHelperBtn.setOnClickListener(this);
        AutoTraceHelper.bindData(mHelperBtn, null);

        int loginWay = SharedPreferencesUtil.getInstance(getActivity()).getInt(PreferenceConstantsInHost.TIMGMAIN_KEY_SHARED_PRE_LOGIN_WAY, AppConstants.LOGIN_FLAG_XIMALAYA);//登录方式,
        String countryCode = SharedPreferencesUtil.getInstance(getActivity()).getString(PreferenceConstantsInOpenSdk.TINGMAIN_KEY_SHARED_PRE_COUNTRY_CODE);
        if (!TextUtils.isEmpty(countryCode)) {
            mCountryCode = countryCode;
        }

        String recordPhoneNum = null;
        if (loginWay == AppConstants.LOGIN_FLAG_XIMALAYA) {
            //有可能是 "手机账号和密码" 或 "邮箱账号和密码" 或 "未登录过"
            String account = MmkvCommonUtil.getInstance(getActivity()).
                    getStringFromEncryptStr(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_ACCOUNT);
            if(!TextUtils.isEmpty(account)) {
                if(StringUtil.verifyGlobalPhone(mCountryCode, account)) {
                    recordPhoneNum = account;
                }
            }

        } else if (loginWay == AppConstants.LOGIN_FLAG_PHONE) {
            recordPhoneNum = MmkvCommonUtil.getInstance(getActivity()).
                    getStringFromEncryptStr(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_ACCOUNT);
        }

        mPhoneNum.setText(recordPhoneNum);
        if(!TextUtils.isEmpty(recordPhoneNum)) {
            mPhoneNum.setSelection(recordPhoneNum.length());
        }

        mLoginHint = view.findViewById(R.id.login_login_hint_state);
        mLoginHint.setOnClickListener(this);
        mLoginHint.setSelected(isSelectd);
        mRegisterHint = view.findViewById(R.id.login_regiset_hint);

        mRegisterHint.setText(LoginUtil.createLoginSpannableStr(getContext(), true, 0));
        mRegisterHint.setMovementMethod(LinkMovementMethod.getInstance());
        mRegisterHint.setHighlightColor(Color.TRANSPARENT);
        mRegisterHint.setOnClickListener(this);
        view.findViewById(R.id.login_login_hint_layout).setOnClickListener(this);

        new XMTraceApi.Trace()
                .setMetaId(32306)
                .setServiceId("dialogView")
                .put("dialogTitle", getPageTitle())
                .createTrace();

        return view;
    }

    private void setTitleByLoginBy() {
        if (mTitle == null) {
            return;
        }

        loginBy = LOGIN_BY_DEFUALT;
        if (getArguments() != null) {
            loginBy = getArguments().getInt(BundleKeyConstants.KEY_LOGIN_BY, LOGIN_BY_DEFUALT);
        }

        new UserTracking()
                .setModuleType("半屏登录页弹窗")
                .setSrcPage(getCurrSrcPage(loginBy))
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);

        mTitle.setText(LoginByConstants.getHintStrByLoginBy(loginBy));
    }

    private String getCurrSrcPage(@LoginByConstants.LoginBy int logBy) {
        if (logBy == LOGIN_BY_SUB_OVER_10) {
            return "订阅第11个";
        } else if (logBy == LOGIN_BY_SUB_OVER_4) {
            return "订阅第4个";
        } else if (logBy == LOGIN_BY_LISTEN_MORE) {
            return "播放前引导";
        } else if (logBy == LOGIN_BY_DOWNLOAD_MORE) {
            return "下载第11个";
        } else if (loginBy == LOGIN_BY_COMMENT) {
            return "评论";
        } else if(logBy == LoginByConstants.LOGIN_BY_SUB) {
            return "订阅";
        } else {
            return "其他";
        }
    }

    @Override
    protected String getPageLogicName() {
        return "smsLogin";
    }

    @Override
    public void onDestroyView() {
        if (mPhoneNum != null) {
            mPhoneNum.removeTextChangedListener(mTextWatcher);
        }
        super.onDestroyView();
    }

    private TextWatcher mTextWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }

        @Override
        public void afterTextChanged(Editable s) {
            if (s != null && !TextUtils.isEmpty(s.toString())) {
                if (mLoginBtn != null) {
                    mLoginBtn.setEnabled(true);
                }

                if (mClearAccout != null) {
                    mClearAccout.setVisibility(View.VISIBLE);
                }
            } else {
                if (mLoginBtn != null) {
                    mLoginBtn.setEnabled(false);
                }

                if (mClearAccout != null) {
                    mClearAccout.setVisibility(View.GONE);
                }
            }
        }
    };

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.login_region_number) {
            startActivityForResult(new Intent(getActivity(), ChooseCountryActivity.class), REQUEST_CODE);
            return;
        }

        if(id == R.id.login_login) {
            if(!mLoginHint.isSelected()) {

                FragmentActivity activity = getActivity();
                if(ToolUtil.activityIsValid(activity)) {
                    LoginHintChooseRuleDialog loginHintChooseRuleDialog =
                            new LoginHintChooseRuleDialog(getActivity());
                    final View tempView = v;
                    loginHintChooseRuleDialog.setOkHandle(new IHandleOk() {
                        @Override
                        public void onReady() {
                            mLoginHint.setSelected(true);
                            onClick(tempView);
                        }
                    });
                    loginHintChooseRuleDialog.show();
                }
                return;
            }
        }

        if (id == R.id.login_login) {
            if (mPhoneNum != null) {
                Editable text = mPhoneNum.getText();

                if (text != null) {
                    final String phoneNumber = text.toString();
                    if (StringUtil.verifyGlobalPhone(mCountryCode, phoneNumber)) {
                        new UserTracking()
                                .setSrcModule("半屏登录页弹窗")
                                .setItem("button")
                                .setItemId("登录")
                                .setSrcPage(getCurrSrcPage(loginBy))
                                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);

                        String buttonTitle = "登录";

                        if (mLoginBtn != null && mLoginBtn.getText() != null) {
                            buttonTitle = mLoginBtn.getText().toString();
                        }

                        new XMTraceApi.Trace()
                                .setMetaId(32310)
                                .setServiceId("dialogClick")
                                .put("item", buttonTitle)
                                .put("dialogTitle", getPageTitle())
                                .createTrace();

                        getPhoneCheckCode(LoginRequest.SEND_SMS_TYPE_FOR_LOGIN ,
                                phoneNumber,
                                mCountryCode,
                                new WeakReference<BaseLoginFragment>(this),
                                new IHandleOk() {
                            @Override
                            public void onReady() {
                                if(!canUpdateUi()) {
                                    return;
                                }

                                // 点击登录到验证码接收页面
                                Bundle bundle = new Bundle();
                                bundle.putString(BundleKeyConstants.KEY_PHONE_NUMBER, phoneNumber);
                                bundle.putString("countryCode", mCountryCode);
                                bundle.putBoolean(BundleKeyConstants.KEY_IS_FULL_LOGIN, false);
                                bundle.putString("dialogTitle", getPageTitle());
                                HalfScreenSmsVerificationCodeFragment smsVerificationCodeFragment =
                                        HalfScreenSmsVerificationCodeFragment.newInstance(bundle);
                                try {
                                    FragmentManager fragmentManager = null;
                                    if(getParentFragment() != null) {
                                        fragmentManager = getParentFragment().getChildFragmentManager();
                                    }
                                    if(fragmentManager == null) {
                                        fragmentManager = getFragmentManager();
                                    }

                                    if(fragmentManager != null) {
                                        fragmentManager.beginTransaction().replace(R.id.login_sms_login_proxy_fra,
                                                smsVerificationCodeFragment).addToBackStack(null).commitAllowingStateLoss();
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        });
                    } else {
//                        if (mActivity != null) {
//                            new DialogBuilder(mActivity).setMessage("请输入正确的手机号码").showWarning();
//                        }
                        CustomToast.showToast("手机号格式错误");
                    }
                }
            }
        } else if(id == R.id.login_login_hint_state) {
            new XMTraceApi.Trace()
                    .setMetaId(32309)
                    .setServiceId("dialogClick")
                    .put("status", mLoginHint.isSelected() ? "勾选" : "未勾选")
                    .put("dialogTitle", getPageTitle())
                    .createTrace();
            mLoginHint.setSelected(!mLoginHint.isSelected());
        } else if(id == R.id.main_other_login_btn) {
            if (getActivity() != null && !getActivity().isFinishing()) {
                LoginConfig loginConfig = new LoginConfig();
                loginConfig.useOneKeyLogin = false;
                Intent intent = getActivity().getIntent();

                if(intent != null) {
                    loginConfig.bundle = intent.getExtras();
                }

                new XMTraceApi.Trace()
                        .setMetaId(32311)
                        .setServiceId("dialogClick")
                        .put("dialogTitle", getPageTitle())
                        .createTrace();
                LoginDeferredActionHelper.getInstance().setKeepCacheOnce(true);
                UserInfoMannage.gotoLogin(getActivity(), LoginByConstants.LOGIN_BY_FULL_SCREEN ,
                        loginConfig);
            }
        } else if(id == R.id.login_iv_clear_accout) {
            mPhoneNum.setText("");
        } else if(id == R.id.login_sms_help_btn) {
            if (getActivity() == null) {
                return;
            }

            try {
                int[] location = new int[2]; // 记录anchor在屏幕中的位置
                mHelperBtn.getLocationOnScreen(location);
                int offsetY = location[1] + mHelperBtn.getHeight() - BaseUtil.getStatusBarHeight(ToolUtil.getCtx());
                LoginComplaintPopupWindow loginComplaintPopupWindow = new LoginComplaintPopupWindow(getActivity());
                loginComplaintPopupWindow.showAtLocation(mHelperBtn, Gravity.RIGHT | Gravity.TOP,
                        BaseUtil.dp2px(ToolUtil.getCtx(), 16), offsetY);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            if (data != null) {
                Serializable extra = data.getSerializableExtra(BundleKeyConstants.KEY_CODE_MODEL);
                if (extra instanceof InternationalCodeModel) {
                    mCountryCode = ((InternationalCodeModel) extra).countryCode;
                    if (mRegionNum != null) {
                        mRegionNum.setText("+" + ((InternationalCodeModel) extra).countryCode);
                    }
                }
            }
            return;
        }

        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onCountryChosenListener(InternationalCodeModel model) {
        if (model != null && mRegionNum != null) {
            mCountryCode = model.countryCode;
            mRegionNum.setText("+" + model.countryCode);
        }
    }

    private String getPageTitle() {
        return mTitle != null && mTitle.getText() != null ?
                mTitle.getText().toString() : "登录";
    }

    @Override
    protected void loadData() {
        super.loadData();

        VerifyManager.preloadGTCaptchClient(getmActivity());
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        VerifyManager.destroyGTCaptchClient();
    }
}
