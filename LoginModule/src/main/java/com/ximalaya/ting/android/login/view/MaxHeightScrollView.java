package com.ximalaya.ting.android.login.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.widget.ScrollView;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.login.R;

/**
 * Created by zhangkaikai on 12/4/20.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15721173906
 */
public class MaxHeightScrollView extends ScrollView {

    private final int mMaxHeight;

    public MaxHeightScrollView(Context context) {
        this(context, null);
    }

    public MaxHeightScrollView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MaxHeightScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.login_MaxHeightScrollView);
        mMaxHeight = typedArray.getDimensionPixelSize(R.styleable.login_MaxHeightScrollView_login_maxHeight,
            BaseUtil.dp2px(context, 200.0f));
        typedArray.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, MeasureSpec.makeMeasureSpec(mMaxHeight, MeasureSpec.AT_MOST));
    }
}
