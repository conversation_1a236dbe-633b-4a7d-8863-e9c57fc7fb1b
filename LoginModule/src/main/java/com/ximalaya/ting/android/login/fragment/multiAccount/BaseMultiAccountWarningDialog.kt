package com.ximalaya.ting.android.login.fragment.multiAccount

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.*
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.other.BaseLoadDialogFragment
import com.ximalaya.ting.android.host.listener.IFragmentFinish
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.login.R
import com.ximalaya.ting.android.login.fragment.ChangeAccountFragment
import com.ximalaya.ting.android.login.model.MultiAccountInfoForDialog
import java.lang.ref.WeakReference

abstract class BaseMultiAccountWarningDialog(protected val data: MultiAccountInfoForDialog)
    : BaseLoadDialogFragment(), View.OnClickListener {
    companion object {
        const val TYPE_FOR_RECHARGE = MultiAccountInfoForDialog.TYPE_FOR_RECHARGE
        const val TYPE_FOR_VIP_PURCHASE = MultiAccountInfoForDialog.TYPE_FOR_VIP_PURCHASE

        const val CODE_CONTINUE_OPERATION: Int = 200

        private const val TAG: String = "MultiAccountWarningDialog"

        private var dialogReference: WeakReference<BaseMultiAccountWarningDialog>? = null
        fun getWarningDialog(dataJsonString: String?, type: Int, callBack: IFragmentFinish?): BaseMultiAccountWarningDialog? {
            dataJsonString?: return null
            val data: MultiAccountInfoForDialog = MultiAccountInfoForDialog.parse(dataJsonString, type)?: return null
            val dialog: BaseMultiAccountWarningDialog? = getDialog(dataJsonString, data, type)
            dialogReference?.get()?.let { lastOne ->
                lastOne.dismiss()
            }
            if (null != dialog) {
                dialog.callBack = callBack
                dialogReference = WeakReference(dialog)
                return dialog
            }
            return null
        }

        private fun getDialog(dataJsonString: String, data: MultiAccountInfoForDialog, type: Int): BaseMultiAccountWarningDialog? {
            return when (type) {
                TYPE_FOR_RECHARGE ->
                    MultiAccountWarningDialogForRecharge.getDialog(data)
                TYPE_FOR_VIP_PURCHASE ->
                    MultiAccountWarningDialogForVipPurchase.getDialog(data)
                else -> null
            }
        }
    }

    init {
        parentNeedBg = false
    }

    protected var resultCode: Int = 0
    protected var callBack: IFragmentFinish? = null;

    private var titleTv: TextView? = null
    private var contentRv: RecyclerView? = null
    private var btn1: TextView? = null
    private var btn2: TextView? = null

    override fun initUi(view: View?, savedInstanceState: Bundle?) {
        titleTv = view?.findViewById(R.id.login_multi_account_title)
        contentRv = view?.findViewById(R.id.login_multi_account_content)
        contentRv?.layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
        contentRv?.adapter = getAdapter()
        getItemDecoration()?.let { decoration ->
            contentRv?.addItemDecoration(decoration)
        }
        btn1 = view?.findViewById(R.id.login_multi_account_btn_1)
        btn2 = view?.findViewById(R.id.login_multi_account_btn_2)

        ViewStatusUtil.setOnClickListener(btn1, this)
        ViewStatusUtil.setOnClickListener(btn2, this)
    }

    override fun loadData() {
        // do Nothing
    }

    private fun goToLoginSelectFragment() {
        BaseApplication.getMainActivity()?.let { activity ->
            (activity as? MainActivity)?.startFragment(ChangeAccountFragment())
        }
    }

    abstract fun getAdapter(): MultiAccountWarningAdapter

    abstract fun getItemDecoration(): RecyclerView.ItemDecoration?

    abstract fun doOnBtn1Clicked(view: View?, tag: Any?, tag2: Any?): Boolean

    protected open fun doOnBtn2Clicked(view: View?, tag: Any?, tag2: Any?): Boolean {
        goToLoginSelectFragment()
        dismiss()
        return true
    }

    override fun onClick(v: View?) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return
        }
        when(v?.id?: 0) {
            R.id.login_multi_account_btn_1 -> {
                doOnBtn1Clicked(v, null, null)
            }
            R.id.login_multi_account_btn_2 -> {
                doOnBtn2Clicked(v, null, null)
            }
        }
    }

    override fun onStart() {
        super.onStart()
        setFromBottomLayoutParams(this)
    }

    private fun setFromBottomLayoutParams(dialog: BaseLoadDialogFragment?) {
        dialog?.setStyle(DialogFragment.STYLE_NO_TITLE, com.ximalaya.ting.android.host.R.style.host_share_dialog)
        dialog?.dialog?.window?.let {
            // it.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            it.decorView?.setPadding(0, 0, 0, 0)
            val params: WindowManager.LayoutParams? = it.attributes
            params?.gravity = Gravity.BOTTOM
            params?.width = WindowManager.LayoutParams.MATCH_PARENT
            it.setWindowAnimations(com.ximalaya.ting.android.host.R.style.host_popup_window_from_bottom_animation)
            it.attributes = params
        }
    }

    override fun dismiss() {
        super.dismiss()
        callBack?.onFinishCallback(this.javaClass, 0, "{\"resultCode\":${resultCode}}")
    }

    abstract class MultiAccountWarningAdapter(private val data: MultiAccountInfoForDialog): RecyclerView.Adapter<MultiAccountWarningViewHolder>() {
        private var currentIndex: Int = 0

        override fun onBindViewHolder(holder: MultiAccountWarningViewHolder, position: Int) {
            val isCurrent: Boolean = position == currentIndex
            ViewStatusUtil.setVisible(if (isCurrent) View.VISIBLE else View.GONE, holder.mask)
            val infos: List<MultiAccountInfoForDialog.AccountInfo?> = data.accountInfos
            if (infos.size > position) {
                val info: MultiAccountInfoForDialog.AccountInfo = infos.get(position)?: return
                ImageManager.from(BaseApplication.getMyApplicationContext()).displayImage(holder.cover, info.avatar, -1)
                if (StringUtil.isEmpty(info.nickname)) {
                    ViewStatusUtil.setVisible(View.GONE, holder.name)
                } else {
                    ViewStatusUtil.setVisible(View.VISIBLE, holder.name)
                    ViewStatusUtil.setText(holder.name, info.nickname)
                }

                if (StringUtil.isEmpty(info.icon)) {
                    ViewStatusUtil.setVisible(View.GONE, holder.icon)
                } else {
                    ViewStatusUtil.setVisible(View.VISIBLE, holder.icon)
                    ImageManager.from(BaseApplication.getMyApplicationContext()).displayImage(holder.icon, info.icon, -1)
                }

                if (StringUtil.isEmpty(info.access)) {
                    ViewStatusUtil.setVisible(View.GONE, holder.access)
                } else {
                    ViewStatusUtil.setVisible(View.VISIBLE, holder.access)
                    ViewStatusUtil.setText(holder.access, "登录方式：${info.access}登录")
                }

                if (StringUtil.isEmpty(info.registerAt)) {
                    ViewStatusUtil.setVisible(View.GONE, holder.time)
                } else {
                    ViewStatusUtil.setVisible(View.VISIBLE, holder.time)
                    ViewStatusUtil.setText(holder.time, "注册时间：${info.registerAt}")
                }

                ViewStatusUtil.setVisible(View.GONE, holder.label, holder.mask)

                customBindViewHolder(info, holder, position)
            }
        }

        protected abstract fun customBindViewHolder(info: MultiAccountInfoForDialog.AccountInfo, holder: MultiAccountWarningViewHolder, position: Int)

        override fun getItemCount(): Int {
            return data.accountInfos.size
        }
    }

    open class MultiAccountWarningViewHolder constructor(wholeView: View): RecyclerView.ViewHolder(wholeView) {
        val mask: View? = itemView.findViewById(R.id.login_item_multi_account_mask)
        val cover: ImageView? = itemView.findViewById(R.id.login_item_multi_account_cover)
        val label: TextView? = itemView.findViewById(R.id.login_item_multi_account_label)
        val name: TextView? = itemView.findViewById(R.id.login_item_multi_account_name)
        val icon: ImageView? = itemView.findViewById(R.id.login_item_multi_account_icon)
        val divider: View? = itemView.findViewById(R.id.login_item_multi_account_divider)
        val access: TextView? = itemView.findViewById(R.id.login_item_multi_account_access)
        val time: TextView? = itemView.findViewById(R.id.login_item_multi_account_time)
    }
}