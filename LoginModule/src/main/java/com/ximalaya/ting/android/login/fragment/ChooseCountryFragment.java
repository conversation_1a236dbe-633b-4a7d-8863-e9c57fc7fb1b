package com.ximalaya.ting.android.login.fragment;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.github.promeg.pinyinhelper.Pinyin;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.activity.login.ChooseCountryActivity;
import com.ximalaya.ting.android.host.activity.login.IChooseCountryListener;
import com.ximalaya.ting.android.host.activity.login.LoginActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.login.ILoginFragmentAction;
import com.ximalaya.ting.android.host.model.account.InternationalCodeModel;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.view.bar.indexsidebar.BaseIndexBarDataHelper;
import com.ximalaya.ting.android.host.view.bar.indexsidebar.BasePinYinData;
import com.ximalaya.ting.android.host.view.bar.indexsidebar.IndexSideBar;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR> on 2017/4/24.
 */

public class ChooseCountryFragment extends BaseFragment2 implements AdapterView.OnItemClickListener, View.OnClickListener {

    private List<InternationalCodeModel> constantModels;
    private ListView mListView;
    private IndexSideBar mIndexSideBar;
    private TextView mTvShowIndex;
    private List<InternationalCodeModel> multiListData = new ArrayList<>();//复合国家信息,有常用国家
    private List<InternationalCodeModel> listData = new ArrayList<>();//基本国家信息
    private List<InternationalCodeModel> searchListData = new ArrayList<>();//搜索国家信息
    private ImageView backBtn;
    private EditText searchCountryEt;
    private TextView searchCountryBtn;
    private TextView title;
    private ImageView searchBtn;
//    private View borderView;
    private boolean isFirstPage = true;
    private MyAdapter adapter;
    private InputMethodManager inputManager;
    private IChooseCountryListener listener;

    @SuppressWarnings("NullAway")
    public ChooseCountryFragment() {
        super(AppConstants.isPageCanSlide, SlideView.TYPE_RELATIVELAYOUT, null);
    }

    {
        constantModels = new ArrayList<InternationalCodeModel>() {{
            add(new InternationalCodeModel("中国", "86", "常用"));
            add(new InternationalCodeModel("中国澳门", "853", "常用"));
            add(new InternationalCodeModel("中国台湾", "886", "常用"));
            add(new InternationalCodeModel("中国香港", "852", "常用"));
        }};
    }

    @Override
    protected String getPageLogicName() {
        return ILoginFragmentAction.PAGE_LOGIC_CHOOSE_COUNTRY;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        backBtn = (ImageView) findViewById(R.id.login_back_btn);
        searchCountryBtn = (TextView) findViewById(R.id.login_tv_search);
        searchCountryEt = (EditText) findViewById(R.id.login_et_search_input);
        title = (TextView) findViewById(R.id.login_choose_country_title);
        searchBtn = (ImageView) findViewById(R.id.login_iv_search);
        mListView = (ListView) findViewById(R.id.login_list_view);
        mIndexSideBar = (IndexSideBar) findViewById(R.id.login_side_bar);
        mTvShowIndex = (TextView) findViewById(R.id.login_tv_show_index);
        mIndexSideBar.setDataHelper(new CountryIndexBarDataHelper());
//        borderView = findViewById(R.id.login_index_border);
        adapter = new MyAdapter(mContext, new ArrayList<InternationalCodeModel>());
        initListener();
    }

    private void initListener() {
        mListView.setOnItemClickListener(this);
        backBtn.setOnClickListener(this);
        searchBtn.setOnClickListener(this);
        searchCountryBtn.setOnClickListener(this);
        AutoTraceHelper.bindData(backBtn,"");
        AutoTraceHelper.bindData(searchBtn,"");
        AutoTraceHelper.bindData(searchCountryBtn,"");
        searchCountryEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s != null)
                    filterData(s.toString());
            }
        });
    }

    @Override
    public void onClick(View v) {
        int actionId = v.getId();
        if (actionId == R.id.login_back_btn) {
            if (isFirstPage) {
                finishFragment();
            } else {
                isFirstPage = true;
                updatePageView();
                loadData();
            }
        } else if (actionId == R.id.login_iv_search) {
            if (isFirstPage) {
                isFirstPage = false;
                updatePageView();
                searchCountryEt.requestFocus();
            }
        } else if (actionId == R.id.login_tv_search) {
            if (!isFirstPage) {
                if (searchCountryEt != null && searchCountryEt.getText() != null) {
//                    filterData(searchCountryEt.getText().toString());
                }
            }
        } else {
        }
    }

    private void updatePageView() {
        searchCountryBtn.setVisibility(isFirstPage ? View.GONE : View.VISIBLE);
        searchCountryEt.setVisibility(isFirstPage ? View.GONE : View.VISIBLE);
        title.setVisibility(isFirstPage ? View.VISIBLE : View.GONE);
        searchBtn.setVisibility(isFirstPage ? View.VISIBLE : View.GONE);
        mIndexSideBar.setVisibility(isFirstPage ? View.VISIBLE : View.GONE);
//        borderView.setVisibility(isFirstPage ? View.VISIBLE : View.GONE);
        adapter.clear();
        if (!isFirstPage) {
            searchCountryEt.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (!isFirstPage) {
                        if (inputManager == null)
                            inputManager = SystemServiceManager.getInputMethodManager(searchCountryEt.getContext());
                        searchCountryEt.requestFocus();
                        inputManager.showSoftInput(searchCountryEt, 0);
                    }
                }
            }, 300);
        } else {
            hideSoftInput();
        }
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    protected void loadData() {
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (canUpdateUi()) {
                    mListView.setVisibility(View.VISIBLE);
                    fetchAndTrimData();
                }
            }
        });
    }

    private void fetchAndTrimData() {
        String[] data = getResourcesSafe().getStringArray(R.array.login_country_list);
        multiListData.clear();
        listData.clear();
        multiListData.addAll(constantModels);
        if (data != null && data.length > 0) {
            for (int i = 0; i < data.length; i++) {
                listData.add(new InternationalCodeModel(data[i]));
            }
            multiListData.addAll(listData);
            adapter.setListData(multiListData);
            mIndexSideBar.setListViewWithIndexBar(mListView);
            mIndexSideBar.setNeedRealIndex(true);
            mIndexSideBar.setSourceDatasAlreadySorted(true);
            mIndexSideBar.setSourceDatas(multiListData);//设置数据
            mIndexSideBar.setmPressedShowTextView(mTvShowIndex);
            mIndexSideBar.invalidate();
            mListView.setAdapter(adapter);
            onPageLoadingCompleted(LoadCompleteType.OK);
        }
    }

    private void filterData(String searchKey) {

        searchListData.clear();
        if (TextUtils.isEmpty(searchKey)) {
            return;
        }
        boolean hasMatch = false;
        for (InternationalCodeModel info : listData) {
            if (info.countryName.contains(searchKey)
                    || info.pinyinContent.contains(searchKey.toUpperCase(Locale.getDefault()))
                    || info.simplePinyin.contains(searchKey.toUpperCase(Locale.getDefault()))) {
                if (!searchListData.contains(info)) {
                    if (info.simplePinyin.startsWith(searchKey.toUpperCase(Locale.getDefault()))) {
                        if (info.simplePinyin.equals(searchKey.toUpperCase(Locale.getDefault()))) {
                            searchListData.add(0, info);
                            hasMatch = true;
                        } else {
                            if (hasMatch && searchListData.size() > 0) {
                                searchListData.add(1, info);
                            } else {
                                searchListData.add(0, info);
                            }
                        }
                    } else {
                        searchListData.add(info);
                    }
                }
            }
        }
        if (searchListData.isEmpty()) {
            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
            mListView.setVisibility(View.GONE);
            adapter.notifyDataSetChanged();
        } else {
            mListView.setVisibility(View.VISIBLE);
            adapter.setListData(searchListData);
            adapter.notifyDataSetChanged();
            onPageLoadingCompleted(LoadCompleteType.OK);
        }

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.login_fra_choose_country;
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        InternationalCodeModel model = null;
        if (isFirstPage) {
            if (multiListData != null && position >= 0 && position < multiListData.size()) {
                model = multiListData.get(position);
            }
        } else {
            if (searchListData != null && position >= 0 && position < searchListData.size()) {
                model = searchListData.get(position);
            }
        }
        if (listener != null && model != null) {
            listener.onCountryChosenListener(model);
            isFirstPage = true;
            finishFragment();
        }
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 45948;
        super.onMyResume();
        hideSoftInput();
    }

    private void hideSoftInput() {
        Activity activity = getActivity();
        if (activity == null || activity.getCurrentFocus() == null)
            return;
        SystemServiceManager.hideSoftInputFromWindow(activity, activity.getCurrentFocus().getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
    }

    @Override
    public boolean onBackPressed() {
        if (isFirstPage) {
            return super.onBackPressed();
        } else {
            isFirstPage = true;
            updatePageView();
            loadData();
            return true;
        }
    }

    public void setIChooseCountryListener(IChooseCountryListener listener) {
        this.listener = listener;
    }

    class MyAdapter extends HolderAdapter<InternationalCodeModel> {
        int borderLeftMargin, borderRightMargin, borderRightMargin2;

        public MyAdapter(Context context, List<InternationalCodeModel> listData) {
            super(context, listData);
            borderLeftMargin = BaseUtil.dp2px(mContext, 15);
            borderRightMargin = BaseUtil.dp2px(mContext, 30);
            borderRightMargin2 = BaseUtil.dp2px(mContext, 60);
        }

        @Override
        public void onClick(View view, InternationalCodeModel s, int position, BaseViewHolder holder) {

        }

        @Override
        public int getConvertViewId() {
            return R.layout.login_item_country;
        }

        @Override
        public BaseViewHolder buildHolder(View convertView) {
            return new ViewHolder(convertView);
        }

        @Override
        public void bindViewDatas(BaseViewHolder holder, InternationalCodeModel model, int position) {
            ViewHolder viewHolder = (ViewHolder) holder;
            viewHolder.borderTop.setVisibility(View.GONE);
            viewHolder.borderBottom.setVisibility(View.GONE);
            viewHolder.indexTop.setVisibility(View.GONE);

            if (isFirstPage) {
                //显示拼音分类的间隔
                if (position > 0) {
                    InternationalCodeModel lastOne = listData.get(position - 1);
                    if (!TextUtils.equals(lastOne.indexTag, model.indexTag)) {
                        viewHolder.borderTop.setVisibility(View.VISIBLE);
                        viewHolder.borderBottom.setVisibility(View.VISIBLE);
                        setBorderMargin(viewHolder.borderBottom, true);
                        viewHolder.indexTop.setText(model.indexTag);
                        viewHolder.indexTop.setVisibility(View.VISIBLE);
                    } else {
                        viewHolder.borderBottom.setVisibility(View.VISIBLE);
                        setBorderMargin(viewHolder.borderBottom, false);
                    }
                } else if (position == 0) {
                    viewHolder.borderTop.setVisibility(View.GONE);
                    viewHolder.borderBottom.setVisibility(View.VISIBLE);
                    setBorderMargin(viewHolder.borderBottom, true);
                    viewHolder.indexTop.setText(model.pinyinContent);
                    viewHolder.indexTop.setVisibility(View.VISIBLE);
                }
            } else {
                viewHolder.borderTop.setVisibility(View.VISIBLE);
            }
            viewHolder.countryName.setText(model.countryName);
            viewHolder.countryNum.setText("+" + model.countryCode);
        }

        void setBorderMargin(View view, boolean top) {
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) view.getLayoutParams();
            layoutParams.setMargins(top ? 0 : borderLeftMargin, 0, borderRightMargin, 0);
            view.setLayoutParams(layoutParams);
        }
    }

    static class ViewHolder extends HolderAdapter.BaseViewHolder {
        public TextView indexTop, countryName, countryNum;
        public View borderTop, borderBottom;

        public ViewHolder(View convertView) {
            indexTop = (TextView) convertView.findViewById(R.id.login_tv_index);
            countryName = (TextView) convertView.findViewById(R.id.login_tv_country_name);
            countryNum = (TextView) convertView.findViewById(R.id.login_tv_country_num);
            borderTop = convertView.findViewById(R.id.login_border_top);
            borderBottom = convertView.findViewById(R.id.login_border_bottom);
        }
    }

    static class CountryIndexBarDataHelper extends BaseIndexBarDataHelper {

        @Override
        protected void convertStrToPing(List<? extends BasePinYinData> data) {
            for (Object o : data) {

                InternationalCodeModel member = (InternationalCodeModel) o;
                String name = member.countryName;

                if (TextUtils.isEmpty(member.pinyinContent)) {
                    StringBuilder pySb = new StringBuilder();
                    StringBuilder spySb = new StringBuilder();

                    for (int i = 0; i < name.length(); i++) {
                        String py = Pinyin.toPinyin(name.charAt(i)).toUpperCase(Locale.getDefault());
                        pySb.append(py);
                        spySb.append(py.charAt(0));
                    }
                    member.pinyinContent = pySb.toString();
                    member.simplePinyin = spySb.toString();

                }

            }
        }

        @Override
        public BaseIndexBarDataHelper fillInexTag(List<? extends BasePinYinData> data) {
            if (data == null || data.size() == 0) {
                return this;
            }

            int size = data.size();
            for (int i = 0; i < size; i++) {
                BasePinYinData bean = data.get(i);
                if (bean.isNeedToPinyin && !TextUtils.isEmpty(bean.pinyinContent)) {
                    char headStr = bean.pinyinContent.charAt(0);
                    if (headStr >= 'A' && headStr <= 'Z') {
                        bean.indexTag = String.valueOf(headStr);
                    } else {
                        bean.indexTag = "★";
                    }
                }
            }
            return this;
        }
    }

    @Override
    protected boolean onPrepareNoContentView() {
        setNoContentImageView(com.ximalaya.ting.android.host.R.drawable.host_no_search_result);
        setNoContentTitle("没有找到相关结果");
        return false;
    }

    @Override
    public void finish() {
        super.finish();
        if(getActivity() instanceof ChooseCountryActivity) {
            getActivity().finish();
        } else if(getActivity() instanceof LoginActivity) {
            ((LoginActivity) getActivity()).removeFragment(this);
        }
    }
}
