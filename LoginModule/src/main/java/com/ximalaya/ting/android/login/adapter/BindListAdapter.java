package com.ximalaya.ting.android.login.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.login.model.SettingInfo;

import java.util.List;

/**
 * Created by xmly on 10/08/2018.
 *
 * <AUTHOR>
 */
public class BindListAdapter extends BaseAdapter {
    private final LayoutInflater layoutInflater;
    private final List<SettingInfo> list;

    public BindListAdapter(Context context, List<SettingInfo> list) {
        layoutInflater = LayoutInflater.from(context);
        this.list = list;
    }

    @Override
    public int getCount() {
        return list.size();
    }

    @Override
    public Object getItem(int arg0) {
        return list.get(arg0);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder mViewHolder;
        if (convertView == null) {
            mViewHolder = new ViewHolder();
            convertView = layoutInflater.inflate(R.layout.login_item_bind_list,
                    parent, false);
            mViewHolder.settingnameTextView = (TextView) convertView
                    .findViewById(R.id.login_setting_name);
            mViewHolder.otherInfo = (TextView) convertView
                    .findViewById(R.id.login_setting_otherinfo);

            mViewHolder.rigthImageView = (ImageView) convertView.findViewById(R.id.login_right);
            mViewHolder.border = convertView.findViewById(R.id.login_bindlist_divider);
            convertView.setTag(mViewHolder);
        } else {
            mViewHolder = (ViewHolder) convertView.getTag();
        }
        SettingInfo info = (SettingInfo) getItem(position);

        if (info.isSetting()) {
            if (info.isExpired()) {
                mViewHolder.otherInfo.setText(R.string.login_bind_expire);
            } else {
                mViewHolder.otherInfo.setText("" + info.getTextWake());
            }

        } else {
            mViewHolder.otherInfo.setText(R.string.login_go_bind);
        }
        mViewHolder.settingnameTextView.setText(info.getNameWake());
        if (position + 1 == list.size()) {
            mViewHolder.border.setVisibility(View.GONE);
        } else {
            mViewHolder.border.setVisibility(View.VISIBLE);
        }
        return convertView;
    }

    private static class ViewHolder {
        ImageView rigthImageView;
        TextView settingnameTextView;
        TextView otherInfo;
        View border;
    }
}
