package com.ximalaya.ting.android.login.fragment;

import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import com.ximalaya.ting.android.encryptservice.EncryptUtil;
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.login.LoginHelper;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.view.SoftInputUtil;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.loginservice.BaseResponse;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.loginservice.LoginService;
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.player.MD5;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.util.HashMap;
import java.util.Map;

import androidx.annotation.Nullable;

/**
 * 设置页面——修改密码页面
 *
 * <AUTHOR>
 */
public class ModifyPwdFragment extends BaseFragment2 implements View.OnClickListener{

    private EditText mEtOldPwd;
    private EditText mEtNewPwd;
    private EditText mEtVerifyPwd;


    @Override
    protected String getPageLogicName() {
        return "修改密码";
    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        super.setTitleBar(titleBar);
        titleBar.getBack().setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                SoftInputUtil.hideSoftInput(ModifyPwdFragment.this);
                finishFragment();
            }
        });
        AutoTraceHelper.bindData(titleBar.getBack(),"");

        TitleBar.ActionType completeAction = new TitleBar.ActionType(
                "tagComplete", TitleBar.RIGHT, R.string.login_complete, 0,
                com.ximalaya.ting.android.host.R.drawable.host_titlebar_send_btn_text_color,
                TextView.class);
        completeAction.setFontSize(16);
        titleBar.addAction(completeAction, new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (TextUtils.isEmpty(mEtOldPwd.getText())
                        || TextUtils.isEmpty(mEtNewPwd.getText())
                        || TextUtils.isEmpty(mEtVerifyPwd.getText())) {
                    CustomToast.showFailToast(mContext.getString(R.string.login_pwd_toast_null));
                    return;
                }
                if (!mEtNewPwd.getText().toString()
                        .equals(mEtVerifyPwd.getText().toString())) {
                    CustomToast.showFailToast("新密码和确认密码输入不一致");
                    return;
                }

                String newPassword = mEtNewPwd.getText().toString();

//                if (!StringUtil.isValidPassword(newPassword)) {
//                    return;
//                }

                SoftInputUtil.hideSoftInput(ModifyPwdFragment.this);

                Map<String, String> params = new HashMap<>();
                params.put("oldPwd" , LoginHelper.encryPsw(mEtOldPwd.getText().toString().trim()));
                params.put("newPwd" ,LoginHelper.encryPsw(mEtNewPwd.getText().toString().trim()));
                LoginRequest.updatePwd(LoginService.getInstance().getRquestData() , params, new IDataCallBackUseLogin<BaseResponse>() {
                    @Override
                    public void onSuccess(@Nullable final BaseResponse object) {
                        doAfterAnimation(new IHandleOk() {

                            @Override
                            public void onReady() {
                                if (object != null && object.getRet() == 0) {
                                    CustomToast.showSuccessToast("修改密码成功");
                                    InputMethodManager imm = SystemServiceManager.getInputMethodManager(mContext);
                                    imm.toggleSoftInput(
                                            InputMethodManager.HIDE_IMPLICIT_ONLY,
                                            0);
                                    finish();
                                } else {
                                    CustomToast.showFailToast("修改密码失败");
                                }
                            }
                        });
                    }

                    @Override
                    public void onError(int code, String message) {
                        CustomToast.showFailToast(TextUtils.isEmpty(message) ? "修改密码失败" : message);
                    }
                });
            }
        });
        AutoTraceHelper.bindData(titleBar.getActionView("tagComplete"),"");
        titleBar.update();
    }

    public ModifyPwdFragment() {
        super(AppConstants.isPageCanSlide, null);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle(R.string.login_change_pwd);

        mEtOldPwd = (EditText) findViewById(R.id.login_et_old_pwd);
        mEtNewPwd = (EditText) findViewById(R.id.login_et_new_pwd);
        mEtVerifyPwd = (EditText) findViewById(R.id.login_et_verify_pwd);
        if (BaseFragmentActivity.sIsDarkMode) {
            mEtOldPwd.setHintTextColor(0xff888888);
            mEtOldPwd.setTextColor(0xffcfcfcf);

            mEtNewPwd.setHintTextColor(0xff888888);
            mEtNewPwd.setTextColor(0xffcfcfcf);

            mEtVerifyPwd.setHintTextColor(0xff888888);
            mEtVerifyPwd.setTextColor(0xffcfcfcf);
        }

        findViewById(R.id.login_tv_forget_password).setOnClickListener(this);
        AutoTraceHelper.bindData(findViewById(R.id.login_tv_forget_password),"");
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if(id == R.id.login_tv_forget_password){
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().handleITing(getActivity(), Uri.parse
                        ("iting://open?msg_type=94&bundle=account&pageName=forgetPassword"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private String getEncrypted(String value) {
        try {
            if (ConstantsOpenSdk.isDebug) {
                Log.i("MainCommonRequest","修改密码ok");
            }

            EncryptUtil mEncryptUtil = EncryptUtil.getInstance(MainApplication.getMyApplicationContext());
            return  mEncryptUtil.encryptByPublicKeyNative(MD5.md5(value));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.login_fra_modify_pwd;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.login_title_bar;
    }

    @Override
    protected boolean setNetworkErrorButtonVisiblity() {
        // TODO Auto-generated method stub
        return false;
    }

    @Override
    protected boolean onPrepareNoContentView() {
        // TODO Auto-generated method stub
        return false;
    }

    @Override
    protected void onNoContentButtonClick(View view) {
        // TODO Auto-generated method stub

    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38511;
        super.onMyResume();
    }
}
