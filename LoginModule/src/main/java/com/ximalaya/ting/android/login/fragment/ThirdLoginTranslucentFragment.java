package com.ximalaya.ting.android.login.fragment;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import com.tencent.connect.common.Constants;
import com.tencent.tauth.IUiListener;
import com.tencent.tauth.Tencent;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.share.QQShareHelper;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.login.manager.LoginManager;
import com.ximalaya.ting.android.loginservice.IHandleRequestCode;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.loginservice.LoginService;
import com.ximalaya.ting.android.loginservice.XMLoginCallBack;
import com.ximalaya.ting.android.loginservice.XmLoginInfo;
import com.ximalaya.ting.android.loginservice.base.ILogin;
import com.ximalaya.ting.android.loginservice.base.ILoginStrategy;
import com.ximalaya.ting.android.loginservice.base.IThirdLoginStrategyFactory;
import com.ximalaya.ting.android.loginservice.base.LoginFailMsg;
import com.ximalaya.ting.android.loginservice.loginstrategy.AbLoginStrategy;
import com.ximalaya.ting.android.loginservice.loginstrategy.MeiZuLogin;
import com.ximalaya.ting.android.loginservice.loginstrategy.QQLogin;
import com.ximalaya.ting.android.loginservice.loginstrategy.SinaWbLogin;
import com.ximalaya.ting.android.loginservice.loginstrategy.WXLogin;
import com.ximalaya.ting.android.loginservice.loginstrategy.XiaoMiLogin;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.util.AsyncGson;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;

import java.lang.ref.WeakReference;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import static com.ximalaya.ting.android.host.activity.login.ThirdLoginTranslucentActivity.ACTION_ON_THIRD_SDK_LOGIN_CANCLE_OR_FAIL;
import static com.ximalaya.ting.android.host.activity.login.ThirdLoginTranslucentActivity.ACTION_ON_THIRD_SDK_LOGIN_SUCCESS;
import static com.ximalaya.ting.android.host.activity.login.ThirdLoginTranslucentActivity.DATA_LOGIN_RESULT;

/**
 * Created by le.xin on 2020/3/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class ThirdLoginTranslucentFragment extends BaseFragment2 {
    public static final String BUNDLE_KEY_LOGINSTRATEGY = "loginStrategy";

    @Override
    public String getPageLogicName() {
        return "ThirdLoginTranslucentFragment";
    }

    private IHandleRequestCode handlerRequestCode;

    private ILoginStrategy iLoginStrategy;

    private int loginStrategy;

    @Override
    protected void initUi(Bundle savedInstanceState) {

        Bundle arguments = getArguments();
        if(arguments != null) {
            loginStrategy = arguments.getInt(BUNDLE_KEY_LOGINSTRATEGY);
        }

        handlerRequestCode = LoginManager.getHandlerRequestCode(getActivity(), new LoginManager.ILoginHandler() {
            @Override
            public int getLoginStrategy() {
                return loginStrategy;
            }

            @Override
            public void onHandlerRequestCode() {

            }
        },false);
        LoginRequest.setHandleRequestCode(new WeakReference<IHandleRequestCode>(handlerRequestCode));

        String itemId = "";
        switch (loginStrategy) {
            case ILogin.LOGIN_FLAG_WEIXIN:
                itemId = "weixin";
                break;
            case ILogin.LOGIN_FLAG_WEIBO:
                itemId = "weibo";
                break;
            case ILogin.LOGIN_FLAG_QQ:
                itemId = "qq";
                break;
            case ILogin.LOGIN_FLAG_XIAOMI:
                itemId = "xiaomi";
                break;
            case ILogin.LOGIN_FLAG_MEIZU:
                itemId = "meizu";
                break;
            default:break;
        }

        new UserTracking()
                .setSrcPage("一键登录页")
                .setID("7390")
                .setItem("button")
                .setItemId(itemId)
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);

        SharedPreferencesUtil.getInstance(getContext()).saveBoolean(
                PreferenceConstantsInHost.TINGMAIN_KEY_LOGIN_FROM_XMLY, false);
        if (LoginService.getInstance() != null) {
            try {
                LoginService.getInstance().loginWithThirdSdk(loginStrategy,
                        new IThirdLoginStrategyFactory() {
                            @Override
                            public AbLoginStrategy getLoginStrategyByType(int type) {
                                AbLoginStrategy abLoginStrategy;
                                switch (type) {
                                    case ILogin.LOGIN_FLAG_MEIZU:
                                        abLoginStrategy = new MeiZuLogin();
                                        break;
                                    case ILogin.LOGIN_FLAG_QQ:
                                        abLoginStrategy = new QQLogin();
                                        break;
                                    case ILogin.LOGIN_FLAG_WEIBO:
                                        abLoginStrategy = new SinaWbLogin();
                                        break;
                                    case ILogin.LOGIN_FLAG_WEIXIN:
                                        abLoginStrategy = new WXLogin();
                                        break;
                                    case ILogin.LOGIN_FLAG_XIAOMI:
                                        abLoginStrategy = new XiaoMiLogin();
                                        break;
                                    default:
                                        if (ConstantsOpenSdk.isDebug) {
                                            throw new RuntimeException("没有可以选择的第三方登录方式");
                                        }

                                        return null;
                                }

                                iLoginStrategy = abLoginStrategy;

                                return abLoginStrategy;
                            }
                        }, getActivity(),
                        new XMLoginCallBack() {
                            private MyProgressDialog loginProgressDialog;
                            private void showLoginProgress(Activity activity) {
                                if (activity == null || activity.isFinishing())
                                    return;
                                if (loginProgressDialog == null) {
                                    loginProgressDialog = new MyProgressDialog(activity);
                                } else {
                                    loginProgressDialog.cancel();
                                }
                                loginProgressDialog.setTitle("登录");
                                loginProgressDialog.setMessage("正在登录...");
                                loginProgressDialog.show();
                            }

                            private void dismissLoginProgress() {
                                if (loginProgressDialog != null) {
                                    loginProgressDialog.dismiss();
                                    loginProgressDialog = null;
                                }
                            }

                            @Override
                            public void onXMLoginSuccess(LoginInfoModelNew loginInfoModelNew, XmLoginInfo xmLoginInfo) {
                                dismissLoginProgress();

                                if(loginInfoModelNew != null && loginInfoModelNew.getRet() == 0) {
                                    new AsyncGson<String>().toJson(loginInfoModelNew, new AsyncGson.IResult<String>() {
                                        @Override
                                        public void postResult(String result) {
                                            Intent intent = new Intent(ACTION_ON_THIRD_SDK_LOGIN_SUCCESS);
                                            intent.putExtra(DATA_LOGIN_RESULT ,result);
                                            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext())
                                                    .sendBroadcast(intent);
                                            finishFragment(true);
                                        }

                                        @Override
                                        public void postException(Exception e) {
                                            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext())
                                                    .sendBroadcast(new Intent(ACTION_ON_THIRD_SDK_LOGIN_CANCLE_OR_FAIL));
                                            finishFragment(true);
                                        }
                                    });
                                }

                            }

                            @Override
                            public void onLoginBegin() {
                                showLoginProgress(MainApplication.getTopActivity());
                            }

                            @Override
                            public void onLoginFailed(LoginFailMsg loginFailMsg) {
                                dismissLoginProgress();

                                LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext())
                                        .sendBroadcast(new Intent(ACTION_ON_THIRD_SDK_LOGIN_CANCLE_OR_FAIL));

                                finishFragment(true);
                            }
                        });
            } catch (Exception exception) {
                exception.printStackTrace();
            }
        }

    }

    @Override
    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {
        return com.ximalaya.ting.android.host.R.layout.host_third_login_translucent_layout;
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if(iLoginStrategy instanceof SinaWbLogin) {
            if (((SinaWbLogin) iLoginStrategy).getSsoHandler() != null) {
                ((SinaWbLogin) iLoginStrategy).getSsoHandler().authorizeCallback(requestCode,
                        resultCode, data);
            }
        }

        if (requestCode == Constants.REQUEST_QQ_SHARE || requestCode == Constants.REQUEST_QZONE_SHARE) {// 分享到qq或者空间时回调需要
            IUiListener qqShareListener = QQShareHelper.getIUiListener();
            if (qqShareListener != null) {
                Tencent.onActivityResultData(requestCode, resultCode, data, qqShareListener);
            }
        } else if (requestCode == Constants.REQUEST_LOGIN) { // 绑定qq时 回调需要
            Tencent.onActivityResultData(requestCode, resultCode, data, null);
        }

    }
}
