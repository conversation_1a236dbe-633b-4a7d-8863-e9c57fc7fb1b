package com.ximalaya.ting.android.login.fragment;

import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.loginservice.BaseResponse;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.loginservice.LoginService;
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import java.util.HashMap;
import java.util.Map;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;

import static com.ximalaya.ting.android.framework.view.SlideView.TYPE_RELATIVELAYOUT;

/**
 * Created by le.xin on 2018/6/7.
 * 校验手机号中间提示页
 * <AUTHOR>
 * @email <EMAIL>
 */
public class VerifyIdentidyFragment extends BaseFragment2 implements View.OnClickListener {

    private EditText mPhoneEdit;
    private Button mButton;
    private TextView mEnableFalseCheck;

    public static final String VERIFY_IDENTIDY_PHONE_NUM = "PHONE_NUM";
    public static final String VERIFY_IDENTIDY_PHONE_NUM_REAL = "PHONE_NUM_REAL";
    public static final String VERIFY_IDENTIDY_FROM = "FROM";
    public static final String VERIFY_IDENTIDY_LOGIN_IS_PSW = "LOGIN_IS_PSW";   // 是否是通过账号密码登录 ( 手机账号密码或者邮箱账号密码)

    public static final int FROM_LOGIN = 1;     // 从登录来的
    public static final int FROM_SET_PSW = 2;   // 从设置密码来的

    private String mPhoneNum;   // 手机号密文
    private String mBizKey;
    private int mCurFrom;
    private boolean mLoginIsPsw;
    private String showPhoneNum;
    private boolean isFormOAuth2SDK;

    private LoadingDialog progressDialog;


    @Override
    public int getTitleBarResourceId() {
        return R.id.login_title_bar;
    }

    public VerifyIdentidyFragment() {
        super(AppConstants.isPageCanSlide, TYPE_RELATIVELAYOUT, null);
    }

    @Override
    protected String getPageLogicName() {
        return "verifyIdentidy";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("验证身份");

        mPhoneEdit = (EditText) findViewById(R.id.login_phone);
        mButton = (Button) findViewById(R.id.login_login);
        mEnableFalseCheck = (TextView) findViewById(R.id.login_enable_false_check);
        mButton.setOnClickListener(this);
        mEnableFalseCheck.setOnClickListener(this);

        if(getArguments() != null) {
            mPhoneNum = getArguments().getString(VERIFY_IDENTIDY_PHONE_NUM_REAL);
            showPhoneNum = getArguments().getString(VERIFY_IDENTIDY_PHONE_NUM);
            mPhoneEdit.setText(showPhoneNum);
            mBizKey = getArguments().getString(BundleKeyConstants.KEY_VERIFY_BIZKEY);
            mCurFrom = getArguments().getInt(VERIFY_IDENTIDY_FROM);
            mLoginIsPsw = getArguments().getBoolean(VERIFY_IDENTIDY_LOGIN_IS_PSW);
            isFormOAuth2SDK = getArguments().getBoolean(BundleKeyConstants.KEY_LOGIN_FROM_OAUTH_SDK, false);
        }

        AutoTraceHelper.bindData(mButton ,"");
        AutoTraceHelper.bindData(mEnableFalseCheck ,"");
    }

    @Override
    public void onMyResume() {
        super.onMyResume();

        new XMTraceApi.Trace()
                .pageView(32331, "riskVerificationPage")
                .put("currPage", "riskVerificationPage")
                .createTrace();
    }

    @Override
    public void onPause() {
        super.onPause();

        new XMTraceApi.Trace()
                .pageExit2(32332)
                .put("currPage", "riskVerificationPage")
                .createTrace();
    }

    @Override
    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.login_verify_identidy_lay;
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.login_login) {
            if(mCurFrom == FROM_LOGIN) {
                if(TextUtils.isEmpty(mPhoneNum)) {
                    return;
                }

                new XMTraceApi.Trace()
                        .click(32336)
                        .put("currPage", "riskVerificationPage")
                        .createTrace();

                if (progressDialog == null) {
                    progressDialog = new LoadingDialog(getActivity());
                } else {
                    progressDialog.cancel();
                }
                progressDialog.setTitle("正在为你获取验证码...");
                progressDialog.showIcon(true);
                progressDialog.showBg(true);
                progressDialog.show();

                Map<String ,String> maps = new HashMap<>();
                maps.put("mobile", mPhoneNum);
                maps.put("sendType" ,"1");
                LoginRequest.sendSms(getActivity() ,
                        mLoginIsPsw
                                ? LoginRequest.SEND_SMS_TYPE_FOR_LOGIN_BY_PSW
                                : LoginRequest.SEND_SMS_TYPE_FOR_THIRD_LOGIN_CHECK ,
                        LoginService.getInstance().getRquestData() ,
                        maps,
                        new IDataCallBackUseLogin<BaseResponse>() {
                    @Override
                    public void onSuccess(@Nullable BaseResponse object) {
                        progressDialog.cancel();
                        if (canUpdateUi() && object != null && object.getRet() == 0) {
                            Bundle bundle = new Bundle();
                            bundle.putString(BundleKeyConstants.KEY_PHONE_NUMBER, mPhoneNum);
                            if(getArguments() != null) {
                                bundle.putString(BundleKeyConstants.KEY_PHONE_NUMBER_FOR_SHOW,
                                        getArguments().getString(VERIFY_IDENTIDY_PHONE_NUM));
                            }
                            bundle.putBoolean(BundleKeyConstants.KEY_IS_FULL_LOGIN ,true);
                            bundle.putBoolean(BundleKeyConstants.KEY_IS_VERIFY_INDENTIDY , true);
                            bundle.putString(BundleKeyConstants.KEY_VERIFY_BIZKEY, mBizKey);
                            bundle.putBoolean(BundleKeyConstants.KEY_VERIFY_LOGIN_IS_PSW, mLoginIsPsw);
                            bundle.putBoolean(BundleKeyConstants.KEY_LOGIN_FROM_OAUTH_SDK, isFormOAuth2SDK);
                            SmsVerificationCodeFragment smsVerificationCodeFragment = SmsVerificationCodeFragment.newInstance(bundle);
                            try {
                                FragmentManager fragmentManager = getChildFragmentManager();
                                if(fragmentManager != null) {
                                    fragmentManager.beginTransaction().add(R.id.login_fragment_container,
                                            smsVerificationCodeFragment).addToBackStack(null).commitAllowingStateLoss();
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        progressDialog.cancel();

                        CustomToast.showFailToast(message);
                    }
                });
            } else if(mCurFrom == FROM_SET_PSW) {

            }
        } else if(i == R.id.login_enable_false_check) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().handleITing(getActivity(), Uri.parse
                        ("iting://open?msg_type=94&bundle=account&pageName=AccountAppeal&loginKey="
                                + mBizKey+"&mobile=" + showPhoneNum));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }
}
