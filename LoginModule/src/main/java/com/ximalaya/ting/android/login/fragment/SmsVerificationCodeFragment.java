package com.ximalaya.ting.android.login.fragment;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog;
import com.ximalaya.ting.android.host.manager.account.LoginDeferredActionHelper;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.login.ILoginFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.login.LoginHelper;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.view.other.SoftKeyBoardWindowListener;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.login.view.gridedittext.GridPasswordView;
import com.ximalaya.ting.android.loginservice.BaseResponse;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.loginservice.LoginService;
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin;
import com.ximalaya.ting.android.loginservice.model.VerifySmsResponse;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * Created by nali on 2018/5/3.
 * 免密登录验证码页面
 *
 * <AUTHOR>
 */
public class SmsVerificationCodeFragment extends BaseLoginFragment implements View.OnClickListener {

    private String mPhoneNumber;
    private String mPhoneNumberForShow;
    private ScheduledExecutorService mService;
    private int mElapsedTime;
    private TextView mTiming;
    private LoadingDialog mProgressDialog;
    private boolean isLoading = false;
    private boolean isGetting = false;
    private String mCountryCode = "86";
    private TextView mGetVoiceCode;
    private TextView mPhoneNumberLabel;
    private TextView mIfPhoneNoLongerWorks;

    private GridPasswordView vPasswordGrid;
    private TextView mRegisterHint;
    private boolean isFullLogin = true; // 是否是满屏登录
    private boolean isVerifyIdentidy = false;    // 是否是验证用户
    private String mBizKey = null; // 验证合法用户时后台在登陆接口返回的值
    private boolean mLoginIsPsw; // 是否是通过账号密码登录
    private boolean isFormOAuth2SDK;
    private SoftKeyBoardWindowListener keyBoardWindowListener;  // 键盘高度监听，为mIfPhoneNoLongerWorks控件服务
    private SoftKeyBoardWindowListener.OnSoftKeyBoardChangeListener realListener = new KeyBoardHeightListener();

    public static SmsVerificationCodeFragment newInstance(Bundle bundle) {
        SmsVerificationCodeFragment fragment = new SmsVerificationCodeFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.login_top;
    }


    private int mPhoneNumberViewStatus;
    private final int VERIFY_ERROR = -1;
    private final int VERIFY_SEND = 1;

    private void setPhoneNumberView(int type) {
        if (type == VERIFY_SEND && mPhoneNumberViewStatus != VERIFY_SEND) {
            mPhoneNumberViewStatus = VERIFY_SEND;
            mPhoneNumberLabel.setTextColor(getResourcesSafe().getColor(com.ximalaya.ting.android.host.R.color.host_color_666666_888888));
            if(!TextUtils.isEmpty(mPhoneNumberForShow)) {
                mPhoneNumberLabel.setText("验证码已发送至:" + mPhoneNumberForShow);
            } else {
                mPhoneNumberLabel.setText("验证码已发送至" + "+" + mCountryCode + " " + mPhoneNumber);
            }
        } else if (type == VERIFY_ERROR && mPhoneNumberViewStatus != VERIFY_ERROR) {
            mPhoneNumberViewStatus = VERIFY_ERROR;
            mPhoneNumberLabel.setTextColor(Color.parseColor("#f43530"));
            mPhoneNumberLabel.setText("验证码错误，请重新填写");
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("");
        if (getArguments() != null) {
            mPhoneNumber = getArguments().getString(BundleKeyConstants.KEY_PHONE_NUMBER);
            mPhoneNumberForShow = getArguments().getString(BundleKeyConstants.KEY_PHONE_NUMBER_FOR_SHOW);
            String countryCode = getArguments().getString(BundleKeyConstants.KEY_PHONE_COUNTRY_CODE);
            if (!TextUtils.isEmpty(countryCode)) {
                mCountryCode = countryCode;
            }
            isFullLogin = getArguments().getBoolean(BundleKeyConstants.KEY_IS_FULL_LOGIN, true);
            isVerifyIdentidy = getArguments().getBoolean(BundleKeyConstants.KEY_IS_VERIFY_INDENTIDY, false);
            mBizKey = getArguments().getString(BundleKeyConstants.KEY_VERIFY_BIZKEY);
            mLoginIsPsw = getArguments().getBoolean(BundleKeyConstants.KEY_VERIFY_LOGIN_IS_PSW);
            isFormOAuth2SDK = getArguments().getBoolean(BundleKeyConstants.KEY_LOGIN_FROM_OAUTH_SDK);
        }

        if (!isFullLogin) {
            findViewById(R.id.login_top).setVisibility(View.GONE);
        }
        mRegisterHint = (TextView) findViewById(R.id.login_regiset_hint);
        mRegisterHint.setVisibility(View.GONE);

        mPhoneNumberLabel = (TextView) findViewById(R.id.login_phone_number);
        setPhoneNumberView(VERIFY_SEND);

        mIfPhoneNoLongerWorks = (TextView) findViewById(R.id.login_enable_false_check);
        mIfPhoneNoLongerWorks.setOnClickListener(this);
        // 键盘的高度变化监听要在唤起键盘前，否则无法设置正确的控件位置
        startKeyBoardListener();

        vPasswordGrid = (GridPasswordView) findViewById(R.id.login_verification_code1);
        vPasswordGrid.requestFocus();
        vPasswordGrid.requestFocusFromTouch();
        vPasswordGrid.setOnPasswordChangedListener(
                new GridPasswordView.OnPasswordChangedListener() {
                    @Override
                    public void onTextChanged(String psw) {
                        setPhoneNumberView(VERIFY_SEND);
                    }

                    @Override
                    public void onInputFinish(String psw) {
                        if (TextUtils.isEmpty(psw)) {
                            showFailToast("验证码不能为空");
                            return;
                        }
                        if (isVerifyIdentidy) {
                            checkSmsCodeAndLogin(mPhoneNumber, psw, mCountryCode, mBizKey);
                        } else {
                            doLoginWithoutPwd(mPhoneNumber, psw, mCountryCode);
                        }
                    }
                });
        vPasswordGrid.forceInputViewGetFocus();


        mTiming = (TextView) findViewById(R.id.login_timing);
        mTiming.setOnClickListener(this);
        mProgressDialog = new LoadingDialog(getActivity());
        if(getActivity() != null) {
            getActivity().getWindow().setSoftInputMode(
                    WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        }

        mGetVoiceCode = (TextView) findViewById(R.id.login_tv_get_voice_code);
        mGetVoiceCode.setOnClickListener(this);
        AutoTraceHelper.bindData(mTiming, "");
        AutoTraceHelper.bindData(mGetVoiceCode, "");
        startTimingResend();
    }

    @Override
    public void onResume() {
        super.onResume();

        new XMTraceApi.Trace()
                .pageView(32341, "verificationPage")
                .put("currPage", "verificationPage")
                .createTrace();
    }

    @Override
    public void onPause() {
        super.onPause();

        new XMTraceApi.Trace()
                .pageExit2(32342)
                .put("currPage", "verificationPage")
                .createTrace();
    }

    private LoadingDialog loginProgressDialog;

    private void showLoginProgress(Activity activity) {
        if (activity == null || activity.isFinishing())
            return;
        if (loginProgressDialog == null) {
            loginProgressDialog = new LoadingDialog(activity);
        } else {
            loginProgressDialog.cancel();
        }
        loginProgressDialog.setTitle("正在登录...");
        loginProgressDialog.showIcon(true);
        loginProgressDialog.showBg(true);
        loginProgressDialog.show();
    }

    private void dismissLoginProgress() {
        if (loginProgressDialog != null) {
            loginProgressDialog.dismiss();
            loginProgressDialog = null;
        }
    }

    // 检查验证码并去登陆
    private void checkSmsCodeAndLogin(String phoneNumber, String psw, String countryCode, String checkKey) {
        if (TextUtils.isEmpty(phoneNumber)) {
            return;
        }

        showLoginProgress(getActivity());

        Map<String, String> map = new HashMap<>();
        // 这里不需要添加城市编号
        map.put("mobile" , phoneNumber);
        map.put("code" ,psw);

        LoginRequest.verifySms(LoginService.getInstance().getRquestData() , map,
                new IDataCallBackUseLogin<VerifySmsResponse>() {
            @Override
            public void onSuccess(@Nullable VerifySmsResponse object) {

                if (object != null && object.getRet() == 0 && !TextUtils.isEmpty(object.getBizKey())) {

                    Map<String ,String> requestParams = new HashMap<>();
                    requestParams.put("smsKey" ,object.getBizKey());
                    requestParams.put("bizKey" ,mBizKey);
                    LoginRequest.loginValidateMobile(LoginService.getInstance().getRquestData() , requestParams,
                            new IDataCallBackUseLogin<LoginInfoModelNew>() {
                        @Override
                        public void onSuccess(@Nullable LoginInfoModelNew object) {
                            dismissLoginProgress();

                            if(canUpdateUi()) {
                                if(object != null && object.getRet() == 0) {
                                    UserInfoMannage.getInstance().setUser(object);
                                    if (!LoginDeferredActionHelper.getInstance().isRunningDeferredAction()) {
                                        CustomToast.showSuccessToast("登录成功!");
                                    }
                                    handleLoginSuccess(object ,false);
                                    if(!isFormOAuth2SDK) {
                                        toMainActivity(true);
                                    } else {
                                        finishFragment();
                                    }
                                } else {
                                    showFailToast("登录失败,请重试");
                                }
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            if(canUpdateUi()) {
                                showFailToast(message);
                            }
                        }
                    });
                } else {
                    showFailToast("操作失败,请稍后再试!");
                }
            }

            @Override
            public void onError(int code, String message) {
                dismissLoginProgress();

                if (canUpdateUi()) {
                    showFailToast(message);
                }
            }
        });

    }

    private void toMainActivity(boolean loginSuccess) {
        if (getActivity() != null) {
            hideSoftInput();
            if (getActivity() instanceof MainActivity) {
                ((MainActivity) getActivity()).goHome();
            } else {
                Intent intent = MainActivity.getMainActivityIntent(getActivity());
                getActivity().finish();
                getActivity().startActivity(intent);
            }
            if (loginSuccess) {
                // 登录时回调给Datasupport
//                getActivity().getContentResolver()
//                        .notifyChange(
//                                DataContentObserver
//                                        .getUriByType(DataContentObserver.TypeLoginIn),
//                                null);
            }
            ToolUtil.removeLastBindPhoneInfo();
        }
    }

    private void hideSoftInput() {
        SystemServiceManager.hideSoftInput(mContext);
    }

    public static abstract class MyClickText extends ClickableSpan {
        private Context context;

        public MyClickText(Context context) {
            if(context != null) {
                this.context = context.getApplicationContext();
            }
        }

        @Override
        public void updateDrawState(TextPaint ds) {
            super.updateDrawState(ds);
            ds.setColor(ContextCompat.getColor(context,
                    com.ximalaya.ting.android.host.R.color.host_orange));
            ds.setUnderlineText(false);
        }
    }

    @Override
    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.login_sms_verification_code_layout;
    }

    @Override
    public void onDetach() {
        super.onDetach();
        stopTiming();
        cancelKeyBoardListener();
    }

    private void startKeyBoardListener() {
        if (null != getActivity()
                && null != getActivity().getWindow()
                && null == keyBoardWindowListener) {
            keyBoardWindowListener = new SoftKeyBoardWindowListener();
        }
        keyBoardWindowListener.init(getWindow(), true);
        keyBoardWindowListener.setOnSoftKeyBoardChangeListener(realListener);
    }

    private void cancelKeyBoardListener() {
        if (null != keyBoardWindowListener) {
            keyBoardWindowListener.removeOnSoftKeyBoardChangeListener();
        }
    }

    private void startTimingResend() {
        mElapsedTime = 0;
        if (mService == null || mService.isShutdown()) {
            mService = new ScheduledThreadPoolExecutor(1, new ThreadFactory() {
                @Override
                public Thread newThread(@NonNull Runnable r) {
                    return new Thread(r, "SmsVerificationCodeFragment#thread");
                }
            });
        }
        mService.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                mElapsedTime++;
                timingResend();
            }
        }, 0, 1000, TimeUnit.MILLISECONDS);
    }

    private void stopTiming() {
        if (mService != null && !mService.isShutdown()) {
            mService.shutdown();
        }
    }

    private void timingResend() {
        mTiming.post(new Runnable() {
            @Override
            public void run() {

                if (!canUpdateUi())
                    return;

                if (mElapsedTime >= 60) {
                    stopTiming();
                    mTiming.setText("重新发送");
                    mTiming.setEnabled(true);
                    mTiming.setTextColor(getResourcesSafe().getColor(
                            com.ximalaya.ting.android.host.R.color.host_color_f86442));
                } else {
                    int leftTime = 60 - mElapsedTime;
                    if (leftTime <= 40) {
                        if (TextUtils.equals(mCountryCode, "86")) {
                            mGetVoiceCode.setVisibility(View.VISIBLE);
                            if (!isFullLogin && leftTime == 40) {
                                new UserTracking()
                                        .setModuleType("引导用户登录半屏")
                                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);
                            }
                        }
                    }

                    mTiming.setEnabled(false);
                    mTiming.setText(leftTime + "s后再次发送");
                    mTiming.setTextColor(getResourcesSafe().getColor(
                            com.ximalaya.ting.android.host.R.color.host_color_999999_888888));
                }
            }
        });
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.login_tv_get_voice_code) {
            if (OneClickHelper.getInstance().onLongTimeGapClick(v)) {
                if (isVerifyIdentidy || StringUtil.verifyGlobalPhone(mCountryCode, mPhoneNumber)) {
                    if (!isFullLogin && !isVerifyIdentidy) {
                        new UserTracking()
                                .setItem("button")
                                .setItemId("语音验证码")
                                .setSrcModule("引导用户登录半屏")
                                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
                    }

                    int sendSmsType;

                    if(isVerifyIdentidy) {
                        sendSmsType = (mLoginIsPsw
                                ? LoginRequest.SEND_SMS_TYPE_FOR_LOGIN_BY_PSW
                                : LoginRequest.SEND_SMS_TYPE_FOR_THIRD_LOGIN_CHECK);
                    } else {
                        sendSmsType = LoginRequest.SEND_SMS_TYPE_FOR_LOGIN;
                    }

                    // 如果是验证手机不需要传城市编号
                    LoginHelper.sendVoiceSmsCode(getmActivity() ,
                            isVerifyIdentidy ? mPhoneNumber : getCountryCodePhoneNum(mCountryCode ,mPhoneNumber) ,sendSmsType);

                    new XMTraceApi.Trace()
                            .click(32343)
                            .put("currPage", "verificationPage")
                            .createTrace();
                } else {
                    showFailToast("用户手机号输入有误");
                }
            } else {
                showFailToast(R.string.login_click_voice_code_too_fast);
            }
        } else if (i == R.id.login_timing) {
            if (isVerifyIdentidy) {
                getVerifySmsCode(mPhoneNumber, mCountryCode, new WeakReference<SmsVerificationCodeFragment>(this));
            } else {
                getPhoneCheckCode(LoginRequest.SEND_SMS_TYPE_FOR_LOGIN,
                        mPhoneNumber, mCountryCode,
                        new WeakReference<SmsVerificationCodeFragment>(this), new IHandleOk() {
                            @Override
                            public void onReady() {
                                startTimingResend();
                            }
                        });
            }

            new XMTraceApi.Trace()
                    .click(32344)
                    .put("currPage", "verificationPage")
                    .createTrace();
        } else if (R.id.login_enable_false_check == i) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().handleITing(getActivity(), Uri.parse
                        ("iting://open?msg_type=94&bundle=account&pageName=AccountAppeal"));

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static void getVerifySmsCode(String phoneNumber, String countryCode,
                                         final WeakReference<SmsVerificationCodeFragment> softReference) {
        final SmsVerificationCodeFragment fragment = softReference.get();
        if (fragment == null)
            return;

        if (fragment.isGetting) {
            return;
        }
        fragment.isGetting = true;

        if (fragment.mProgressDialog == null) {
            fragment.mProgressDialog = new LoadingDialog(fragment.getActivity());
        } else {
            fragment.mProgressDialog.cancel();
        }
        fragment.mProgressDialog.setTitle("正在为你获取验证码...");
        fragment.mProgressDialog.showIcon(true);
        fragment.mProgressDialog.showBg(true);
        fragment.mProgressDialog.show();

        Map<String ,String> maps = new HashMap<>();
        // 验证手机不需要传手机编号
        maps.put("mobile" ,phoneNumber);
        maps.put("sendType" ,"1");
        LoginRequest.sendSms(fragment.getmActivity() ,fragment.mLoginIsPsw
                ? LoginRequest.SEND_SMS_TYPE_FOR_LOGIN_BY_PSW
                : LoginRequest.SEND_SMS_TYPE_FOR_THIRD_LOGIN_CHECK ,
                LoginService.getInstance().getRquestData() ,maps,
                new IDataCallBackUseLogin<BaseResponse>() {
            @Override
            public void onSuccess(@Nullable BaseResponse object) {
                SmsVerificationCodeFragment fragment = softReference.get();
                if (fragment == null) {
                    return;
                }

                if (fragment.canUpdateUi() && fragment.mProgressDialog != null) {
                    fragment.mProgressDialog.cancel();
                }
                fragment.isGetting = false;

                fragment.setPhoneNumberView(fragment.VERIFY_SEND);
                fragment.startTimingResend();
            }

            @Override
            public void onError(int code, String message) {
                final SmsVerificationCodeFragment fragment = softReference.get();
                if (fragment == null) {
                    return;
                }

                fragment.isGetting = false;

                if (fragment.canUpdateUi()) {
                    fragment.showFailToast(message);
                    if (fragment.mProgressDialog != null) {
                        fragment.mProgressDialog.cancel();
                    }
                }
            }
        });
    }

    @Override
    public void onMyResume() {
        super.onMyResume();

        postOnUiThreadDelayed(new Runnable() {
            @Override
            public void run() {
               if(vPasswordGrid != null) {
                   vPasswordGrid.forceInputViewGetFocus();
               }
            }
        }, 300);
    }

    private boolean handledCancle = false;
    @Override
    public boolean onBackPressed() {
        if(!handledCancle) {
            DialogBuilder dialogBuilder = new DialogBuilder(getActivity())
                    .setMessage("验证码短信可能略有延迟，要再等等吗？")
                    .setOkBtn("再等等")
                    .setcancelApplyToButton(false)
                    .setCancelBtn("不等了", new DialogBuilder.DialogCallback() {
                        @Override
                        public void onExecute() {
                            handledCancle = true;
                            finishFragment();
                        }
                    });

            dialogBuilder.setOnDismissListener(new WeakReference<DialogInterface.OnDismissListener>(mOnDismissListener));
            dialogBuilder
                    .showConfirm();

            return true;
        }

        return super.onBackPressed();
    }

    DialogInterface.OnDismissListener mOnDismissListener = new DialogInterface.OnDismissListener() {
        @Override
        public void onDismiss(DialogInterface dialogInterface) {
            handledCancle = false;
        }
    };

    @Override
    protected String getPageLogicName() {
        return ILoginFragmentAction.PAGE_LOGIC_SMS_VERIFICATION_CODE;
    }

    private class KeyBoardHeightListener implements SoftKeyBoardWindowListener.OnSoftKeyBoardChangeListener {
        @Override
        public void keyBoardShow(int height) {
            if (canUpdateUi() && 0 < height) {
                updateBottomMargin(mIfPhoneNoLongerWorks, height + BaseUtil.dp2px(mContext, 25));
            }
        }

        @Override
        public void keyBoardHide(int height) {
            if (canUpdateUi()) {
                updateBottomMargin(mIfPhoneNoLongerWorks, BaseUtil.dp2px(mContext, 25));
            }
        }

        private void updateBottomMargin(View view, int size) {
            ViewGroup.LayoutParams params = null;
            if (null == view || null == (params = view.getLayoutParams())) {
                return;
            }
            if (params instanceof ViewGroup.MarginLayoutParams) {
                ((ViewGroup.MarginLayoutParams) params).bottomMargin = size;
                view.setLayoutParams(params);
                view.postInvalidate();
            }
        }
    }
}
