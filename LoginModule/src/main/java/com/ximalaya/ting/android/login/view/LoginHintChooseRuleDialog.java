package com.ximalaya.ting.android.login.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.manager.ElderlyModeManager;
import com.ximalaya.ting.android.host.manager.account.LoginUtil;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * <AUTHOR> on 2017/7/13.
 */

public class LoginHintChooseRuleDialog extends XmBaseDialog implements View.OnClickListener {
    public static final String TAG = "ADAppDownloadRemindDialogFragment";
    private TextView mSubTitle;
    @Nullable
    private TextView mDownloadBtn;
    @Nullable
    private ImageView mCancleBtn;
    @Nullable
    private IHandleOk okHandle;

    private String mProtocolName;
    private String mProtocolUrl;
    private @Nullable String mOkBtnText = null;

    public LoginHintChooseRuleDialog(@NonNull Context context) {
        super(context);
    }

    public void setProtocolData(String protocolName, String protocolUrl) {
        mProtocolName = protocolName;
        mProtocolUrl = protocolUrl;
    }

    public void setOkBtnText(String text) {
        mOkBtnText = text;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);

        super.onCreate(savedInstanceState);

        if (getWindow() != null) {
            View decorView = getWindow().getDecorView();
            if (decorView != null) {
                decorView.setPadding(0, 0, 0, 0);
            }

            getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            WindowManager.LayoutParams lp = getWindow().getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            lp.gravity = Gravity.BOTTOM;
            getWindow().setAttributes(lp);
        }

        setContentView(R.layout.login_layout_hint_choose_rule);

        mSubTitle = (TextView) findViewById(R.id.login_sub_title);

        SpannableStringBuilder spannableStringBuilder =
                new SpannableStringBuilder();
        if (mProtocolName != null) {
            LoginUtil.protocolSpannable(mProtocolName, mProtocolUrl, spannableStringBuilder, 0);
        }
        LoginUtil.createCommonLoginSpannable(getContext(), spannableStringBuilder, 0);

        mSubTitle.setText(spannableStringBuilder);
        mSubTitle.setMovementMethod(LinkMovementMethod.getInstance());
        mSubTitle.setHighlightColor(Color.TRANSPARENT);


        mCancleBtn = (ImageView) findViewById(R.id.login_cancel);
        mDownloadBtn = (TextView) findViewById(R.id.login_ok);
        ElderlyModeManager.getInstance().setTextBackground(mDownloadBtn, R.drawable.login_button_half_circle_corner_elderly);
        if (mCancleBtn != null) {
            mCancleBtn.setOnClickListener(this);
            AutoTraceHelper.bindData(mCancleBtn,"");
        }
        if (mDownloadBtn != null) {
            if (!TextUtils.isEmpty(mOkBtnText)) {
                mDownloadBtn.setText(mOkBtnText);
            }
            mDownloadBtn.setOnClickListener(this);
            AutoTraceHelper.bindData(mDownloadBtn,"");
        }
        setCanceledOnTouchOutside(true);
        setCancelable(true);
        statDialogShow();
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.login_cancel) {
            dismiss();
        } else if (i == R.id.login_ok) {
            if (okHandle != null) {
                okHandle.onReady();
            }
            statOkBtnClick();
            dismiss();
        }
    }

    public void setOkHandle(@Nullable IHandleOk okHandle) {
        this.okHandle = okHandle;
    }

    private void statDialogShow() {
        // 协议确认弹窗  弹框展示
        new XMTraceApi.Trace()
                .setMetaId(55547)
                .setServiceId("dialogView") // 弹窗展示时上报
                .put("currPage", "")
                .createTrace();
    }

    private void statOkBtnClick() {
        String text = "";
        if (mDownloadBtn != null && mDownloadBtn.getText() != null) {
            text = mDownloadBtn.getText().toString();
        }
        // 协议确认弹窗-按钮  弹框控件点击
        new XMTraceApi.Trace()
                .setMetaId(55548)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("currPage", "")
                .put("Item", text)
                .createTrace();
    }
}
