package com.ximalaya.ting.android.login.model;

/**
 * <AUTHOR>
 */
public class SettingInfo {
    private String nameWake;
    private String textWake;
    private boolean isSetting;
    private boolean isExpired;
    private boolean isVerified;
    private int time;
    private long spaceOccupySize;
    private String thirdPartId;

    public SettingInfo() {
    }

    public boolean isExpired() {
        return isExpired;
    }

    public void setExpired(boolean isExpired) {
        this.isExpired = isExpired;
    }

    public boolean isVerified() {
        return isVerified;
    }

    public void setVerified(boolean isVerified) {
        this.isVerified = isVerified;
    }

    public long getSpaceOccupySize() {
        return spaceOccupySize;
    }

    public void setSpaceOccupySize(long spaceOccupySize) {
        this.spaceOccupySize = spaceOccupySize;
    }

    public SettingInfo(String name, boolean isSetting) {
        this.nameWake = name;
        this.isSetting = isSetting;
    }

    public String getNameWake() {
        return nameWake;
    }

    public String getTextWake() {
        return textWake;
    }

    public int getTime() {
        return time;
    }

    public boolean isSetting() {
        return isSetting;
    }

    public void setNameWake(String nameWake) {
        this.nameWake = nameWake;
    }

    public void setSetting(boolean isSetting) {
        this.isSetting = isSetting;
    }

    public void setTextWake(String textWake) {
        this.textWake = textWake;
    }

    public void setTime(int time) {
        this.time = time;
    }

    @Override
    public String toString() {
        return "SettingInfo [nameWake=" + nameWake + ", textWake=" + textWake
                + ", isSetting=" + isSetting + ", isExpired=" + isExpired
                + ", isVerified=" + isVerified + ", time=" + time
                + ", spaceOccupySize=" + spaceOccupySize + "]";
    }


    public String getThirdPartId() {
        return thirdPartId;
    }

    public void setThirdPartId(String thirdPartId) {
        this.thirdPartId = thirdPartId;
    }
}