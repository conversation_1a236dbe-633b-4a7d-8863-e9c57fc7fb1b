package com.ximalaya.ting.android.login.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Created by le.xin on 2019-08-14.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class CatchGetPrivateFlagsCrachFrameLayout extends FrameLayout {

    public CatchGetPrivateFlagsCrachFrameLayout(@NonNull Context context) {
        super(context);
    }

    public CatchGetPrivateFlagsCrachFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public CatchGetPrivateFlagsCrachFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        boolean b = true;
        try {
            b = super.dispatchTouchEvent(ev);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return b;
    }
}
