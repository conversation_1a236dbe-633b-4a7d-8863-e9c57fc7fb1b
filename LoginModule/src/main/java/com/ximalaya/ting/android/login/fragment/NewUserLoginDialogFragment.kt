package com.ximalaya.ting.android.login.fragment

import android.app.Activity
import android.graphics.Bitmap
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import com.ximalaya.ting.android.basequicklogin.IVerifyResultCallback
import com.ximalaya.ting.android.basequicklogin.PreVerifyResult
import com.ximalaya.ting.android.basequicklogin.QuickLoginUtil
import com.ximalaya.ting.android.basequicklogin.VerifyResult
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog
import com.ximalaya.ting.android.host.constants.LoginByConstants
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment
import com.ximalaya.ting.android.host.manager.account.LoginUtil
import com.ximalaya.ting.android.host.manager.account.NewUserLoginManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.login.R
import com.ximalaya.ting.android.login.view.LoginHintChooseRuleDialog
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin
import com.ximalaya.ting.android.loginservice.base.ILogin
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger

/**
 * Created by WolfXu on 2023/5/10.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber ***********
 */
class NewUserLoginDialogFragment : BaseDialogFragment<NewUserLoginDialogFragment>() {

    companion object {
        private val TAG = NewUserLoginDialogFragment::class.simpleName
        private const val ARGUMENT_KEY_ACTIVITY_INFO = "activity_info"

        fun newInstance(
            result: PreVerifyResult,
            loginBy: Int,
            openChannel: String?,
            activityBitmap: Bitmap?
        ): NewUserLoginDialogFragment {
            val args = Bundle()
            args.putParcelable(BundleKeyConstants.KEY_ONE_KEY_QUICK_PRE_VERIFY, result)
            args.putInt(BundleKeyConstants.KEY_LOGIN_BY, loginBy)
            args.putString(BundleKeyConstants.KEY_OPEN_CHANNEL, openChannel)
            args.putParcelable(ARGUMENT_KEY_ACTIVITY_INFO, activityBitmap)
            val fragment = NewUserLoginDialogFragment()
            fragment.arguments = args
            return fragment
        }
    }

    private var mPreVerifyResult: PreVerifyResult? = null
    private var mVerifyResult: VerifyResult? = null
    private var mLoginBy: Int = 0
    private var mOpenChannel: String? = null
    private var mActivityBitmap: Bitmap? = null
    private var mProgressDialog : MyProgressDialog? = null
    private var mIvClose: ImageView? = null
    private var mIvImage: ImageView? = null
    private var mTvPhoneNumber: TextView? = null
    private var mTvChange: TextView? = null
    private var mTvLoginBtn: TextView? = null
    private var mRegisterHintState: ImageView? = null
    private var mRegisterHint: TextView? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        Logger.i(TAG, "onCreateView")
        configureDialogStyle()
        return inflater.inflate(R.layout.login_dialog_fra_new_user_login, container, false)
    }

    private fun configureDialogStyle() {
        val dialog = dialog ?: return
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setCanceledOnTouchOutside(false)
        dialog.window?.let {
            it.setWindowAnimations(com.ximalaya.ting.android.host.R.style.host_popup_window_animation_fade)
            it.setBackgroundDrawableResource(R.color.host_transparent)
            if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    it.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
                    var systemUiVisibility = it.decorView.systemUiVisibility
                    systemUiVisibility = systemUiVisibility or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    it.decorView.systemUiVisibility = systemUiVisibility
                    it.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                    it.statusBarColor = Color.TRANSPARENT
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                    it.setFlags(
                        WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS,
                        WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
                }
            }
            it.setGravity(Gravity.BOTTOM)
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.let {
            val width = BaseUtil.getScreenWidth(context) - 16.dp * 2
            it.setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT)
            val windowParams = it.attributes
//            windowParams.dimAmount = 0.0f
            it.attributes = windowParams
        }
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        init()
    }

    private fun init() {
        parseArgs()
//        loadActivityInfo()
        mIvImage = findViewById(R.id.login_iv_img) as? ImageView
        bindActivityInfo(mActivityBitmap)
        verifyData(false)
        mIvClose = findViewById(R.id.login_iv_close) as? ImageView
        mTvPhoneNumber = findViewById(R.id.login_tv_phone_number) as? TextView
        mTvChange = findViewById(R.id.login_tv_change) as? TextView
        mTvLoginBtn = findViewById(R.id.login_tv_login_btn) as? TextView
        mRegisterHintState = findViewById(R.id.login_login_hint_state) as? ImageView
        mRegisterHint = findViewById(R.id.login_regiset_hint) as? TextView
        setClickListener()
        bindVerifyInfo()
        statDialogView()
    }

    private fun setClickListener() {
        val onClickListener = View.OnClickListener {
            if (OneClickHelper.getInstance().onClick(it)) {
                when (it) {
                    mTvChange -> {
                        gotoNormalLogin(false)
                        statItemClick("更换")
                    }
                    mIvClose -> {
                        dismiss()
                        statItemClick("关闭")
                    }
                    mTvLoginBtn -> {
                        login()
                        statItemClick("立即登录")
                    }
                    mRegisterHintState -> {
                        val targetIsSelected = mRegisterHintState?.isSelected?.not() ?: false
                        mRegisterHintState?.isSelected = targetIsSelected
                        statItemClick(if (targetIsSelected) "勾选" else "取消勾选")
                    }
                }
            }
        }
        mIvClose?.setOnClickListener(onClickListener)
        mTvLoginBtn?.setOnClickListener(onClickListener)
        mTvChange?.setOnClickListener(onClickListener)
        mRegisterHintState?.setOnClickListener(onClickListener)
    }

    private fun parseArgs() {
        arguments?.let {
            mPreVerifyResult = it.getParcelable(BundleKeyConstants.KEY_ONE_KEY_QUICK_PRE_VERIFY)
            mLoginBy = it.getInt(BundleKeyConstants.KEY_LOGIN_BY, 0)
            mOpenChannel = it.getString(BundleKeyConstants.KEY_OPEN_CHANNEL, null)
            mActivityBitmap = it.getParcelable(ARGUMENT_KEY_ACTIVITY_INFO)
        }
    }

    private fun verifyData(verifyToLogin: Boolean) {
        QuickLoginUtil.getInstance().verify(ToolUtil.getCtx(), object : IVerifyResultCallback {
            override fun onSuccess(result: VerifyResult?) {
                mVerifyResult = result
                if (verifyToLogin && canUpdateUi()) {
                    if (result == null) {
                        gotoNormalLogin(true)
                    } else {
                        login()
                    }
                }
            }

            override fun onFailure(code: Int, message: String?) {
                if (verifyToLogin && canUpdateUi()) {
                    gotoNormalLogin(true)
                }
            }
        })
    }

    private fun bindVerifyInfo() {
        mPreVerifyResult?.let {
            mTvPhoneNumber?.text = it.number
            mRegisterHint?.let { registerHint ->
                registerHint.text = it.protocolName
                val spannableStringBuilder = LoginUtil.createLoginSpannableStrUseOneKeyQuick(context, false, it.protocolName, it.protocolUrl, 0)
                registerHint.text = spannableStringBuilder
                registerHint.movementMethod = LinkMovementMethod.getInstance()
                registerHint.highlightColor = Color.TRANSPARENT
            }
        }
    }

    private fun bindActivityInfo(data: Bitmap?) {
        data ?: return
        mIvImage?.setImageBitmap(data)
    }

    private fun confirmProtocolDialogAutoLogin(): Boolean {
        return NewUserLoginManager.confirmProtocolDialogAutoLogin()
    }

    private fun login() {
        if (mRegisterHintState?.isSelected == false) {
            if (ToolUtil.activityIsValid(activity)) {
                activity?.let {
                    val loginHintChooseRuleDialog = LoginHintChooseRuleDialog(it)
                    loginHintChooseRuleDialog.setProtocolData(mPreVerifyResult?.protocolName, mPreVerifyResult?.protocolUrl)
                    val confirmProtocolDialogAutoLogin = confirmProtocolDialogAutoLogin()
                    if (confirmProtocolDialogAutoLogin) {
                        loginHintChooseRuleDialog.setOkBtnText("同意协议并登录")
                    }
                    loginHintChooseRuleDialog.setOkHandle {
                        mRegisterHintState?.isSelected = true
                        if (confirmProtocolDialogAutoLogin) {
                            login()
                        }
                    }
                    loginHintChooseRuleDialog.show()
                }
            }
            return
        }

        val verifyResult = mVerifyResult
        showLoginProgress(activity)
        if (verifyResult == null) {
            verifyData(true)
        } else {
            mVerifyResult?.let {
                QuickLoginUtil.getInstance().toLogin(it, object :
                    IDataCallBackUseLogin<LoginInfoModelNew> {
                    override fun onSuccess(data: LoginInfoModelNew?) {
                        if (canUpdateUi()) {
                            dismissLoginProgress()
                            CustomToast.showToast("登录成功")
                            LoginUtil.loginSuccess(
                                activity, data,
                                ILogin.LOGIN_FLAG_XIMALAYA, "新用户登录", arguments
                            )
                            dismiss()
                        }
                    }

                    override fun onError(code: Int, message: String?) {
                        if (canUpdateUi()) {
                            dismissLoginProgress()
                            if (message.isNullOrEmpty()) {
                                CustomToast.showFailToast("登录失败，请重试")
                            } else {
                                CustomToast.showFailToast(message)
                            }
                        }
                    }
                })
            }
        }
    }

    private fun gotoNormalLogin(showToast : Boolean = true) {
        if (showToast) {
            CustomToast.showFailToast("登录失败，已切换其他登录方式")
        }
        dismissLoginProgress()
        dismiss()
        UserInfoMannage.loginBySelfPage(context, mLoginBy, false, mOpenChannel, null)
    }

    private fun showLoginProgress(activity: Activity?) {
        if (activity == null || activity.isFinishing)
            return
        if (mProgressDialog == null) {
            mProgressDialog = MyProgressDialog(activity);
        } else if (mProgressDialog?.isShowing == false) {
            mProgressDialog?.cancel();
            mProgressDialog?.setTitle("登录")
            mProgressDialog?.setMessage("正在登录...")
            mProgressDialog?.show()
        }
    }

    private fun dismissLoginProgress() {
        mProgressDialog?.dismiss();
        mProgressDialog = null;
    }

    private fun statDialogView() {
        // 新首页-登录享新人福利弹框  控件曝光
        XMTraceApi.Trace()
            .setMetaId(54052)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newHomePage")
            .put("dialogType", getType())
            .createTrace()
    }

    private fun statItemClick(action: String) {
        // 新首页-登录享新人福利弹框  点击事件
        val trace = XMTraceApi.Trace()
            .click(54051) // 用户点击时上报
            .put("currPage", "newHomePage")
            .put("Item", action)
            .put("dialogType", getType())
        if (action == "立即登录") {
            trace.put("status", if (mRegisterHintState?.isSelected == true) "true" else "false")
        }
        trace.createTrace()
    }

    private fun getType(): String {
        return if (mLoginBy == LoginByConstants.LOGIN_BY_SOUND_EFFECT_PRIZE) "领音效权益" else ""
    }
}