package com.ximalaya.ting.android.login.view;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.manager.ElderlyModeManager;
import com.ximalaya.ting.android.login.R;

import androidx.annotation.NonNull;

/**
 * Created by changle.fang on 2020-03-03.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15050162674
 */
public class HintKeepOnLoginDialog extends XmBaseDialog {

    private ICancelListener mCancelListener;
    private TextView mTvContent;
    private String mContent;

    private HintKeepOnLoginDialog(@NonNull Context context) {
        super(context, com.ximalaya.ting.android.host.R.style.host_share_dialog);
    }

    public HintKeepOnLoginDialog(@NonNull Context context, String content,
                                 ICancelListener cancelListener) {
        this(context);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        mContent = "真的要放弃本次登录吗?";
        this.mCancelListener = cancelListener;
        initUi();
    }

    private void initUi() {

        View view = View.inflate(getContext(), R.layout.login_layout_retain_login_dialog, null);
        TextView tvCancel = view.findViewById(R.id.host_tv_cancel);
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mCancelListener != null) {
                    mCancelListener.onCancel();
                }
                HintKeepOnLoginDialog.this.dismiss();
            }
        });
        TextView tvConfirm = view.findViewById(R.id.host_tv_confirm);
        if (tvConfirm.getPaint() != null){
            tvConfirm.getPaint().setFakeBoldText(true);
        }
        ElderlyModeManager.getInstance().setTextBackground(tvConfirm, com.ximalaya.ting.android.host.R.drawable.host_bg_rect_cf3636_f24646_radius_0_0_0_10);
        tvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HintKeepOnLoginDialog.this.dismiss();
            }
        });
        mTvContent = view.findViewById(R.id.host_tv_content);
        mTvContent.setText(mContent);
        setContentView(view);
        setCanceledOnTouchOutside(true);
        setCancelable(true);
        if (getWindow() != null) {
            WindowManager.LayoutParams lp = getWindow().getAttributes();
            int screenWidth = BaseUtil.getScreenWidth(getContext());
            lp.width = screenWidth - BaseUtil.dp2px(getContext(), 100);
            lp.gravity = Gravity.CENTER;
            getWindow().setAttributes(lp);
        }
    }

    public interface ICancelListener {
        void onCancel();
    }
}
