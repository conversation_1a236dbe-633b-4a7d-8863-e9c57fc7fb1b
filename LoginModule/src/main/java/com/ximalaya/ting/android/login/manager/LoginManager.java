package com.ximalaya.ting.android.login.manager;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.ximalaya.ting.android.basequicklogin.IPreVerifyResultCallback;
import com.ximalaya.ting.android.basequicklogin.PreVerifyResult;
import com.ximalaya.ting.android.basequicklogin.QuickLoginUtil;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.SmsLoginDialogActivity;
import com.ximalaya.ting.android.host.activity.login.LoginActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.manager.account.ScoreManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.JsonUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.view.image.TouchableImageView;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.login.constants.LoginUrlConstants;
import com.ximalaya.ting.android.login.fragment.GetAndVerifySmsCodeFragment;
import com.ximalaya.ting.android.login.fragment.OneKeyQuickLoginFragment;
import com.ximalaya.ting.android.login.fragment.VerifyIdentidyFragment;
import com.ximalaya.ting.android.login.view.LoginUnlockDialog;
import com.ximalaya.ting.android.loginservice.IHandleRequestCode;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin;
import com.ximalaya.ting.android.loginservice.base.ILogin;
import com.ximalaya.ting.android.loginservice.base.IRequestData;
import com.ximalaya.ting.android.loginservice.callback.IRequestCallBack;
import com.ximalaya.ting.android.loginservice.model.BindLoginInfoModel;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import static com.ximalaya.ting.android.login.fragment.VerifyIdentidyFragment.FROM_LOGIN;
import static com.ximalaya.ting.android.login.fragment.VerifyIdentidyFragment.VERIFY_IDENTIDY_FROM;
import static com.ximalaya.ting.android.login.fragment.VerifyIdentidyFragment.VERIFY_IDENTIDY_PHONE_NUM;
import static com.ximalaya.ting.android.login.fragment.VerifyIdentidyFragment.VERIFY_IDENTIDY_PHONE_NUM_REAL;
import static com.ximalaya.ting.android.loginservice.base.ILogin.LOGIN_FLAG_PHONE;

/**
 * Created by le.xin on 2019-12-07.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class LoginManager {
    /**
     * 设置各个登录方式的状态
     * @param layout
     */
    public static void setThirdPlatformViews(View layout) {
        ((TouchableImageView) layout.findViewById(R.id.login_login_weibo))
                .setImageType(TouchableImageView.ImageTypeCircle);
        ((TouchableImageView) layout.findViewById(R.id.login_login_weixin))
                .setImageType(TouchableImageView.ImageTypeCircle);
        ((TouchableImageView) layout.findViewById(R.id.login_login_xiaomi))
                .setImageType(TouchableImageView.ImageTypeCircle);
        ((TouchableImageView) layout.findViewById(R.id.login_login_meizu))
                .setImageType(TouchableImageView.ImageTypeCircle);
        ((TouchableImageView) layout.findViewById(R.id.login_login_qq))
                .setImageType(TouchableImageView.ImageTypeCircle);

        if (!DeviceUtil.isMIUI()) {
            layout.findViewById(R.id.login_xiaomi).setVisibility(View.GONE);
        }
        if (!DeviceUtil.isMeizu()) {
            layout.findViewById(R.id.login_meizu).setVisibility(View.GONE);
        }

        int loginWay = SharedPreferencesUtil.getInstance(layout.getContext()).
                getInt(PreferenceConstantsInHost.TIMGMAIN_KEY_SHARED_PRE_LOGIN_WAY,
                        AppConstants.LOGIN_FLAG_XIMALAYA);//登录方式,
        switch (loginWay) {
            case AppConstants.LOGIN_FLAG_WEIXIN:
                layout.findViewById(R.id.login_latest_login_wechat).setVisibility(View.VISIBLE);
                break;
            case AppConstants.LOGIN_FLAG_QQ:
                layout.findViewById(R.id.login_latest_login_qq).setVisibility(View.VISIBLE);
                break;
            case AppConstants.LOGIN_FLAG_WEIBO:
                layout.findViewById(R.id.login_latest_login_sina).setVisibility(View.VISIBLE);
                break;
            case AppConstants.LOGIN_FLAG_XIAOMI:
                layout.findViewById(R.id.login_latest_login_xiaomi).setVisibility(View.VISIBLE);
                break;
            case AppConstants.LOGIN_FLAG_MEIZU:
                layout.findViewById(R.id.login_latest_login_meizu).setVisibility(View.VISIBLE);
                break;
            default:
                break;
        }
    }

    public interface ILoginHandler {
        int getLoginStrategy();

        void onHandlerRequestCode();
    }

    public static IHandleRequestCode getHandlerRequestCode(final Activity activity ,
                                                           final ILoginHandler loginHandler,
                                                           final boolean isFormOAuth2SDK) {
        final WeakReference<Activity> activityWeakReference = new WeakReference<>(activity);
        return new IHandleRequestCode() {
            @Override
            public void noSetPswd() {
                // 产品说不再提示需要再次设置密码
//            if (getmActivity() != null) {
//                // 如果是手机验证码登录,没有设置密码不再进行设置密码操作
//                try {
//                    Toast.makeText(mActivity, "登录成功," +
//                            "如需使用密码登录，请先进入【设置】→【设置密码】完成设置", Toast.LENGTH_LONG).show();
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
            }

            @Override
            public void accountFroze(String msg, final long uid) {
                if(!ToolUtil.activityIsValid(activityWeakReference)) {
                    return;
                }

                final Activity actvity = activityWeakReference.get();
//                new DialogBuilder(actvity)
//                        .setTitleVisibility(false)
//                        .setMessage(TextUtils.isEmpty(msg)
//                                ? "很抱歉，您的账号存在违规操作，已被冻结，请联系客服解封" : msg)
//                        .setMsgGravity(Gravity.CENTER)
//                        .setCancelBtn("知道了")
//                        .setOkBtn("去解封", new DialogBuilder.DialogCallback() {
//                            @Override
//                            public void onExecute() {
//                                CustomerFeedBackManager.jumpToChat();
//                            }
//                        })
//                        .showConfirm();

                LoginUnlockDialog unlockDialog = new LoginUnlockDialog(activity);
                unlockDialog.setHintMsg(TextUtils.isEmpty(msg) ? "很抱歉，您的账号已被冻结" : msg);
                unlockDialog.setOkHandle(new IHandleOk() {
                    @Override
                    public void onReady() {
                        String url = LoginUrlConstants.getInstanse().getLoginUnlockUrl() + uid;
                        Intent intent2 = new Intent();
                        intent2.setAction(Intent.ACTION_VIEW);
                        intent2.setData(Uri.parse("iting://open?msg_type=14&url=" + url));
                        intent2.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        try {
                            activity.startActivity(intent2);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
                unlockDialog.show();

                if (loginHandler != null) {
                    loginHandler.onHandlerRequestCode();
                }
            }

            @Override
            public void noBindPhone(LoginInfoModelNew result) {
                if(ToolUtil.activityIsValid(activityWeakReference)) {
                    int loginStrategy = 0;
                    if(loginHandler != null) {
                        loginStrategy = loginHandler.getLoginStrategy();
                    }
                    LoginManager.gotoBind(result ,activityWeakReference.get() ,loginStrategy, isFormOAuth2SDK);

                    if (loginHandler != null) {
                        loginHandler.onHandlerRequestCode();
                    }
                }
            }

            @Override
            public void gotoVerficate(LoginInfoModelNew result) {
                if(ToolUtil.activityIsValid(activityWeakReference)) {
                    int loginStrategy = 0;
                    if(loginHandler != null) {
                        loginStrategy = loginHandler.getLoginStrategy();
                    }

                    goto2Verficate(result ,activityWeakReference.get() ,loginStrategy, isFormOAuth2SDK);

                    if (loginHandler != null) {
                        loginHandler.onHandlerRequestCode();
                    }
                }
            }

            @Override
            public void alreadyBinded(final LoginInfoModelNew result , final IRequestData requestData , final String url,
                                      final Map<String, String> specificParams,
                                      final IDataCallBackUseLogin callBack, final IRequestCallBack successCallBack,
                                      final String requestType) {
                if(!ToolUtil.activityIsValid(activityWeakReference)) {
                    return;
                }

                String message = "该手机已绑定,是否换绑";
                if (result != null && !TextUtils.isEmpty(result.getMsg())) {
                    message = result.getMsg();
                }

                DialogBuilder dialogBuilder = new DialogBuilder(activityWeakReference.get()).setMessage(message).setCancelBtn("否",
                        new DialogBuilder.DialogCallback() {
                            @Override
                            public void onExecute() {

                            }
                        }).setOkBtn("是", new DialogBuilder.DialogCallback() {
                    @Override
                    public void onExecute() {

                        Map<String, String> map = new HashMap<>();
                        map.put("forceBind", "true");

                        if (result != null) {
                            map.put("bizKey", result.getBizKey());
                            map.put("smsKey", result.getSmsKey());

                            if (!TextUtils.isEmpty(result.getMobile())) {
                                map.put("mobile", result.getMobile());
                            }
                        }
                        LoginRequest.bindPhone(requestData, map, callBack);
                    }
                });
                dialogBuilder.setOnDismissListener(new WeakReference<DialogInterface.OnDismissListener>(mDismissListener));
                dialogBuilder.showConfirm();

                if (loginHandler != null) {
                    loginHandler.onHandlerRequestCode();
                }
            }

            @Override
            public void resetPsw(LoginInfoModelNew result) {
                if(!ToolUtil.activityIsValid(activityWeakReference)) {
                    return;
                }

                final Activity mActivity = activityWeakReference.get();

                String message = "账户存在风险，请修改密码";
                if (result != null && !TextUtils.isEmpty(result.getMsg())) {
                    message = result.getMsg();
                }

                DialogBuilder dialogBuilder = new DialogBuilder(mActivity).setMessage(message).setCancelBtn("取消",
                        new DialogBuilder.DialogCallback() {
                            @Override
                            public void onExecute() {

                            }
                        }).setOkBtn("去修改", new DialogBuilder.DialogCallback() {
                    @Override
                    public void onExecute() {
                        String url = "iting://open?msg_type=94&bundle=account&pageName=forgetPassword";
                        Intent intent = new Intent(Intent.ACTION_VIEW);
                        intent.setData(Uri.parse(url));
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        mActivity.startActivity(intent);
                    }
                });
                dialogBuilder.setOnDismissListener(new WeakReference<DialogInterface.OnDismissListener>(mDismissListener));
                dialogBuilder.showConfirm();

                if (loginHandler != null) {
                    loginHandler.onHandlerRequestCode();
                }
            }
        };
    }

    private static DialogInterface.OnDismissListener mDismissListener = new DialogInterface.OnDismissListener() {
        @Override
        public void onDismiss(DialogInterface dialog) {
            dismissLoginProgress();
        }
    };

    private static MyProgressDialog loginProgressDialog;
    private static void showLoginProgress(Activity activity) {
        if (activity == null || activity.isFinishing())
            return;
        if (loginProgressDialog == null) {
            loginProgressDialog = new MyProgressDialog(activity);
        } else {
            loginProgressDialog.cancel();
        }
        loginProgressDialog.setTitle("登录");
        loginProgressDialog.setMessage("正在登录...");
        loginProgressDialog.show();
    }

    private static void dismissLoginProgress() {
        if (loginProgressDialog != null) {
            loginProgressDialog.dismiss();
            loginProgressDialog = null;
        }
    }


    /**
     * 去跳转bind
     *
     * @param result 登陆信息
     * @return 是否强制绑定
     */
    private static void gotoBind(LoginInfoModelNew result, final Activity activity,
                                 int loginStrategy, boolean isFormOAuth2SDK) {
        if(activity == null || result == null) {
            return;
        }

        if (ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, CConstants.Group_android.ITEM_BIND_DATA_SET_USEINFO, false)) {
            UserInfoMannage.getInstance().setUser(result);
        }
        ScoreManage scoreManage = ScoreManage.getInstance(activity.getApplicationContext());
        if (scoreManage != null) {
            scoreManage.initBehaviorScore();
            scoreManage.updateScore();
        }

        boolean canGotoOneKeyLogin = ToolUtil.hasSimCard(activity)
                && loginStrategy != LoginByConstants.LOGIN_BY_CHANGE_USER
                && ConfigureCenter.getInstance().getBool("toc" ,"switch_one_click_login_new" , true);

        if(canGotoOneKeyLogin) {
            gotoBindByOneKeyPage(result, activity, loginStrategy, isFormOAuth2SDK);
        } else {
            gotoBindSelfPage(result, activity, loginStrategy, isFormOAuth2SDK);
        }
    }

    // 通过自己的界面进行一键登录
    private static void gotoBindSelfPage(LoginInfoModelNew result, final Activity activity,
                                         int loginStrategy, boolean isFormOAuth2SDK) {

        boolean isLoginByEmail = loginStrategy == ILogin.LOGIN_FLAG_XIMALAYA;

        if (activity != null) {
            if (activity instanceof LoginActivity) {
                ((LoginActivity) activity).startFragment(GetAndVerifySmsCodeFragment.newInstanceForLogin(result.getUid(),
                        result.getBizKey(), true ,isLoginByEmail, isFormOAuth2SDK));
            } else {
                Intent intent = new Intent(activity, LoginActivity.class);
                intent.putExtra("shouldBindPhone", true);
                Bundle bundle = new Bundle();
                bundle.putString("bizKey" ,result.getBizKey());
                bundle.putLong("uid", result.getUid());
                intent.putExtra("data", bundle);
                activity.startActivity(intent);
            }
        }


        BindLoginInfoModel tempInfoModule = new BindLoginInfoModel();
        tempInfoModule.setLoginInfoModel(result);

        tempInfoModule.setCurTimeStamp(System.currentTimeMillis());
        tempInfoModule.setMobile("");
        JsonUtil.toJson(tempInfoModule, new JsonUtil.IResult() {
            @Override
            public void execute(String result) {
                if (activity != null && !TextUtils.isEmpty(result)) {
                    SharedPreferencesUtil.getInstance(activity.getApplicationContext()).saveString
                            (PreferenceConstantsInHost.TINGMAIN_KEY_BINDPHONE_JSON_NEW, result);
                }
            }
        });
    }

    // 通过一键登录页面进行一键绑定
    private static void gotoBindByOneKeyPage(final LoginInfoModelNew loginResult,
                                             final Activity activity,
                                             final int loginStrategy,
                                             final boolean isFormOAuth2SDK) {
        QuickLoginUtil.getInstance().preload(ToolUtil.getCtx(), new IPreVerifyResultCallback() {
            @Override
            public void onSuccess(PreVerifyResult result) {
                if (result != null) {
                    if (activity != null) {
                        Bundle tempBundle = new Bundle();
                        tempBundle.putParcelable(BundleKeyConstants.KEY_ONE_KEY_QUICK_PRE_VERIFY, result);
                        tempBundle.putBoolean(BundleKeyConstants.KEY_ONE_KEY_QUICK_FROM_BIND, true);
                        tempBundle.putParcelable(BundleKeyConstants.KEY_ONE_KEY_QUICK_LOGIN_INFO, loginResult);
                        tempBundle.putInt(BundleKeyConstants.KEY_ONE_KEY_QUICK_LOGIN_STRATEGY, loginStrategy);
                        tempBundle.putBoolean(BundleKeyConstants.KEY_ONE_KEY_QUICK_LOGIN_FORM_OAUTH2SDK, isFormOAuth2SDK);
                        if (activity instanceof LoginActivity) {
                            OneKeyQuickLoginFragment fragment = new OneKeyQuickLoginFragment();
                            fragment.setArguments(tempBundle);
                            ((LoginActivity) activity).startFragment(fragment);
                        } else {
                            UserInfoMannage.gotoFullScreenLogin(ToolUtil.getCtx(), LoginByConstants.LOGIN_BY_DEFUALT, false, null, tempBundle, true, false);
                        }
                    }

                } else {
                    onQuickLoginError(loginResult, activity, loginStrategy, isFormOAuth2SDK, null);
                }
            }

            @Override
            public void onFailure(int code, String message) {
                onQuickLoginError(loginResult, activity, loginStrategy, isFormOAuth2SDK, null);
            }
        });
    }

    public static void onQuickLoginError(LoginInfoModelNew result, final Activity activity,
                                          int loginStrategy, boolean isFormOAuth2SDK,
                                          String message) {
        if (TextUtils.isEmpty(message)) {
            CustomToast.showFailToast(R.string.login_one_key_bind_fail);
        } else {
            CustomToast.showFailToast(message);
        }

        gotoBindSelfPage(result, activity, loginStrategy, isFormOAuth2SDK);
    }

    // 判断是否校验
    public static void goto2Verficate(LoginInfoModelNew result, Activity activity,
                                       int loginStrategy, boolean isFormOAuth2SDK) {
        VerifyIdentidyFragment verifyIdentidyFragment = createVerifyIdentidyFragment(result,
                loginStrategy, isFormOAuth2SDK);
        if (verifyIdentidyFragment == null) {
            return;
        }

        if (activity instanceof FragmentActivity) {
            boolean isFinished = false;
            if(ToolUtil.activityIsValid(activity)) {
                FragmentManager supportFragmentManager = ((FragmentActivity) activity)
                        .getSupportFragmentManager();

                if(supportFragmentManager != null) {
                    Fragment smsLoginProxyFragment =
                            supportFragmentManager
                                    .findFragmentByTag(SmsLoginDialogActivity.SMS_LOGIN_PROXY_TAG);
                    if(smsLoginProxyFragment instanceof BaseDialogFragment) {
                        ((BaseDialogFragment) smsLoginProxyFragment).dismiss();
                        isFinished = true;
                    }
                }
            }

            if(isFinished) {
                Activity optActivity = MainApplication.getMainActivity();
                if(optActivity instanceof MainActivity) {
                    ((MainActivity) optActivity).startFragment(verifyIdentidyFragment);
                }
            } else {
                startFragment((FragmentActivity) activity, verifyIdentidyFragment);
            }
        } else if (activity instanceof MainActivity) {
            ((MainActivity) activity).startFragment(verifyIdentidyFragment);
        }
    }

    public static VerifyIdentidyFragment createVerifyIdentidyFragment(LoginInfoModelNew result,
                                                                       int loginStrategy, boolean isFormOAuth2SDK) {
        if (result == null) {
            return null;
        }

        boolean isLoginByPsw = loginStrategy == ILogin.LOGIN_FLAG_XIMALAYA
                || loginStrategy == LOGIN_FLAG_PHONE;


        VerifyIdentidyFragment verifyIdentidyFragment = new VerifyIdentidyFragment();
        Bundle args = new Bundle();
        args.putString(VERIFY_IDENTIDY_PHONE_NUM, result.getMobileMask());
        args.putString(VERIFY_IDENTIDY_PHONE_NUM_REAL, result.getMobileCipher());
        args.putInt(VERIFY_IDENTIDY_FROM, FROM_LOGIN);
        args.putBoolean(VerifyIdentidyFragment.VERIFY_IDENTIDY_LOGIN_IS_PSW, isLoginByPsw);
        args.putString(BundleKeyConstants.KEY_VERIFY_BIZKEY, result.getBizKey());
        args.putBoolean(BundleKeyConstants.KEY_LOGIN_FROM_OAUTH_SDK, isFormOAuth2SDK);
        verifyIdentidyFragment.setArguments(args);
        return verifyIdentidyFragment;
    }

    private static void startFragment(FragmentActivity activity, Fragment fragment) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed())
            return;
        if (fragment == null) return;
        FragmentTransaction ft = activity.getSupportFragmentManager().beginTransaction();
        ft.add(android.R.id.content, fragment);
        ft.addToBackStack(null);
        ft.commitAllowingStateLoss();
    }


    public static SharedPreferencesUtil getOnlyUseMainProcessSharePre(Context mContext) {
        SharedPreferencesUtil onlyUseMainProcessSharePre;
        try {
            onlyUseMainProcessSharePre = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().
                    getOnlyUseMainProcessSharePre(mContext);
        } catch (Exception e) {
            e.printStackTrace();

            onlyUseMainProcessSharePre = SharedPreferencesUtil.getInstance(mContext);
        }

        return onlyUseMainProcessSharePre;

    }
}
