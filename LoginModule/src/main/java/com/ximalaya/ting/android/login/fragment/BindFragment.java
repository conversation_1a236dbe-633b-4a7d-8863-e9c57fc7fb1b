package com.ximalaya.ting.android.login.fragment;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Html;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.ListView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder.DialogCallback;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.model.account.HomePageModel;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.login.adapter.BindListAdapter;
import com.ximalaya.ting.android.login.model.SettingInfo;
import com.ximalaya.ting.android.loginservice.BaseResponse;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.loginservice.LoginService;
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin;
import com.ximalaya.ting.android.loginservice.base.ILogin;
import com.ximalaya.ting.android.loginservice.loginstrategy.QQBind;
import com.ximalaya.ting.android.loginservice.loginstrategy.SinaWbBind;
import com.ximalaya.ting.android.loginservice.loginstrategy.WXBind;
import com.ximalaya.ting.android.loginservice.model.BindStatus;
import com.ximalaya.ting.android.loginservice.model.BindStatusResult;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.Nullable;



/**
 * 我 - 绑定页面
 *
 * <AUTHOR> on 2017/6/29.
 */
public class BindFragment extends BaseFragment2 implements IFragmentFinish {
    public static final String flag = "com.ximalaya.ting.android.fragmemt.myspace.child.bind";

    @SuppressLint("StaticFieldLeak")
    @Nullable
    private static LoadingDialog pd;
    @Nullable
    private ListView mBindListView;
    @Nullable
    private BindListAdapter mBindAdapter;
    @Nullable
    private List<SettingInfo> mBindInfos;
    private String mMobile = "";
    private boolean isFirstLoading = true;

    @SuppressWarnings("NullAway")
    public BindFragment() {
        super(AppConstants.isPageCanSlide, null);
    }

    public static BindFragment newInstance(Bundle bundle) {
        BindFragment bindFragment = new BindFragment();
        bindFragment.setArguments(bundle);
        return bindFragment;
    }

    @Override
    protected String getPageLogicName() {
        return "bind";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("账号绑定");
        mMobile = getArguments() == null ? "" : getArguments().getString(
                "mobile");

        mBindListView = (ListView) findViewById(R.id.login_bind_list);

        mBindInfos = new ArrayList<>();
        initBindInfo();

        TextView bindHint = (TextView) findViewById(R.id.login_tv_bind_hint);
        CharSequence richText = Html.fromHtml("标记为“非登录手机”字样的号码仅用于验证身份，不能作为登录目前账号的手机号使用。<a href='http://m.ximalaya.com/carnival/imgShare/277'>查看如何设置为“登录手机”</a>");
        bindHint.setText(richText);
        bindHint.setMovementMethod(LinkMovementMethod.getInstance());

        mBindAdapter = new BindListAdapter(getActivity(), mBindInfos);
        mBindListView.setAdapter(mBindAdapter);
        initListener();
    }

    private void initBindInfo() {
        String[] strBind = getResourcesSafe().getStringArray(
                R.array.login_bind_setting_list);
        if (mBindInfos != null) {
            mBindInfos.clear();
            for (int i = 0; i < strBind.length; i++) {
                SettingInfo info = new SettingInfo();
                info.setNameWake(strBind[i]);

                if (i == 0) {
                    if (StringUtil.isNotBlank(mMobile)) {
                        info.setSetting(true);
                        if (!TextUtils.isEmpty(mMobile) && mMobile.length() > 7) {
                            mMobile = StringUtil.getGonePhoneNum(mMobile);
                        }
                        info.setTextWake(mMobile);
                    } else {
                        info.setSetting(false);
                        info.setTextWake("绑定");
                    }
                } else {
                    info.setSetting(false);
                    info.setTextWake("绑定");
                }

                mBindInfos.add(info);
            }
        }
    }

    private void initListener() {
        if (mBindListView != null) {
            mBindListView.setOnItemClickListener(new OnItemClickListener() {

                @Override
                public void onItemClick(AdapterView<?> parent, final View view,
                                        int position, long id) {
                    if (mBindInfos == null || mBindInfos.isEmpty())
                        return;

                    final SettingInfo info = mBindInfos.get(position);
                    switch (position) {
                        case 0:     // 手机
                            if (!mBindInfos.get(position).isSetting()) {
                                if (UserInfoMannage.hasLogined()) {
                                    startFragment(GetAndVerifySmsCodeFragment.newInstance(false, AppConstants.FROM_BIND_PAGE));
                                } else {
                                    UserInfoMannage.gotoLogin(getActivity());
                                }

                            } else {
                                String phoneNum;
                                if (!TextUtils.isEmpty(mMobile)) {
                                    phoneNum = StringUtil.getGonePhoneNum(mMobile);
                                } else {
                                    phoneNum = info.getTextWake();
                                }
                                UnBindFragment fra = UnBindFragment.newInstance(phoneNum);
                                fra.setCallbackFinish(BindFragment.this);
                                startFragment(fra, view);
                            }
                            break;
                        case 2:   // qq

                            if (info.isSetting() && !info.isExpired()) {
                                new DialogBuilder(getActivity()).setMessage("确定要解绑?")
                                        .setCancelBtn("取消")
                                        .setOkBtn("解绑", new DialogCallback() {

                                            @Override
                                            public void onExecute() {
                                                unBindThird(ILogin.LOGIN_FLAG_QQ);
                                            }

                                        }).showConfirm();
                            } else {
                                bindQQ(getActivity(), responseCallBack);
                            }
                            break;
                        case 3: // 微博
                            if (info.isSetting()) {
                                new DialogBuilder(getActivity()).setMessage("确定要解绑?")
                                        .setCancelBtn("取消")
                                        .setOkBtn("解绑", new DialogCallback() {

                                            @Override
                                            public void onExecute() {
                                                unBindThird(ILogin.LOGIN_FLAG_WEIBO);
                                            }

                                        }).showConfirm();
                            } else {
                                weiboBind(getActivity(), responseCallBack);
                            }
                            break;
                        case 1: // 微信
                            if (info.isSetting() && !info.isExpired()) {
                                new DialogBuilder(getActivity()).setMessage("确定要解绑?")
                                        .setCancelBtn("取消")
                                        .setOkBtn("解绑", new DialogCallback() {

                                            @Override
                                            public void onExecute() {
                                                unBindThird(ILogin.LOGIN_FLAG_WEIXIN);
                                            }

                                        }).showConfirm();
                            } else {
                                doBindWX(getActivity(), responseCallBack);
                            }

                            break;
                        default:
                            break;
                    }
                }
            });
        }
    }

    private IDataCallBack<BaseResponse> responseCallBack = new IDataCallBack<BaseResponse>() {
        @Override
        public void onSuccess(BaseResponse object) {
            if(canUpdateUi()) {
                cancleProgressDialog();

                if(object != null) {
                    if(object.getRet() == 0) {
                        CustomToast.showSuccessToast("绑定成功");
                        loadData();
                    } else {
                        if(TextUtils.isEmpty(object.getMsg())) {
                            CustomToast.showFailToast(TextUtils.isEmpty(object.getMsg()) ?
                                    "网络错误,请重试" : object.getMsg());
                        } else {
                            UnBindMessageDialog unbindDialogFragment =
                                    UnBindMessageDialog.newInstance(object.getRet() ,object.getMsg());
                            unbindDialogFragment.show(getChildFragmentManager(), UnBindMessageDialog.TAG);
                        }
                    }
                }
            }
        }

        @Override
        public void onError(int code, String message) {
            cancleProgressDialog();

            CustomToast.showFailToast(message);
        }
    };

    public static void weiboBind(final Activity activity , final IDataCallBack<BaseResponse> callBack) {
        if(!ToolUtil.isInstalledByPackageName(activity ,"com.sina.weibo")) {
            if(callBack != null) {
                callBack.onError(1 ,"请安装微博");
            }
            return;
        }

        showProgressDialog(activity);

        LoginService.getInstance().bindWeibo(activity, new SinaWbBind(), new IDataCallBackUseLogin<BaseResponse>() {
            @Override
            public void onSuccess(@androidx.annotation.Nullable BaseResponse object) {
                cancleProgressDialog();

                if(callBack != null) {
                    callBack.onSuccess(object);
                }
            }

            @Override
            public void onError(int code, String message) {
                cancleProgressDialog();
                if(callBack != null) {
                    callBack.onError(code ,message);
                }

                CustomToast.showFailToast(message);
            }
        });
    }

    private void unBindThird(int thirdPartyId) {
        Map<String ,String> maps = new HashMap<>();
        LoginRequest.unBindThird(LoginService.getInstance().getRquestData(), thirdPartyId, maps,
                new IDataCallBackUseLogin<BaseResponse>() {
            @Override
            public void onSuccess(BaseResponse object) {
                if (canUpdateUi()) {
                    if (object == null || object.getRet() != 0) {
                        CustomToast.showFailToast("解绑失败,请稍后重试");
                        return;
                    }

                    loadData();
                }
            }

            @Override
            public void onError(int code, String message) {
                CustomToast.showFailToast(TextUtils.isEmpty(message) ? "解绑失败,请稍后重试" : message);
            }
        });
    }

    private static void showProgressDialog(Activity activity) {
        pd = new LoadingDialog(activity);
        pd.setTitle("正在绑定，请稍候!");
        pd.showIcon(true);
        pd.showBg(true);
        pd.show();
    }

    private static void cancleProgressDialog() {
        if (pd != null) {
            pd.cancel();
            pd = null;
        }
    }

    @Override
    public void onDestroy() {
        cancleProgressDialog();
        super.onDestroy();
    }

    public static void bindQQ(final Activity activity, final IDataCallBack<BaseResponse> callBack) {
        showProgressDialog(activity);
        LoginService.getInstance().bindQQ(activity, new QQBind(), new IDataCallBackUseLogin<BaseResponse>() {
            @Override
            public void onSuccess(@androidx.annotation.Nullable BaseResponse object) {
                cancleProgressDialog();

                if(callBack != null) {
                    callBack.onSuccess(object);
                }
            }

            @Override
            public void onError(int code, String message) {
                cancleProgressDialog();

                if(callBack != null) {
                    callBack.onError(code ,message);
                }
            }
        });
    }

    private Map<String, String> parseQQParams(JSONObject json) throws JSONException {
        Map<String ,String> requestParams = new HashMap<>();

        requestParams.put("thirdpartyId" ,ILogin.LOGIN_FLAG_QQ + "");
        requestParams.put("accessToken" ,json.getString("access_token"));
        if(!TextUtils.isEmpty(json.optString("expires_in"))) {
            requestParams.put("expireIn" ,json.optString("expires_in"));
        }

        if(!TextUtils.isEmpty(json.optString("openid"))) {
            requestParams.put("openId" ,json.optString("openid"));
        }

        return requestParams;
    }

    @Override
    protected void loadData() {
        getBindState();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }

    public static void doBindWX(final Activity activity ,final IDataCallBack<BaseResponse> callBack) {
        showProgressDialog(activity);

        LoginService.getInstance().bindWx(activity, new WXBind(), new IDataCallBackUseLogin<BaseResponse>() {
            @Override
            public void onSuccess(@androidx.annotation.Nullable BaseResponse object) {
                cancleProgressDialog();
                if(callBack != null) {
                    callBack.onSuccess(object);
                }
            }

            @Override
            public void onError(int code, String message) {
                cancleProgressDialog();

                if(callBack != null) {
                    callBack.onError(code ,message);
                }
            }
        });
    }

    private void getBindState() {
        if (!UserInfoMannage.hasLogined()) {
            CustomToast.showFailToast("请登录");
            finish();
            return;

        }

        Map<String, String> homeParams = new HashMap<>();
        homeParams.put("uid", UserInfoMannage.getUid() + "");
        homeParams.put("device", "android");

        try {
            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().getHomePage(homeParams, new IDataCallBack<HomePageModel>() {
                @Override
                public void onSuccess(@Nullable final HomePageModel object) {
                    doAfterAnimation(new IHandleOk() {

                        @Override
                        public void onReady() {
                            if (object != null) {
                                if (mBindInfos != null && mBindInfos.size() > 0) {
                                    SettingInfo phoneInfo = mBindInfos.get(0);
                                    String result = object.getMobile();
                                    if (TextUtils.isEmpty(result)) {
                                        result = object.getPhone();
                                    }

                                    if (!TextUtils.isEmpty(result)) {
                                        if (result.length() > 7) {
                                            mMobile = StringUtil.getGonePhoneNum(result);
                                        } else {
                                            mMobile = result;
                                        }
                                        phoneInfo.setSetting(true);
                                        phoneInfo.setTextWake(mMobile + (object.isMobileLoginable() ? "" : "    非登录手机"));
                                    } else {
                                        phoneInfo.setSetting(false);
                                        phoneInfo.setTextWake("绑定");
                                    }
                                    if (mBindAdapter != null) {
                                        mBindAdapter.notifyDataSetChanged();
                                    }
                                }
                                try {
                                    refreshLoginInfo(object);
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    });
                }

                @Override
                public void onError(int code, String message) {
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }


        LoginRequest.getBindStatus(LoginService.getInstance().getRquestData(), null, new IDataCallBackUseLogin<BindStatusResult>() {
            @Override
            public void onSuccess(final BindStatusResult object) {
                if(object == null || object.getCode() != 0) {
                    return;
                }

                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if(!canUpdateUi()) {
                            return;
                        }

                        if (mBindInfos != null) {
                            initBindInfo();
                            if(!ToolUtil.isEmptyCollects(object.getBindStatuses())) {
                                for (BindStatus bindStatus : object.getBindStatuses()) {
                                    if (TextUtils.isEmpty(bindStatus.getThirdpartyName())) continue;

                                    switch (bindStatus.getThirdpartyName()) {
                                        case "tSina": {
                                            if (mBindInfos.get(3) != null) {
                                                mBindInfos.get(3).setSetting(true);
                                                mBindInfos.get(3).setExpired(false);
                                                mBindInfos.get(3).setTextWake(bindStatus.getNickname());
                                                mBindInfos.get(3).setThirdPartId(ILogin.LOGIN_FLAG_WEIBO + "");
                                            }
                                        }
                                        break;

                                        case "qq":
                                        case "qzone": {
                                            if (mBindInfos.get(2) != null) {
                                                mBindInfos.get(2).setSetting(true);
                                                mBindInfos.get(2).setExpired(false);
                                                mBindInfos.get(2).setTextWake(bindStatus.getNickname());
                                                mBindInfos.get(2).setThirdPartId(ILogin.LOGIN_FLAG_QQ + "");
                                            }
                                            break;
                                        }
                                        case "weixin": {
                                            if (mBindInfos.get(1) != null) {
                                                mBindInfos.get(1).setSetting(true);
                                                mBindInfos.get(1).setExpired(false);
                                                mBindInfos.get(1).setTextWake(bindStatus.getNickname());
                                                mBindInfos.get(1).setThirdPartId(ILogin.LOGIN_FLAG_WEIXIN + "");
                                            }
                                        }
                                        break;
                                        default:
                                            break;
                                    }
                                }
                            }
                        }

                        if (mBindAdapter != null) {
                            mBindAdapter.notifyDataSetChanged();
                        }
                        isFirstLoading = false;
                    }
                });
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.login_fra_bind;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.login_title_bar;
    }

    @Override
    public void onFinishCallback(Class<?> cls, int fid, Object... objects) {
        if (!canUpdateUi()) return;

        if (cls == GetAndVerifySmsCodeFragment.class) {
            if (objects != null && objects.length > 0 && objects[0] != null && objects[0] instanceof String) {
                mMobile = (String) objects[0];
                if (TextUtils.isEmpty(mMobile)) {
                    mMobile = "" + UserInfoMannage.getInstance().getUser().getMobile();
                }
                if (!TextUtils.isEmpty(mMobile) && mMobile.length() > 7) {
                    mMobile = StringUtil.getGonePhoneNum(mMobile);
                }
                if (mBindInfos != null && mBindInfos.get(0) != null) {
                    mBindInfos.get(0).setTextWake(mMobile);
                    mBindInfos.get(0).setSetting(true);
                }
                if (mBindAdapter != null) {
                    mBindAdapter.notifyDataSetChanged();
                }
            } else {
                if (mBindInfos != null && mBindInfos.get(0) != null) {
                    mBindInfos.get(0).setTextWake("");
                    mBindInfos.get(0).setSetting(false);
                }
                mMobile = "";
                if (mBindAdapter != null) {
                    mBindAdapter.notifyDataSetChanged();
                }
            }
        }
    }

    @Override
    public void onStart() {

        super.onStart();
    }

    @Override
    protected boolean setNetworkErrorButtonVisiblity() {
        // TODO Auto-generated method stub
        return false;
    }

    @Override
    protected boolean onPrepareNoContentView() {
        // TODO Auto-generated method stub
        return false;
    }

    @Override
    protected void onNoContentButtonClick(View view) {
        // TODO Auto-generated method stub

    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38329;
        super.onMyResume();
        if (!isFirstLoading) {
            if (UserInfoMannage.hasLogined()) {
                loadData();
            } else {
                if (mBindInfos != null && mBindInfos.get(0) != null && mBindAdapter != null) {
                    mBindInfos.get(0).setTextWake("");
                    mBindInfos.get(0).setSetting(false);
                    mBindAdapter.notifyDataSetChanged();
                }
            }
        }
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    private void refreshLoginInfo(HomePageModel homePageModel)
            throws JSONException {
        setPersonalInfo(homePageModel);
    }

    private void setPersonalInfo(HomePageModel model) {
        LoginInfoModelNew loginInfo = UserInfoMannage.getInstance().getUser();

        if(loginInfo == null) {
            return;
        }

        if (!TextUtils.isEmpty(model.getMobile())) {
            loginInfo.setMobile(model.getMobile());
        } else if (!TextUtils.isEmpty(model.getPhone())) {
            loginInfo.setMobile(model.getPhone());
        }
        if (!TextUtils.isEmpty(model.getEmail()))
            loginInfo.setEmail(model.getEmail());
        UserInfoMannage.getInstance().setIsVip(model.isVip());
        UserInfoMannage.getInstance().setUser(loginInfo);
    }
}
