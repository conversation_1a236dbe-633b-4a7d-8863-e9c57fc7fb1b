package com.ximalaya.ting.android.login.fragment.multiAccount

import android.graphics.Color
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.login.R
import com.ximalaya.ting.android.login.model.MultiAccountInfoForDialog
import com.ximalaya.ting.android.xmtrace.XMTraceApi

class MultiAccountWarningDialogForVipPurchase private constructor(data: MultiAccountInfoForDialog): BaseMultiAccountWarningDialog(data) {

    companion object {
        fun getDialog(data: MultiAccountInfoForDialog): MultiAccountWarningDialogForVipPurchase {
            return MultiAccountWarningDialogForVipPurchase(data)
        }
    }

    private val adapter: MultiAccountWarningDialogForVipPurchaseAdapter by lazy {
        MultiAccountWarningDialogForVipPurchaseAdapter(data)
    }

    override fun getAdapter(): MultiAccountWarningAdapter {
        return adapter
    }

    override fun getItemDecoration(): RecyclerView.ItemDecoration? {
        return ItemSpace()
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.login_fra_multi_account_warning_vip_purcharse
    }

    override fun initUi(view: View?, savedInstanceState: Bundle?) {
        super.initUi(view, savedInstanceState)
        // 商品半浮层-多账号登录弹框  弹框展示
        XMTraceApi.Trace()
                .setMetaId(44841)
                .setServiceId("dialogView")
                .put("currPage", "")
                .createTrace()
    }

    override fun doOnBtn1Clicked(view: View?, tag: Any?, tag2: Any?): Boolean {
        // 商品半浮层-多账号登录弹框-按钮  弹框控件点击
        XMTraceApi.Trace()
                .setMetaId(44842)
                .setServiceId("dialogClick")
                .put("currPage", "")
                .put("Item", "取消")
                .createTrace()
        resultCode = BaseMultiAccountWarningDialog.CODE_CONTINUE_OPERATION
        dismiss()
        return true
    }

    override fun doOnBtn2Clicked(view: View?, tag: Any?, tag2: Any?): Boolean {
        val result: Boolean = super.doOnBtn2Clicked(view, tag, tag2)
        // 商品半浮层-多账号登录弹框-按钮  弹框控件点击
        XMTraceApi.Trace()
                .setMetaId(44842)
                .setServiceId("dialogClick")
                .put("currPage", "")
                .put("Item", "切换账号")
                .createTrace()
        return result
    }

    class MultiAccountWarningDialogForVipPurchaseAdapter(data: MultiAccountInfoForDialog)
        : BaseMultiAccountWarningDialog.MultiAccountWarningAdapter(data) {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MultiAccountWarningViewHolder {
            val view: View = LayoutInflater.from(BaseApplication.getMyApplicationContext()).inflate(R.layout.login_item_multi_account_warning_vip_purchase, null)
            view.layoutParams?.width = ViewGroup.LayoutParams.MATCH_PARENT
            view.layoutParams?.height = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 108f)
            return MultiAccountWarningViewHolder(view)
        }

        override fun customBindViewHolder(info: MultiAccountInfoForDialog.AccountInfo, holder: MultiAccountWarningViewHolder, position: Int) {
            if (info.localIsCurrentAccount) {
                ViewStatusUtil.setVisible(View.VISIBLE, holder.label, holder.mask)
                ViewStatusUtil.setText(holder.label, "当前登录")
                ViewStatusUtil.setTextColor(holder.label, Color.parseColor("#FFFFFF"))
                ViewStatusUtil.setBackgroundDrawableRes(R.drawable.login_bg_rect_ff3e4e_fe9449_radius_0_12_0_10, holder.label)
                ViewStatusUtil.setBackgroundColorRes(R.color.login_color_40f6aa7b, holder.divider)
            } else {
                ViewStatusUtil.setVisible(View.GONE, holder.mask)
                ViewStatusUtil.setVisible(View.VISIBLE, holder.label)
                ViewStatusUtil.setText(holder.label, "常用账号")
                ViewStatusUtil.setTextColor(holder.label, Color.parseColor("#FA2800"))
                ViewStatusUtil.setBackgroundDrawableRes(R.drawable.login_bg_rect_ffeee9_radius_0_12_0_10, holder.label)
                ViewStatusUtil.setBackgroundColorRes(com.ximalaya.ting.android.host.R.color.host_color_f6f7f8, holder.divider)
            }
        }
    }

    private class ItemSpace : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
            super.getItemOffsets(outRect, view, parent, state)
            outRect.bottom = BaseUtil.dp2px(view.context, 8f)
            val position = parent.getChildAdapterPosition(view)
            outRect.top = if (position != 0) BaseUtil.dp2px(view.context, 8f) else 0
        }
    }
}