package com.ximalaya.ting.android.login.request;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.base.BaseModel;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.login.constants.LoginUrlConstants;
import com.ximalaya.ting.android.login.model.MultiAccountCombineResult;
import com.ximalaya.ting.android.login.model.MultiAccountInfo;
import com.ximalaya.ting.android.login.model.NewUserLoginActivityInfo;
import com.ximalaya.ting.android.login.model.VerifyNicknameModel;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by le.xin on 2020/3/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class LoginCommonRequest extends CommonRequestM {

    /**
     * 注册手机 验证昵称
     *
     * @param params
     * @param callback
     */
    public static void checkNickname(Map<String, String> params, IDataCallBack<VerifyNicknameModel> callback) {
        basePostRequest(UrlConstants.getInstanse().checkNickname(), params,
                callback, new IRequestCallBack<VerifyNicknameModel>() {

                    @Override
                    public VerifyNicknameModel success(String content)
                            throws Exception {
                        return new Gson().fromJson(content,
                                new TypeToken<VerifyNicknameModel>() {
                                }.getType());
                    }
                });
    }

    /**
     * 注册手机 设置昵称
     *
     * @param params
     * @param callback
     */
    public static void setNickname(Map<String, String> params,
                                   IDataCallBack<JSONObject> callback) {
        basePostRequest(UrlConstants.getInstanse().setNickname(), params,
                callback, new IRequestCallBack<JSONObject>() {

                    @Override
                    public JSONObject success(String content) throws Exception {
                        return new JSONObject(content);
                    }
                });
    }

    /**
     * 注册手机 设置密码
     *
     * @param params
     * @param callback
     */
    public static void setPassword(Map<String, String> params,
                                   IDataCallBack<JSONObject> callback) {
        basePostRequest(UrlConstants.getInstanse().setPassword(), params,
                callback, new IRequestCallBack<JSONObject>() {

                    @Override
                    public JSONObject success(String content) throws Exception {
                        return new JSONObject(content);
                    }
                });
    }

    public static void checkIsSwitchLogin(Map<String, String> params, IDataCallBack<BaseModel> callback) {
        baseGetRequest(LoginUrlConstants.getInstanse().getCheckSwitchLoginUrl(), params, callback, new IRequestCallBack<BaseModel>() {
            @Override
            public BaseModel success(String content) throws Exception {
                if (TextUtils.isEmpty(content)) {
                    return null;
                }
                return new Gson().fromJson(content, BaseModel.class);
            }
        });
    }

    public static void getSyncInfoToken(int thirdpartyId, IDataCallBack<String> callback) {
        String url = LoginUrlConstants.getInstanse().getSyncInfoTokenUrl(thirdpartyId);
        Map<String, String> params = new HashMap<>();
        params.put("uid", String.valueOf(UserInfoMannage.getUid()));
        basePostRequest(url, params, callback, new IRequestCallBack<String>() {
            @Override
            public String success(String content) {
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(content);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (jsonObject == null) {
                    return null;
                }

                return jsonObject.optString("nonce", "");
            }
        });
    }

    public static void getMultiAccountInfo(IDataCallBack<MultiAccountInfo> callback) {
        Map<String, String> params = new HashMap<>();
        baseGetRequest(LoginUrlConstants.getInstanse().multiAccountInfo(), params, callback, new IRequestCallBack<MultiAccountInfo>() {
            @Override
            public MultiAccountInfo success(String content) {
                return new Gson().fromJson(content, MultiAccountInfo.class);
            }
        });
    }

    public static void confirmMultiAccountCombine(Map<String, String> params, IDataCallBack<MultiAccountCombineResult> callBack) {
        basePostRequest(LoginUrlConstants.getInstanse().confirmMultiAccountCombine(), null, callBack, new IRequestCallBack<MultiAccountCombineResult>() {
            @Override
            public MultiAccountCombineResult success(String content) {
                return new Gson().fromJson(content, MultiAccountCombineResult.class);
            }
        }, new JSONObject(params).toString(), BaseCall.DEFAULT_TIMEOUT);

    }

    public static void getNewUserLoginAct(String source, IDataCallBack<NewUserLoginActivityInfo> callBack) {
        String url = ToolUtil.addTsToUrl(LoginUrlConstants.getInstanse().getNewUserLoginActUrl());
        Map<String, String> params = new HashMap<>();
        params.put("source", source);
        getData(url, params, NewUserLoginActivityInfo.class, callBack);
    }
}
