package com.ximalaya.ting.android.login.fragment

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.text.method.LinkMovementMethod
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.ximalaya.ting.android.basequicklogin.IVerifyResultCallback
import com.ximalaya.ting.android.basequicklogin.PreVerifyResult
import com.ximalaya.ting.android.basequicklogin.QuickLoginUtil
import com.ximalaya.ting.android.basequicklogin.VerifyResult
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.StringUtil
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.activity.PrivacyHintActivity
import com.ximalaya.ting.android.host.activity.login.IChooseCountryListener
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog
import com.ximalaya.ting.android.host.manager.account.LoginUtil
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.login.ILoginFragmentAction
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew
import com.ximalaya.ting.android.host.model.account.InternationalCodeModel
import com.ximalaya.ting.android.host.util.LoginTraceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.login.R
import com.ximalaya.ting.android.login.util.LoginFragmentUtil
import com.ximalaya.ting.android.login.view.LoginHintChooseRuleDialog
import com.ximalaya.ting.android.loginservice.LoginRequest
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.android.xmutil.SystemServiceManager
import java.lang.ref.WeakReference

/**
 * Created by nali on 2022/2/24.
 * 引导登录页
 * <AUTHOR>
 */
class GuideLoginFragment : BaseLoginFragment(false, null), View.OnClickListener,
    IChooseCountryListener, LoginUtil.IOneKeyLoginInitCallBack {

    private lateinit var mOneKeyLoginLay: FrameLayout
    private lateinit var mPhoneLoginLay: LinearLayout
    private lateinit var mOneKeyPhoneNum: TextView

    private lateinit var mRegionNumber: TextView
    private lateinit var mLoginPhoneNum: EditText

    private lateinit var mLoginBtn: Button
    private lateinit var mWalkBtn: TextView

    private lateinit var mHintState: ImageView
    private lateinit var mRegisetHint: TextView

    private lateinit var mLoginWx: ImageView
    private lateinit var mLoginQQ: ImageView
    private lateinit var mLoginOther: TextView

    private lateinit var mLoginCenterContent: LinearLayout

    private var mUseHasSet: Boolean = false
    private var mPreVerifyResult: PreVerifyResult? = null
    private var mProgressDialog : LoadingDialog? = null

    private var mVerifyResult : VerifyResult? = null

    override fun initUi(savedInstanceState: Bundle?) {
        super.initUi(savedInstanceState)

        mOneKeyLoginLay = findViewById(R.id.login_one_key_lay)
        mPhoneLoginLay = findViewById(R.id.login_phone_lay)
        mOneKeyPhoneNum = findViewById(R.id.login_one_key_number)
        mRegionNumber = findViewById(R.id.login_region_number)
        mLoginPhoneNum = findViewById(R.id.login_username)
        mLoginBtn = findViewById(R.id.login_login)
        mWalkBtn = findViewById(R.id.login_walk)
        mHintState = findViewById(R.id.login_login_hint_state)
        mRegisetHint = findViewById(R.id.login_regiset_hint)
        mLoginWx = findViewById(R.id.login_wechat)
        mLoginQQ = findViewById(R.id.login_qq)
        mLoginOther = findViewById(R.id.login_tv_other_method_login_btn)
        mLoginCenterContent = findViewById(R.id.login_center_content_lay)

        mOneKeyLoginLay.setOnClickListener(this)
        mRegionNumber.setOnClickListener(this)
        mLoginBtn.setOnClickListener(this)
        mWalkBtn.setOnClickListener(this)
        mHintState.setOnClickListener(this)
        mLoginWx.setOnClickListener(this)
        mLoginQQ.setOnClickListener(this)
        mLoginOther.setOnClickListener(this)
        LoginUtil.removePhoneNumSpace(mLoginPhoneNum)
        mLoginPhoneNum.addTextChangedListener(mPhoneNumWatcher)

        chooseNormalPhoneLogin(false)

        // 新激活强引导登录页  页面展示
        XMTraceApi.Trace()
            .pageView(23781, "first_login_guide")
            .put("currPage", "first_login_guide")
            .put("isTel", if (ToolUtil.hasSimCard(ToolUtil.getCtx())) "1" else "0")
            .createTrace()

        fitSmallScreen()
    }

    // 适配小屏
    private fun fitSmallScreen() {
        val bottomMargin = BaseUtil.dp2px(ToolUtil.getCtx(), (45 + 28 + 66 + 43).toFloat())
        mLoginCenterContent?.post {
            if ((mLoginCenterContent.bottom + bottomMargin) > BaseUtil.getScreenHeight(ToolUtil.getCtx())) {
                val topMargin =
                    (mLoginCenterContent.bottom + bottomMargin) - BaseUtil.getScreenHeight(ToolUtil.getCtx())

                val layoutParams = mLoginCenterContent.layoutParams
                if (layoutParams is ViewGroup.MarginLayoutParams) {
                    var realTop = BaseUtil.dp2px(ToolUtil.getCtx(), 200F) - topMargin
                    if (realTop < BaseUtil.dp2px(ToolUtil.getCtx(), 100F)) {
                        realTop = BaseUtil.dp2px(ToolUtil.getCtx(), 100F)
                    }
                    layoutParams.topMargin = realTop;
                    mLoginCenterContent.layoutParams = layoutParams
                }
            }
        }
    }

    override fun loadData() {
        super.loadData()

        if (PrivacyHintActivity.sPreVerifyResult != null) {
            onVerifyResultSuccess(PrivacyHintActivity.sPreVerifyResult)
        } else {
            PrivacyHintActivity.sOneKeyLoginInitCallBack = this
        }
    }

    // 当一键登录预加载成功
    private fun onVerifyResultSuccess(preVerifyResult: PreVerifyResult) {
        if (mUseHasSet) {
            return
        }

        mPreVerifyResult = preVerifyResult

        mPreVerifyResult?.let {
            QuickLoginUtil.getInstance().verify(ToolUtil.getCtx(), object : IVerifyResultCallback {
                override fun onSuccess(result: VerifyResult?) {
                    if (mUseHasSet) {
                        return
                    }

                    mVerifyResult = result
                    if (result != null) {
                        val spannableStringBuilder = LoginUtil.createLoginSpannableStrUseOneKeyQuick(mContext, true, it.protocolName, it.protocolUrl, com.ximalaya.ting.android.host.R.color.host_color_ffffff)
                        mRegisetHint.text = spannableStringBuilder
                        mRegisetHint.movementMethod = LinkMovementMethod.getInstance()
                        mRegisetHint.highlightColor = Color.TRANSPARENT

                        mPhoneLoginLay.visibility = View.GONE
                        mOneKeyLoginLay.visibility = View.VISIBLE

                        mOneKeyPhoneNum.text = it.number

                        mLoginBtn.text = "本机号码一键登录"
                        updateLoginBtnEnable(true)

                        hideSoftInput(this@GuideLoginFragment)

                        LoginTraceUtil.traceOnOneKeyLoginShowOrError("new")
                    } else {
                        LoginTraceUtil.traceOnLoginResult("failed", "new",
                            LoginTraceUtil.QuickLoginError.VERIFY_ERROR.errorCode,
                            LoginTraceUtil.QuickLoginError.VERIFY_ERROR.errorMessages)

                        verifyError(false)
                    }
                }

                override fun onFailure(code: Int, message: String?) {
                    Logger.log("QuickLoginFragment : onFailure code=$code  message=$message")
                    LoginTraceUtil.traceOnLoginResult("failed", "new", code, message)

                    verifyError(false)
                }
            })
        }
    }

    private fun updateLoginBtnEnable(enable : Boolean) {
        mLoginBtn.isEnabled = enable
        mLoginBtn.alpha = if (enable) 1.0f else 0.4f
    }

    // 当用户选择不使用一键登录
    private fun chooseNormalPhoneLogin(setState : Boolean = true) {
        if (setState) {
            mUseHasSet = true
        }

        val spannableStringBuilder = LoginUtil.createLoginSpannableStr(mContext, true, com.ximalaya.ting.android.host.R.color.host_color_ffffff)
        mRegisetHint.text = spannableStringBuilder
        mRegisetHint.movementMethod = LinkMovementMethod.getInstance()
        mRegisetHint.highlightColor = Color.TRANSPARENT

        mPhoneLoginLay.visibility = View.VISIBLE
        mOneKeyLoginLay.visibility = View.GONE

        mLoginBtn.text = "获取验证码"
        mLoginPhoneNum.text?.let {
            updateLoginBtnEnable(it.isNotEmpty())
        }

    }

    private fun hideSoftInput(fragment: Fragment) {
        val activity: Activity? = fragment.activity
        if (activity == null || activity.currentFocus == null) return
        SystemServiceManager.hideSoftInputFromWindow(
            activity,
            activity.currentFocus!!.windowToken, InputMethodManager.HIDE_NOT_ALWAYS
        )
    }

    private fun showSoftKeyboard() {
        mLoginPhoneNum.requestFocus()
        SystemServiceManager.showSoftInput(mLoginPhoneNum)
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.login_guide_lay
    }

    override fun onClick(v: View?) {
        when (v) {
            mOneKeyLoginLay -> {
                chooseNormalPhoneLogin()
                // 显示软键盘
                showSoftKeyboard()
            }
            mRegionNumber -> showChooseCountry()
            mWalkBtn -> startMainActivity()
            mHintState -> hintStateChange()
            mLoginWx -> doLoginWithWeiXin()
            mLoginQQ -> doLoginWithQQ()
            mLoginOther -> goLoginFragment()
            mLoginBtn -> doLogin()
        }
    }

    override fun doLoginWithWeiXin() {
        super.doLoginWithWeiXin()

        // 新激活强引导登录页-其他登录方式  点击事件
        XMTraceApi.Trace()
            .click(23785)
            .put("Item", "weixin")
            .put("currPage", "first_login_guide")
            .put("isTel", if (ToolUtil.hasSimCard(ToolUtil.getCtx())) "1" else "0")
            .createTrace()
    }

    override fun doLoginWithQQ() {
        super.doLoginWithQQ()

        // 新激活强引导登录页-其他登录方式  点击事件
        XMTraceApi.Trace()
            .click(23785)
            .put("Item", "QQ")
            .put("currPage", "first_login_guide")
            .put("isTel", if (ToolUtil.hasSimCard(ToolUtil.getCtx())) "1" else "0")
            .createTrace()
    }

    private fun hintStateChange() {
        mHintState.isSelected = !mHintState.isSelected
        // 新激活强引导登录页-隐私协议  点击事件
        XMTraceApi.Trace()
            .click(32419)
            .put("status", if(mHintState.isSelected) "1" else "0")
            .put("currPage", "first_login_guide")
            .put("isTel", if (ToolUtil.hasSimCard(ToolUtil.getCtx())) "1" else "0")
            .createTrace()
    }

    private fun goLoginFragment() {
        val tempArg = if(arguments != null) Bundle(arguments) else Bundle()
        tempArg.putBoolean(BundleKeyConstants.KEY_INTERCEPT_BACK, false)
        startFragment(getmActivity(), LoginFragmentUtil.getLoginFragment(tempArg))

        // 新激活强引导登录页-其他登录方式  点击事件
        XMTraceApi.Trace()
            .click(23785)
            .put("Item", "其他登录")
            .put("currPage", "first_login_guide")
            .put("isTel", if (ToolUtil.hasSimCard(ToolUtil.getCtx())) "1" else "0")
            .createTrace()
    }

    override fun onDestroy() {
        super.onDestroy()

        PrivacyHintActivity.sOneKeyLoginInitCallBack = null
    }

    private fun doLogin() {
        if (!mHintState.isSelected) {
            if (ToolUtil.activityIsValid(activity)) {
                activity?.let {
                    val loginHintChooseRuleDialog = LoginHintChooseRuleDialog(it)
                    if (mPreVerifyResult != null) {
                        loginHintChooseRuleDialog.setProtocolData(
                            mPreVerifyResult?.protocolName,
                            mPreVerifyResult?.protocolUrl
                        )
                    }
                    loginHintChooseRuleDialog.setOkHandle {
                        mHintState.isSelected = true
                        doLogin()
                    }
                    loginHintChooseRuleDialog.show()
                }
            }
            return
        }

        hideSoftInput(this)

        if (mUseHasSet || mVerifyResult == null) {
            loginUsePhone()

            // 新激活强引导登录页-验证码  点击事件
            XMTraceApi.Trace()
                .click(42971)
                .put("currPage", "first_login_guide")
                .put("isTel", if (ToolUtil.hasSimCard(ToolUtil.getCtx())) "1" else "0")
                .createTrace()
        } else {
            loginUseOneKey()

            // 新激活强引导登录页-本机号码一键登录  点击事件
            XMTraceApi.Trace()
                .click(23783)
                .put("currPage", "first_login_guide")
                .put("isTel", if (ToolUtil.hasSimCard(ToolUtil.getCtx())) "1" else "0")
                .createTrace()
        }
    }

    private fun loginUsePhone() {
        val phoneNum = mLoginPhoneNum.text.toString().trim()
        if (StringUtil.verifyGlobalPhone(mCountryCode, phoneNum)) {
            getPhoneCheckCode(
                LoginRequest.SEND_SMS_TYPE_FOR_LOGIN,
                phoneNum, mCountryCode, WeakReference<BaseLoginFragment>(this)
            )
        } else {
            CustomToast.showToast("手机号格式错误")
        }
    }

    private fun loginUseOneKey() {
        mVerifyResult?.let {
            showLoginProgress(mActivity)
            LoginTraceUtil.traceOnClickOneKey("new")
            QuickLoginUtil.getInstance().toLogin(it, object :
                IDataCallBackUseLogin<LoginInfoModelNew> {
                override fun onSuccess(data: LoginInfoModelNew?) {
                    if (canUpdateUi()) {
                        dismissLoginProgress()
                        if (data != null && data.ret == 0) {
                            LoginTraceUtil.traceOnLoginResult("succeed", "new")
                            handleLoginSuccess(data, false)
                        } else {
                            LoginTraceUtil.traceOnLoginResult("failed", "new",
                                LoginTraceUtil.QuickLoginError.NET_ERROR.errorCode,
                                LoginTraceUtil.QuickLoginError.NET_ERROR.errorMessages)

                            verifyError(true)
                        }
                    }
                }

                override fun onError(code: Int, message: String?) {
                    LoginTraceUtil.traceOnLoginResult("failed", "new", code, message)
                    dismissLoginProgress()
                    verifyError(true)
                }
            })
        }
    }

    private fun verifyError(showToast: Boolean = true) {
        if (showToast) {
            CustomToast.showFailToast(R.string.login_one_key_login_fail)
        }
        chooseNormalPhoneLogin()
    }

    private fun showLoginProgress(activity: Activity?) {
        if (activity == null || activity.isFinishing)
            return
        if (mProgressDialog == null) {
            mProgressDialog = LoadingDialog(activity);
        } else {
            mProgressDialog?.cancel();
        }

        mProgressDialog?.setTitle("正在登录...")
        mProgressDialog?.showIcon(true)
        mProgressDialog?.showBg(true)
        mProgressDialog?.show()
    }

    private fun dismissLoginProgress() {
        mProgressDialog?.dismiss();
        mProgressDialog = null;
    }

    fun dialog(msg: String?) {
        if (mActivity != null) {
            DialogBuilder<DialogBuilder<*>>(mActivity).setMessage(msg).showWarning()
        }
    }

    private fun startMainActivity() {
        // 新激活强引导登录页-随便逛逛  点击事件
        // 新激活强引导登录页-随便逛逛  点击事件
        XMTraceApi.Trace()
            .click(23784)
            .put("currPage", "first_login_guide")
            .put("isTel", if (ToolUtil.hasSimCard(ToolUtil.getCtx())) "1" else "0")
            .createTrace()
        try {
            val intent = Intent(mActivity, MainActivity::class.java)
            ToolUtil.checkIntentAndStartActivity(mActivity, intent)
        } catch (e: Exception) {
            Logger.e(e)
        }
        finish()
    }

    private fun showChooseCountry() {
        val fragment = ChooseCountryFragment()
        fragment.setIChooseCountryListener(this)
        startFragment(fragment)
    }

    override fun onCountryChosenListener(model: InternationalCodeModel?) {
        model?.let {
            mCountryCode = it.countryCode
            mRegionNumber.text = "+" + it.countryCode
        }
    }

    override fun initBegin() {
    }

    override fun onLoginInitSuccess(result: PreVerifyResult?) {
        result?.let {
            if (!mUseHasSet && canUpdateUi()) {
                onVerifyResultSuccess(it)
            }
        }
    }

    override fun onLoginInitFail(code: Int, msg: String?) {
    }

    private var mPhoneNumWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }

        override fun afterTextChanged(s: Editable?) {
            updateLoginBtnEnable(s != null && !TextUtils.isEmpty(s.toString()))
        }
    }

    override fun onBackPressed(): Boolean {
        startMainActivity()
        return true
    }

    override fun getPageLogicName(): String {
        return ILoginFragmentAction.PAGE_LOGIC_LOGIN_GUIDE
    }

    override fun onPause() {
        super.onPause()

        // 新激活强引导登录页  页面离开
        XMTraceApi.Trace()
            .pageExit2(23782)
            .put("currPage", "first_login_guide")
            .put("isTel", if (ToolUtil.hasSimCard(ToolUtil.getCtx())) "1" else "0")
            .createTrace()
    }
}