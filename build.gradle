apply from: "config.gradle"
apply from: 'dependencies.gradle'


buildscript {
//    ext.anko_version = '0.10.1'
    repositories {
        maven { url "http://************:8082/artifactory/ximalaya-android-maven-repo/" }
        maven { url "http://************:8082/artifactory/host/" }
        maven { url "https://maven.aliyun.com/repository/google" }
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url "https://maven.aliyun.com/repository/jcenter" }
        maven { url "https://maven.aliyun.com/repository/gradle-plugin" }
        google()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:${ANDROID_TOOLS_BUILD_GRADLE}"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${KOTLIN_PLUGIN_VERSION}"
        classpath "org.jetbrains.kotlin:kotlin-android-extensions:${KOTLIN_PLUGIN_VERSION}"
        classpath 'com.ximalaya.gradle:bundle-developer:2.0'
//        classpath 'com.neenbedankt.gradle.plugins:android-apt:1.8'
    }
//    project.gradle.startParameter.excludedTaskNames.add("lint")
}

allprojects {
    repositories {
        maven { url "http://************:8082/artifactory/ximalaya-android-maven-repo/" }
        maven { url "http://************:8082/artifactory/host/" }
        maven { url "https://maven.aliyun.com/repository/google" }
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url "https://maven.aliyun.com/repository/jcenter" }
        maven { url "https://maven.aliyun.com/repository/gradle-plugin" }
    }
}

ext {
    compileSdkVersion = 26
    buildToolsVersion = '25.0.2'
    minSdkVersion = "16"
    targetSdkVersion = "23"
}

//static String[] runShell(String[] execShell, File path, String... params) throws IOException, InterruptedException {
//    Process process = null;
//    println "ready do shell cmd :" + execShell + ",params:" + params + ",path:" + path
//    BufferedReader _in = null;
//    try {
//        process = Runtime.getRuntime().exec(execShell, params, path);
//        int waitFor = process.waitFor()
//        String result = ""
//        if (process.getIn() != null) {
//            _in = new BufferedReader(new InputStreamReader(
//                    process.getIn()));
//            StringBuffer sb = new StringBuffer();
//            String lineTxt;
//            while ((lineTxt = _in.readLine()) != null && lineTxt != "") {
//                sb.append(lineTxt + "\n");
//            }
//            result += sb.toString()
//        }
//        if (process.getErr() != null) {
//            _in = new BufferedReader(new InputStreamReader(
//                    process.getErr()));
//            StringBuffer sb = new StringBuffer();
//            String lineTxt;
//            while ((lineTxt = _in.readLine()) != null && lineTxt != "") {
//                sb.append(lineTxt + "\n");
//            }
//            result += sb.toString()
//        }
//        // 部分mac出现环境配置问题，需要手动配置
//        if (result != null && result.contains("~/.gitcinclude")) {
//            new Exception(result+",请尝试手动执行install_git_hook.sh脚本")
//        }
//        return ["" + waitFor, result]
//    } catch (IOException e) {
//        println e.getMessage()
//        throw e;
//    } catch (InterruptedException e) {
//        println e.getMessage()
//        throw e;
//    } catch (Exception e) {
//        println e.getMessage()
//    } finally {
//        if (process != null) {
//            process.destroy();
//        }
//        if (_in != null) {
//            try {
//                _in.close();
//            } catch (IOException e) {
//                System.out.println("close inputstream error");
//            }
//        }
//    }
//}

//project.afterEvaluate {
//    println "================== git hooks config check ================== "
//    File srcDir = new File(project.projectDir, "/git_hook/")
//    if (!srcDir.exists())
//        return
//    String currentHookPath = srcDir.getAbsolutePath()
//    println "project git config hooks path " + currentHookPath
//
//    def hookConfigCheckCmd = ["git", "config", "--get", "core.hooksPath"] as String[]
//    String[] result = runShell(hookConfigCheckCmd, project.projectDir)
//    String gitConfigHookPath = result != null && result.length > 1 ? result[1]?.trim() : ""
//    println "- local git config hooks path " + gitConfigHookPath
//    boolean needConfigGitHook = currentHookPath?.trim() != gitConfigHookPath?.trim()
//    println "- need config hooks Path:" + needConfigGitHook
//    if (needConfigGitHook) {
//        def hooksPathConfigCmd = ["git", "config", gitConfigHookPath?.length() > 0 && new File(gitConfigHookPath).exists() ? "--replace" : "--add", "core.hooksPath", currentHookPath] as String[]
//        try {
//            println "do git hooks config"
//            runShell(hooksPathConfigCmd, project.projectDir)
//        } catch (Exception e) {
//            throw new GradleException("请手动执行git hook配置, 自动配置失败:" + e.getMessage())
//        }
//    }
//    println "------------------------------------------------------------"
//    File gitTempPath = new File(srcDir, "git_commit_template.template")
//    if (gitTempPath.exists()) {
//        println "- local git config commit.template path " + gitTempPath
//        def commitTemplate = ["git", "config", "--get", "commit.template"] as String[]
//        String[] commitTemplateResult = runShell(commitTemplate, project.projectDir)
//        String path = commitTemplateResult != null && commitTemplateResult.length > 1 ? commitTemplateResult[1]?.trim() : ""
//        println "- current git config commit.template path " + path
//        if (path?.trim() != gitTempPath.getAbsolutePath().trim()) {
//            def configCommitTemplate = ["git", "config", path?.length() > 0 && new File(path).exists() ? "--replace" : "--add", "commit.template", gitTempPath] as String[]
//            try {
//                println "do git commit.template config"
//                runShell(configCommitTemplate, project.projectDir)
//            } catch (Exception e) {
//                println("请手动执行git commit.template配置, 自动配置失败:" + e.getMessage())
//            }
//        }
//    }
//    println "================== git hooks config check ================== "
//}