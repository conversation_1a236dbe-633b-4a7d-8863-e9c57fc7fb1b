// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "config.gradle"

buildscript {
    repositories {
        maven { url "http://************:8082/artifactory/ximalaya-android-maven-repo/" }
        maven { url "http://************:8082/artifactory/host/" }
        maven { url "https://maven.aliyun.com/repository/google" }
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url "https://maven.aliyun.com/repository/jcenter" }
        maven { url "https://maven.aliyun.com/repository/gradle-plugin" }
    }
    dependencies {
        classpath "com.android.tools.build:gradle:${ANDROID_TOOLS_BUILD_GRADLE}"

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven { url "http://************:8082/artifactory/ximalaya-android-maven-repo/" }
        maven { url "http://************:8082/artifactory/host/" }
        maven { url "https://maven.aliyun.com/repository/google" }
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url "https://maven.aliyun.com/repository/jcenter" }
        maven { url "https://maven.aliyun.com/repository/gradle-plugin" }
    }
}

ext {
    compileSdkVersion = 26
    buildToolsVersion = "26.0.2"

    targetSdkVersion = 25

    versionCode = 0
    versionName = "0.6.3"

    minSdkVersion = 15

    isDemo = "true"
}

task clean(type: Delete) {
    delete rootProject.buildDir
}