#!/bin/bash
set -e
log_d() {
  echo -e "\033[34m$1\033[0m"
}

log_w() {
  echo -e "\033[35m$1\033[0m"
}

log_e() {
  echo -e "\033[31m$1\033[0m"
}

url_encode() {
  local length="${#1}"
  for ((i = 0; i < length; i++)); do
    local c="${1:i:1}"
    case ${c} in
    [a-zA-Z0-9.~_-]) printf "$c" ;;
    *) printf "$c" | xxd -p -c1 | while read x; do printf "%%%s" "$x"; done ;;
    esac
  done
}


GITLAB_URL='http://gitlab.ximalaya.com'
API_URL=${GITLAB_URL}/api/v4
# git 地址
REMOTE_URL=$(git remote -v | grep push | awk '{print $2}')

# http://gitlab.ximalaya.com/ervin/codereview.git
# ***********************:ervin/codereview.git
get_project_name(){
    url_project=$(echo ${REMOTE_URL} | grep ${GITLAB_URL} || true)
    if [[ -n ${url_project} ]];then
       project_name=$(echo ${REMOTE_URL} | awk -F '.com/' '{print $2}' |  awk -F '.' '{print $1}' )
       echo ${project_name}
    else
       project_name=$(echo ${REMOTE_URL} | awk -F ':' '{print $2}' | awk -F '.' '{print $1}')
       echo ${project_name}
    fi
}
PROJECT_NAME=$(get_project_name)
PROJECT_SIMPLE_NAME=$(echo ${PROJECT_NAME} | awk -F '/' '{print $2}')
PROJECT_URL=${GITLAB_URL}/${PROJECT_NAME}
DING_GROUP_TOKEN_CONFIG_URL="http://ops.test.ximalaya.com/gitlabwebhook/project_config"
GET_PROJECT_DING_TOKEN="http://ops.test.ximalaya.com/gitlabwebhook/get_project_ding_token"
#----------------信息配置部分------------------
# merge请求钉钉发送token
force_update=0
if [[ $1 == "-force" ]];then
    force_update=1
    ding_group_token=$2
else
    ding_group_token=$1
fi
#echo ${force_update}
#exit 1
#----------------信息配置部分------------------
log_w "当前脚本执行前请确保codereview_config已经执行成功, 如若没有, 请按照以下步骤完成基础配置:"
log_w "1.下载脚本 curl http://gitlab.ximalaya.com/ervin/codereview/raw/master/codereview_config?inline=false -o codereview_config.sh"
log_w "2.执行脚本 根据脚本提示 完成配置"

#----------------access token部分------------------
gitlab_access_token=$(git config --get gitlab.token )
if [[ -z "${gitlab_access_token}" ]];then
   log_e "未获取到Gitlab Personal Access Tokens, 请执行以下操作:"
   log_e "1. 请前往 ${GITLAB_URL}/profile/personal_access_tokens 地址生成完全访问的Personal Access Token"
   log_e "注意 Access Token需要勾选所有权限，防止过期请不要填写过期时间"
   exit 1
fi
log_d "token:${gitlab_access_token}"

#----------------user id部分------------------
user_id=$(git config  --get gitlab.userid)
if [[ -z ${user_id} ]];then
    log_e "未获取到gitlab user_id, 请执行以下操作:"
    log_e "1. 请前往 ${GITLAB_URL}/profile/ 中查看user_id"
    log_e "2. 通过git config --global --add gitlab.userid \$user_id 保存到配置"
    exit 1
fi
log_d "user_id:${user_id}"

#----------------project id部分------------------
parse_real_project_id(){
    result_data=$1
    find_project_id=$(echo ${result_data} | grep ${REMOTE_URL} | awk -F ${REMOTE_URL} '{print $1}' | awk -F '{"id":' '{print $2}' | awk -F ',' '{print $1}' | grep -E '^[0-9]+$' | sed 's/ //g')
    echo ${find_project_id}
}

project_name=${PROJECT_SIMPLE_NAME}
log_d "project_name:${project_name}"
project_id=$(git config --get gitlab.projectid || true)
if [[ -z ${project_id} ]];then
    log_d "-------请求projectid---------"
    projects=$(curl --header \
      "PRIVATE-TOKEN: ${gitlab_access_token}" \
      "${API_URL}/users/${user_id}/projects?search=${project_name}&simple=true")
    echo "公开项目请求project结果:"${projects}
    project_id=$(parse_real_project_id "${projects}")
    if [[ -n ${project_id} ]];then
        git config --add gitlab.projectid ${project_id}
    else
        log_w "未在公开项目中找到该项目信息，尝试从内部项目中获取"
        projects=$(curl --header \
          "PRIVATE-TOKEN: ${gitlab_access_token}" \
          "${API_URL}/projects?search=${project_name}&visibility=internal&simple=true")
        echo "内部项目请求project结果:"${projects}
        project_id=$(parse_real_project_id "${projects}")
        if [[ -n ${project_id} ]];then
           git config --add gitlab.projectid ${project_id}
        else
           log_w "未在内部项目中找到该项目信息，尝试从私有项目中获取"
           projects=$(curl --header \
           "PRIVATE-TOKEN: ${gitlab_access_token}" \
           "${API_URL}/projects?search=${project_name}&visibility=private&simple=true")
           echo "私有项目请求project结果:"${projects}
           project_id=$(parse_real_project_id "${projects}")
           if [[ -n ${project_id} ]];then
                git config --add gitlab.projectid ${project_id}
           else
                log_e "project_id获取失败,请手动配置:"
                log_e "1. 前往项目gitlab地址首页${PROJECT_URL}，复制项目id"
                log_e "2. 执行 git config --add gitlab.projectid 项目id"
                exit 1
           fi
        fi
    fi
fi
log_d "project_id:${project_id}"

log_w "请确认project_id是否正确, 如不正确, 请前往gitlab后台查看projectId 然后进行配置, 具体步骤如下:"
log_w "打开${GITLAB_URL}/${PROJECT_NAME} 复制Project ID"
log_w "在项目路径下执行shell git config --replace gitlab.projectid 复制的Project ID"

#----------------ding_group_token部分------------------

log_d "ding_group_token:${ding_group_token},force_update:${force_update} "
if [[ ${force_update} -eq 0 && -n ${ding_group_token} && -n ${project_id} ]];then
    log_d "-------------项目钉钉群消息配置----------------"
    request_url="${DING_GROUP_TOKEN_CONFIG_URL}?project_id=${project_id}&ding_group_token=${ding_group_token}"
    log_d "请求url:${request_url}"
    curl ${request_url}
    log_d "========================================="
elif [[ -n ${project_id} ]];then
    request_project_ding_token_url="${GET_PROJECT_DING_TOKEN}?project_id=${project_id}"
    project_token_result=$(curl -s ${request_project_ding_token_url})
    echo ${project_token_result}
    result_code=$(echo ${project_token_result}|grep 'ding_token')
    if [[ -z ${result_code} ]];then
        log_e "当前项目未完成钉钉群token配置,请增加钉钉群机器人token"
        exit 1
    fi
fi

#----------------git hook文件部分------------------
# 请求git_hook信息
current_path=`pwd`
git_hook_path=${current_path}/git_hook
check_style_dir_path=${current_path}/check-style
if [[ ${force_update} -eq 0 && -d "${check_style_dir_path}" && -n `ls ${check_style_dir_path}` ]];then
    log_d "--------当前项目存在checkstyle相关文件--------"
    echo ${check_style_dir_path}
    log_d "========================================="
else
    checkstyle_file=check-style.tar
    log_d "--------请求checkstyle相关文件--------"
    curl http://gitlab.ximalaya.com/ervin/codereview/-/archive/master/codereview-master.tar\?path\=check-style -o ${checkstyle_file}
    log_d "========================================="
    log_d "--------执行checkstyle文件拷贝--------"
    tar -zxv -f ${checkstyle_file} --strip-components 1
    if [[ -d "${check_style_dir_path}" ]];then
        rm ${checkstyle_file}
    else
        log_e "请手动下载checkstyle文件"
        log_e "http://gitlab.ximalaya.com/ervin/codereview/-/archive/master/codereview-master.tar\?path\=check-style"
    fi
    log_d "========================================="
fi
if [[ ${force_update} -eq 0 && -d "${git_hook_path}" && -n `ls ${git_hook_path}` ]];then
    log_d "--------当前项目存在git_hook相关文件--------"
    echo ${git_hook_path}
    log_d "========================================="
    log_d "------------增加执行权限------------"
    find ${git_hook_path} -name '*'|grep -v '.template$'|  grep  '/' | xargs -I {} chmod +x {}
    log_d "========================================="
else
    git_hook_tar_file=git_hook.tar
    log_d "--------请求git_hook文件--------"
    curl http://gitlab.ximalaya.com/ervin/codereview/-/archive/master/codereview-master.tar\?path\=git_hook -o ${git_hook_tar_file}
    log_d "========================================="
    log_d "--------执行git_hook文件拷贝--------"
    tar -zxv -f ${git_hook_tar_file} --strip-components 1
    if [[ -d "${git_hook_path}" ]];then
        rm ${git_hook_tar_file}
     else
        log_e "git_hook下载失败"
        exit 1
    fi
    log_d "========================================="
    log_d "------------增加执行权限------------"
    find ${git_hook_path} -name '*'|grep -v '.template$'|  grep  '/' | xargs -I {} chmod -v +x {}
    log_d "========================================="
fi
# commit-msg 模版
git_template_name=$(ls ${git_hook_path}|grep -s '.template$')
if [[ -n ${git_template_name} ]];then
    log_d "--------配置git commit.template--------"
    git_temp_path=${git_hook_path}/${git_template_name}
    echo "当前git commit.template:${git_temp_path}"
    git config commit.template ${git_temp_path}
    log_d "========================================="
fi
# 配置git hooks
git_version=`git --version |cut -d ' ' -f3`
version_array=(${git_version//./ })
echo "当前 git 版本 ${git_version}"
if [[ ${version_array[0]}  -gt 2 ]];then
    # git.hooksPath
    log_d "------------配置git.hooks路径------------"
    echo "git.hook路径:${git_hook_path}"
    git config core.hooksPath ${git_hook_path}
    log_d "========================================="
elif [[ ${version_array[0]}  -eq 2 ]] && [[ ${version_array[1]}  -gt 9 ]]; then
    # git.hooksPath
    log_d "------------配置git.hooks路径------------"
    echo "git.hook路径:${git_hook_path}"
    git config core.hooksPath ${git_hook_path}
    log_d "========================================="
else
    log_d "------------拷贝git.hooks脚本------------"
    local_hooks_dir=${current_path}/.git/hooks/
    echo "当前git hook路径:${local_hooks_dir}"
    # git commit-msg pre-commit pre-push等hook脚本拷贝
    if [[ ! -d ${local_hooks_dir} ]]
        then
        mkdir -p ${local_hooks_dir}
    fi
    find ${git_hook_path} -name '*'|grep -v '.template$'|  grep  '/' | xargs -I {} cp {} ${local_hooks_dir}
    log_d "========================================="
fi
submit_merge_path=${current_path}/submit_merge.sh
if [[ ${force_update} -eq 1 || ! -f ${submit_merge_path} ]];then
   log_d "------------安装submit_merge脚本-----------"
   curl http://gitlab.ximalaya.com/ervin/codereview/raw/master/submit_merge.sh?inline=false -o submit_merge.sh && chmod +x submit_merge.sh
   log_d "========================================="
fi
gitlab_member=${current_path}/**********************.sh
if [[ ${force_update} -eq 1 || ! -f ${gitlab_member} ]];then
   log_d "------------安装**********************脚本-----------"
   curl http://gitlab.ximalaya.com/ervin/codereview/raw/master/**********************.sh?inline=false -o **********************.sh && chmod +x **********************.sh
   log_d "========================================="
fi
log_d "------------安装脚本执行结束------------"
exit 0