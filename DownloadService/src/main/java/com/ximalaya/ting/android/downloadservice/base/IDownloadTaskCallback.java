package com.ximalaya.ting.android.downloadservice.base;


import androidx.annotation.Nullable;

/**
 * 下载任务状态监听器
 */
/**
 * Created by roc on 2017/12/13.
 * <AUTHOR>
 */
public interface IDownloadTaskCallback {
    /**
     * 事件和方法对应
     */
    int EVENT_PROGRESS = 1;
    int EVENT_START_NEW_TASK = 3;
    int EVENT_COMPLETE = 4;
    int EVENT_UPDATE_TASK = 5;
    int EVENT_CANCEL = 6;
    int EVENT_ERROR = 7;
    int EVENT_DELETE = 8;

    void onDownloadProgress(@Nullable BaseDownloadTask downloadTask);

    void onCancel(@Nullable BaseDownloadTask downloadTask);

    void onComplete(@Nullable BaseDownloadTask downloadTask);

    void onUpdateTrack(@Nullable BaseDownloadTask downloadTask);

    void onStartNewTask(@Nullable BaseDownloadTask downloadTask);

    void onError(@Nullable BaseDownloadTask downloadTask);

    void onDelete();
}
