import groovy.xml.XmlUtil

task modifyAppName {
    if(rootProject.ext.modifyApplicationName.toBoolean()) {
        println "=================modify application name start================="
        def filePath = "../TingMainApp/res/values-zh-rCN/strings.xml"
        def xmlFile = file(filePath)
        if (xmlFile.exists()) {
            def resources = new XmlParser().parse(xmlFile.getAbsolutePath())
            def app_name = resources.string.find { it.attribute("name") == "app_name" }
            println app_name
            app_name.value = rootProject.ext.applicationName
            xmlFile.write(XmlUtil.serialize(resources))
        } else {
            println xmlFile.getAbsolutePath() + " not exists"
        }
        println "=================modify application name end================="
    }else{
        println "=================not modify application name================="
    }
}
