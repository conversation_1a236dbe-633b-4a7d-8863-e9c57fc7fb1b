project.afterEvaluate {
    if (project.hasProperty('isBuildMapping')) {
        def isBuildMapping = project.property('isBuildMapping')
        println("isBuildMapping " + isBuildMapping)
        if (isBuildMapping.toBoolean()) {
            def assembleTask = project.tasks.getByName("assembleRelease")
            if (assembleTask) {
                assembleTask.doLast {
                    def rootProjectBuildPath = project.buildDir.absolutePath + "/outputs/mapping/" + rootProject.ext.channel + "/release";
                    def fileName = "mapping.txt";
                    def srcFile = new File(rootProjectBuildPath, fileName);


                    File destFile = new File(project.rootDir.absolutePath + "/TingMainHost/Application/", fileName)
                    copyFile(srcFile, destFile)

                    destFile = new File(project.rootDir.absolutePath + "/ChatBundle/ChatModuleApplication/", fileName)
                    copyFile(srcFile, destFile)

                    destFile = new File(project.rootDir.absolutePath + "/LiveBundle/LiveModuleApplication/", fileName)
                    copyFile(srcFile, destFile)

                    destFile = new File(project.rootDir.absolutePath + "/CarBundle/CarModuleApplication/", fileName)
                    copyFile(srcFile, destFile)

                    destFile = new File(project.rootDir.absolutePath + "/RecordBundle/RecordModuleApplication/", fileName)
                    copyFile(srcFile, destFile)

                    destFile = new File(project.rootDir.absolutePath + "/SmartDeviceBundle/SmartDeviceApplication/", fileName)
                    copyFile(srcFile, destFile)

                    destFile = new File(project.rootDir.absolutePath + "/WatchBundle/WatchApplication/", fileName)
                    copyFile(srcFile, destFile)
                }
            }else{
                def assembleTaskName = "assemble" + toUpperCaseFirstOne(rootProject.ext.channel ) + "Release"
                def assembleChannelTask = project.tasks.getByName(assembleTaskName)
                if(assembleChannelTask){
                    assembleChannelTask.doLast{
                        def rootProjectBuildPath = project.buildDir.absolutePath + "/outputs/mapping/" + rootProject.ext.channel + "/release";
                        def fileName = "mapping.txt";
                        def srcFile = new File(rootProjectBuildPath, fileName);


                        File destFile = new File(project.rootDir.absolutePath + "/TingMainHost/Application/", fileName)
                        copyFile(srcFile, destFile)

                        destFile = new File(project.rootDir.absolutePath + "/ChatBundle/ChatModuleApplication/", fileName)
                        copyFile(srcFile, destFile)

                        destFile = new File(project.rootDir.absolutePath + "/LiveBundle/LiveModuleApplication/", fileName)
                        copyFile(srcFile, destFile)

                        destFile = new File(project.rootDir.absolutePath + "/CarBundle/CarModuleApplication/", fileName)
                        copyFile(srcFile, destFile)

                        destFile = new File(project.rootDir.absolutePath + "/RecordBundle/RecordModuleApplication/", fileName)
                        copyFile(srcFile, destFile)

                        destFile = new File(project.rootDir.absolutePath + "/SmartDeviceBundle/SmartDeviceApplication/", fileName)
                        copyFile(srcFile, destFile)

                        destFile = new File(project.rootDir.absolutePath + "/WatchBundle/WatchApplication/", fileName)
                        copyFile(srcFile, destFile)
                    }
                }
            }
        }
    } else {
        println("No isBuildMapping ")
    }
}

static void copyFile(File srcFile, File destFile) {
    println("======srcFile " + srcFile.absolutePath)
    println("======destFile " + destFile.absolutePath)
    destFile.getParentFile().mkdirs()
    def inputStream = srcFile.newInputStream()
    def outputStream = destFile.newOutputStream()
    outputStream << inputStream
    inputStream.close()
}

