<?xml version="1.0"?><!DOCTYPE module PUBLIC
    "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
    "https://checkstyle.org/dtds/configuration_1_3.dtd"><!--
    Checkstyle configuration that checks the Google coding conventions from Google Java Style
    that can be found at https://google.github.io/styleguide/javaguide.html

    Checkstyle is very configurable. Be sure to read the documentation at
    http://checkstyle.sf.net (or in your downloaded distribution).

    To completely disable a check, just comment it out or delete it from the file.

    Authors: <AUTHORS>
 -->
<module name="Checker">
    <!--以下是pre-commit,checkstyle检查规则,如有疑问或建议请联系李宏果/赖达元-->
    <property name="charset" value="UTF-8" />
    <!--违规级别-->
    <property name="severity" value="warning" />
    <!--可接受的文件扩展名-->
    <property name="fileExtensions" value="java" />
    <!-- See https://checkstyle.org/config_filefilters.html -->
    <!--检查过滤-->
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value="module\-info\.java$" />
    </module>
    <!-- 过滤指定配置-->
    <module name="SuppressionFilter">
        <property name="file" value="./check-style/suppressions.xml" />
        <property name="optional" value="false" />
    </module>
    <!--检查Java源文件并定义一些适用于检查此类文件的一些属性-->
    <module name="TreeWalker">
        <!--检查外部类型名称和文件名是否匹配，例如，类Foo必须位于名为Foo.java的文件中-->
        <module name="OuterTypeFilename" />
        <!--String char限制不能直接使用八进制及unicode,编码可读性考虑-->
        <module name="IllegalTokenText">
            <property name="tokens" value="STRING_LITERAL, CHAR_LITERAL" />
            <property name="format"
                value="\\u00(09|0(a|A)|0(c|C)|0(d|D)|22|27|5(C|c))|\\(0(10|11|12|14|15|42|47)|134)" />
            <property name="message"
                value="Consider using special escape sequence instead of octal value or Unicode escaped value." />
        </module>
        <!--        checkstyle idea插件不支持-->
        <!--        &lt;!&ndash;每行不超过200个字符&ndash;&gt;-->
        <!--        <module name="LineLength">-->
        <!--            <property name="max" value="200" />-->
        <!--            &lt;!&ndash;可以忽略的行&ndash;&gt;-->
        <!--            <property name="ignorePattern"-->
        <!--                value="^package.*|^import.*|a href|href|http://|https://|ftp://" />-->
        <!--        </module>-->
        <!--if嵌套的层次深度最深5层-->
        <module name="NestedIfDepth">
            <property name="max" value="5" />
        </module>
        <!--try的嵌套深度最深3层-->
        <module name="NestedTryDepth">
            <property name="max" value="3" />
        </module>
        <!--不允许空的代码段-->
        <module name="EmptyStatement" />
        <!--clone方法重写必须调用super-->
        <module name="SuperClone" />
        <!--finalize方法重写必须调用super-->
        <module name="SuperFinalize" />
        <!--常量命名必须全大写-->
        <module name="ConstantName" />
        <!--不允许无用的import-->
        <module name="UnusedImports" />
        <!--不允许使用import *-->
        <module name="AvoidStarImport" />
        <!--不允许类和扩展类在一个java文件中-->
        <module name="OneTopLevelClass" />
        <!--import package 不允许换行-->
        <!--https://checkstyle.sourceforge.io/config_whitespace.html#NoLineWrap-->
        <module name="NoLineWrap" />
        <!--下面tokens中语句中不允许包含空块-->
        <module name="EmptyBlock">
            <property name="option" value="TEXT" />
            <property name="tokens"
                value="LITERAL_TRY, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_SWITCH" />
        </module>
        <!--检查代码块必须有大括号包裹 if(x>0) return; no-->
        <module name="NeedBraces" >
            <property name="allowSingleLineStatement" value="true" />
        </module>
        <!--检查强制转型的圆括号的空白规则(String)object)-->
        <module name="TypecastParenPad" />
        <!--检查每行只有一个语句-->
        <module name="OneStatementPerLine" />
        <!--不允许同一行一次声明多个变量-->
        <module name="MultipleVariableDeclarations" />
        <!--检查数组类型定义的样式 String strings[]; no-->
        <module name="ArrayTypeStyle" />
        <!--case default必须有-->
        <module name="MissingSwitchDefault" />
        <!--检查switch代码的case中是否缺少break，return，throw和continue-->
        <module name="FallThrough" />
        <!--检查long型约束是否有大写的“L-->
        <module name="UpperEll" />
        <!--检查使用分隔符的换行-->
        <module name="EmptyLineSeparator">
            <property name="allowNoEmptyLineBetweenFields" value="true" />
            <property name="tokens" value="PACKAGE_DEF, IMPORT, STATIC_IMPORT, CLASS_DEF, INTERFACE_DEF, ENUM_DEF, STATIC_INIT, INSTANCE_INIT, METHOD_DEF, CTOR_DEF" />
        </module>
        <!--package名称小写-->
        <module name="PackageName">
            <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$" />
            <message key="name.invalidPattern"
                value="Package name ''{0}'' must match pattern ''{1}''." />
        </module>
        <!--类名称必须以大写字母开头-->
        <module name="TypeName">
            <property name="format" value="^[A-Z](_?$?[a-zA-Z0-9]+)*$" />
            <property name="applyToProtected" value="false" />
            <property name="applyToPrivate" value="false" />
            <message key="name.invalidPattern"
                value="Type name ''{0}'' must match pattern ''{1}''." />
        </module>
        <!--成员变量命名规范-->
        <module name="MemberName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$" />
            <message key="name.invalidPattern"
                value="Member name ''{0}'' must match pattern ''{1}''." />
            <property name="applyToPublic" value="false" />
            <property name="applyToProtected" value="true" />
            <property name="applyToPrivate" value="true" />
        </module>
        <!--        &lt;!&ndash;https://checkstyle.sourceforge.io/config_naming.html#MemberName&ndash;&gt;-->
        <!--        <module name="MemberName">-->
        <!--            <property name="format" value="^[a-z][A-Z][a-zA-Z0-9]*$" />-->
        <!--            <property name="applyToProtected" value="false" />-->
        <!--            <property name="applyToPrivate" value="false" />-->
        <!--            <property name="applyToPublic" value="public" />-->
        <!--        </module>-->
        <!--检查方法参数名称是否符合format属性指定的格式-->
        <module name="ParameterName">
            <property name="format" value="^[a-z][_a-zA-Z0-9]+$" />
            <property name="ignoreOverridden" value="true"/>
            <message key="name.invalidPattern"
                value="Parameter name ''{0}'' must match pattern ''{1}''." />
        </module>
        <!--检查catch参数名称是否符合format属性指定的格式-->
        <module name="LambdaParameterName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$" />
            <message key="name.invalidPattern"
                value="Lambda parameter name ''{0}'' must match pattern ''{1}''." />
        </module>
<!--        &lt;!&ndash;检查catch参数名称是否符合format属性指定的格式&ndash;&gt;-->
<!--        <module name="CatchParameterName">-->
<!--            <property name="format" value="^[a-z][a-zA-Z0-9]+$" />-->
<!--            <message key="name.invalidPattern"-->
<!--                value="Catch parameter name ''{0}'' must match pattern ''{1}''." />-->
<!--        </module>-->
        <!--局部变量-->
        <module name="LocalVariableName">
            <property name="tokens" value="VARIABLE_DEF" />
            <property name="format" value="^[a-z](_?[a-zA-Z0-9]+)*$" />
            <message key="name.invalidPattern"
                value="Local variable name ''{0}'' must match pattern ''{1}''." />
        </module>
        <!--类泛形描述-->
        <module name="ClassTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)" />
            <message key="name.invalidPattern"
                value="Class type name ''{0}'' must match pattern ''{1}''." />
        </module>
        <!--方法泛形描述-->
        <module name="MethodTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)" />
            <message key="name.invalidPattern"
                value="Method type name ''{0}'' must match pattern ''{1}''." />
        </module>
        <!--接口定义的泛形描述-->
        <module name="InterfaceTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)" />
            <message key="name.invalidPattern"
                value="Interface type name ''{0}'' must match pattern ''{1}''." />
        </module>
        <!--        &lt;!&ndash;不允许连续大写字母的长度超过1&ndash;&gt;-->
        <!--        <module name="AbbreviationAsWordInName">-->
        <!--            <property name="ignoreFinal" value="false" />-->
        <!--            <property name="allowedAbbreviationLength" value="1" />-->
        <!--        </module>-->
        <!--必须保证重载方法申明顺序-->
        <module name="OverloadMethodsDeclarationOrder" />
        <module name="MethodParamPad" />
        <!--不允许tokens指定的字符前面是否有空格-->
        <module name="NoWhitespaceBefore">
            <property name="tokens"
                value="COMMA, SEMI, POST_INC, POST_DEC, DOT, ELLIPSIS, METHOD_REF" />
            <property name="allowLineBreaks" value="true" />
        </module>
        <module name="ParenPad" />
<!--        &lt;!&ndash;@注释必须添加描述&ndash;&gt;-->
<!--        <module name="NonEmptyAtclauseDescription" />-->
        <!--方法名称必须满足驼峰标示-->
        <module name="MethodName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$" />
            <message key="name.invalidPattern"
                value="Method name ''{0}'' must match pattern ''{1}''." />
        </module>
        <!--不允许存在空的catch块-->
        <module name="EmptyCatchBlock">
            <property name="exceptionVariableName" value="expected" />
        </module>
    </module>
</module>