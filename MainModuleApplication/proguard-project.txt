# To enable ProGuard in your project, edit project.properties
# to define the proguard.config property as described in that file.
#
# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in ${sdk.dir}/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the ProGuard
# include property in project.properties.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

-ignorewarnings
-optimizationpasses 5
-dontusemixedcaseclassnames #【混淆时不会产生形形色色的类名 】
-dontskipnonpubliclibraryclasses #【指定不去忽略非公共的库类。 】
-dontpreverify #【不预校验】
-verbose
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/* #【优化】
-dontshrink


#-----MainBundle----start #
-keep class com.ximalaya.ting.android.main.fragment.find.other.recommend.ReportFragment$* { *; }
-keep class com.ximalaya.ting.android.main.playModule.fragment.PlayingSoundDetailView$* { *; }
-keep class com.ximalaya.ting.android.main.playModule.trainingcamp.** { *; }
-keep class com.ximalaya.ting.android.main.util.ui.RingtoneUtil$* { *; }
-keep class com.ximalaya.ting.android.main.adapter.** { *; }
-keep class com.ximalaya.ting.android.main.adModule.manager.** {*; }
-keep class com.ximalaya.ting.android.main.model.** { *; }
-keep class com.ximalaya.ting.android.main.request.MainCommonRequest { *; }
-keep public class com.ximalaya.ting.android.main.adModule.manager.AdsDataHandler { *; }
-keep public class com.ximalaya.ting.android.main.accountModule.bind.other.BindPhoneManager$* { *; }
-keep public class com.ximalaya.ting.android.main.MainApplication {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment {*;}
-keep public class com.ximalaya.ting.android.main.model.category.CategoryM {*;}
-keep public class com.ximalaya.ting.android.main.model.category.CategoryMList {*;}
-keep public class com.ximalaya.ting.android.main.dialog.ShareDialog {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment {*;}
-keep public class com.ximalaya.ting.android.main.listener.IBindAction {*;}
-keep public class com.ximalaya.ting.android.main.R { *; }
-keep public class com.ximalaya.ting.android.main.R$* { *; }
-keep public class com.ximalaya.ting.android.main.view.layout.NineGridLayout {*;}
-keep public class com.ximalaya.ting.android.main.view.layout.NineGridLayout$* {*;}
-keep public class com.ximalaya.ting.android.main.view.NineGridForUseLayout {*;}
-keep public class com.ximalaya.ting.android.main.view.NineGridForUseLayout$* {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment {*;}
-keep public class com.ximalaya.ting.android.login.fragment.GetAndVerifySmsCodeFragment {*;}
-keep public class com.ximalaya.ting.android.main.view.other.ShareDialog {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.util.http.TokenIntercepterUrlList {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment {*;}
-keep public class com.ximalaya.ting.android.main.model.category.CategoryM {*;}
-keep public class com.ximalaya.ting.android.main.model.category.CategoryMList {*;}
-keep public class com.ximalaya.ting.android.main.dialog.ShareDialog {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment {*;}
-keep public class com.ximalaya.ting.android.main.listener.IBindAction {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment {*;}
-keep public class com.ximalaya.ting.android.login.fragment.GetAndVerifySmsCodeFragment {*;}
-keep public class com.ximalaya.ting.android.main.listener.IBindAction {*;}
-keep public class com.ximalaya.ting.android.main.util.http.TokenIntercepterUrlList {*;}
-keep public class com.ximalaya.ting.android.main.view.other.ShareDialog {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
#-----MainBundle----end #
