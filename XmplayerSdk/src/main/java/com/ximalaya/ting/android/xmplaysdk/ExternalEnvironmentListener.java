package com.ximalaya.ting.android.xmplaysdk;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothHeadset;
import android.bluetooth.BluetoothProfile;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioManager;
import android.telephony.PhoneStateListener;
import android.telephony.TelephonyManager;

import java.util.HashSet;

/**
 * Created by sigma on 2018/2/9.
 * <AUTHOR>
 */
public class ExternalEnvironmentListener {

    private Context mContext;
    private TelephonyManager mTelephonyManager;
    private HashSet<Callback> mCallbacks = new HashSet<>();
    private boolean mIsListening = false;

    public static final String ACTION_ALARM_ALERT = "com.android.deskclock.ALARM_ALERT";
    public static final String ACTION_ALARM_SNOOZE = "com.android.deskclock.ALARM_SNOOZE";
    public static final String ACTION_ALARM_DISMISS = "com.android.deskclock.ALARM_DISMISS";
    public static final String ACTION_ALARM_DONE = "com.android.deskclock.ALARM_DONE";

    public ExternalEnvironmentListener(Context context) {
        mContext = context;
        mTelephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
    }

    public void startListen() {
        if (mIsListening) {
            return;
        }
        mIsListening = true;
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_HEADSET_PLUG);
        filter.addAction(AudioManager.ACTION_AUDIO_BECOMING_NOISY);
        filter.addAction(BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED);
        filter.addAction(ACTION_ALARM_ALERT);
        try {
            mContext.registerReceiver(mReceiver, filter);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            mTelephonyManager.listen(mPhoneStateListener, PhoneStateListener.LISTEN_CALL_STATE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void stopListen() {
        if (!mIsListening) {
            return;
        }
        mIsListening = false;
        try {
            mContext.unregisterReceiver(mReceiver);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            mTelephonyManager.listen(mPhoneStateListener, PhoneStateListener.LISTEN_NONE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (AudioManager.ACTION_AUDIO_BECOMING_NOISY.equals(action)) {
                //耳机断开立即收到通知
                for (Callback callback : mCallbacks) {
                    callback.onHeadsetStateChanged(false);
                }
            } else if (Intent.ACTION_HEADSET_PLUG.equals(action)) {
                if (intent.hasExtra("state")) {
                    int state = intent.getIntExtra("state", 0);
                    if (state == 0) {
                        //耳机断开1秒内收到通知
                    } else if (intent.getIntExtra("state", 0) == 1) {
                        //耳机接入
                        for (Callback callback : mCallbacks) {
                            callback.onHeadsetStateChanged(true);
                        }
                    }
                }
            } else if (BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED.equals(action)) {
                int state = BluetoothAdapter.getDefaultAdapter().getProfileConnectionState(
                        BluetoothProfile.HEADSET);
                if (BluetoothProfile.STATE_DISCONNECTED == state) {
                    //蓝牙耳机断开
                } else if (BluetoothProfile.STATE_CONNECTED == state) {
                    //蓝牙耳机接入
                }
            } else if (ACTION_ALARM_ALERT.equals(action)) {
                for (Callback callback : mCallbacks) {
                    callback.onAlarmAlert();
                }
            }
        }
    };

    private PhoneStateListener mPhoneStateListener = new PhoneStateListener() {
        @Override
        public void onCallStateChanged(int state, String incomingNumber) {
            switch (state) {
                case TelephonyManager.CALL_STATE_IDLE:
                    // 电话挂断
                    break;
                case TelephonyManager.CALL_STATE_OFFHOOK:
                    // 来电响铃
                    for (Callback callback : mCallbacks) {
                        callback.onPhoneOffHook();
                    }
                    break;
                case TelephonyManager.CALL_STATE_RINGING:
                    // 来电接通或去电
                    for (Callback callback : mCallbacks) {
                        callback.onPhoneRinging();
                    }
                    break;
                default:
                    //do nothing
            }
        }
    };

    public void register(Callback callback) {
        mCallbacks.add(callback);
    }

    public void unregister(Callback callback) {
        mCallbacks.remove(callback);
    }

    public interface Callback {
        void onHeadsetStateChanged(boolean connected);

        void onPhoneRinging();

        void onPhoneOffHook();

        void onAlarmAlert();
    }

}
