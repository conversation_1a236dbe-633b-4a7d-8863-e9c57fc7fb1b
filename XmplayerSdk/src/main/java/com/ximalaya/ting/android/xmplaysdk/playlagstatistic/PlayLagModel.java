package com.ximalaya.ting.android.xmplaysdk.playlagstatistic;

import androidx.annotation.Keep;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021/5/12.
 */
@Keep
public class PlayLagModel {

    public int playType; //当前视频源类型  0：点播  1：直播
    public String playUrl; //当前播放url
    public int lagCount; //卡顿次数
    public long playTime; //视频播放时长
    public long[] jankTime; //卡顿时间
    public int androidPlayerType;//android播放器类型，（2 ijk ）  （3  exo）
}
