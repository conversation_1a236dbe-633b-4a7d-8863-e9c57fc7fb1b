package com.ximalaya.ting.android.xmplaysdk;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.google.android.exoplayer2.database.ExoDatabaseProvider;
import com.google.android.exoplayer2.upstream.DataSpec;
import com.google.android.exoplayer2.upstream.cache.Cache;
import com.google.android.exoplayer2.upstream.cache.CacheDataSource;
import com.google.android.exoplayer2.upstream.cache.CacheKeyFactory;
import com.google.android.exoplayer2.upstream.cache.CacheSpan;
import com.google.android.exoplayer2.upstream.cache.CacheWriter;
import com.google.android.exoplayer2.upstream.cache.LeastRecentlyUsedCacheEvictor;
import com.google.android.exoplayer2.upstream.cache.SimpleCache;
import com.ximalaya.ting.android.exoplayer.MediaCacheManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.NavigableSet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/**
 * Created by jack.qin on 2021/7/1.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Deprecated
public class VideoMediaCacheManager {
    public static final int DEFAULT_FLV_MIN_BUFFER_MS = 100;//缓冲最短时长

    public static final int DEFAULT_FLV_MAX_BUFFER_MS = 3000;//缓冲最短时长

//    public static final int DEFAULT_MIN_BUFFER_MS = 5 * 60 * 1000;//缓冲最短时长
    public static final int DEFAULT_MIN_BUFFER_MS = 10 * 1000;//缓冲最短时长
    public static final int DEFAULT_MAX_BUFFER_MS = 16 * 60 * 1000;//缓冲最大时长

    private static final int DEFAULT_SINGLE_CACHE_SIZE = 512 * 1024;
    private static final int DEFAULT_DISK_CACHE_SIZE = 1024 * 1024 * 100;//100M磁盘缓存

    private static SimpleCache cache;
    private static final String TAG = "MediaCacheManager";
    private CacheKeyFactory cacheKeyFactory;
//    private Map<Uri, Boolean> cacheMap = new ConcurrentHashMap<>();

    private ExecutorService executor;
    private volatile boolean isInit = false;

    private static VideoMediaCacheManager mMediaCacheManager;

    public static VideoMediaCacheManager get() {
        if (mMediaCacheManager == null) {
            synchronized (MediaCacheManager.class) {
                if (mMediaCacheManager == null) {
                    mMediaCacheManager = new VideoMediaCacheManager();
                }
            }
        }
        return mMediaCacheManager;
    }

    public synchronized void init(Context context, String cacheDirName, CacheKeyFactory cacheKeyFactory) {
        if (isInit) {
            return;
        }
        isInit = true;
        this.cacheKeyFactory = cacheKeyFactory;
        File cacheDir = null;
        try {
            cacheDir = getCacheDir(context, cacheDirName);
            if (!cacheDir.exists()) {
                if (!cacheDir.mkdirs()) {
                    String message = "Failed to create cache directory: " + cacheDir;
                    Logger.logToFile("XmExoMediaPlayer :1 " + message);
                    return;
                }
            }

            File[] files = cacheDir.listFiles();
            if (files == null) {
                String message = "Failed to list cache directory files: " + cacheDir;
                Logger.logToFile("XmExoMediaPlayer :2 " + message);
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }

        if (cache == null) {
            try {
                // https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/38437792?pid=1
                cache = new SimpleCache(cacheDir, new LeastRecentlyUsedCacheEvictor(DEFAULT_DISK_CACHE_SIZE), new ExoDatabaseProvider(context));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        executor = Executors.newSingleThreadExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                return new Thread(r, "PreLoadMeidaDataForExoplayer");
            }
        });
    }

    @Nullable
    public Cache getCache() {
        return cache;
    }

    private VideoMediaCacheManager() {
    }

    public void preload(final Uri uri, final CacheDataSource originSource) {

        if (!isInit || cache == null) {
            return;
        }

//        if (cacheMap.containsKey(uri)) {
//            return;
//        }
//
//        if (cacheMap.size() > 100) {
//            cacheMap.clear();
//        }

        executor.execute(new Runnable() {
            @Override
            public void run() {
                Logger.i(TAG, "preload url : " + uri.toString());
                DataSpec dataSpec = new DataSpec(uri, 0, DEFAULT_SINGLE_CACHE_SIZE);
//                boolean isCache = cache.isCached(cacheKeyFactory.buildCacheKey(dataSpec),0,DEFAULT_SINGLE_CACHE_SIZE);
//                if (isCache) {
//                    return;
//                }
//                cacheMap.put(uri, true);
//                MyProgressListener progressListener = new MyProgressListener(uri);
                CacheWriter cacheWriter = new CacheWriter(
                        originSource,
                        dataSpec,
                        new byte[DEFAULT_SINGLE_CACHE_SIZE],
                        null);  // an optional ProgressListener to listen to download progress
//                progressListener.writer = cacheWriter;

                try {
                    cacheWriter.cache();
                } catch (Exception e) {
                    e.printStackTrace();
//                    cacheMap.remove(uri);
                }

            }
        });
    }

    public void remove(final Uri uri) {

        if (!isInit || cache == null) {
            return;
        }

        executor.execute(() -> {
            DataSpec spec = new DataSpec(uri);
            try {
                NavigableSet<CacheSpan> cachedSpans = cache.getCachedSpans(cacheKeyFactory.buildCacheKey(spec));
                for (CacheSpan cachedSpan : cachedSpans) {
                    cache.removeSpan(cachedSpan);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    public static void deleteAllCacheFile() {
        try {
            recursiveDelete(mPlayerCacheFileDir);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static void recursiveDelete(File file) {

        //to end the recursive loop
        if (file == null || !file.exists()) {
            return;
        }

        //if directory, go inside and call recursively
        if (file.isDirectory()) {
            for (File f : file.listFiles()) {
                //call recursively
                recursiveDelete(f);
            }
        }
        file.delete();
    }

    public static File mPlayerCacheFileDir;

    public static File getCacheDir(Context context, String cacheDir) {
        File dir = new File(context.getExternalFilesDir(""), cacheDir);
//        File dir = new File("/sdcard/atest/");
        mPlayerCacheFileDir = dir;
        return dir;
    }

    public static String mPlayUrl;

    public static void setPlayUrlForTest(String playUrl) {
        mPlayUrl = playUrl;
    }

    public String downloadErrorFileForAnalyse(String playPath) {
        if (!isInit || cache == null || MediaCacheManager.mPlayerCacheFileDir == null || TextUtils.isEmpty(playPath) || !playPath.startsWith("http")) {
            return null;
        }

        Uri uri = Uri.parse(playPath);
        File outPutFile = new File(MediaCacheManager.mPlayerCacheFileDir.getParent(), "play_error_file/" + uri.getLastPathSegment().split("\\.")[0]);

        executor.execute(() -> {
            FileOutputStream fileOutput = null;
            InputStream inputStream = null;
            try {

                //确保缓存的错误文件不能超过20个
                File errorFileDir = outPutFile.getParentFile();
                if (errorFileDir.exists()) {
                    File[] files = errorFileDir.listFiles();
                    if (files != null && files.length > 20) {
                        for (File file : files) {
                            file.delete();
                        }
                    }
                }

                if (outPutFile.exists() && outPutFile.length() > 0) {
                    return;
                }
                outPutFile.getParentFile().mkdirs();

                URL url = new URL(playPath);
                HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
                urlConnection.setRequestMethod("GET");
                urlConnection.setDoOutput(true);
                urlConnection.connect();

                fileOutput = new FileOutputStream(outPutFile);
                inputStream = urlConnection.getInputStream();

                byte[] buffer = new byte[1024];
                int bufferLength = 0;

                while ((bufferLength = inputStream.read(buffer)) > 0) {
                    fileOutput.write(buffer, 0, bufferLength);
                }

            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (fileOutput != null) {
                    try {
                        fileOutput.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        });

        if (outPutFile != null) {
            return outPutFile.getAbsolutePath();
        }
        return null;
    }

}
