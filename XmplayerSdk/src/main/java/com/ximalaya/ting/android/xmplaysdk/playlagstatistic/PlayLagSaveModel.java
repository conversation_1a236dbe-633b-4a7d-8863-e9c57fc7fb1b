package com.ximalaya.ting.android.xmplaysdk.playlagstatistic;

import androidx.annotation.Keep;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by duruochen on 2021/5/12.
 */
@Deprecated
public class PlayLagSaveModel {

    public int playType; //当前视频源类型  0：点播  1：直播
    public String playUrl; //当前播放url
    public long playTime; //视频播放时长
    public List<Long> jankTime = new ArrayList<>(); //卡顿时间
    public long startTime; //开始播放时间
    public long lagStartTime;
    public boolean isSeeking;

}
