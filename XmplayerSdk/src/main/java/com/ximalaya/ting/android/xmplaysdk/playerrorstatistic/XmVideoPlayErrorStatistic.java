package com.ximalaya.ting.android.xmplaysdk.playerrorstatistic;

import android.text.TextUtils;
import android.util.Log;

import com.google.android.exoplayer2.ExoPlaybackException;
import com.google.gson.Gson;
import com.ximalaya.ting.android.player.cdn.CdnUtil;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 视频播放器播放错误监测上报
 * Created by duruochen on 2020/5/11.
 */
public class XmVideoPlayErrorStatistic {
    private static final String TAG = "XmVideoPlayErrorStatistic";
    private final Gson gson = new Gson();

    private XmVideoPlayErrorStatistic() {
    }

    private static class HolderClass {
        private final static XmVideoPlayErrorStatistic instance = new XmVideoPlayErrorStatistic();
    }

    public static XmVideoPlayErrorStatistic getInstance() {
        return HolderClass.instance;
    }


    private void postErrorRecord(boolean isLive, String url, int netErrorCode, String netRequestException, int systemError) {
        try {
            if(netRequestException == null) {
                netRequestException = "";
            }
            PlayErrorModel playErrorModel = new PlayErrorModel();
            playErrorModel.playType = isLive ? 1 : 0;
            playErrorModel.playUrl = url;
            playErrorModel.netErrorCode = netErrorCode;
            playErrorModel.netRequestException = netRequestException;
            playErrorModel.systemError = systemError;
            String str = gson.toJson(playErrorModel);
            XmLogger.log(PlayErrorConstants.TYPE, PlayErrorConstants.SUB_TYPE, str);
            Logger.i(TAG, "postErrorRecord " + str);
            CdnUtil.statToXDCSError(TAG, str);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Map<String, PlayUrlErrorSaveModel> mPlayUrlModel = new LinkedHashMap<String, PlayUrlErrorSaveModel>() {
        @Override
        protected boolean removeEldestEntry(Entry<String, PlayUrlErrorSaveModel> eldest) {
            return size() >= 3;
        }
    };


    //触发来自播放器onPlayError
    public void onPlayErrorForLast(boolean isLive, String url, int errorCode) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        PlayUrlErrorSaveModel playUrlErrorSaveModel = mPlayUrlModel.get(url);

        if (playUrlErrorSaveModel != null) {
            postErrorRecord(isLive, url, playUrlErrorSaveModel.getNetErrorCode(),
                    playUrlErrorSaveModel.getNetRequestException(), 0);
        } else {
            postErrorRecord(isLive, url, 0,
                    null, errorCode);
        }
    }

    //触发来自播放网络请求错误，存入请求错误code和exception
    public void onVideoPlayError(String url, int netErrorCode, String netRequestException) {
        Logger.d(TAG, "onVideoPlayError:" + url + "  netErrorCode:" + netErrorCode + "  netRequestException:" + netRequestException);
        try {
            PlayUrlErrorSaveModel playUrlErrorSaveModel = mPlayUrlModel.get(url);

            if (playUrlErrorSaveModel == null) {
                playUrlErrorSaveModel = new PlayUrlErrorSaveModel();
                mPlayUrlModel.put(url, playUrlErrorSaveModel);
                playUrlErrorSaveModel.setNetErrorCode(netErrorCode);
                playUrlErrorSaveModel.setNetRequestException(netRequestException);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String getExceptionContent(Throwable e) {
        try {
            String netRequestException = "";
            if (e != null) {
                String className = e.getClass().getName().toLowerCase();
                if (className.contains(PlayErrorConstants.HttpErrorMessage.TIMEOUT)) {
                    netRequestException = PlayErrorConstants.HttpErrorMessage.TIMEOUT;
                } else if (className.contains("connect")) {
                    netRequestException = PlayErrorConstants.HttpErrorMessage.CONNECT_EXCEPTION;
                } else if (className.contains(PlayErrorConstants.HttpErrorMessage.UNKNOWN_HOST)) {
                    netRequestException = PlayErrorConstants.HttpErrorMessage.UNKNOWN_HOST;
                }
                if (TextUtils.isEmpty(netRequestException)) {
                    Throwable cause = e.getCause();
                    if (cause != null) {
                        className = cause.getClass().getName().toLowerCase();
                        if (className.contains(PlayErrorConstants.HttpErrorMessage.TIMEOUT)) {
                            netRequestException = PlayErrorConstants.HttpErrorMessage.TIMEOUT;
                        } else if (className.contains("connect")) {
                            netRequestException = PlayErrorConstants.HttpErrorMessage.CONNECT_EXCEPTION;
                        } else if (className.contains(PlayErrorConstants.HttpErrorMessage.UNKNOWN_HOST)) {
                            netRequestException = PlayErrorConstants.HttpErrorMessage.UNKNOWN_HOST;
                        }
                    }
                }
                if (TextUtils.isEmpty(netRequestException)) {
                    netRequestException = PlayErrorConstants.HttpErrorMessage.OTHER;
                }
                return netRequestException;
            }
        }catch (Exception exception) {
            exception.printStackTrace();
        }
        return PlayErrorConstants.HttpErrorMessage.OTHER;

//        if (e instanceof SocketTimeoutException || (e != null && e.getCause() instanceof SocketTimeoutException)) {
//            netRequestException = "timeout";
//        } else if (e instanceof ConnectException || (e != null && e.getCause() instanceof ConnectException)) {
//            netRequestException = "connectexception";
//        } else if (e instanceof UnknownHostException || (e != null && e.getCause() instanceof UnknownHostException)) {
//            netRequestException = "unknownhost";
//        }
//
//        return netRequestException;
    }

}
