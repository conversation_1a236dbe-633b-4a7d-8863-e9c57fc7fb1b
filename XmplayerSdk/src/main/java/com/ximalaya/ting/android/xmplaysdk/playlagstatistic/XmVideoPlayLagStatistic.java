package com.ximalaya.ting.android.xmplaysdk.playlagstatistic;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.ximalaya.ting.android.player.cdn.CdnUtil;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 视频播放器卡顿率监测上报
 * Created by duruochen on 2021/5/12.
 */
@Deprecated
public class XmVideoPlayLagStatistic {
    private static final String TAG = "XmVideoPlayLagStatistic";

    private final Gson gson = new Gson();
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private boolean isLooping = false;

    private static class HolderClass {
        private static XmVideoPlayLagStatistic instance = new XmVideoPlayLagStatistic();
    }

    public static XmVideoPlayLagStatistic getInstance() {
        return HolderClass.instance;
    }

    private Map<String, PlayLagSaveModel> mPlayUrlModel = new LinkedHashMap<String, PlayLagSaveModel>() {
        @Override
        protected boolean removeEldestEntry(Entry<String, PlayLagSaveModel> eldest) {
            return size() >= 3;
        }
    };


    public void postLagRecord_(PlayLagModel playLagModel) {
        if (playLagModel == null) {
            return;
        }
        try {
            String str = gson.toJson(playLagModel);
            XmLogger.log(PlayLagConstants.TYPE, PlayLagConstants.SUB_TYPE, str);
            Logger.i(TAG, "postLagRecord_exo:" + str);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void postLagRecord(PlayLagSaveModel model) {
        if (model.jankTime.size() == 0) {
            return;
        }
        try {
            PlayLagModel playLagModel = new PlayLagModel();
            playLagModel.playType = model.playType;
            playLagModel.playUrl = model.playUrl;
            playLagModel.androidPlayerType = 2;
            playLagModel.playTime = model.playTime;
            playLagModel.lagCount = model.jankTime.size();
            long[] jankTime = new long[model.jankTime.size()];
            for (int i = 0; i < model.jankTime.size(); i++) {
                jankTime[i] = model.jankTime.get(i);
            }
            playLagModel.jankTime = jankTime;
            String str = gson.toJson(playLagModel);
            XmLogger.log(PlayLagConstants.TYPE, PlayLagConstants.SUB_TYPE, str);
            Logger.i(TAG, "postLagRecord:" + str);
            CdnUtil.statToXDCSError(TAG, str);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void startPlay(boolean isLive, String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        //如果之前app异常退出，则将本地的卡顿信息上报至apm
        if (!TextUtils.isEmpty(MMKVUtil.getInstance().getString(TAG))) {
            try {
                PlayLagSaveModel model = gson.fromJson(MMKVUtil.getInstance().getString(TAG), PlayLagSaveModel.class);
                MMKVUtil.getInstance().saveString(TAG, "");
                postLagRecord(model);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (mPlayUrlModel.get(url) == null) {
            PlayLagSaveModel model = new PlayLagSaveModel();
            model.playType = isLive ? 1 : 0;
            model.playUrl = url;
            model.startTime = System.currentTimeMillis();
            mPlayUrlModel.put(url, model);
        }
    }

    //发生卡顿，每隔1分钟将卡顿信息写至本地
    public void lagStart(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        Logger.i(TAG, "lagStart:" + url);
        if (mPlayUrlModel.get(url) != null) {
            PlayLagSaveModel model = mPlayUrlModel.get(url);
            if (model.isSeeking) {
                Logger.i(TAG, "lagStart isSeeking");
                model.lagStartTime = 0;
                return;
            }
            model.lagStartTime = System.currentTimeMillis();

            if (!isLooping) {
                isLooping = true;
                mHandler.postDelayed(mRunnable, 60 * 1000);
            }
        }
    }

    public void seekStart(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        if (mPlayUrlModel.get(url) != null) {
            PlayLagSaveModel model = mPlayUrlModel.get(url);
            model.isSeeking = true;
        }
    }

    public void seekEnd(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        if (mPlayUrlModel.get(url) != null) {
            PlayLagSaveModel model = mPlayUrlModel.get(url);
            model.isSeeking = false;
        }
    }

    //卡顿结束
    public void lagEnd(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        if (mPlayUrlModel.get(url) != null) {
            PlayLagSaveModel model = mPlayUrlModel.get(url);
            if (model.lagStartTime == 0) {
                Logger.i(TAG, "此处为seek结束，不算做卡顿");
                return;
            }
            long lag = System.currentTimeMillis() - model.lagStartTime;
            if (model.lagStartTime > 0 && lag >= 200) {
                model.jankTime.add(lag);
                Logger.i(TAG, "lagEnd:" + url);
            }
            model.lagStartTime = 0;
        }
    }

    private Runnable mRunnable = new Runnable() {
        @Override
        public void run() {
            if (mPlayUrlModel.size() > 0) {
                PlayLagSaveModel model = mPlayUrlModel.values().iterator().next();
                model.playTime = System.currentTimeMillis() - model.startTime;
                String str = gson.toJson(model);
                Logger.i(TAG, "save to mmkv:" + str);
                MMKVUtil.getInstance().saveString(TAG, str);
                mHandler.postDelayed(mRunnable, 60 * 1000);
            }
        }
    };

    //结束播放，若有卡顿现象则上报至apm
    public void endPlay(String url) {
        Logger.i(TAG, "endpaly:" + url);
        if (TextUtils.isEmpty(url)) {
            return;
        }
        if (mPlayUrlModel.get(url) != null) {
            PlayLagSaveModel model = mPlayUrlModel.get(url);
            model.playTime = System.currentTimeMillis() - model.startTime;
            MMKVUtil.getInstance().saveString(TAG, "");
            postLagRecord(model);
            mPlayUrlModel.remove(url);

            mHandler.removeCallbacks(mRunnable);
            isLooping = false;
        }
    }
}
