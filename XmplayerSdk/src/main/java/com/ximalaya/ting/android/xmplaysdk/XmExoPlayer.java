package com.ximalaya.ting.android.xmplaysdk;

import android.content.Context;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.Surface;
import android.view.SurfaceHolder;

import com.google.android.exoplayer2.C;
import com.google.android.exoplayer2.DefaultLoadControl;
import com.google.android.exoplayer2.ExoPlaybackException;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackParameters;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.SimpleExoPlayer;
import com.google.android.exoplayer2.analytics.AnalyticsListener;
import com.google.android.exoplayer2.analytics.XmPlaybackStats;
import com.google.android.exoplayer2.analytics.XmPlaybackStatsListener;
import com.google.android.exoplayer2.ext.rtmp.RtmpDataSource;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.source.dash.DashMediaSource;
import com.google.android.exoplayer2.source.hls.HlsMediaSource;
import com.google.android.exoplayer2.source.smoothstreaming.SsMediaSource;
import com.google.android.exoplayer2.upstream.AssetDataSource;
import com.google.android.exoplayer2.upstream.BandwidthMeter;
import com.google.android.exoplayer2.upstream.ContentDataSource;
import com.google.android.exoplayer2.upstream.DataSchemeDataSource;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DataSpec;
import com.google.android.exoplayer2.upstream.DefaultBandwidthMeter;
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
import com.google.android.exoplayer2.upstream.DefaultHttpDataSource;
import com.google.android.exoplayer2.upstream.FileDataSource;
import com.google.android.exoplayer2.upstream.RawResourceDataSource;
import com.google.android.exoplayer2.upstream.TransferListener;
import com.google.android.exoplayer2.upstream.cache.Cache;
import com.google.android.exoplayer2.upstream.cache.CacheDataSink;
import com.google.android.exoplayer2.upstream.cache.CacheDataSource;
import com.google.android.exoplayer2.util.Util;
import com.google.android.exoplayer2.video.VideoListener;
import com.google.gson.Gson;
import com.ximalaya.ting.android.exoplayer.CustomDefaultLoadControl;
import com.ximalaya.ting.android.exoplayer.datasource.DataSourceFactory;
import com.ximalaya.ting.android.host.manager.application.ConfigureInit;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.player.MD5;
import com.ximalaya.ting.android.xmplaysdk.playlagstatistic.PlayLagModel;
import com.ximalaya.ting.android.xmplaysdk.playlagstatistic.XmVideoPlayLagStatistic;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.player.XMediaplayerImpl;

import java.io.FileDescriptor;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.ximalaya.ting.android.player.video.player.AbstractMediaPlayer;
import com.ximalaya.ting.android.player.video.player.IMediaPlayer;
import com.ximalaya.ting.android.player.video.player.model.MediaInfo;
import com.ximalaya.ting.android.player.video.player.misc.ITrackInfo;

/**
 * Created by jack.qin on 2021/6/23.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Deprecated
public class XmExoPlayer extends AbstractMediaPlayer {
    private final Context mContext;
    private SimpleExoPlayer mPlayer;
    protected String mUrl;
    private MediaSource mMediaSource;
    private PlayerEventListener mEventListener;
    private PlayerVideoListener mVideoListener;
    private int mPlayState = PREPARE_NULL;
    private int mVideoWidth;
    private int mVideoHeight;
    private Surface mSurface;
    private Handler mHandler;
    private static final String TAG = "XmExoPlayer__";
    private CustomDefaultLoadControl mLoadControl;
    private boolean isLive;
    public static boolean sStatisticVideoPlayLag = false;

    private static final int PREPARE_NULL = 0x0;
    private static final int PREPARING_STATE = 0x1;
    private static final int PREPARED_STATE = 0x2;
    private static final int ENDED_STATE = 0x3;
    private boolean mIsInitPlayerListener = false;
    private double netSpeed;

    private static final String SCHEME_ASSET = "asset";
    private static final String SCHEME_CONTENT = "content";
    private static final String SCHEME_RTMP = "rtmp";
    private static final String SCHEME_RAW = RawResourceDataSource.RAW_RESOURCE_SCHEME;

    public XmExoPlayer(Context context) {
        this.mContext = context;
        mPlayer = createPlayer();
        mHandler = new Handler(Looper.getMainLooper());

        if (Looper.myLooper() == Looper.getMainLooper()) {
            if (!mIsInitPlayerListener) {
                initPlayerListener();
            }
        } else {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    if (!mIsInitPlayerListener) {
                        initPlayerListener();
                    }
                }
            });
        }

    }

    public void setIsLive(boolean isLive) {
        this.isLive = isLive;
    }

    private SimpleExoPlayer createPlayer() {
        long time = System.currentTimeMillis();
        VideoMediaCacheManager.get().init(mContext, "video_exo_player_cache", dataSpec -> dataSpec.key != null ? dataSpec.key : MD5.md5(dataSpec.uri.getPath()));
        mLoadControl = new CustomDefaultLoadControl.Builder()
                .setBufferDurationsMs(
                        VideoMediaCacheManager.DEFAULT_MIN_BUFFER_MS,
                        VideoMediaCacheManager.DEFAULT_MAX_BUFFER_MS,
                        DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_MS,
                        DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS)
                .build();
        if (isLive) {
            mLoadControl.setIsLive(true, XMediaplayerImpl.TYPE_AUDIO);
        }
        mPlayer = new SimpleExoPlayer.Builder(mContext).setLooper(Looper.getMainLooper()).setLoadControl(mLoadControl).build();
        XmPlaybackStatsListener sPlaybackStatsListener =
                new XmPlaybackStatsListener(/* keepHistory= */ false, /* callback= */ new XmPlaybackStatsListener.Callback() {
                    @Override
                    public void onPlaybackStatsReady(AnalyticsListener.EventTime eventTime, XmPlaybackStats playbackStats, List<Long> rebufferDurationsMsList) {
//                        if (PlaybackStatsListener.hasError) {
//                            return;
//                        }
                        if (!ConfigureInit.sStatisticPlayError || ConstantsOpenSdk.isDebug) {
                            Logger.i("play_video_lag", "not_statistic_video_lag");
                            return;
                        }
                        try {
                            if (playbackStats == null || isLive) {// 直播的卡顿上传不在此处进行
                                return;
                            }
                            if (!ConstantsOpenSdk.isDebug) {
                                if (playbackStats.getTotalPlayTimeMs() < 20000) {
                                    return;
                                }
                            }
                            if (playbackStats.getPlayRebufferDurationsMs() == null) {
                                return;
                            }

                            if (playbackStats.getTotalRebufferTimeMs() > playbackStats.getTotalPlayTimeMs()) {
                                return;
                            }

                            double time1 = playbackStats.getTotalRebufferTimeMs();
                            double time2 = playbackStats.getTotalPlayTimeMs();
                            if (time1 / time2 > 0.5) {
                                return;
                            }

                            PlayLagModel playLagModel = new PlayLagModel();

                            long jankTime = 0;
                            List<Long> lagDurationsMs = new ArrayList<>();
                            for (Long lagTime : playbackStats.getPlayRebufferDurationsMs()) {
                                if (lagTime != null && lagTime > 500) {
                                    lagDurationsMs.add(lagTime);
                                    jankTime += lagTime;
                                }
                            }

                            playLagModel.lagCount = lagDurationsMs.size();
                            playLagModel.playUrl = mUrl;
                            playLagModel.playType = isLive ? 1 : 0;
                            if (playLagModel.lagCount > 0) {
                                playLagModel.jankTime = new long[playLagModel.lagCount];
//                                playLagModel.lagThreshold = 1000;
                                for (int i = 0; i < lagDurationsMs.size(); i++) {
                                    playLagModel.jankTime[i] = lagDurationsMs.get(i);
                                }
                            } else {
                                playLagModel.jankTime = new long[1];
                                playLagModel.jankTime[0] = 0;
                            }
                            playLagModel.playTime = playbackStats.getTotalPlayTimeMs() + jankTime;
                            playLagModel.androidPlayerType = 3;
                            Gson gson = new Gson();
                            String str = gson.toJson(playLagModel);
                            Logger.i("play_video_lag", str);
                            Logger.i("play_video_lag", "totalRebufferCount=" + playbackStats.totalRebufferCount
                                    + "maxRebufferTimeMs= " + playbackStats.maxRebufferTimeMs
                                    + "getTotalRebufferTimeMs=  " + playbackStats.getTotalRebufferTimeMs()
                                    + "getTotalPlayTimeMs=  " + playbackStats.getTotalPlayTimeMs());
                            for (Long rebufferTime : playbackStats.getPlayRebufferDurationsMs()) {
                                Log.i("play_video_lag", "rebufferTime " + rebufferTime);
                            }
                            XmVideoPlayLagStatistic.getInstance().postLagRecord_(playLagModel);
                        } catch (Throwable throwable) {
                            throwable.printStackTrace();
                        }
                    }
                });
        mPlayer.addAnalyticsListener(sPlaybackStatsListener);
        return mPlayer;
    }

    @Override
    public void setDisplay(SurfaceHolder sh) {
        if (sh == null)
            setSurface(null);
        else
            setSurface(sh.getSurface());
    }

    @Override
    public void setDataSource(Context context, Uri uri) throws IOException, IllegalArgumentException, SecurityException, IllegalStateException {
        setDataSource(uri.toString());
    }

    @Override
    public void setDataSource(Context context, Uri uri, Map<String, String> headers) throws IOException, IllegalArgumentException, SecurityException, IllegalStateException {
        setDataSource(uri.toString());
    }

    @Override
    public void setDataSource(FileDescriptor fd) throws IOException, IllegalArgumentException, IllegalStateException {

    }

    @Override
    public void setDataSource(final String path) throws IOException, IllegalArgumentException, SecurityException, IllegalStateException {
        Uri uri = Uri.parse(path);
        mMediaSource = createMediaSource(uri, null);
        mUrl = uri.toString();
        ///storage/emulated/0/Android/data/com.ximalaya.ting.android/files/Documents/ting/record/content_product/audio_video_no_watermark/06de249f22c83a192238ddf32d271c6c_no_lrc.mp4
    }

    @Override
    public String getDataSource() {
        return mUrl;
    }

    @Override
    public void prepareAsync() throws IllegalStateException {
        if (mMediaSource == null) {
            return;
        }

        if (mHandler != null) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    if (mPlayer == null) {
                        mPlayer = createPlayer();
                    }
                    if (mSurface != null)
                        mPlayer.setVideoSurface(mSurface);

                    mPlayer.setMediaSource(mMediaSource);
                    mPlayer.prepare();
                    mPlayer.setPlayWhenReady(false);
                    mPlayState = PREPARING_STATE;
                }
            });
        }
    }

    @Override
    public void start() throws IllegalStateException {
        if (mPlayer != null && mHandler != null) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    if (mPlayState == ENDED_STATE) {
                        if (!TextUtils.isEmpty(mUrl)) {
                            try {
                                prepareAsync();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }

                    } else {
                        mPlayer.setPlayWhenReady(true);
                    }
                }
            });
        }
    }

    @Override
    public void stop() throws IllegalStateException {
        if (mPlayer != null && mHandler != null) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    mPlayer.stop();
                }
            });
        }
    }

    @Override
    public void pause() throws IllegalStateException {
        if (mPlayer != null && mHandler != null) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    mPlayer.setPlayWhenReady(false);
                }
            });
        }
    }

    @Override
    public void setScreenOnWhilePlaying(boolean screenOn) {

    }

    @Override
    public int getVideoWidth() {
        return mVideoWidth;
    }

    @Override
    public int getVideoHeight() {
        return mVideoHeight;
    }

    @Override
    public boolean isPlaying() {
        return mPlayer.isPlaying();
    }

    @Override
    public void seekTo(final long msec) throws IllegalStateException {
        if (mPlayer != null && mHandler != null) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    mPlayer.seekTo(msec);
                }
            });
        }
    }

    @Override
    public long getCurrentPosition() {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            return 0;
        }
        return mPlayer.getCurrentPosition();
    }

    @Override
    public long getDuration() {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            return 0;
        }
        return mPlayer.getDuration();
    }

    @Override
    public void release() {
        if (mPlayer != null && mHandler != null) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    mPlayer.removeVideoListener(mVideoListener);
                    mPlayer.removeListener(mEventListener);
                    mPlayer.release();
                }
            });
        }
    }

    @Override
    public void reset() {
        if (mPlayer != null && mHandler != null) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    mPlayer.stop(true);
                }
            });
        }
    }

    @Override
    public void setVolume(final float leftVolume, float rightVolume) {
        if (mPlayer != null && mHandler != null) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    mPlayer.setVolume(leftVolume);
                }
            });
        }
    }

    @Override
    public int getAudioSessionId() {
        return 0;
    }

    @Override
    public void setSpeed(float speed) {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            mHandler.post(() -> setSpeed(speed));
            return;
        }
        if (mPlayer == null) {
            return;
        }

        PlaybackParameters lastPlaybackParameters = mPlayer.getPlaybackParameters();
        if (lastPlaybackParameters != null && lastPlaybackParameters.speed == speed) {
            return;
        }
        PlaybackParameters playbackParameters = new PlaybackParameters(speed);
        mPlayer.setPlaybackParameters(playbackParameters);
    }

    @Override
    public float getSpeed() {
        // 如果是非主线程 是拿不到准确数据的
        if (Looper.myLooper() != Looper.getMainLooper()) {
            return 1.0f;
        }
        if (mPlayer == null) {
            return 1.0f;
        }
        PlaybackParameters playbackParameters = mPlayer.getPlaybackParameters();
        return playbackParameters != null ? playbackParameters.speed : 1.0f;
    }

    @Override
    public MediaInfo getMediaInfo() {
        return null;
    }

    @Override
    public void setLogEnabled(boolean enable) {

    }

    @Override
    public boolean isPlayable() {
        return false;
    }

    @Override
    public int getBufferPercentage() {
        return 0;
    }

    @Override
    public long getTotalBufferedDuration() {
        if (mPlayer != null) {
            return mPlayer.getTotalBufferedDuration();
        }
        return 0;
    }

    @Override
    public void changeResolution(int index) {

    }

    @Override
    public void setAudioStreamType(int streamtype) {

    }

    @Override
    public void setKeepInBackground(boolean keepInBackground) {

    }

    @Override
    public int getVideoSarNum() {
        return 0;
    }

    @Override
    public int getVideoSarDen() {
        return 0;
    }

    @Override
    public void setWakeMode(Context context, int mode) {

    }

    @Override
    public void setLooping(boolean looping) {

    }

    @Override
    public boolean isLooping() {
        return false;
    }

    @Override
    public ITrackInfo[] getTrackInfo() {
        return new ITrackInfo[0];
    }

    @Override
    public void setSurface(final Surface surface) {
        if (mPlayer != null && mHandler != null) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    mSurface = surface;
                    mPlayer.setVideoSurface(surface);
                }
            });
        }
    }

    @Override
    public void seekTo2(final long msec) throws IllegalStateException {
        if (mPlayer != null && mHandler != null) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    seekTo(msec);
                }
            });
        }
    }

    @Override
    public double getNetSpeed() {
        return netSpeed;
    }

    private DataSource.Factory buildDataSourceFactory(Uri uri) {
        DefaultBandwidthMeter bandwidthMeter = new DefaultBandwidthMeter.Builder(mContext).build();
        bandwidthMeter.addEventListener(new Handler(Looper.getMainLooper()), new BandwidthMeter.EventListener() {
            @Override
            public void onBandwidthSample(int elapsedMs, long bytesTransferred, long bitrateEstimate) {
                Logger.i(TAG, "elapsedMs=" + elapsedMs + ",bytesTransferred=" + bytesTransferred + ",bitrateEstimate=" + bitrateEstimate);
                if (bitrateEstimate > 0) {
                    netSpeed = (double) bitrateEstimate / 8 / 1024;
                }
            }
        });

        String scheme = uri.getScheme();
        if (com.google.android.exoplayer2.util.Util.isLocalFileUri(uri)) {
            if (uri.getPath() != null && uri.getPath().startsWith("/android_asset/")) {
                return new DataSourceFactory(new AssetDataSource(mContext));
            }
            return new FileDataSource.Factory();
        }
        if (SCHEME_ASSET.equals(scheme)) {
            return new DataSourceFactory(new AssetDataSource(mContext));
        }
        if (SCHEME_CONTENT.equals(scheme)) {
            return new DataSourceFactory(new ContentDataSource(mContext));
        }
        if (SCHEME_RTMP.equals(scheme)) {
            return new DataSourceFactory(new RtmpDataSource());
        }
        if (DataSchemeDataSource.SCHEME_DATA.equals(scheme)) {
            return new DataSourceFactory(new DataSchemeDataSource());
        }
        if (SCHEME_RAW.equals(scheme)) {
            return new DataSourceFactory(new RawResourceDataSource(mContext));
        }

        Cache cache = VideoMediaCacheManager.get().getCache();
        if (cache == null || isLive) {
            return new DefaultDataSourceFactory(mContext, bandwidthMeter, new DefaultHttpDataSource.Factory());
        }

        CacheDataSource.Factory factory = new CacheDataSource.Factory();
        factory.setUpstreamDataSourceFactory(new DefaultHttpDataSource.Factory().setTransferListener(bandwidthMeter));
        factory.setCache(cache);
        factory.setCacheReadDataSourceFactory(new FileDataSource.Factory().setListener(new TransferListener() {
            @Override
            public void onTransferInitializing(DataSource source, DataSpec dataSpec, boolean isNetwork) {

            }

            @Override
            public void onTransferStart(DataSource source, DataSpec dataSpec, boolean isNetwork) {
            }

            @Override
            public void onBytesTransferred(DataSource source, DataSpec dataSpec, boolean isNetwork, int bytesTransferred) {
                if (dataSpec.position == 0) {
                    Logger.i("video_cache_print", dataSpec.uri.toString());
                }
            }

            @Override
            public void onTransferEnd(DataSource source, DataSpec dataSpec, boolean isNetwork) {

            }
        }));
        factory.setCacheWriteDataSinkFactory(new CacheDataSink.Factory().setCache(cache));
        factory.setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR);
        factory.setCacheKeyFactory(dataSpec -> dataSpec.key != null ? dataSpec.key : Objects.requireNonNull(MD5.md5(dataSpec.uri.getPath())));
        return factory;
    }

    private MediaSource createMediaSource(Uri uri, String extension) {
        netSpeed = 0;
        int type = Util.inferContentType(uri, extension);
        MediaItem mediaItem = MediaItem.fromUri(uri);
        DataSource.Factory dataSourceFactory = buildDataSourceFactory(uri);
        switch (type) {
            case C.TYPE_DASH:
                return new DashMediaSource.Factory(dataSourceFactory)
                        .createMediaSource(mediaItem);
            case C.TYPE_SS:
                return new SsMediaSource.Factory(dataSourceFactory)
                        .createMediaSource(mediaItem);
            case C.TYPE_HLS:
                return new HlsMediaSource.Factory(dataSourceFactory)
                        .createMediaSource(mediaItem);
            case C.TYPE_OTHER:
                return new ProgressiveMediaSource.Factory(dataSourceFactory)
                        .createMediaSource(mediaItem);
            default:
                throw new IllegalStateException("Unsupported type: " + type);
        }
    }

    private class PlayerEventListener implements Player.EventListener {
        private boolean mIsBuffering = false;

        @Override
        public void onPlaybackStateChanged(int playbackState) {
            Log.d("xxx", "onPlayerStateChanged  , playbackState=" + playbackState);

            if (mIsBuffering) {
                switch (playbackState) {
                    case Player.STATE_ENDED:
                    case Player.STATE_READY:
                        if (mHandler != null) {
                            mHandler.post(new Runnable() {
                                @Override
                                public void run() {
                                    notifyOnInfo(IMediaPlayer.MEDIA_INFO_BUFFERING_END, mPlayer.getBufferedPercentage());
                                }
                            });
                        }
                        mIsBuffering = false;
                        break;
                }
            }

            switch (playbackState) {
                case Player.STATE_BUFFERING:
                    if (mHandler != null) {
                        mHandler.post(new Runnable() {
                            @Override
                            public void run() {
                                notifyOnInfo(IMediaPlayer.MEDIA_INFO_BUFFERING_START, mPlayer.getBufferedPercentage());
                            }
                        });
                    }
                    mIsBuffering = true;
                    break;
                case Player.STATE_IDLE:
//                    notifyOnCompletion();
                    break;
                case Player.STATE_READY:
                    if (mPlayState == PREPARING_STATE) {
                        if (mHandler != null) {
                            mHandler.post(new Runnable() {
                                @Override
                                public void run() {

                                    notifyOnPrepared();
                                }
                            });
                        }
                        mPlayState = PREPARED_STATE;
                    }
                    break;
                case Player.STATE_ENDED:
                    mPlayState = ENDED_STATE;
                    if (mHandler != null) {
                        mHandler.post(new Runnable() {
                            @Override
                            public void run() {
                                notifyOnCompletion();
                            }
                        });
                    }
                    break;
                default:
                    break;
            }
        }

        @Override
        public void onPlayerError(ExoPlaybackException error) {
            notifyOnError(error.type, 1);
        }

        @Override
        public void onIsPlayingChanged(boolean isPlaying) {
            Log.d("xxx", "onIsPlayingChanged isPlaying=" + isPlaying);
        }
    }

    private class PlayerVideoListener implements VideoListener {

        @Override
        public void onVideoSizeChanged(final int width, final int height, final int unappliedRotationDegrees, float pixelWidthHeightRatio) {
            mVideoWidth = width;
            mVideoHeight = height;
            if (mHandler != null) {
                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        notifyOnVideoSizeChanged(width, height, 1, 1);
                        if (unappliedRotationDegrees > 0)
                            notifyOnInfo(IMediaPlayer.MEDIA_INFO_VIDEO_ROTATION_CHANGED, unappliedRotationDegrees);
                    }
                });
            }
        }

        @Override
        public void onRenderedFirstFrame() {
            notifyOnInfo(3, 0);
        }

        @Override
        public void onSurfaceSizeChanged(int width, int height) {

        }
    }

    private void initPlayerListener() {
        mEventListener = new PlayerEventListener();
        mVideoListener = new PlayerVideoListener();
        mPlayer.addListener(mEventListener);
        mPlayer.addVideoListener(mVideoListener);
        mIsInitPlayerListener = true;
    }

    @Override
    public void setContentId(long contentId) {

    }
}
