apply plugin: 'com.android.library'

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    api rootProject.ext.xmDependencies.androidxAnnotations
//    compileOnly 'com.ximalaya.ting.android.xmedia:xmedia-' + XMEDIA_REPO_BRANCH + ':' + XMEDIA_REPO_VERSION
//    api 'com.ximalaya.ting.android.xmvideo:xmvideo-' + XM_VIDEO_REPO_BRANCH + ':' + XM_VIDEO_REPO_VERSION + '@aar'

    compileOnly 'com.ximalaya.ting.android.xmlogmanager:xmlogmanager-' + XMLOGMANAGER_REPO_BRANCH + ':' + XMLOGMANAGER_REPO_VRSION
    compileOnly 'com.ximalaya.ting.android.xmlymmkv:xmlymmkv-' + XmMMKV_REPO_BRANCE + ':' + XmMMKV_REPO_VERSION
    api project(':TingMainHost:TingMainApp')
}

android {
    // http://tools.android.com/tech-docs/new-build-system/tips
    //noinspection GroovyAssignabilityCheck
    compileSdkVersion rootProject.ext.compileSdkVersion
    //noinspection GroovyAssignabilityCheck
    buildToolsVersion rootProject.ext.buildToolsVersion

    lintOptions {
        abortOnError false
    }
    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets.main {
//        jniLibs.srcDirs = ['src/main/libs']
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

//apply from: new File(rootProject.projectDir, "tools/gradle-on-demand.gradle");
