ext {
    xmDependencies = [
            paletteV7         :'com.android.support:palette-v7:26.1.0',
            supportV4         : 'com.android.support:support-v4:26.1.0',
            supportAnnotations: 'com.android.support:support-annotations:26.1.0',
            supportAppCompatV7: 'com.android.support:appcompat-v7:26.1.0',
            recyclerView: 'com.android.support:recyclerview-v7:26.1.0',
            gson: 'com.google.code.gson:gson:2.6.1',
            fastJson: 'com.alibaba:fastjson:1.2.9',
            junit: 'junit:junit:4.12',
            multiDex: 'com.android.support:multidex:1.0.1',
            okHttp: 'com.squareup.okhttp3:okhttp:3.8.1',
            converterGson: 'com.squareup.retrofit2:converter-gson:2.0.0',
            wechatSdkVersion  : 'com.tencent.mm.opensdk:wechat-sdk-android-without-mta:1.4.0',
            okHttpUrlConnect : 'com.squareup.okhttp3:okhttp-urlconnection:3.8.1',
            picasso : 'com.squareup.picasso:picasso:2.5.2',
            gifDrawable : 'pl.droidsonroids.gif:android-gif-drawable:1.2.10'
    ]
    //编译控制参数
    /**
        2. ALL_COMPILE_SRC  是否生产全量包
        3. BUILD_PLUGIN_APK  是否生产包含插件的apk

     */

    versionCode = "138"
    versionName = "6.3.90.3.dev"
    compileSdkVersion = 26
    buildToolsVersion = "26.0.2"
    minSdkVersion = "16"
    targetSdkVersion = "23"

    openBlockCanary = "false"  //是否开启BlockCanary
    generate_public_xml = false
    //extChannels = "and-d3"//不能命名test/debug等字段开头
    channel = "and-f5-ocpa"// 开发时渠道改为 ceshi
    needChannel = "false"  //生成的apk中是否需要加入渠道信息
    modifyApplicationName = "false"  //是否需要修改应用名称
    applicationName = "喜马拉雅FM"
    //pChannels shell 输入参数
    shallReadChannelfromFile = "false"
    isProguard = "false"
    isReleaseDebug = "true"
    isDebugGradle = "true"  //控制是否输出gradle 调试信息


    createBaseJar = "true"
    createPatchJar = "false"
}

if(rootProject.isDebugGradle.toBoolean()){
    println "=====================set ext property start====================="
}
if (project.hasProperty('pChannels')) {
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pChannels")
    }
    rootProject.channel = project.property('pChannels')
}

if (project.hasProperty('pVersionCode')) {
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pVersionCode")
    }
    rootProject.versionCode = project.property('pVersionCode')
}

if (project.hasProperty('pVersionName')) {
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pVersionName")
    }
    rootProject.versionName = project.property('pVersionName')
}

if (project.hasProperty('pIsProguard')) {
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pIsProguard")
    }
    rootProject.isProguard = project.property('pIsProguard')
}

if (project.hasProperty('pIsReleaseDebug')) {
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pIsReleaseDebug")
    }
    rootProject.isReleaseDebug = project.property('pIsReleaseDebug')
}

if (project.hasProperty('pNeedChannel')) {
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pNeedChannel")
    }
    rootProject.needChannel = project.property('pNeedChannel')
}

if(project.hasProperty('pModifyApplicationName')){
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pModifyApplicationName")
    }
    rootProject.modifyApplicationName = project.property('pModifyApplicationName')
}

if(project.hasProperty('pApplicationName')){
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pApplicationName")
    }
    rootProject.applicationName = project.property('pApplicationName')
}

if(project.hasProperty("pOpenBlockCanary")){
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pOpenBlockCanary")
    }
    rootProject.openBlockCanary = project.property('pOpenBlockCanary')
}


if(project.hasProperty('pCreateBaseJar')){
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pCreateBaseJar")
    }
    rootProject.createBaseJar = project.property('pCreateBaseJar')
}

if(project.hasProperty('pCreatePatchJar')){
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pCreatePatchJar")
    }
    rootProject.createPatchJar = project.property('pCreatePatchJar')
}

if(rootProject.isDebugGradle.toBoolean()) {
    println "=====================set ext property end====================="
}

task printExtProperties doLast{
    println "============================"
    println "channel: " + channel
    println "versionCode: " + versionCode
    println "versionName: " + versionName
    println "isProguard: " + isProguard
    println "isReleaseDebug: " + isReleaseDebug
    println "needChannel: " + needChannel
    println "modifyApplicationName: " + modifyApplicationName
    println "applicationName: " + applicationName
    println "openBlockCanary: " + openBlockCanary
    println "============================"
}



