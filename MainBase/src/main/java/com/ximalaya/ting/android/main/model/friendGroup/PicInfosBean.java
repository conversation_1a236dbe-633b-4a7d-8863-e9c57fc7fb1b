package com.ximalaya.ting.android.main.model.friendGroup;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class PicInfosBean {
    private int id;
    private String originUrl;
    private String rectangleUrl;
    private String squareUrl;

    public PicInfosBean() {
    }

    private static PicInfosBean parse(JSONObject jsonObject) {
        if (jsonObject != null) {
            PicInfosBean picInfosBean = new PicInfosBean();
            picInfosBean.id = jsonObject.optInt("id");
            picInfosBean.originUrl = jsonObject.optString("originUrl");
            picInfosBean.rectangleUrl = jsonObject.optString("rectangleUrl");
            picInfosBean.squareUrl = jsonObject.optString("squareUrl");
            return picInfosBean;
        }
        return null;
    }

    public static List<PicInfosBean> parse(JSONArray array) {
        if (array != null && array.length() > 0) {
            ArrayList<PicInfosBean> picInfosBeans = new ArrayList<>();
            for (int i = 0, len = array.length(); i < len; i++) {
                String pic = array.optString(i);
                if (TextUtils.isEmpty(pic)) {
                    continue;
                }
                PicInfosBean bean = PicInfosBean.parse(array.optJSONObject(i));
                if (bean == null) {
                    continue;
                }
                picInfosBeans.add(bean);
            }
            return picInfosBeans;
        }
        return null;
    }

    public PicInfosBean(String originUrl, String rectangleUrl, String squareUrl) {
        this.originUrl = originUrl;
        this.rectangleUrl = rectangleUrl;
        this.squareUrl = squareUrl;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getOriginUrl() {
        return originUrl;
    }

    public void setOriginUrl(String originUrl) {
        this.originUrl = originUrl;
    }

    public String getRectangleUrl() {
        return rectangleUrl;
    }

    public void setRectangleUrl(String rectangleUrl) {
        this.rectangleUrl = rectangleUrl;
    }

    public String getSquareUrl() {
        return squareUrl;
    }

    public void setSquareUrl(String squareUrl) {
        this.squareUrl = squareUrl;
    }
}