@file:JvmMultifileClass
@file:JvmName("ViewExtensions")

package com.ximalaya.ting.android.main.mine.extension

import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import android.view.animation.AlphaAnimation
import android.widget.ImageView
import androidx.annotation.ColorInt
import androidx.annotation.IntDef
import com.astuetz.PagerSlidingTabStrip
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper

/**
 * Created by dekai.liu on 3/24/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18392566603
 */
fun ImageView?.fitDark(@ColorInt color: Int = 0xFFDCDCDC.toInt()) {
    this?.apply {
        if (BaseFragmentActivity2.sIsDarkMode) {
            colorFilter = PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN)
        }
    }
}

fun PagerSlidingTabStrip?.setDefaultIndicatorStyle() {
    this?.apply {
        setIndicatorGradientColors(intArrayOf(0xFFFF4C2E.toInt(), 0xFFFFA697.toInt()))
    }
}

@IntDef(View.VISIBLE, View.INVISIBLE, View.GONE)
@kotlin.annotation.Retention(AnnotationRetention.SOURCE)
annotation class Visibility

fun View?.visible(@Visibility visibility: Int) {
    this?.also { v ->
        if (v.visibility != visibility) {
            v.visibility = visibility
        }
    }
}

@JvmOverloads
fun View?.bindData(moduleType: String = AutoTraceHelper.MODULE_DEFAULT,
                   data: Any? = "") {
    if (this == null) {
        return
    }

    AutoTraceHelper.bindData(this, moduleType, data ?: "")
}

fun View?.bindDataCallback(dataProvider: AutoTraceHelper.IDataProvider) {
    if (this == null) {
        return
    }

    AutoTraceHelper.bindDataCallback(this, dataProvider)
}

fun View?.isRealVisible(): Boolean {
    if (this == null) {
        return false
    }
    return this.visibility == View.VISIBLE && this.getGlobalVisibleRect(Rect())
}

inline fun View?.onSingleClick(onClick: (View) -> Unit) {
    if (this == null || !OneClickHelper.getInstance().onClick(this)) {
        return
    }

    onClick(this)
}

fun View.fadeOut(duration: Long = 500, endStatus: Int = View.GONE) {
    visibility = endStatus
    startAnimation(AlphaAnimation(1f, 0f).apply {
        setDuration(duration)
    })
}

fun View.fadeIn(duration: Long = 500) {
    visibility = View.VISIBLE
    startAnimation(AlphaAnimation(0f, 1f).apply {
        setDuration(duration)
    })
}

fun View?.removeSelf() {
    if (this != null) {
        (this.parent as? ViewGroup)?.removeView(this)
    }
}