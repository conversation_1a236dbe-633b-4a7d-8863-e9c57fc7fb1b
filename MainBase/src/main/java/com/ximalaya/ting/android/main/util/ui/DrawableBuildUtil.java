package com.ximalaya.ting.android.main.util.ui;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.StateListDrawable;
import android.view.View;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;

/**
 * Created by qianmenchao on 2019/4/29.
 *
 * <AUTHOR>
 */
public class DrawableBuildUtil {

    public static final int COLOR_3FF6Fe = Color.parseColor("#3ff6fe");

    public static final int COLOR_00EDE3 = Color.parseColor("#00ede3");

    public static void setRoundBackground(View view,int color){
        newGradientDrawableBuilder().cornerRadius(BaseUtil.dp2px(view.getContext(),300)).color(color).setViewBackground(view);
    }

    public static GradientDrawableBuilder newGradientDrawableBuilder(){
        return new GradientDrawableBuilder();
    }

    public static StateListDrawableBuilder newStateListDrawableBuilder(){
        return new StateListDrawableBuilder();
    }

    public static class GradientDrawableBuilder {
        float cornerRadius;
        int[] colors;
        int color = Color.TRANSPARENT;
        float[] cornerRadII;
        int stroke;
        int strokeColor;
        GradientDrawable.Orientation mOrientation = GradientDrawable.Orientation.LEFT_RIGHT;

        public GradientDrawableBuilder cornerRadius(float cornerRadius) {
            this.cornerRadius = cornerRadius;
            return this;
        }

        /**
         * Specifies radii for each of the 4 corners. For each corner, the array
         * contains 2 values, <code>[X_radius, Y_radius]</code>. The corners are
         * ordered top-left, top-right, bottom-right, bottom-left. This property
         */
        public GradientDrawableBuilder cornerRadius(float leftTopRadius, float leftBottomRadius, float rightTopRadius, float rightBottomRadius) {
            if (cornerRadII == null) {
                cornerRadII = new float[8];
            }
            this.cornerRadII[0] = leftTopRadius;
            this.cornerRadII[1] = leftTopRadius;
            this.cornerRadII[2] = rightTopRadius;
            this.cornerRadII[3] = rightTopRadius;
            this.cornerRadII[4] = rightBottomRadius;
            this.cornerRadII[5] = rightBottomRadius;
            this.cornerRadII[6] = leftBottomRadius;
            this.cornerRadII[7] = leftBottomRadius;
            return this;
        }

        public GradientDrawableBuilder stroke(int width, int color) {
            this.stroke = width;
            this.strokeColor = color;
            return this;
        }

        public GradientDrawableBuilder strokeWidth(int width) {
            this.stroke = width;
            return this;
        }

        public GradientDrawableBuilder strokeColor(int color) {
            this.strokeColor = color;
            return this;
        }

        public GradientDrawableBuilder color(int color) {
            this.color = color;
            return this;
        }

        public GradientDrawableBuilder color(int[] color) {
            this.colors = color;
            return this;
        }

        public GradientDrawableBuilder startColor(int color) {
            if(colors == null){
                colors = new int[2];
            }
            this.colors[0] = color;
            return this;
        }

        public GradientDrawableBuilder endColor(int color) {
            if(colors == null){
                colors = new int[2];
            }
            this.colors[1] = color;
            return this;
        }



        public GradientDrawableBuilder orientation(GradientDrawable.Orientation orientation) {
            this.mOrientation = orientation;
            return this;
        }

        public GradientDrawable build() {
            GradientDrawable drawable = new GradientDrawable();
            drawable.setCornerRadius(cornerRadius);
            if (cornerRadII != null) {
                drawable.setCornerRadii(cornerRadII);
            } else {
                drawable.setCornerRadius(cornerRadius);
            }
            if (colors != null) {
                drawable.setColors(colors);
            }
            if (color != Color.TRANSPARENT) {
                drawable.setColor(color);
            }
            if (stroke > 0) {
                drawable.setStroke(stroke, strokeColor);
            }
            drawable.setOrientation(mOrientation);
            return drawable;
        }

        public GradientDrawable setViewBackground(View view){
            GradientDrawable drawable = build();
            if(view == null){
                return drawable;
            }
            view.setBackground(drawable);
            return drawable;
        }
    }

    public static class StateListDrawableBuilder {
        float cornerRadius;
        int[] colors;
        int color = Color.TRANSPARENT;
        int pressColor;
        float[] cornerRadII;
        int stroke;
        int strokeColor;

        int[] pressColors;
        int[] normalColors;

        GradientDrawable.Orientation orientation = GradientDrawable.Orientation.LEFT_RIGHT;

        public StateListDrawableBuilder cornerRadius(float cornerRadius) {
            this.cornerRadius = cornerRadius;
            return this;
        }

        /**
         * Specifies radii for each of the 4 corners. For each corner, the array
         * contains 2 values, <code>[X_radius, Y_radius]</code>. The corners are
         * ordered top-left, top-right, bottom-right, bottom-left. This property
         */
        public StateListDrawableBuilder cornerRadius(float leftTopRadius, float leftBottomRadius, float rightTopRadius, float rightBottomRadius) {
            if (cornerRadII == null) {
                cornerRadII = new float[8];
            }
            this.cornerRadII[0] = leftTopRadius;
            this.cornerRadII[1] = leftTopRadius;
            this.cornerRadII[2] = rightTopRadius;
            this.cornerRadII[3] = rightTopRadius;
            this.cornerRadII[4] = rightBottomRadius;
            this.cornerRadII[5] = rightBottomRadius;
            this.cornerRadII[6] = leftBottomRadius;
            this.cornerRadII[7] = leftBottomRadius;
            return this;
        }

        public StateListDrawableBuilder stroke(int width, int color) {
            this.stroke = width;
            this.strokeColor = color;
            return this;
        }

        public StateListDrawableBuilder normalColor(int color) {
            this.color = color;
            return this;
        }

        public StateListDrawableBuilder pressColor(int color) {
            this.pressColor = color;
            return this;
        }

        public StateListDrawableBuilder color(int[] color) {
            this.colors = color;
            return this;
        }
        public StateListDrawableBuilder pressColors(int[] colors) {
            this.pressColors = colors;
            return this;
        }

        public StateListDrawableBuilder normalColors(int[] colors) {
            this.normalColors = colors;
            return this;
        }

        public StateListDrawableBuilder oritention(GradientDrawable.Orientation orientation) {
            this.orientation = orientation;
            return this;
        }

        public StateListDrawable build(){
            return buildStateDrawable();
        }

        @Deprecated
        public StateListDrawable buildStateDrawable() {
            if (cornerRadII == null) {
                cornerRadII = new float[8];
                this.cornerRadII[0] = cornerRadius;
                this.cornerRadII[1] = cornerRadius;
                this.cornerRadII[2] = cornerRadius;
                this.cornerRadII[3] = cornerRadius;
                this.cornerRadII[4] = cornerRadius;
                this.cornerRadII[5] = cornerRadius;
                this.cornerRadII[6] = cornerRadius;
                this.cornerRadII[7] = cornerRadius;
            }
            GradientDrawable pressDrawable = new GradientDrawable();
            GradientDrawable normalDrawable = new GradientDrawable();
            normalDrawable.setCornerRadii(cornerRadII);
            if(normalColors!=null){
                normalDrawable.setColors(normalColors);
            }else if(color!=0){
                normalDrawable.setColor(color);
            }
            if (stroke > 0) {
                normalDrawable.setStroke(stroke, strokeColor);
            }

            normalDrawable.setOrientation(orientation);
            pressDrawable.setCornerRadii(cornerRadII);
            if(pressColors!=null){
                pressDrawable.setColors(pressColors);
            }else if(pressColor!=0){
                pressDrawable.setColor(pressColor);
            }
            if (stroke > 0) {
                pressDrawable.setStroke(stroke, strokeColor);
            }
            pressDrawable.setOrientation(orientation);

            StateListDrawable states = new StateListDrawable();
            states.addState(new int[]{android.R.attr.state_pressed}, pressDrawable);
            states.addState(new int[]{}, normalDrawable);
            return states;
        }
    }

    public static void setFoldEarCommonOKButtonEnableBackground(View view){
        Drawable drawable = DrawableBuildUtil
                .newGradientDrawableBuilder()
                .startColor(Color.parseColor("#FF728C"))
                .endColor(Color.parseColor("#FF237B"))
                .cornerRadius(BaseUtil.dp2px(BaseApplication.getMyApplicationContext(),100))
                .build();
        view.setBackground(drawable);
    }

    public static Drawable getFoldEarCommonOKButtonEnableBackground(){
        return DrawableBuildUtil
                .newGradientDrawableBuilder()
                .startColor(Color.parseColor("#FF728C"))
                .endColor(Color.parseColor("#FF237B"))
                .cornerRadius(BaseUtil.dp2px(BaseApplication.getMyApplicationContext(),100))
                .build();
    }



    public static void setFoldEarCommonOKButtonDisableBackground(View view){
        Drawable drawable = DrawableBuildUtil
                .newGradientDrawableBuilder()
                .startColor(Color.parseColor("#30FF728C"))
                .endColor(Color.parseColor("#30FF237B"))
                .cornerRadius(BaseUtil.dp2px(BaseApplication.getMyApplicationContext(),100))
                .build();
        view.setBackground(drawable);
    }

    public static Drawable getFoldEarCommonOKButtonDisableBackground(){
        return DrawableBuildUtil
                .newGradientDrawableBuilder()
                .startColor(Color.parseColor("#30FF728C"))
                .endColor(Color.parseColor("#30FF237B"))
                .cornerRadius(BaseUtil.dp2px(BaseApplication.getMyApplicationContext(),100))
                .build();
    }
}
