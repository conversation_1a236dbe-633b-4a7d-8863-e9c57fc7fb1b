package com.ximalaya.ting.android.main.util.ui;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.StateListDrawable;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.IntRange;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.drawable.RoundDrawable;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.find.IFeedFunctionAction;
import com.ximalaya.ting.android.host.socialModule.util.StatusUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.base.R;
import com.ximalaya.ting.android.main.model.friendGroup.PicInfosBean;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

/**
 * Created by qianmenchao on 2018/6/1.
 *
 * <AUTHOR>
 */

public class ViewStatusUtil {
    public static void setVisible(@IntRange(from = View.VISIBLE, to = View.GONE) int visibility, View... views) {
        if (views != null) {
            for (View view : views) {
                if (view != null && view.getVisibility() != visibility) {
                    view.setVisibility(visibility);
                }
            }
        }
    }

    public static void setContentDescription(String contentDescription, View... views) {
        if (null != contentDescription) {
            for (View view : views) {
                if (view != null) {
                    view.setContentDescription(contentDescription);
                }
            }
        }
    }

    public static void showOrHideView(boolean show, View... views) {
        if (views != null) {
            for (View view : views) {
                if (view != null) {
                    view.setVisibility(show ? View.VISIBLE : View.GONE);
                }
            }
        }
    }

    public static void setText(TextView textView, CharSequence charSequence) {
        if (textView != null && !TextUtils.isEmpty(charSequence)) {
            textView.setText(charSequence);
        }
    }

    public static void setTextVisibleAuto(TextView textView, String charSequence) {
        if (textView != null) {
            if (!TextUtils.isEmpty(charSequence)) {
                textView.setText(charSequence.trim());
                textView.setVisibility(View.VISIBLE);
            } else {
                textView.setVisibility(View.GONE);
            }
        }
    }

    public static void setTag(View view, Object tag) {
        if (view != null && tag != null) {
            view.setTag(tag);
        }
    }

    public static void setTag(View view, int id, Object tag) {
        if (view != null && tag != null) {
            view.setTag(id, tag);
        }
    }

    public static void setVisibleOnCondition(boolean condition, @IntRange(from = View.VISIBLE, to = View.GONE) int visibility, View... views) {
        if (views != null) {
            for (View view : views) {
                if (condition && view != null && view.getVisibility() != visibility) {
                    view.setVisibility(visibility);
                }
            }
        }
    }

    public static GradientDrawable newGradientDrawable(int color, int cr) {
        return new GradientDrawableBuilder()
                .color(color)
                .cornerRadius(cr).build();
    }

    public static GradientDrawable newGradientDrawable(int color, int cr, int stroke, int strokeColor) {
        return new GradientDrawableBuilder()
                .color(color)
                .stroke(stroke, strokeColor)
                .cornerRadius(cr).build();
    }

    public static GradientDrawable newGradientDrawable(int[] color, int cr) {
        return new GradientDrawableBuilder()
                .color(color)
                .cornerRadius(cr).build();
    }

    public static void setPadding(View view, int position, int size) {
        if (view != null) {
            int[] sizes = new int[4];
            sizes[position] = size;
            view.setPadding(sizes[0], sizes[1], sizes[2], sizes[3]);
        }
    }

    public static void setPadding(View view, Integer... sizes) {
        if (view != null && sizes != null) {

            // array  只写不用，没看懂

//            int[] array = new int[4];
//            for (int index = 0; index < array.length; index++) {
//                array[index] = sizes[index];
//            }
            view.setPadding(sizes[0], sizes[1], sizes[2], sizes[3]);
        }
    }

    public static void setTextColor(TextView textView, int color) {
        if (textView != null) {
            textView.setTextColor(color);
        }
    }

    public static void setTextColorRes(TextView textView, int res) {
        if (textView != null && res != -1) {
            textView.setTextColor(textView.getResources().getColor(res));
        }
    }

    public static void removeViewParent(View view) {
        if (view != null && view.getParent() != null && view.getParent() instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view.getParent();
            viewGroup.removeView(view);
        }
    }

    public static void returnTop(ListView listView) {
        if (listView != null) {
            listView.setSelection(0);
        }
    }

    public static void returnTopAuto(ListView listView) {
        if (listView != null) {
            int firstVisiblePosition = listView.getFirstVisiblePosition();
            if (firstVisiblePosition > 15) {
                returnTop(listView);
            } else {
                returnTopSmooth(listView);
            }
        }
    }

    public static void returnTopSmooth(ListView listView) {
        if (listView != null) {
            listView.smoothScrollToPosition(0);
        }
    }

    public static void returnTop(RefreshLoadMoreListView refreshLoadMoreListView) {
        if (refreshLoadMoreListView != null) {
            returnTop(refreshLoadMoreListView.getRefreshableView());
        }
    }

    public static RecyclerView.ItemDecoration createItemDecortion(final Rect rect) {
        return new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect = rect;
            }
        };
    }

    public static RecyclerView.ItemDecoration createItemDecortion(final int leftInit, final int left, final int right, final int top, final int bottom) {
        return new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                if (parent == null) return;
                Context context = parent.getContext();
                int initLeft = BaseUtil.dp2px(context, leftInit);
                int l = BaseUtil.dp2px(context, left);
                int r = BaseUtil.dp2px(context, right);
                int t = BaseUtil.dp2px(context, top);
                int b = BaseUtil.dp2px(context, bottom);
                int position = parent.getChildAdapterPosition(view);
                outRect.left = position == 0 ? initLeft : l;
                outRect.right = r;
                outRect.top = t;
                outRect.bottom = b;
            }
        };
    }

    public static RecyclerView.ItemDecoration createItemDecoration4TopInit(final int topInit, final int left,
        final int right, final int top, final int bottom, final int bottomLast, final int size) {
        return new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                if (parent == null) {
                    return;
                }
                int initTop = BaseUtil.dp2px(ToolUtil.getCtx(), topInit);
                int l = BaseUtil.dp2px(ToolUtil.getCtx(), left);
                int r = BaseUtil.dp2px(ToolUtil.getCtx(), right);
                int t = BaseUtil.dp2px(ToolUtil.getCtx(), top);
                int b = BaseUtil.dp2px(ToolUtil.getCtx(), bottom);
                int bl = BaseUtil.dp2px(ToolUtil.getCtx(), bottomLast);
                int position = parent.getChildAdapterPosition(view);
                outRect.left = l;
                outRect.right = r;
                outRect.top = position == 0 ? initTop : t;
                outRect.bottom = position == size - 1 ? bl : b;
            }
        };
    }

    public static void setMargin(View view, int left, int top, int right, int bottom) {
        if (view == null || !(view.getLayoutParams() != null
            && view.getLayoutParams() instanceof ViewGroup.MarginLayoutParams)) {
            return;
        }
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        if (marginLayoutParams == null) {
            marginLayoutParams = new ViewGroup.MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        marginLayoutParams.leftMargin = left;
        marginLayoutParams.topMargin = top;
        marginLayoutParams.rightMargin = right;
        marginLayoutParams.bottomMargin = bottom;
        view.setLayoutParams(marginLayoutParams);
    }

    public static void setMargin(View view, int size, @IntRange(from = 0, to = 3) int position) {
        if (view == null || !(view.getLayoutParams() != null && view.getLayoutParams() instanceof ViewGroup.MarginLayoutParams))
            return;
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        if (marginLayoutParams == null) {
            marginLayoutParams = new ViewGroup.MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        int s = BaseUtil.dp2px(view.getContext(), size);
        if (position == 0) {
            marginLayoutParams.leftMargin = s;
        } else if (position == 1) {
            marginLayoutParams.topMargin = s;
        } else if (position == 2) {
            marginLayoutParams.rightMargin = s;
        } else if (position == 3) {
            marginLayoutParams.bottomMargin = s;
        }
        view.setLayoutParams(marginLayoutParams);
    }

    public static void setMarginTop(View view, int size) {
        setMargin(view, size, 1);
    }

    public static void setBgColor(View view, int colorRes) {
        if (view == null || view.getContext() == null) return;
        view.setBackgroundColor(view.getContext().getResources().getColor(colorRes));
    }

    public static void changeLoadStateViewLocation(ViewGroup parent, ViewGroup.LayoutParams lp, BaseFragment.LoadCompleteType type) {
        changeLoadStateViewLocation(parent, lp, type, 120, 100);
    }

    public static void setImageRes(ImageView imageView, int res) {
        if (imageView != null) {
            imageView.setImageResource(res);
        }
    }

    public static void setOnClickListener(View view, View.OnClickListener clickListener) {
        if (view != null) {
            view.setOnClickListener(clickListener);
        }
    }

    public static void setOnClickListener(int id, Object tag, View.OnClickListener clickListener, View... views) {
        if (views != null) {
            for (View view : views) {
                if (view == null) continue;
                if (id > 0) {
                    view.setTag(id, tag);
                } else {
                    view.setTag(tag);
                }
                view.setOnClickListener(clickListener);
            }
        }
    }

    public static void setOnClickAndTraceBindData(Object data, View.OnClickListener clickListener, View... views) {
        if (views != null) {
            for (View view : views) {
                if (view == null) continue;
                view.setOnClickListener(clickListener);
                AutoTraceHelper.bindData(view, data);
            }
        }
    }



    public static <T> T getTag(View view, int res, Class<? extends T> cls) {
        if (view != null && cls != null) {
            Object tag = view.getTag(res);
            if (tag != null && cls.isInstance(tag)) {
                return (T) tag;
            }
        }
        return null;
    }

    public static <T> T getDataFromTraceBindTag(View view, Class<? extends T> cls) {
        return getTag(view, R.id.trace_id_key_bind_trace_data, cls);
    }


    public static void setImageViewResourceWithVisibleState(ImageView imageView, @DrawableRes int resId) {
        if (imageView != null) {
            imageView.setImageResource(resId);
            setVisible(View.VISIBLE, imageView);
        }
    }

    public static <T> T cast(Object o, Class<T> tClass) {
        if (o != null && tClass.isInstance(o)) {
            return (T) o;
        }
        return null;
    }

    public static <T> T cast(Object o) {
        if (o != null) {
            try {
                return (T) o;
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        return null;
    }


    public static <TAG> TAG getTag(View view, int resId) {
        if (view != null) {
            Object tag = view.getTag(resId);
            if (tag != null) {
                try {
                    return (TAG) tag;
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    public  static <T>  T getSafe(List<T> list, int position) {
        return !ToolUtil.isEmptyCollects(list) && position >= 0 && position < list.size() ? list.get(position) : null;
    }

    public  static <T>  T removeSafe(List<T> list, int position) {
        return !ToolUtil.isEmptyCollects(list) && position >= 0 && position < list.size() ? list.remove(position) : null;
    }

    public  static <T>  boolean removeSafe(List<T> list, T t) {
        return (!ToolUtil.isEmptyCollects(list) && t != null) && list.remove(t);
    }

    public static int countSafe(List list) {
        return !ToolUtil.isEmptyCollects(list) ? list.size() : 0;
    }

    public static int getSizeSafe(List items) {
        return items != null ? items.size():0;
    }

    public static void setRoundDrawable(View view, int resId, int radius) {
        if (view != null) {
            Drawable drawable = RoundDrawable.fromDrawable(view.getResources().getDrawable(resId), BaseUtil.dp2px(view.getContext(), radius));
            view.setBackground(drawable);
        }
    }

    public static class GradientDrawableBuilder {
        float cornerRadius;
        int[] colors;
        int color = Color.TRANSPARENT;
        float[] cornerRadII;
        int stroke;
        int strokeColor;
        GradientDrawable.Orientation mOrientation = GradientDrawable.Orientation.LEFT_RIGHT;

        public GradientDrawableBuilder cornerRadius(float cornerRadius) {
            this.cornerRadius = cornerRadius;
            return this;
        }

        /**
         * Specifies radii for each of the 4 corners. For each corner, the array
         * contains 2 values, <code>[X_radius, Y_radius]</code>. The corners are
         * ordered top-left, top-right, bottom-right, bottom-left. This property
         */
        public GradientDrawableBuilder cornerRadius(float leftTopRadius, float leftBottomRadius, float rightTopRadius, float rightBottomRadius) {
            if (cornerRadII == null) {
                cornerRadII = new float[8];
            }
            this.cornerRadII[0] = leftTopRadius;
            this.cornerRadII[1] = leftTopRadius;
            this.cornerRadII[2] = rightTopRadius;
            this.cornerRadII[3] = rightTopRadius;
            this.cornerRadII[4] = rightBottomRadius;
            this.cornerRadII[5] = rightBottomRadius;
            this.cornerRadII[6] = leftBottomRadius;
            this.cornerRadII[7] = leftBottomRadius;
            return this;
        }

        public GradientDrawableBuilder stroke(int width, int color) {
            this.stroke = width;
            this.strokeColor = color;
            return this;
        }

        public GradientDrawableBuilder color(int color) {
            this.color = color;
            return this;
        }

        public GradientDrawableBuilder color(int[] color) {
            this.colors = color;
            return this;
        }

        public GradientDrawableBuilder orientation(GradientDrawable.Orientation orientation) {
            this.mOrientation = orientation;
            return this;
        }

        public GradientDrawable build() {
            GradientDrawable drawable = new GradientDrawable();
            drawable.setCornerRadius(cornerRadius);
            if (cornerRadII != null) {
                drawable.setCornerRadii(cornerRadII);
            } else {
                drawable.setCornerRadius(cornerRadius);
            }
            if (colors != null) {
                drawable.setColors(colors);
            }
            if (color != Color.TRANSPARENT) {
                drawable.setColor(color);
            }
            if (stroke > 0) {
                drawable.setStroke(stroke, strokeColor);
            }
            drawable.setOrientation(mOrientation);
            return drawable;
        }
    }

    public static class StateListDrawableBuilder {
        float cornerRadius;
        int[] colors;
        int color = Color.TRANSPARENT;
        int pressColor;
        float[] cornerRadII;
        int stroke;
        int strokeColor;

        public StateListDrawableBuilder cornerRadius(float cornerRadius) {
            this.cornerRadius = cornerRadius;
            return this;
        }

        /**
         * Specifies radii for each of the 4 corners. For each corner, the array
         * contains 2 values, <code>[X_radius, Y_radius]</code>. The corners are
         * ordered top-left, top-right, bottom-right, bottom-left. This property
         */
        public StateListDrawableBuilder cornerRadius(float leftTopRadius, float leftBottomRadius, float rightTopRadius, float rightBottomRadius) {
            if (cornerRadII == null) {
                cornerRadII = new float[8];
            }
            this.cornerRadII[0] = leftTopRadius;
            this.cornerRadII[1] = leftTopRadius;
            this.cornerRadII[2] = rightTopRadius;
            this.cornerRadII[3] = rightTopRadius;
            this.cornerRadII[4] = rightBottomRadius;
            this.cornerRadII[5] = rightBottomRadius;
            this.cornerRadII[6] = leftBottomRadius;
            this.cornerRadII[7] = leftBottomRadius;
            return this;
        }

        public StateListDrawableBuilder stroke(int width, int color) {
            this.stroke = width;
            this.strokeColor = color;
            return this;
        }

        public StateListDrawableBuilder normalColor(int color) {
            this.color = color;
            return this;
        }

        public StateListDrawableBuilder pressColor(int color) {
            this.pressColor = color;
            return this;
        }

        public StateListDrawableBuilder color(int[] color) {
            this.colors = color;
            return this;
        }

        public StateListDrawable buildStateDrawable() {
            if (cornerRadII == null) {
                cornerRadII = new float[8];
                this.cornerRadII[0] = cornerRadius;
                this.cornerRadII[1] = cornerRadius;
                this.cornerRadII[2] = cornerRadius;
                this.cornerRadII[3] = cornerRadius;
                this.cornerRadII[4] = cornerRadius;
                this.cornerRadII[5] = cornerRadius;
                this.cornerRadII[6] = cornerRadius;
                this.cornerRadII[7] = cornerRadius;
            }
            GradientDrawable pressDrawable = new GradientDrawable();
            GradientDrawable normalDrawable = new GradientDrawable();
            normalDrawable.setCornerRadii(cornerRadII);
            normalDrawable.setColor(color);
            if (stroke > 0) {
                normalDrawable.setStroke(stroke, strokeColor);
            }

            pressDrawable.setCornerRadii(cornerRadII);
            pressDrawable.setColor(pressColor);
            if (stroke > 0) {
                pressDrawable.setStroke(stroke, strokeColor);
            }

            StateListDrawable states = new StateListDrawable();
            states.addState(new int[]{android.R.attr.state_pressed}, pressDrawable);
            states.addState(new int[]{}, normalDrawable);
            return states;
        }
    }

    public static void setFollowBtnDrawable(TextView textView, boolean follow) {
        if (textView == null) {
            return;
        }
        Context context = textView.getContext();
        if (follow) {
            int normalColor = context.getResources().getColor(R.color.main_color_f3f4f5);
            int pressColor = (Color.parseColor("#dddddd"));
            int c = BaseUtil.dp2px(context, 100);
            Drawable drawable = new ViewStatusUtil.StateListDrawableBuilder().normalColor(normalColor).pressColor(pressColor).cornerRadius(c, c, c, c).buildStateDrawable();
            textView.setBackground(drawable);
            textView.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
            textView.setText("已关注");
            textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13);
            textView.setTextColor(context.getResources().getColor(R.color.main_color_cccccc));
        } else {
            int normalColor = context.getResources().getColor(R.color.main_color_ffece8);
            int pressColor = (Color.parseColor("#ffc7ba"));
            int c = BaseUtil.dp2px(context, 100);
            Drawable drawable = new ViewStatusUtil.StateListDrawableBuilder().normalColor(normalColor).pressColor(pressColor).cornerRadius(c, c, c, c).buildStateDrawable();
            textView.setBackground(drawable);
            textView.setCompoundDrawablesWithIntrinsicBounds(com.ximalaya.ting.android.host.R.drawable.host_rec_subscribe_plus, 0, 0, 0);
            textView.setText("关注");
            textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13);
            textView.setTextColor(context.getResources().getColor(R.color.main_color_f86442));
        }
    }


    public static IFeedFunctionAction.FollowStatus getFollowStatus(long uid) {
        return StatusUtil.getFollowStatus(uid);
    }

    /**
     * 修改Fragment 加载Loading图等位置，由默认的局中改成期望的位置，一般是外层包了StickLayout时，Fragment显示不全
     * 1。loading图较小，需要距顶100dp
     * 2。noContent,NetworkError图较大，居顶即可
     **/
    public static void changeLoadStateViewLocation(View parent, ViewGroup.LayoutParams lp, BaseFragment.LoadCompleteType type, int loadingMargin, int otherMargin) {
        if (lp != null) {
            Context context = parent.getContext();
            if (parent instanceof FrameLayout) {
                ((FrameLayout.LayoutParams) lp).gravity = Gravity.CENTER_HORIZONTAL;
                if (type == BaseFragment.LoadCompleteType.LOADING) {
                    ((FrameLayout.LayoutParams) lp).topMargin = BaseUtil.dp2px(context, loadingMargin);
                } else {
                    ((FrameLayout.LayoutParams) lp).topMargin = BaseUtil.dp2px(context, otherMargin);
                }
            } else if (parent instanceof RelativeLayout) {
                ((RelativeLayout.LayoutParams) lp).addRule(RelativeLayout.CENTER_HORIZONTAL);
                ((RelativeLayout.LayoutParams) lp).addRule(RelativeLayout.ALIGN_PARENT_TOP);
                if (type == BaseFragment.LoadCompleteType.LOADING) {
                    ((RelativeLayout.LayoutParams) lp).topMargin = BaseUtil.dp2px(context, loadingMargin);
                } else {
                    ((RelativeLayout.LayoutParams) lp).topMargin = BaseUtil.dp2px(context, otherMargin);
                }
            } else if (parent instanceof LinearLayout) {
                ((LinearLayout.LayoutParams) lp).gravity = Gravity.CENTER_HORIZONTAL;
                if (type == BaseFragment.LoadCompleteType.LOADING) {
                    ((LinearLayout.LayoutParams) lp).topMargin = BaseUtil.dp2px(context, loadingMargin);
                } else {
                    ((LinearLayout.LayoutParams) lp).topMargin = BaseUtil.dp2px(context, otherMargin);
                }
            }
        }
    }

    public static List<String> getBigPic(List<PicInfosBean> picInfos) {
        List<String> picList = new ArrayList<>();
        for (PicInfosBean picInfoBean : picInfos) {
            if (!TextUtils.isEmpty(picInfoBean.getRectangleUrl())) {
                picList.add(ToolUtil.addFilePrefix(picInfoBean.getRectangleUrl()));
            } else if (!TextUtils.isEmpty(picInfoBean.getOriginUrl())) {
                picList.add(ToolUtil.addFilePrefix(picInfoBean.getOriginUrl()));
            } else if (!TextUtils.isEmpty(picInfoBean.getSquareUrl())) {
                picList.add(ToolUtil.addFilePrefix(picInfoBean.getSquareUrl()));
            }
        }
        return picList;
    }

    public static boolean isVisible(View view) {
        return view != null && view.getVisibility() == View.VISIBLE;
    }

    public static boolean isSameViewState(View view, int state) {
        boolean same = false;
        if (view != null) {
            same = view.getVisibility() == state;
            if (view.getParent() != null) {
                same = same && ((ViewGroup) view.getParent()).getVisibility() == state;
            }
        }
        return same;
    }


    public static void startFragment(BaseFragment fragment) {
        if (fragment == null) return;
        Activity activity = MainApplication.getOptActivity();
        if (activity instanceof MainActivity) {
            MainActivity mainActivity = (MainActivity) activity;
            if (mainActivity.getManageFragment() != null) {
                mainActivity.getManageFragment().startFragment(fragment);
            }
        }
    }

    public static void setDrawable(TextView textView, @IntRange(from = 0, to = 3) int position, Drawable drawable) {
        if (textView != null) {
            Drawable[] drawables = textView.getCompoundDrawables();
            if (drawable != null) {
                drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
            }
            drawables[position] = drawable;
            textView.setCompoundDrawables(drawables[0], drawables[1], drawables[2], drawables[3]);
        }
    }

    public static Drawable filterDrawable(Drawable src, @ColorInt int color) {
        return filterDrawable(src, null, color);
    }

    public static Drawable filterDrawable(Context context, int drawableRes, @ColorInt int colorRes) {
        if (context == null) return null;
        return filterDrawable(context.getResources().getDrawable(drawableRes), PorterDuff.Mode.SRC_IN, colorRes);
    }


    public static void setImageDrawableWithFilter(ImageView imageView, int drawableRes, @ColorInt int colorRes) {
        if (imageView == null||imageView.getContext()==null) return ;
        Drawable drawable = filterDrawable(imageView.getResources().getDrawable(drawableRes), PorterDuff.Mode.SRC_IN, colorRes);
        imageView.setImageDrawable(drawable);
    }

    public static Drawable filterDrawable(Drawable src, PorterDuff.Mode mode, @ColorInt int tint) {
        if (src != null) {
            Drawable des = src.mutate();
            des = DrawableCompat.wrap(des);
            if (mode != null) {
                DrawableCompat.setTintMode(des, mode);
            }
            DrawableCompat.setTint(des, tint);
//            des.setColorFilter(new PorterDuffColorFilter(tint,mode));
            return des;
        }
        return null;
    }

    public static String parseTimeToString(long time) {
        if (time < 0) {
            time = 0;
        }
        return parseTimeToString(time, TimeUnit.MILLISECONDS);
    }

    public static String parseTimeToString(long time, TimeUnit timeUnit) {
        if (timeUnit != TimeUnit.SECONDS && timeUnit != TimeUnit.MILLISECONDS) {
            throw new RuntimeException("time unit must be second or millisecond");
        }
        long totalSeconds = time;
        if (timeUnit == TimeUnit.MILLISECONDS) {
            totalSeconds = time / 1000;
        }
        long seconds = totalSeconds % 60;
        long minutes = (totalSeconds / 60);

        return String.format(Locale.US, "%02d:%02d", minutes, seconds);
    }

}
