package com.ximalaya.ting.android.main.view;

import android.content.Context;
import android.os.CountDownTimer;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.main.base.R;


/**
 * 课程倒计时显示控件
 *
 * <AUTHOR>
 * @date 17/12/25
 */

public class CountDownTimerView extends LinearLayout {

    private Context mContext;
    private TextView mTvDay;
    private TextView mTvHour1;
    private TextView mTvHour2;
    private TextView mTvMin1;
    private TextView mTvMin2;
    private TextView mTvSecond1;
    private TextView mTvSecond2;
    private TextView mTvStart;
    private TextView mTvEnd;

    private CountDownTimer mTimer;

    public static final int TIME_ONE_SEC = 1000;
    public static final int TIME_ONE_MIN = TIME_ONE_SEC * 60;
    public static final int TIME_ONE_HR = TIME_ONE_MIN * 60;
    public static final int TIME_ONE_DAY = TIME_ONE_HR * 24;

    private int[] mTimeShowNums = {0, 0, 0, 0};
    private int[] mTemp = {0, 0, 0, 0};

    private IOnFinishTimerCallback mOnFinishCallback;

    public CountDownTimerView(Context context) {
        this(context, null);
    }

    public CountDownTimerView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CountDownTimerView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        initView(context);
    }

    private void initView(Context context) {
        mContext = context;
        View.inflate(context, R.layout.main_view_conutdown_timer, this);

        mTvDay = (TextView) findViewById(R.id.main_tv_day_num);
        mTvHour1 = (TextView) findViewById(R.id.main_tv_hour_num_1);
        mTvHour2 = (TextView) findViewById(R.id.main_tv_hour_num_2);
        mTvMin1 = (TextView) findViewById(R.id.main_tv_min_num_1);
        mTvMin2 = (TextView) findViewById(R.id.main_tv_min_num_2);
        mTvSecond1 = (TextView) findViewById(R.id.main_tv_sec_num_1);
        mTvSecond2 = (TextView) findViewById(R.id.main_tv_sec_num_2);
        mTvStart = (TextView) findViewById(R.id.main_tv_count_down_start);
        mTvEnd = (TextView) findViewById(R.id.main_tv_count_down_end);

        mTvDay.setText("0");
        mTvHour1.setText("0");
        mTvHour2.setText("0");
        mTvMin1.setText("0");
        mTvMin2.setText("0");
        mTvSecond1.setText("0");
        mTvSecond2.setText("0");
    }

    public void startCountDown(long targetTime, IOnFinishTimerCallback callback) {

        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
            mOnFinishCallback = null;
        }

        mOnFinishCallback = callback;

        long nowTime = System.currentTimeMillis();
        long time = targetTime - nowTime;


        mTimer = new CountDownTimer(time, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                showDiff(mTimeShowNums, parseTimeNum(millisUntilFinished, mTemp));
            }

            @Override
            public void onFinish() {
                if (mOnFinishCallback != null) {
                    mOnFinishCallback.onFinish();
                    mOnFinishCallback = null;
                }

                mTimer.cancel();
            }
        };

        mTimer.start();

    }


    public void startCountDownUseRemindTime(long time, IOnFinishTimerCallback callback) {

        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
            mOnFinishCallback = null;
        }

        mOnFinishCallback = callback;

        mTimer = new CountDownTimer(time, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                showDiff(mTimeShowNums, parseTimeNum(millisUntilFinished, mTemp));
            }

            @Override
            public void onFinish() {
                if (mOnFinishCallback != null) {
                    mOnFinishCallback.onFinish();
                    mOnFinishCallback = null;
                }

                mTimer.cancel();
            }
        };

        mTimer.start();

    }

    private int[] parseTimeNum(long time, int[] timeArry) {

        timeArry[0] = (int) (time / TIME_ONE_DAY);//day

        timeArry[1] = (int) ((time % TIME_ONE_DAY) / TIME_ONE_HR);//hour

        long leftTime = time - TIME_ONE_DAY * timeArry[0] - TIME_ONE_HR * timeArry[1];

        timeArry[2] = (int) (leftTime / TIME_ONE_MIN);
        timeArry[3] = (int) ((leftTime % TIME_ONE_MIN) / TIME_ONE_SEC);

        return timeArry;

    }

    private void showDiff(int[] before, int[] after) {

        if (before[0] != after[0]) {
            setTimeShow(mTvDay, after[0]);
            before[0] = after[0];
        }

        if (before[1] != after[1]) {
            setTimeShow(mTvHour1, mTvHour2, after[1]);
            before[1] = after[1];
        }

        if (before[2] != after[2]) {
            setTimeShow(mTvMin1, mTvMin2, after[2]);
            before[2] = after[2];
        }

        if (before[3] != after[3]) {
            setTimeShow(mTvSecond1, mTvSecond2, after[3]);
            before[3] = after[3];
        }

    }

    private void setTimeShow(TextView tv, int num) {
        tv.setText(Integer.toString(num));
    }

    private void setTimeShow(TextView tv1, TextView tv2, int num) {
        if (num >= 10) {
            int ge = num % 10;
            int shi = num / 10;
            tv1.setText(Integer.toString(shi));
            tv2.setText(Integer.toString(ge));
        } else {
            tv1.setText(Integer.toString(0));
            tv2.setText(Integer.toString(num));
        }
    }

    public void showCountDownDay(long time) {
        int[] dateArray = parseTimeNum(time, mTemp);
        if (dateArray[0] > 0) {
            for (int index = 0; index < getChildCount(); index++) {
                View view = getChildAt(index);
                if (view == null) continue;
                if (view == mTvDay) {
                    mTvDay.setText(Integer.toString(dateArray[0]));
                    view.setVisibility(View.VISIBLE);
                } else if (view == mTvStart || view == mTvEnd) {
                    view.setVisibility(View.VISIBLE);
                } else {
                    view.setVisibility(View.GONE);
                }
            }
        }
    }



    public static interface IOnFinishTimerCallback {
        void onFinish();
    }


    @Override
    protected void onDetachedFromWindow() {

        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
            mOnFinishCallback = null;
        }

        super.onDetachedFromWindow();
    }
}
