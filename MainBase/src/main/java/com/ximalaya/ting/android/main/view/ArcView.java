package com.ximalaya.ting.android.main.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;

import com.ximalaya.ting.android.main.base.R;


public class ArcView extends View {
    public static final int EDGE_TOP = 0;
    public static final int EDGE_BOTTOM = 1;
    public static final int DIR_IN = 0;
    public static final int DIR_OUT = 1;
    private int mDir;
    private int mColor = Color.BLACK;
    private int mEdge;
    private Paint mPaint;
    private int mArcHeight;
    private int mWidth;
    private int mHeight;
    private Rect mRect = new Rect();
    private Path mPath = new Path();

    public ArcView(Context context) {
        this(context, null);
    }

    public ArcView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ArcView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.ArcView);
        mArcHeight = typedArray.getDimensionPixelSize(R.styleable.ArcView_arc_height, 0);
        mEdge = typedArray.getInt(R.styleable.ArcView_arc_edge, EDGE_TOP);
        mColor = typedArray.getColor(R.styleable.ArcView_arc_color, Color.BLACK);
        mDir = typedArray.getColor(R.styleable.ArcView_arc_dir, DIR_IN);
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mPaint.setColor(mColor);
        typedArray.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mWidth = getMeasuredWidth();
        mHeight = getMeasuredHeight();
        mArcHeight = Math.min(mHeight, mArcHeight);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mEdge == EDGE_TOP) {
            if (mDir == DIR_IN) {
                mPath.moveTo(0, 0);
                mPath.quadTo(mWidth / 2, mArcHeight, mWidth, 0);
                mPath.lineTo(mWidth, mHeight);
                mPath.lineTo(0, mHeight);
                mPath.close();
            } else {
                mPath.moveTo(0, mArcHeight);
                mPath.quadTo(mWidth / 2, 0, mWidth, mArcHeight);
                mPath.lineTo(mWidth, mHeight);
                mPath.lineTo(0, mHeight);
                mPath.close();
            }
        } else if (mEdge == EDGE_BOTTOM) {
            // todo:
        }
        canvas.drawPath(mPath, mPaint);
    }

    public void setColor(int color) {
        mColor = color;
        mPaint.setColor(color);
        invalidate();
    }
}
