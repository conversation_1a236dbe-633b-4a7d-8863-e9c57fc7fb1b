package com.ximalaya.ting.android.main.view.image

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Path
import android.graphics.RectF
import android.util.AttributeSet
import android.widget.ImageView
import com.ximalaya.ting.android.framework.util.BaseUtil

/**
 * created by chendekun 2020/11/17
 * @email <EMAIL>
 * @phoneNumber 13032178206
 * des:左上角圆角的imageview
 */
@SuppressLint("AppCompatCustomView")
class TopOvallImageView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : ImageView(context, attrs, defStyleAttr) {
    val path = Path()
    val rectF = RectF()
    //圆角弧度依次为左上角xy 右上角 右下角 左下角
    val rids = floatArrayOf(BaseUtil.dp2px(context, 8f).toFloat(), BaseUtil.dp2px(context, 8f).toFloat(), 0f, 0f, 0.0f, 0.0f, 0.0f, 0.0f)

    override fun onDraw(canvas: Canvas?) {
        //绘制圆角imageview
        rectF.left = 0f
        rectF.bottom = height.toFloat()
        rectF.right = width.toFloat()
        rectF.top = 0f
        path.addRoundRect(rectF, rids, Path.Direction.CW)
        canvas!!.clipPath(path)
        super.onDraw(canvas)
    }
}