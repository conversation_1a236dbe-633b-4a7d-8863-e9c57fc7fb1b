package com.ximalaya.ting.android.main.view;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by WolfXu on 2019/2/15.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class GridItemDecoration extends RecyclerView.ItemDecoration {
    private int mSpacing;
    private int mSpanCount;
    private int mVerticalSpacing;
    private boolean mHasMargin = true;

    public GridItemDecoration(int spacing, int span_count) {
        mSpacing = spacing;
        mSpanCount = span_count;
    }

    public GridItemDecoration(int mSpacing, int mVerticalSpacing, int mSpanCount) {
        this.mSpacing = mSpacing;
        this.mSpanCount = mSpanCount;
        this.mVerticalSpacing = mVerticalSpacing;
    }

    public GridItemDecoration(int mSpacing, int mVerticalSpacing, int mSpanCount, boolean hasMargin) {
        this.mSpacing = mSpacing;
        this.mSpanCount = mSpanCount;
        this.mVerticalSpacing = mVerticalSpacing;
        this.mHasMargin = hasMargin;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        int position = parent.getChildAdapterPosition(view);
        outRect.left = (int) ((1 - (position % mSpanCount) * 1f / mSpanCount) * mSpacing);
        outRect.right = (int) ((position % mSpanCount + 1) * 1f / mSpanCount * mSpacing);
        if (mVerticalSpacing > 0){
            outRect.top = mVerticalSpacing / 2;
            outRect.bottom = mVerticalSpacing / 2;
            if (!mHasMargin) {
                int itemCount = 0;
                if (parent.getAdapter() != null) {
                    itemCount = parent.getAdapter().getItemCount();
                }
                if (position / mSpanCount == 0) {
                    outRect.top = 0;
                    outRect.bottom = mVerticalSpacing / 2;
                } else if (position / mSpanCount == (itemCount - 1) / mSpanCount) {
                    outRect.top = mVerticalSpacing / 2;
                    outRect.bottom = 0;
                }
            }
        }
    }
}
