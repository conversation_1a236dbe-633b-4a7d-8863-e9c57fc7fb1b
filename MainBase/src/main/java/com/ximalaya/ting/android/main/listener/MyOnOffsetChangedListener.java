package com.ximalaya.ting.android.main.listener;

import com.google.android.material.appbar.AppBarLayout;

public abstract class MyOnOffsetChangedListener implements AppBarLayout.OnOffsetChangedListener {
    public enum State {
        EXPANDED, COLLAPSED, IDLE
    }

    private State mCurrentState = State.IDLE;
    private boolean isMId = false;

    @Override
    public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
        if (verticalOffset == 0) {
            if (mCurrentState != State.EXPANDED) {
                onStateChanged(appBarLayout, State.EXPANDED, verticalOffset);
            }
            mCurrentState = State.EXPANDED;
            isMId = false;
        } else if (Math.abs(verticalOffset) >= appBarLayout.getTotalScrollRange()) {
            if (mCurrentState != State.COLLAPSED) {
                onStateChanged(appBarLayout, State.COLLAPSED, verticalOffset);
                mCurrentState = State.COLLAPSED;
            } else {
                if (mCurrentState != State.IDLE) {
                    onStateChanged(appBarLayout, State.IDLE, verticalOffset);
                    mCurrentState = State.IDLE;
                }
            }
            isMId = false;
        } else if (!isMId){
            onStateChanged(appBarLayout, State.IDLE, verticalOffset);
            isMId = true;
            mCurrentState = State.IDLE;
        }
    }

    public abstract void onStateChanged(AppBarLayout appBarLayout, State state, int verticalOffset);
}
