<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="0.5dp"
                android:left="0.5dp"
                android:right="0.5dp"
                android:top="0.5dp" />
            <solid android:color="#005A6277" />
            <corners android:radius="@dimen/host_common_margin_10" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="0.5dp"
                android:left="0.5dp"
                android:right="0.5dp"
                android:top="0.5dp" />
            <solid android:color="#10CCCCCC" />
            <corners android:radius="@dimen/host_common_margin_10" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="0.5dp"
                android:left="0.5dp"
                android:right="0.5dp"
                android:top="0.5dp" />
            <solid android:color="#20CCCCCC" />
            <corners android:radius="@dimen/host_common_margin_10" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="0.5dp"
                android:left="0.5dp"
                android:right="0.5dp"
                android:top="0.5dp" />
            <solid android:color="#30CCCCCC" />
            <corners android:radius="@dimen/host_common_margin_10" />
        </shape>
    </item>

    <item>
        <shape android:shape="rectangle">
            <size android:height="22dp"/>
            <solid android:color="@color/main_white" />
            <corners android:radius="@dimen/host_common_margin_10" />
        </shape>
    </item>

</layer-list>