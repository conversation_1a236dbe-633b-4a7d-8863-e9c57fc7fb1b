apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply from: project.rootDir.absolutePath + '/util.gradle'

dependencies {
    implementation project(':TingMainHost:TingMainApp')
    api project(':MainBundle:MainAnnotation')
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion Integer.parseInt(rootProject.ext.minSdkVersion)
        targetSdkVersion Integer.parseInt(rootProject.ext.targetSdkVersion)
        versionCode 1
        versionName "1.0"
    }

    lintOptions {
        quiet true
        abortOnError false
        checkReleaseBuilds false
    }

    defaultConfig {
        consumerProguardFiles 'proguard-project.txt'
    }
}
