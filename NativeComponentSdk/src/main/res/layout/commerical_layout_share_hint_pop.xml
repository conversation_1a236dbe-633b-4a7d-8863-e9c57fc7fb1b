<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/commerical_down_triangle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="28dp"
        android:layout_gravity="end"
        android:contentDescription=" "
        android:src="@drawable/commerical_album_share_tips_triangle"
        android:tint="@color/host_color_black08"
        android:visibility="visible" />

    <TextView
        android:id="@+id/commerical_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/commerical_album_share_tips_bg"
        android:backgroundTint="@color/host_color_black08"
        android:ellipsize="none"
        android:gravity="center"
        android:text=""
        tools:text="分享给好友免费听"
        android:singleLine="true"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:textColor="@color/host_white"
        android:textSize="10dp"
        android:visibility="visible" />


</LinearLayout>
