<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="70dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <View
        android:id="@+id/universal_id_background"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:background="@drawable/universal_bg_album3_intro_middle_bar_common"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="44dp"
        app:layout_constraintTop_toTopOf="parent"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@color/universal_color_transparent">

        <View
            android:id="@id/universal_id_purchase_btn_1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>


        <View
            android:id="@+id/universal_id_background_color"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.cardview.widget.CardView>


    <TextView
        android:id="@+id/universal_id_function_btn"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:textSize="13sp"
        android:textStyle="bold"
        android:textColor="#ff8989"
        android:background="@drawable/universal_bg_rect_1fff8989_radius_16"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:gravity="center"
        android:layout_marginRight="14dp"
        android:layout_marginBottom="26dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="立即购买" />

    <TextView
        android:id="@+id/universal_id_now_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="22sp"
        android:textStyle="bold"
        android:textColor="#ff8989"
        android:includeFontPadding="false"
        android:layout_marginLeft="14dp"
        app:layout_constraintTop_toTopOf="@id/universal_id_function_btn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/universal_id_function_btn"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="89.00" />

    <TextView
        android:id="@+id/universal_id_price_unit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="11sp"
        android:textColor="#ffffff"
        android:layout_marginLeft="3dp"
        android:layout_marginBottom="3dp"
        app:layout_constraintBottom_toBottomOf="@id/universal_id_now_price"
        app:layout_constraintLeft_toRightOf="@id/universal_id_now_price"
        tools:text="喜点" />

    <TextView
        android:id="@+id/universal_id_original_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="11sp"
        android:textColor="#66ffffff"
        android:layout_marginLeft="8dp"
        android:layout_marginBottom="3dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/universal_id_now_price"
        app:layout_constraintLeft_toRightOf="@id/universal_id_price_unit"
        tools:text="188喜点"
        tools:visibility="visible" />

    <View
        android:id="@+id/universal_id_purchase_btn_2"
        android:layout_width="match_parent"
        android:layout_height="26dp"
        android:layout_marginStart="0.5dp"
        android:layout_marginEnd="0.5dp"
        android:layout_marginBottom="0.5dp"
        android:background="@drawable/universal_bg_album3_intro_middle_bar_common_bottom"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/universal_id_title"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:textSize="11sp"
        android:textColor="#ffffff"
        android:maxLines="1"
        android:gravity="center_vertical"
        android:ellipsize="end"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="44dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:text="开通会员 享8折优惠"/>

    <ImageView
        android:id="@+id/universal_id_icon"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_marginLeft="2dp"
        android:layout_marginTop="1.2dp"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="@id/universal_id_title"
        app:layout_constraintBottom_toBottomOf="@id/universal_id_title"
        app:layout_constraintLeft_toRightOf="@id/universal_id_title"
        android:tint="#ffffff"
        android:src="@drawable/universal_svg_arrow_right1"/>

</androidx.constraintlayout.widget.ConstraintLayout>