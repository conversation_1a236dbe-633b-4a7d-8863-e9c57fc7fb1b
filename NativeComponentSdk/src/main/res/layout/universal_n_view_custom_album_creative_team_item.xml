<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="133dp"
    android:layout_height="121dp"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="@color/universal_color_99000000">

    <androidx.cardview.widget.CardView
        android:layout_width="133dp"
        android:layout_height="121dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@color/universal_color_transparent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/universal_color_ffffff_10"/>

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:layout_width="43dp"
        android:layout_height="43dp"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="21.5dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@color/universal_color_transparent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/universal_color_ffffffff"/>

        <ImageView
            android:id="@+id/universal_id_anchor_portrait"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/universal_ic_default_portrait"/>

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/universal_id_title"
        android:layout_width="0dp"
        android:layout_height="20dp"
        android:textSize="14sp"
        android:textColor="@color/universal_color_ffffffff"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginTop="67dp"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="5dp"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="九王爷-炎漠"/>

    <TextView
        android:id="@+id/universal_id_sub_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="11sp"
        android:textColor="@color/universal_color_ffffff_50"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginTop="90dp"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="5dp"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="声音旁白声音旁白声音旁白声音旁白声音旁白"/>

</androidx.constraintlayout.widget.ConstraintLayout>