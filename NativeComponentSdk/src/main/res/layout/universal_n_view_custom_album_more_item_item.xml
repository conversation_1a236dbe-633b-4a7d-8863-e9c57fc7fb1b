<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    app:cardElevation="0dp"
    app:cardCornerRadius="8dp"
    app:cardBackgroundColor="@color/universal_color_transparent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/universal_color_99000000">

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#0CFFFFFF"/>

        <ImageView
            android:layout_width="37dp"
            android:layout_height="37dp"
            android:layout_centerInParent="true"
            android:scaleType="fitXY"
            android:src="@drawable/universal_ic_default_drawable_1"/>

        <ImageView
            android:id="@+id/universal_id_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"/>

        <TextView
            android:id="@+id/universal_id_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="11sp"
            android:textColor="@color/universal_color_ffffffff"
            android:padding="4dp"
            android:background="@drawable/universal_bg_rect_33000000_radius_4"
            android:layout_marginTop="8dp"
            android:layout_marginLeft="8dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentLeft="true"
            android:visibility="gone"
            tools:visibility="visible"
            tools:text="互动游戏"/>

    </RelativeLayout>

</androidx.cardview.widget.CardView>