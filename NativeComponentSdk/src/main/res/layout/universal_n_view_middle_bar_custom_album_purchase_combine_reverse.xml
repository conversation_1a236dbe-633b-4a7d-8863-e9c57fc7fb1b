<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="70dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <View
        android:id="@+id/universal_id_background"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:background="@color/universal_color_fff1f1f7_ff212121"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="44dp"
        app:layout_constraintTop_toTopOf="parent"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@color/universal_color_transparent"
        app:cardCornerRadius="8dp">

        <View
            android:id="@id/universal_id_purchase_btn_2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>


        <View
            android:id="@+id/universal_id_background_color"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/universal_color_fff8f8fa_ff1b1b1b"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.cardview.widget.CardView>


    <TextView
        android:id="@+id/universal_id_function_btn"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:minWidth="85dp"
        android:textSize="13sp"
        android:textStyle="bold"
        android:textColor="@color/universal_color_ffffffff"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:gravity="center"
        android:background="@drawable/universal_bg_rect_ff4444_radius_16"
        android:layout_marginRight="14dp"
        android:layout_marginBottom="26dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="立即购买" />

    <TextView
        android:id="@+id/universal_id_now_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="22sp"
        android:textStyle="bold"
        android:textColor="@color/universal_color_ffff4444"
        android:includeFontPadding="false"
        android:layout_marginLeft="14dp"
        app:layout_constraintTop_toTopOf="@id/universal_id_function_btn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/universal_id_function_btn"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="89.00" />

    <TextView
        android:id="@+id/universal_id_price_unit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="11sp"
        android:textColor="@color/universal_color_ff333333_ffdcdcdc"
        android:layout_marginLeft="3dp"
        android:layout_marginBottom="3dp"
        app:layout_constraintBottom_toBottomOf="@id/universal_id_now_price"
        app:layout_constraintLeft_toRightOf="@id/universal_id_now_price"
        tools:text="喜点" />

    <TextView
        android:id="@+id/universal_id_original_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="11sp"
        android:textColor="@color/universal_color_661a1a1a_ff666666"
        android:layout_marginLeft="8dp"
        android:layout_marginBottom="3dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/universal_id_now_price"
        app:layout_constraintLeft_toRightOf="@id/universal_id_price_unit"
        tools:text="188喜点"
        tools:visibility="visible" />

    <View
        android:id="@+id/universal_id_purchase_btn_1"
        android:layout_width="match_parent"
        android:layout_height="26dp"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/universal_id_title"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:textSize="11sp"
        android:textColor="@color/universal_color_ff333333_ffdcdcdc"
        android:maxLines="1"
        android:gravity="center_vertical"
        android:ellipsize="end"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="44dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:text="开通会员 享8折优惠"/>

    <ImageView
        android:id="@+id/universal_id_icon"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_marginLeft="2dp"
        android:layout_marginTop="1.2dp"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="@id/universal_id_title"
        app:layout_constraintBottom_toBottomOf="@id/universal_id_title"
        app:layout_constraintLeft_toRightOf="@id/universal_id_title"
        android:tint="@color/universal_color_ff333333_ffdcdcdc"
        android:src="@drawable/universal_svg_arrow_right1"/>

</androidx.constraintlayout.widget.ConstraintLayout>