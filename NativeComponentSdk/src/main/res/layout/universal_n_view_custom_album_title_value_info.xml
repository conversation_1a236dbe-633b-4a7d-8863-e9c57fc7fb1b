<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="wrap_content"
    android:layout_height="48dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="@color/universal_color_99000000">

    <View
        android:id="@+id/universal_id_background"
        android:layout_width="93dp"
        android:layout_height="48dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <View
        android:id="@+id/universal_id_divider"
        android:layout_width="0.5dp"
        android:layout_height="20dp"
        android:background="@color/universal_color_ffffff_20"
        app:layout_constraintLeft_toLeftOf="@id/universal_id_background"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/universal_id_title"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:textSize="12sp"
        android:textColor="@color/universal_color_ffffff_80"
        android:gravity="bottom"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="9999"/>

    <TextView
        android:id="@+id/universal_id_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:textColor="@color/universal_color_ffffff_50"
        android:drawableTint="@color/universal_color_ffffff_50"
        android:alpha="0.5"
        app:layout_constraintTop_toBottomOf="@id/universal_id_title"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:includeFontPadding="false"
        android:gravity="center_vertical"
        tools:text="9999条评论"
        tools:drawableRight="@drawable/universal_ic_arrow_white" />

</androidx.constraintlayout.widget.ConstraintLayout>