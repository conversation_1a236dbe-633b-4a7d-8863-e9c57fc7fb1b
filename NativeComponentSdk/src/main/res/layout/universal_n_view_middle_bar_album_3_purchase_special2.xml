<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="66dp"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <View
        android:id="@+id/universal_id_background"
        android:layout_width="match_parent"
        android:layout_height="66dp"
        android:background="@color/universal_color_fff8f8f8_14ffffff"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/universal_id_function_btn"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:text="立即购买"
        android:textSize="13sp"
        android:textStyle="bold"
        android:textColor="@color/universal_color_ffffff_ff8989"
        android:gravity="center"
        android:background="@drawable/universal_bg_rect_b3ff4444_1eff8989_radius_16"
        android:paddingHorizontal="@dimen/host_x10"
        android:layout_marginRight="14dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/universal_id_function_btn">

        <TextView
            android:id="@+id/universal_id_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/universal_color_444444_ffffff"
            android:textSize="14sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="专辑由于版权到期即将下架"/>

        <TextView
            android:id="@+id/universal_id_sub_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:alpha="0.7"
            android:textSize="11sp"
            android:textColor="@color/universal_color_99444444_99ffffff"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginTop="4dp"
            tools:text="购买后收听不限时"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>