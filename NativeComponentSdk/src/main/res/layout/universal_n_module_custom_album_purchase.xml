<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:paddingLeft="16dp"
    android:paddingRight="16dp"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="@color/universal_color_99000000">

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/universal_bg_custom_album_module"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <LinearLayout
        android:id="@+id/universal_id_purchase_disable_area"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@id/universal_id_logo"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:scaleType="fitXY"
            android:src="@drawable/universal_ic_exclaimation_mark"
            android:layout_marginRight="8dp" />

        <TextView
            android:id="@+id/universal_id_purchase_disable_description"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/universal_color_ffffffff"
            android:gravity="center_vertical"
            tools:text="所属地区暂时无版权，如有疑问请联系客服" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/universal_id_purchase_enable_area"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_vertical|right"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/universal_id_purchase_activity_area"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:background="@drawable/universal_bg_rect_0cffffff_radius_0_0_10_10"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/universal_id_logo"
                android:layout_width="36dp"
                android:layout_height="24dp"
                android:scaleType="fitXY"
                android:visibility="gone"
                android:layout_marginRight="4dp"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/universal_id_activity_description"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="11sp"
                android:textColor="#F4C8B0"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_marginRight="10dp"
                tools:text="距限时6折活动结束还有" />

            <com.ximalaya.android.nativecomponentsdk.creator.customAlbum.purchase.PurchaseActivityCountDown
                android:id="@id/universal_id_count_down"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:gravity="center_vertical|right" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/universal_id_price_area"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingLeft="16dp"
            android:paddingRight="16dp">

            <ImageView
                android:id="@+id/universal_id_logo"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:scaleType="fitXY"
                android:visibility="gone"
                android:layout_marginRight="7dp"
                tools:visibility="visible"
                tools:layout_width="20dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintBottom_toBottomOf="@id/universal_id_now_price"/>

            <TextView
                android:id="@+id/universal_id_now_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="@color/universal_color_ffffffff"
                android:includeFontPadding="false"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/universal_id_logo"
                tools:text="89.00" />

            <TextView
                android:id="@+id/universal_id_price_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="10sp"
                android:textColor="@color/universal_color_ffffff_80"
                android:layout_marginLeft="3dp"
                android:layout_marginBottom="2dp"
                app:layout_constraintBottom_toBottomOf="@id/universal_id_now_price"
                app:layout_constraintLeft_toRightOf="@id/universal_id_now_price"
                tools:text="喜点" />

            <TextView
                android:id="@+id/universal_id_original_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="10sp"
                android:textColor="@color/universal_color_ffffff_60"
                android:layout_marginLeft="8dp"
                android:layout_marginBottom="2dp"
                android:maxLines="1"
                android:ellipsize="end"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/universal_id_now_price"
                app:layout_constraintLeft_toRightOf="@id/universal_id_price_unit"
                tools:text="188喜点"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/universal_id_icon"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:scaleType="fitXY"
                android:src="@drawable/universal_n_ic_round_arrow_black_right"
                android:visibility="gone"
                android:layout_marginTop="1dp"
                android:layout_marginLeft="4dp"
                app:layout_constraintTop_toTopOf="@id/universal_id_price_unit"
                app:layout_constraintBottom_toBottomOf="@id/universal_id_price_unit"
                app:layout_constraintLeft_toRightOf="@id/universal_id_original_price"
                tools:visibility="visible"/>


            <TextView
                android:id="@+id/universal_id_purchase_btn_1"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:minWidth="80dp"
                android:textSize="12sp"
                android:textColor="@color/universal_color_ff333333"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:gravity="center"
                android:background="@drawable/universal_bg_rect_e6ffffff_radius_12"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:text="VIP尊享价购买" />

        </androidx.constraintlayout.widget.ConstraintLayout>
        
        <LinearLayout
            android:id="@+id/universal_id_privilege_area"
            android:layout_width="match_parent"
            android:layout_height="38dp"
            android:background="@drawable/commercial_n_ic_purchase_privilege"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:layout_marginBottom="16dp"
            android:layout_marginTop="-4dp"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:paddingTop="4dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="此专辑"
                android:textSize="13sp"
                android:textStyle="bold"
                android:textColor="@color/universal_color_ffffffff"/>

            <TextView
                android:id="@+id/universal_id_privilege_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="13sp"
                android:textStyle="bold"
                android:textColor="@color/universal_color_ffffffff"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="会员免费畅听"/>

            <TextView
                android:id="@+id/universal_id_privilege_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="13sp"
                android:textStyle="bold"
                android:textColor="@color/universal_color_ffffffff"
                android:text="立即开通"/>

            <ImageView
                android:id="@+id/universal_id_icon"
                android:layout_width="5dp"
                android:layout_height="8.5dp"
                android:scaleType="fitXY"
                android:layout_marginLeft="4dp"/>

        </LinearLayout>

        <com.ximalaya.android.componentelementarysdk.view.autoFit.AutoFitWidthHorizontalLinearLayout
            android:id="@+id/universal_id_label_area"
            android:layout_width="match_parent"
            android:layout_height="38dp"
            android:gravity="center_vertical"
            android:paddingLeft="16dp"
            android:paddingRight="50dp"
            android:paddingBottom="6dp"/>

        <LinearLayout
            android:id="@+id/universal_id_coupon_request"
            android:layout_width="wrap_content"
            android:layout_height="38dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="-38dp"
            android:layout_marginRight="16dp"
            android:paddingBottom="6dp">

            <TextView
                android:id="@+id/universal_id_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="10sp"
                android:textColor="@color/universal_color_ffffffff"
                android:drawableRight="@drawable/universal_ic_arrow_white"
                android:drawablePadding="2dp"
                tools:text="查看" />

        </LinearLayout>


    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>