<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="65dp"
    android:background="@color/universal_color_ffefe8_14f09068">


    <View
        android:id="@+id/universal_id_purchase_btn_1"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/universal_id_logo"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="1dp"
        android:layout_marginBottom="2dp"
        android:src="@drawable/universal_ic_vip_logo_3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="95:65"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/universal_id_function_btn"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:layout_marginRight="12dp"
        android:background="@drawable/universal_bg_rect_ffcbb1_1ff4b195_radius_16"
        android:gravity="center"
        android:paddingHorizontal="@dimen/host_x10"
        android:includeFontPadding="false"
        android:textColor="@color/universal_color_461717_f9d3c3"
        android:textSize="13sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="立即开通" />

    <TextView
        android:id="@+id/universal_id_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="12dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/universal_color_461717_f9d3c3"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/universal_id_sub_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/universal_id_function_btn"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="开通会员 享8折优惠" />

    <TextView
        android:id="@+id/universal_id_sub_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="4dp"
        android:layout_marginRight="12dp"
        android:alpha="0.7"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/universal_color_461717_f9d3c3"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/universal_id_function_btn"
        app:layout_constraintTop_toBottomOf="@+id/universal_id_title"
        tools:text="开通会员 享8折优惠" />

</androidx.constraintlayout.widget.ConstraintLayout>