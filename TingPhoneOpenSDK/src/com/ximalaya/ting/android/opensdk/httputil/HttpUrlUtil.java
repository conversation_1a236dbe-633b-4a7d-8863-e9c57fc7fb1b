/**
 * HttpUrlUtil.java
 * com.ximalaya.ting.android.opensdk.util
 *
 *
 *   ver     date      		author
 * ---------------------------------------
 *   		 2015-6-10 		chadwii
 *
 * Copyright (c) 2015, chadwii All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.httputil;

import androidx.annotation.NonNull;
import android.text.TextUtils;

import com.ximalaya.ting.android.opensdk.datatrasfer.CommonRequestForMain;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.player.cdn.CdnCollectDataForPlay;
import com.ximalaya.ting.android.routeservice.service.freeflow.IFreeFlowService;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * ClassName:HttpUrlUtil
 * 
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015-6-10 上午11:21:01
 * 
 * @see
 */
public class HttpUrlUtil {
	// 主进程和播放进程都设置了此值
	public static Config mConfig;

	public static HttpURLConnection getHttpURLConnection(String url, Config config , String method , IFreeFlowService.ISetHttpUrlConnectAttribute setHttpUrlConnectAttribute) throws IOException {
		HttpURLConnection httpURLConnection = FreeFlowServiceUtil.getHttpURLConnection(config, url ,method ,setHttpUrlConnectAttribute);
//		logToSD(conn);
		return httpURLConnection;
	}

	private static void logToSD(HttpURLConnection urlConnection) {
		Map<String, List<String>> map = urlConnection.getRequestProperties();
		String content = "OpenSDK.class header头 :";
		
		if(map.containsKey("Authorization")) {
			content += "Authorization:" +  map.get("Authorization") + "   ";
		}
		
		if(map.containsKey("spid")) {
			content += "spid:" +  map.get("spid") + "   ";
		}
		
		content += "是否使用了代理 " + urlConnection.usingProxy();
		
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
		String base = simpleDateFormat.format(new Date(System.currentTimeMillis())) + "     " + content;
		Logger.logToSd(base);
		Logger.log(base);
	}

	public static int downloadFile(String url, String savePath, String fileName) {
		InputStream is = null;
		FileOutputStream fos = null;
		try {
			HttpURLConnection conn = HttpUrlUtil.getHttpURLConnection(url,
					mConfig ,Config.METHOD_GET ,null);
			conn.connect();
			int resCode = conn.getResponseCode();
			String lenStr = conn.getHeaderField("Content-Length");
			if (resCode != 200 || TextUtils.isEmpty(lenStr)
					|| Long.parseLong(lenStr) <= 0) {
				return -1;
			}
			is = conn.getInputStream();
			byte[] buff = new byte[8192];
			int read = -1;
			File dir = new File(savePath);
			if (!dir.exists()) {
				dir.mkdirs();
			}
			File file = new File(dir, fileName);
			fos = new FileOutputStream(file, false);
			while ((read = is.read(buff)) > 0) {
				fos.write(buff, 0, read);
			}
			fos.flush();
			return 0;
		} catch (IOException e) {
			e.printStackTrace();
			return -1;
		} finally {
			if (fos != null) {
				try {
					fos.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	public static int upload(String url, String file) {
		try {
			final File f = new File(file);
			if (!f.exists() || f.isDirectory() || !f.canRead()) {
				return -2;
			}
			HttpURLConnection conn = getHttpURLConnection(url, mConfig, Config.METHOD_POST, new IFreeFlowService.ISetHttpUrlConnectAttribute() {
				@Override
				public void setHttpUrlConnectAttributes(@NonNull HttpURLConnection conn) {
					conn.setDoOutput(true);
					conn.setChunkedStreamingMode(1024 * 1024 * 128);
					conn.setRequestProperty("Charset", "UTF-8");
					conn.setRequestProperty("Content-Type", "multipart/form-data;file="
							+ f.getName());
					conn.setRequestProperty("filename", f.getName());
					conn.setRequestProperty("Content-Length", "" + f.length());
				}
			});

			try {
				OutputStream os = conn.getOutputStream();
				FileInputStream fis = new FileInputStream(f);
				byte[] buff = new byte[8192];
				int read = 0;
				while ((read = fis.read(buff)) > 0) {
					os.write(buff, 0, read);
				}
				os.flush();
				fis.close();
				os.close();
			} catch (IOException e) {
				e.printStackTrace();
			} catch (Exception e) {
				e.printStackTrace();
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return -1;
	}

	public static void statDownLoadCDN(final CdnCollectDataForPlay data) {
		CommonRequestForMain.statDownLoadCDN(data);
	}
	
}
