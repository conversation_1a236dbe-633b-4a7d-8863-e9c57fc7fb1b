package com.ximalaya.ting.android.opensdk.httputil;

import android.text.TextUtils;

import com.sina.util.dnscache.DNSCache;
import com.sina.util.dnscache.DomainInfo;
import com.sina.util.dnscache.Tools;
import com.ximalaya.ting.android.opensdk.model.xdcs.XdcsEvent;
import com.ximalaya.ting.android.player.XMediaPlayerConstants;
import com.ximalaya.ting.android.xmnetmonitor.networkperformance.HttpEventListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import okhttp3.Interceptor;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Created by roc on 2019/5/8.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15618953973
 */
public class PlayRequestInterceptor implements HttpDNSInterceptor.IRequestInterceptor {
    @Override
    public Response onHandleCallBack(Interceptor.Chain chain, Request originRequest, List<DomainInfo> domainInfoList, String originProtocol, HttpEventListener httpEventListener) throws IOException {
        String retryType = originRequest.header(XMediaPlayerConstants.RETRY_TYPE);
        // 这里是针对老的播放器来处理的，新的不再走到该逻辑了
        if(TextUtils.isEmpty(retryType)){
            return null;
        }
        originRequest = originRequest.newBuilder().removeHeader(XMediaPlayerConstants.RETRY_TYPE).build();
        int rTryCount = 0;
        Response resultResponse = null;
        List<XdcsEvent> eventList = new ArrayList<>();
        long requestTime = 0;
        String requestIp = "";

        String errStr = "play request fail";

        for (DomainInfo domainInfo : domainInfoList) {
            rTryCount++;
            requestTime = System.currentTimeMillis();
            try {
                Request.Builder builder = originRequest.newBuilder();

                String handleHost = domainInfo.newHost;
                String handleUrl = domainInfo.url;
                DomainInfo domainInfoDns = replaceIpThroughDnsResolver(chain, domainInfo);
                if(domainInfoDns != null) {
                    handleHost = domainInfoDns.newHost;
                    handleUrl = domainInfoDns.url;
                }
                builder.url(handleUrl);
                builder.header("Host", handleHost);
                resultResponse = chain.proceed(builder.build());
                if(!Tools.isIP(Tools.getHostName(domainInfo.url))) {
                    requestIp = Tools.getUrlIp(domainInfo.url);
                }
            } catch (Throwable e) {
                //mp3 m4a
                resultResponse = null;
                DNSCache.getInstance().addToDarkRoom(domainInfo.httpDnsHost, System.currentTimeMillis(), domainInfo.newHost);
                Throwable cause = e.getCause();
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(e.toString());
                if(cause != null) {
                    stringBuilder.append(" ; cause = ");
                    stringBuilder.append(cause.toString());
                }


                XdcsEvent xdcsEvent = XDCSEventUtil.createXdcsEvent(domainInfo.url, XDCSEventUtil.RESULT_FAIL,
                        rTryCount + "", domainInfo.newHost, originRequest.header("user-agent"), requestTime + "",
                        stringBuilder.toString(), requestIp, "", originProtocol, originProtocol, e.getClass().getSimpleName());
                eventList.add(xdcsEvent);
                errStr = xdcsEvent.toString();

            }
            if (resultResponse != null) {
                if(XMediaPlayerConstants.DOWNLOAD_DATA_FOR_PLAY.equals(retryType) && resultResponse.code()==206){
                    if(rTryCount > 1) {
                        XDCSEventUtil.sendHttpDnsEvent(eventList);
                    }
                    return resultResponse;
                }

                if(XMediaPlayerConstants.COMMON_REQUEST_FOR_PLAY.equals(retryType) && resultResponse.isSuccessful()){
                    if(rTryCount > 1) {
                        XDCSEventUtil.sendHttpDnsEvent(eventList);
                    }
                    return resultResponse;
                }

                if(resultResponse.code() == 403) {
                    return resultResponse;
                }

                boolean if5XX = false;
                if (resultResponse.code() < 600 && resultResponse.code() >= 500) {
                    if5XX = true;
                }
                if (!if5XX) {
                    Protocol protocol = resultResponse.protocol();
                    String newProtocol = originProtocol;
                    if(protocol == Protocol.HTTP_2){
                        newProtocol = "http2";
                    }
                    XdcsEvent xdcsEvent = XDCSEventUtil.createXdcsEvent(domainInfo.url, XDCSEventUtil.RESULT_FAIL,
                            rTryCount + "", domainInfo.newHost, originRequest.header("user-agent"), requestTime + "",
                            "", requestIp, resultResponse.code()+"", newProtocol, originProtocol, "");
                    eventList.add(xdcsEvent);
                }
            }
            DNSCache.getInstance().addToDarkRoom(domainInfo.httpDnsHost, System.currentTimeMillis(), domainInfo.newHost);
        }

        XDCSEventUtil.sendHttpDnsEvent(eventList);

        throw new IOException(errStr);

    }

    private DomainInfo replaceIpThroughDnsResolver(Interceptor.Chain chain, DomainInfo domainInfo) {

        if(!GatewaySwitchManager.USE_TICKET) {
            SslCacheManager.clearSslCache();
        }

        if(GatewaySwitchManager.USE_TICKET && !DnsAspect.getInstance().detectIfProxyExist(DnsAspect.sContext)) {
            if (domainInfo == null) {
                return null;
            }
            if (TextUtils.isEmpty(domainInfo.url)) {
                return null;
            }
            String currentHost = Tools.getHostName(domainInfo.url);
            if (TextUtils.isEmpty(currentHost)) {
                return null;
            }
            if (Tools.isIP(currentHost) || currentHost.contains(":")) {
                String host = domainInfo.newHost;
                String originHost = domainInfo.originHost;
                String httpDnsHost = domainInfo.httpDnsHost;
                String url = domainInfo.url;

                if (Tools.isIPv6(httpDnsHost)) {
                    String ip = "[" + httpDnsHost + "]";
                    url = Tools.getIpUrl(url, ip, host);
                } else {
                    url = Tools.getIpUrl(url, httpDnsHost, host);
                }
                DnsAspect.getInstance().putData(chain.call().hashCode(), host, httpDnsHost);
                return new DomainInfo(httpDnsHost, url, originHost, host);
            }
        }
        return null;
    }
}
