package com.ximalaya.ting.android.opensdk.httputil;

import android.app.Application;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.reflect.Field;

import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;

/**
 * Created by jack.qin on 2021/11/25.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class RealCallHook {
    private static final String TAG = "RealCallHook";
    private static final String MOBILE_HOST = "mobile.ximalaya.com";
    public static Application sApplication;

    public static void realCallConstructMethodHook(OkHttpClient client, Request originalRequest) {
        if (Build.VERSION.SDK_INT < 22) {
            return;
        }
        if (ProtocolManager.getInstanse().getIfUsingFreeFlow()) {
            return;
        }
        if (ProtocolManager.getInstanse().getIfDisableProtocolSwitch()) {
            return;
        }
        if (!ProtocolManager.getInstanse().getIfSwitchProtocol()) {
            return;
        }
        if (sApplication != null) {
            boolean ifUse = MmkvCommonUtil.getInstance(sApplication).getBoolean(
                    PreferenceConstantsInOpenSdk.ITEM_IF_USE_REAL_CALL_HOOK, true);
            if (!ifUse) {
                Logger.i(TAG, "real call hook close");
                return;
            }
        }
        if (originalRequest != null) {
            HttpUrl httpUrl = originalRequest.url();
            if (httpUrl != null) {
                if (!TextUtils.isEmpty(httpUrl.host()) && MOBILE_HOST.equals(httpUrl.host()))
                    if (!httpUrl.isHttps()) {
                        HttpUrl httpsHttpUrl = httpUrl.newBuilder().scheme("https").build();
                        try {
                            Field field = Request.class.getDeclaredField("url");
                            field.setAccessible(true);
                            field.set(originalRequest, httpsHttpUrl);
                            Logger.i(TAG, httpsHttpUrl + "");
                        } catch (Exception exception) {
                            exception.printStackTrace();
                        }
                    }
            }
        }
    }
}
