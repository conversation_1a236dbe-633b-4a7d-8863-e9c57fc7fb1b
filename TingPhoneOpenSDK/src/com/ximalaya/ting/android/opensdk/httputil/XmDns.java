package com.ximalaya.ting.android.opensdk.httputil;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import com.sina.util.dnscache.DNSCache;
import com.ximalaya.ting.android.xmutil.Logger;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import okhttp3.Dns;

/**
 * Created by jack.qin on 2019/2/26.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Deprecated
public class XmDns implements Dns {
    private static final String TAG = "XMDNS";
    private volatile static XmDns singleton;

    private XmDns() {
    }

    public synchronized static XmDns getInstance() {
        if (singleton == null) {
            synchronized (XmDns.class) {
                if (singleton == null) {
                    singleton = new XmDns();
                }
            }
        }
        return singleton;
    }

    private Map<String, String> ipMap = new ConcurrentHashMap<>();

    void putIpData(String domain, String ip) {
        ipMap.put(domain, ip);
    }

    private Dns SYSTEM = new Dns() {
        @Override
        public List<InetAddress> lookup(String hostname) throws UnknownHostException {
            if (hostname == null) throw new UnknownHostException("hostname == null");
            try {
                return Arrays.asList(InetAddress.getAllByName(hostname));
            } catch (NullPointerException e) {
                UnknownHostException unknownHostException =
                        new UnknownHostException("Broken system behaviour for dns lookup of " + hostname);
                unknownHostException.initCause(e);
                throw unknownHostException;
            }
        }
    };

    @Override
    public List<InetAddress> lookup(String hostname) throws UnknownHostException {
        if(DNSCache.getInstance().isUseDnsCache() && GatewaySwitchManager.USE_TICKET) {
            String ip = ipMap.get(hostname);
            if (!TextUtils.isEmpty(ip)) {
                List<InetAddress> inetAddressList = Arrays.asList(InetAddress.getAllByName(ip));
                InetAddress inetAddress = inetAddressList.get(0);
                Logger.i(TAG, "hostname =  " + hostname + " ip = " + ip  + " inetAddressList " + inetAddress.getHostAddress());
                return inetAddressList;
            }
        }
        return SYSTEM.lookup(hostname);
    }

    public static boolean detectIfProxyExist(Context ctx) {
        boolean IS_ICS_OR_LATER = Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH;
        String proxyHost;
        int proxyPort;
        try {
            if (IS_ICS_OR_LATER) {
                proxyHost = System.getProperty("http.proxyHost");
                String port = System.getProperty("http.proxyPort");
                proxyPort = Integer.parseInt(port != null ? port : "-1");
            } else {
                proxyHost = android.net.Proxy.getHost(ctx);
                proxyPort = android.net.Proxy.getPort(ctx);
            }
            return proxyHost != null && proxyPort != -1;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
