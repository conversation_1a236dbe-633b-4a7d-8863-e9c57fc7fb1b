package com.ximalaya.ting.android.opensdk.httputil;

import com.sina.util.dnscache.dnsp.DnsConfig;
import com.ximalaya.ting.android.opensdk.model.xdcs.XdcsEvent;
import com.ximalaya.ting.android.opensdk.model.xdcs.XdcsRecord;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by chengyun.wu on 17/6/14.
 * HTTPDNS&网络日志方案
 * http://gitlab.ximalaya.com/snippets/125
 *
 * <AUTHOR>
 */

public class XDCSEventUtil {

    private static final String TAG = "XDCSEventUtil";

    public static final String RESULT_SUCCESS = "success";

    public static final String RESULT_FAIL = "fail";

    /**
     * 记录应用每次启动时的初始化结果。可能存在多次请求，比如域名请求失败后换ip请求，不管成功失败均需上传。
     */
    public static void sendHttpDnsInitEvent(String requestUrl, String requestTs,
                                            String initResult, String httpDnsSwitch, String errorInfo) {
        XdcsEvent xdcsEvent = new XdcsEvent();
        Map<String, String> props = new HashMap<>();
        props.put("type", "httpdns_init");
        props.put("request_url", requestUrl);
        props.put("request_ts", requestTs);
        props.put("init_result", initResult);
        props.put("httpdns_switch", httpDnsSwitch);
        props.put("error_info", errorInfo);
        xdcsEvent.props = props;
        xdcsEvent.setTs(System.currentTimeMillis());
        xdcsEvent.setType("NETWORKINFO");
        List<XdcsEvent> eventList = new ArrayList<>();
        eventList.add(xdcsEvent);
        Logger.i(TAG, "sendHttpDnsInitEvent " + xdcsEvent);
        IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
        if (xdcsPost != null) {
            xdcsPost.postError(XdcsRecord.createXdcsRecord(eventList));
        }
    }

    /**
     * 记录校验失败的ip、域名，获得ip的dns，以及当时的网络状态等信息。
     */
    public static void sendHttpDnsCheckIPEvent(String domain, String failIp,
                                               String checkTs, String provider) {
        XdcsEvent xdcsEvent = new XdcsEvent();
        Map<String, String> props = new HashMap<>();
        props.put("type", "httpdns_check_ip");
        props.put("domain", domain);//校验的域名
        props.put("fail_ip", failIp);//校验失败的ip
        props.put("check_ts", checkTs);//校验请求的时间
        props.put("provider", provider);//获得ip的途径（dns）

        xdcsEvent.props = props;
        xdcsEvent.setTs(System.currentTimeMillis());
        xdcsEvent.setType("NETWORKINFO");
        List<XdcsEvent> eventList = new ArrayList<>();
        eventList.add(xdcsEvent);
        Logger.i(TAG, "sendHttpDnsCheckIPEvent " + xdcsEvent);
        IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
        if (xdcsPost != null) {
            xdcsPost.postError(XdcsRecord.createXdcsRecord(eventList));
        }
    }

    /**
     * 记录经由httpdns方案的网络请求结果。
     * 只记录存在重试的请求（即至少有一次失败的请求链）。每条日志里的events只包含一次请求链的记录，不可互相混淆。
     */
    public static void sendHttpDnsEvent(List<XdcsEvent> eventList) {

        if(eventList==null||eventList.size()==0){
            return;
        }

        Logger.i(TAG, "sendHttpDnsEvent : " + eventList.size());
        IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
        if (xdcsPost != null && DnsConfig.ERROR_LOG_SWITCH && xdcsPost.getItemSettingValue()) {
            xdcsPost.postError(XdcsRecord.createXdcsRecord(eventList));
        }
    }

    /**
     * create a HttpDns event XdcsEvent
     */
    public static XdcsEvent createXdcsEvent(String requestUrl, String requestResult,
                                            String requestSeq, String requestHost,
                                            String ua, String requestTs, String errorInfo) {
        XdcsEvent xdcsEvent = new XdcsEvent();
        Map<String, String> props = new HashMap<>();
        props.put("type", "httpdns_request");
        props.put("request_url", requestUrl);
        props.put("request_result", requestResult);//请求结果,success/fail
        props.put("request_seq", requestSeq);//此次请求链中的第几次尝试
        props.put("request_host", requestHost); //请求的host头，可为空，ip请求时必需有
        props.put("ua", ua);//user-agent
        props.put("request_ts", requestTs);//发生错误的时间
        props.put("error_info", errorInfo);//错误信息

        xdcsEvent.props = props;
        xdcsEvent.setTs(System.currentTimeMillis());
        xdcsEvent.setType("NETWORKINFO");
        Logger.i(TAG, "createXdcsEvent " + xdcsEvent);
        return xdcsEvent;

    }

    public static XdcsEvent createXdcsEvent(String requestUrl, String requestResult,
                                            String requestSeq, String requestHost,
                                            String ua, String requestTs, String errorInfo, String exceptionType) {
        XdcsEvent xdcsEvent = new XdcsEvent();
        Map<String, String> props = new HashMap<>();
        props.put("type", "httpdns_request");
        props.put("request_url", requestUrl);
        props.put("request_result", requestResult);//请求结果,success/fail
        props.put("request_seq", requestSeq);//此次请求链中的第几次尝试
        props.put("request_host", requestHost); //请求的host头，可为空，ip请求时必需有
        props.put("ua", ua);//user-agent
        props.put("request_ts", requestTs);//发生错误的时间
        props.put("error_info", errorInfo);//错误信息
        props.put("exception", exceptionType);

        xdcsEvent.props = props;
        xdcsEvent.setTs(System.currentTimeMillis());
        xdcsEvent.setType("NETWORKINFO");
        Logger.i(TAG, "createXdcsEvent " + xdcsEvent);
        return xdcsEvent;

    }

    /**
     * 临时方法，发送用户手机token，勿用
     *
     * @param deviceId
     * @return
     */
    public static XdcsEvent createXDCSEvent(String deviceId) {
        XdcsEvent errorEvent = new XdcsEvent();
        Map<String, String> propMap = new HashMap<>();

        propMap.put("trace_device_id", deviceId);
        errorEvent.props = propMap;
        errorEvent.setType("TRACE_DEVICE_ID");
        errorEvent.setTs(System.currentTimeMillis());
        return errorEvent;
    }

    /**
     * create a HttpDns event XdcsEvent
     *
     * @param requestUrl
     * @param requestResult
     * @param requestSeq
     * @param requestHost
     * @param ua
     * @param requestTs
     * @param errorInfo
     * @return
     */
    public static XdcsEvent createXdcsEvent(String requestUrl, String requestResult,
                                            String requestSeq, String requestHost,
                                            String ua, String requestTs, String errorInfo, String ip, String code
            ,String protocol, String originProtocol , String exceptionType) {
        XdcsEvent xdcsEvent = new XdcsEvent();
        Map<String, String> props = new HashMap<>();
        props.put("type", "httpdns_request");
        props.put("request_url", requestUrl);
        props.put("request_result", requestResult);//请求结果,success/fail
        props.put("request_seq", requestSeq);//此次请求链中的第几次尝试
        props.put("request_host", requestHost); //请求的host头，可为空，ip请求时必需有
        props.put("ua", ua);//user-agent
        props.put("request_ts", requestTs);//发生错误的时间
        props.put("error_info", errorInfo);//错误信息
        props.put("request_ip", ip);
        props.put("status_code", code);
        props.put("protocol", protocol);
        props.put("origin_protocol", originProtocol);
        props.put("exception", exceptionType);
        xdcsEvent.props = props;
        xdcsEvent.setTs(System.currentTimeMillis());
        xdcsEvent.setType("NETWORKINFO");
        Logger.i(TAG, "createXdcsEvent " + xdcsEvent);
        return xdcsEvent;

    }

    /**
     * 记录未经由httpdns方案的网络请求结果。只记录失败的请求。
     */
    public static void sendNormalRequestEvent(String requestUrl, String ua,
                                              String requestTs, String errorInfo) {
        XdcsEvent xdcsEvent = new XdcsEvent();
        Map<String, String> props = new HashMap<>();
        props.put("type", "normal_request");
        props.put("request_url", requestUrl);
        props.put("ua", ua);//user-agent
        props.put("request_ts", requestTs); //发生错误的时间
        props.put("error_info", errorInfo);//错误信息

        xdcsEvent.props = props;
        xdcsEvent.setTs(System.currentTimeMillis());
        xdcsEvent.setType("NETWORKINFO");
        List<XdcsEvent> eventList = new ArrayList<>();
        eventList.add(xdcsEvent);
        Logger.i(TAG, "sendNormalRequestEvent " + xdcsEvent);
        IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
        if (xdcsPost != null && DnsConfig.ERROR_LOG_SWITCH && xdcsPost.getItemSettingValue()) {
            xdcsPost.postError(XdcsRecord.createXdcsRecord(eventList));
        }
    }
}
