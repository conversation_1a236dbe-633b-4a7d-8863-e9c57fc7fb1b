package com.ximalaya.ting.android.opensdk.httputil;

import android.text.TextUtils;

import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.httputil.util.Util;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Map;

import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;

/**
 * ClassName:BasicRequest Function: TODO ADD FUNCTION Reason: TODO ADD REASON
 *
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015-6-26 上午10:56:27
 *
 * @see
 */
public class BaseBuilder {
	public static int URL_MAX_LENGTH = 2048;

	private static Request.Builder addCommonCookie(Request.Builder builder) {
		return builder;
	}

	public static Request.Builder urlGet(String url) throws XimalayaException {
		return urlGet(url, null);
	}

	public static Request.Builder urlGet(String url, Map<String, String> specificParams)
			throws XimalayaException {
		return urlGet(url ,specificParams ,true);
	}

	public static Request.Builder urlGet(String url, Map<String, String> specificParams ,boolean checkUrlLength)
			throws XimalayaException {
		if (!ConstantsOpenSdk.isDebug) {
			if (TextUtils.isEmpty(url)) {
				throw XimalayaException.getExceptionByCode(XimalayaException.REQUEST_URL_EMPTY);
			}
		}

		if (specificParams != null && !specificParams.isEmpty()) {
			url = url
					+ "?"
					+ Util.ConvertMap2HttpParams(Util
							.encoderName(specificParams));
		}

		if (checkUrlLength && !TextUtils.isEmpty(url) && url.length() >= URL_MAX_LENGTH){
			throw new XimalayaException(
					XimalayaException.REQUEST_DATA_TOO_LARGE
					, "请求失败，url拼接后长度超过"+ URL_MAX_LENGTH+",请检查拼接参数！");
		}
		Logger.i("url123", url);
		Request.Builder url1 = null;
		try {
			url1 = new Request.Builder().url(url);
		} catch (Exception e) {
			e.printStackTrace();
			throw new XimalayaException(XimalayaException.REQUEST_URL_PARSE_ERROR ,e.getMessage());
		}

		return addCommonCookie(url1);
	}

	public static Request.Builder urlPost(String url, String jsonStr, String meadiaType)
			throws XimalayaException {
		if (!ConstantsOpenSdk.isDebug) {
			if (TextUtils.isEmpty(url)) {
				throw XimalayaException.getExceptionByCode(XimalayaException.REQUEST_URL_EMPTY);
			}
		}
		return addCommonCookie(new Request.Builder().url(url).post(
				RequestBody.create(MediaType.parse(meadiaType), jsonStr)));
	}
	public static Request.Builder urlGzipedPost(String url, byte[] data, String meadiaType)
			throws XimalayaException {
		if (!ConstantsOpenSdk.isDebug) {
			if (TextUtils.isEmpty(url)) {
				throw XimalayaException.getExceptionByCode(XimalayaException.REQUEST_URL_EMPTY);
			}
		}
		return addCommonCookie(new Request.Builder().url(url).post(
				RequestBody.create(MediaType.parse(meadiaType), data)));
	}

	public static Request.Builder urlPost(String url, Map<String, String> specificParams)
			throws XimalayaException {
		if (!ConstantsOpenSdk.isDebug) {
			if (TextUtils.isEmpty(url)) {
				throw XimalayaException.getExceptionByCode(XimalayaException.REQUEST_URL_EMPTY);
			}
		}
		FormBody.Builder builder = new FormBody.Builder();
		if (specificParams != null && specificParams.size() > 0) {
			for (Map.Entry<String, String> entry : specificParams.entrySet()) {
				if(entry.getValue() != null) {
					builder.add(entry.getKey(), entry.getValue());
				}
			}
		} else {
			throw XimalayaException.getExceptionByCode(XimalayaException.FORM_ENCODE_LAST_ONE);
		}
		return addCommonCookie(new Request.Builder().url(url).post(
				builder.build()));
	}

	public static Request.Builder urlPut(String url,String jsonStr, String meadiaType)
            throws XimalayaException{
        if (!ConstantsOpenSdk.isDebug) {
            if (TextUtils.isEmpty(url)) {
                throw XimalayaException.getExceptionByCode(XimalayaException.REQUEST_URL_EMPTY);
            }
        }
        return addCommonCookie(new Request.Builder().url(url).put(
                RequestBody.create(MediaType.parse(meadiaType), jsonStr)));
	}


	public static Request.Builder urlPut(String url,Map<String,String> specificParams)throws XimalayaException{
		if (!ConstantsOpenSdk.isDebug){
			if (TextUtils.isEmpty(url)) {
				throw XimalayaException.getExceptionByCode(XimalayaException.REQUEST_URL_EMPTY);
			}
		}
		FormBody.Builder builder = new FormBody.Builder();
		if (specificParams != null && specificParams.size() > 0) {
			for (Map.Entry<String, String> entry : specificParams.entrySet()) {
				if(entry.getValue() != null) {
					builder.add(entry.getKey(), entry.getValue());
				}
			}
		} else {
			throw XimalayaException.getExceptionByCode(XimalayaException.FORM_ENCODE_LAST_ONE);
		}
		return addCommonCookie(new Request.Builder().url(url).put(
				builder.build()));
	}

	public static Request.Builder urlPost(String url, File file)
			throws XimalayaException {
		if (!ConstantsOpenSdk.isDebug) {
			if (TextUtils.isEmpty(url)) {
				throw XimalayaException.getExceptionByCode(XimalayaException.REQUEST_URL_EMPTY);
			}
		}
		return addCommonCookie(new Request.Builder().url(url).post(
				RequestBody.create(null, file)));
	}

	public static Request.Builder urlPost(String url, byte[] bytes)
			throws XimalayaException {
		if (!ConstantsOpenSdk.isDebug) {
			if (TextUtils.isEmpty(url)) {
				throw XimalayaException.getExceptionByCode(XimalayaException.REQUEST_URL_EMPTY);
			}
		}
		return addCommonCookie(new Request.Builder().url(url).post(
				RequestBody.create(null, bytes)));
	}

	public static Request.Builder urlPost(String url, String str)
			throws XimalayaException {
		if (!ConstantsOpenSdk.isDebug) {
			if (TextUtils.isEmpty(url)) {
				throw XimalayaException.getExceptionByCode(XimalayaException.REQUEST_URL_EMPTY);
			}
		}
		return addCommonCookie(new Request.Builder().url(url).post(
				RequestBody.create(null, str)));
	}

	/**
	 * 上传文件（支持多个）
	 *
	 * @param contentType
	 *            Http请求的内容类型
	 * @param files
	 *            多个文件，key表示文件的标识，
	 * @param params
	 *            文件的多个参数 key对应的是参数名，value对应的是内容
	 * @return
	 * @throws XimalayaException
	 */
	public static RequestBody urlPost(String contentType, Map<String, File> files, Map<String, String> params)
			throws XimalayaException {
		MultipartBody.Builder builder = new MultipartBody.Builder();
		builder.setType(MultipartBody.FORM);
//		MultipartBuilder builder = new MultipartBuilder()
//				.type(MultipartBuilder.FORM);

		for (String key : params.keySet()) {//添加文件参数
			builder.addPart(
					Headers.of("Content-Disposition", "form-data; name=\""
							+ key + "\""),
					RequestBody.create(null, params.get(key)));
		}

		for (String key : files.keySet()) {//添加文件内容
			builder.addPart(
					Headers.of("Content-Disposition", "form-data; name=\""
							+ key + "\"; filename=\""
                            + getEncodedValue(files.get(key).getName()) + "\""),
					RequestBody.create(MediaType.parse(contentType),
							files.get(key)));
		}
		return builder.build();
	}

    private static String getEncodedValue(String value) {
        if (value == null || value.isEmpty()) {
            return "";
        }

        try {
            return URLEncoder.encode(value, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return "";
        }
    }

	/**
	 * 上传byte[]及参数（支持多个）
	 *
	 * @param contentType
	 *            Http请求的内容类型
	 * @param content
	 *            byte[]
	 * @param params
	 *            多个参数 key对应的是参数名，value对应的是内容
	 * @return
	 * @throws XimalayaException
	 */
	public static Request.Builder urlPostByteAndParam(String url,String contentType, byte[] content, Map<String, String> params)
			throws XimalayaException {
		MultipartBody.Builder builder = new MultipartBody.Builder();
		builder.setType(MultipartBody.FORM);
		for (String key : params.keySet()) {//添加param参数
			Headers of = Headers.of("Content-Disposition", "form-data; name=\"" + key + "\"");
			builder.addPart(of, RequestBody.create(null, params.get(key)));
		}
		String data="data";
		builder.addPart(Headers.of("Content-Disposition", "form-data; name=\""
						+ data + "\"; filename=\"" + "" + "\""),
				RequestBody.create(MediaType.parse(contentType), content));
		return addCommonCookie(new Request.Builder().url(url).post(builder.build()));
	}

	public static Request.Builder urlDelete(String url, Map<String, String> specificParams)
			throws XimalayaException {
		if (!ConstantsOpenSdk.isDebug) {
			if (TextUtils.isEmpty(url)) {
				throw XimalayaException.getExceptionByCode(XimalayaException.REQUEST_URL_EMPTY);
			}
		}
		FormBody.Builder builder = new FormBody.Builder();
		if (specificParams != null && specificParams.size() > 0) {
			for (Map.Entry<String, String> entry : specificParams.entrySet()) {
				if(entry.getValue() != null) {
					builder.add(entry.getKey(), entry.getValue());
				}
			}
		} else {
			throw XimalayaException.getExceptionByCode(XimalayaException.FORM_ENCODE_LAST_ONE);
		}
		return addCommonCookie(new Request.Builder().url(url).delete(
				builder.build()));
	}

}
