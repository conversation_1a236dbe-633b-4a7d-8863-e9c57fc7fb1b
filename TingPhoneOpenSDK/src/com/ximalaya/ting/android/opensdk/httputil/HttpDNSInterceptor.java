package com.ximalaya.ting.android.opensdk.httputil;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.sina.util.dnscache.DNSCache;
import com.sina.util.dnscache.DomainInfo;
import com.sina.util.dnscache.Tools;
import com.sina.util.dnscache.model.DomainInfoWrapper;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.httputil.util.Util;
import com.ximalaya.ting.android.opensdk.model.PlayExceptionModel;
import com.ximalaya.ting.android.opensdk.model.xdcs.XdcsEvent;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayExceptionCollector;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.util.ExceptionUtil;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.statistic.audio.error.XmPlayErrorStatistic;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmnetmonitor.networkperformance.HttpEventListener;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.app.XmAppHelper;

import java.io.IOException;
import java.lang.reflect.Method;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

import javax.net.ssl.SSLException;

import okhttp3.EventListener;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.internal.http.RealInterceptorChain;
import okhttp3.internal.http2.StreamResetException;

/**
 * Created by jack.qin
 *
 * <AUTHOR>
 */
public class HttpDNSInterceptor implements Interceptor {
    private static final String KEY_FORCE_OPEN_DNS = "key_force_open_dns";
    private static final String HOST_MMKV_FILE_DNS_COMMON = "HOST_MMKV_FILE_DNS_COMMON";

    public static ConcurrentHashMap<String, String> map = new ConcurrentHashMap<>();
    private static final String TAG = "HttpDNSInterceptor";
    private Context mContext;
    private Set<IDnsCacheListener> mDnsCacheListeners = new HashSet<>();
    public static int EVENT_LIST_NUM = 2;

    private static boolean sForceOpenDns;

    private IRequestHandler iRequestHandler;
    private IRequestInterceptor iRequestInterceptor;
    private IPlayResponseFileLegalCheck iPlayResponseFileLegalCheck;
    private PlayResponseFileLegalCheck playResponseFileLegalCheck;
    public static final String TRACE_UUID = UUID.randomUUID().toString();
    public static long sIncrementId = 0l;
//    public static final String XM_TRACE_ID = "xmTraceId";
    public static boolean sIfNeedUseTraceId = true;

    public int sslExceptionCount = 0;
    public long sslExceptionTime = 0;
    public static boolean notNeedCheckException = false;
    private volatile boolean needUseLastSavedIp;

    public int connectExceptionCount = 0;
    public long connectExceptionTime = 0;

    //    SSLHandshakeException
//    SSLPeerUnverifiedException
//    SSLException
//
//    SocketException
//    SocketTimeoutException
//    ConnectException
//    IOException
//    StreamResetException
    public synchronized void checkException(Throwable e) {
        try {
            if (e == null) {
                return;
            }
            if (mContext == null) {
                return;
            }
            if (!com.ximalaya.ting.android.xmutil.NetworkType.isConnectTONetWork(mContext)) {
                return;
            }

            if (notNeedCheckException) {
                return;
            }

            //大于30s则不检测
            if ((System.currentTimeMillis() - initTime) > 30 * 1000) {
                return;
            }

            if (e instanceof SSLException) {
                sslExceptionCount++;
                if (sslExceptionCount >= 5) {
                    Logger.i(TAG, "cxissuessl " + "close_host_verify");
                    statToXDCSError("cxissuessl", "close_host_verify" + e.getMessage());
                    notNeedCheckException = true;
                    needUseLastSavedIp = true;
                }
            } else if (e instanceof SocketException
                    || e instanceof SocketTimeoutException
                    || e instanceof StreamResetException) {
                connectExceptionCount++;
                if (connectExceptionCount >= 5) {
                    Logger.i(TAG, "cxissueconnection " + "connection_exception");
                    statToXDCSError("cxissueconnection", "connection_exception");
                    notNeedCheckException = true;
                    needUseLastSavedIp = true;
                }
            }
        }catch (Throwable t) {
            t.printStackTrace();
        }
    }

    public static void statToXDCSError(String module, String errorStr) {
        try {
            Class xdcsCollectUtil = Class.forName("com.ximalaya.ting.android.framework.manager.XDCSCollectUtil");
            if (xdcsCollectUtil != null) {
                Method method = xdcsCollectUtil.getDeclaredMethod("statErrorToXDCS", String.class, String.class);
                if (method != null) {
                    method.invoke(null, module, errorStr);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    interface IRequestInterceptor {
        Response onHandleCallBack(Chain chain, Request request, List<DomainInfo> domainInfos, String originProtocol, HttpEventListener httpEventListener) throws IOException;
    }

    public interface IDnsCacheListener {
        void onDnsCache(String oldUrl, String newUrl);
    }

    //1 trackinfo 2 防盗链 3 播放请求
    private static Map<Integer, String> playUrlMap = new HashMap<>();

    public static void addPlayUrl(String url, int requestType) {
        playUrlMap.put(requestType, url);
    }

    private long initTime;
    private RetryHttpRequest retryHttpRequest;
    public HttpDNSInterceptor(Context context) {
        this.mContext = context;
        sForceOpenDns = MmkvCommonUtil.getInstance(context).getBoolean(KEY_FORCE_OPEN_DNS, false);
        if (sForceOpenDns) {
            ProtocolManager.getInstanse().setIsDisableProtocolSwitch(true);
        }
        playResponseFileLegalCheck = new PlayResponseFileLegalCheck();
        initTime = System.currentTimeMillis();
        retryHttpRequest = new RetryHttpRequest();
    }

    public static void setForceOpenDns(boolean forceOpenDns) {
        sForceOpenDns = forceOpenDns;
        ProtocolManager.getInstanse().setIsDisableProtocolSwitch(forceOpenDns);
        MmkvCommonUtil.getInstance(XmAppHelper.getApplication()).saveBoolean(KEY_FORCE_OPEN_DNS, forceOpenDns);
    }

    public static boolean getForceOpenDns() {
        return sForceOpenDns;
    }

    public void setRequestInterceptor(IRequestInterceptor interceptor) {
        this.iRequestInterceptor = interceptor;
    }

    public interface IRequestHandler {
        String onRequestHandler(String url);
    }

    public interface IPlayResponseFileLegalCheck {
        boolean onPlayResponseFileLegalCheck(byte[] data, Response response, int fileType);
    }

    public void setPlayResponseFileLegalCheck(IPlayResponseFileLegalCheck iPlayResponseFileLegalCheck) {
        this.iPlayResponseFileLegalCheck = iPlayResponseFileLegalCheck;
    }

    public void setRequestHandler(IRequestHandler iRequestHandler) {
        this.iRequestHandler = iRequestHandler;
    }

    public void addDnsCacheListener(IDnsCacheListener dnsCacheListener) {
        if (dnsCacheListener != null) {
            mDnsCacheListeners.add(dnsCacheListener);
        }
    }

    public void removeDnsCacheListener(IDnsCacheListener dnsCacheListener) {
        if (dnsCacheListener != null && mDnsCacheListeners.contains(dnsCacheListener)) {
            mDnsCacheListeners.remove(dnsCacheListener);
        }
    }

    private void notifyDnsCacheListeners(String oldUrl, String newUrl) {
        if (mDnsCacheListeners.size() > 0) {
            for (IDnsCacheListener dnsCacheListener : mDnsCacheListeners) {
                dnsCacheListener.onDnsCache(oldUrl, newUrl);
            }
        }
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        HttpEventListener httpEventListener = null;

        if (chain instanceof RealInterceptorChain) {
            RealInterceptorChain realInterceptorChain = (RealInterceptorChain) chain;
            EventListener eventListener = realInterceptorChain.eventListener();
            if (eventListener instanceof HttpEventListener) {
                httpEventListener = (HttpEventListener) eventListener;
            }
        }

        Throwable network_exception = null;
        Throwable no_idc_exception = null;
        Throwable[] throwables = new Throwable[]{network_exception, no_idc_exception};

        Request originRequest = chain.request();
        HttpUrl httpUrl = originRequest.url();
        String url = httpUrl.toString();
        String host = httpUrl.host();
        String originScheme = httpUrl.scheme();
        String originProtocol = "http1.1";
        boolean isCdnUrl = false;
        try {
            isCdnUrl = chain.call().request().url().toString().contains("xmcdn")
                    || chain.call().request().url().toString().contains("pcdn");
        } catch (Exception e) {
            e.printStackTrace();
        }

        String handlerUrlByOther = "";
        boolean hasHanderByOther = false;
        if (iRequestHandler != null) {
            handlerUrlByOther = iRequestHandler.onRequestHandler(url);
            if (!TextUtils.isEmpty(handlerUrlByOther)) {
//                hasHanderByOther = true;
                originRequest = originRequest.newBuilder().url(handlerUrlByOther)
                        .addHeader(DTransferConstants.REQUEST_HEADER_TX_BRFORE_HOST, host).build();
            }
        }

        Response resultResponse = null;
        if (!hasHanderByOther) {
            String handledUrl = ProtocolManager.getInstanse().handlerUrl(originScheme, host, url);
            if (!url.equals(handledUrl)) {
                originRequest = originRequest.newBuilder().url(handledUrl).build();
            }
            if (originScheme.equals("https")) {
                originProtocol = originScheme;
            }

            if (ConstantsOpenSdk.isDebug) {
                Logger.log("ProtocolManager handledUrl = " + handledUrl);
            }

            String userAgent = originRequest.header("user-agent");
            if (TextUtils.isEmpty(userAgent) || userAgent.contains("okhttp")) {
                userAgent = Util.getUserAgent(mContext);
                if (!TextUtils.isEmpty(userAgent)) {
                    originRequest = originRequest.newBuilder().header("user-agent", userAgent).build();
                }
            }

            //proxy情况下禁用dnscache
            if (DNSCache.getInstance().isUseDnsCache() && (!DnsAspect.getInstance().detectIfProxyExist(mContext) || sForceOpenDns)) {
                Logger.i(TAG, "useHttpDns " + url);
                HttpDnsErrorPostManager.getInstance().init(DNSCache.getInstance().getLogCompressTime());

                String[] invalidSourceHost = new String[1];

                resultResponse = this.getDnsCacheUrl(handledUrl, chain, originRequest, host, throwables, originProtocol, httpEventListener, isCdnUrl, invalidSourceHost);
                if (throwables[1] == null) {//ip list 不为空
                    if (resultResponse != null && InvalidContentTypeRetry.isMediaUrl(originRequest) && InvalidContentTypeRetry.isResponseTextPlain(resultResponse)) {
                        Response retryResponse = InvalidContentTypeRetry.retryRequest(mContext, originRequest, chain);
                        if (retryResponse != null) {
                            try {
                                resultResponse.close();
                                resultResponse = null;
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            return retryResponse;
                        }
                    }
                    if (resultResponse == null) {//发生了exception

                        //声音域名 cdn重试
                        if (retryHttpRequest != null) {
                            Response retryResponse = retryHttpRequest.retryRequest(mContext, originRequest, chain, throwables[0]);
                            if (retryResponse != null) {
                                return retryResponse;
                            }
                        }

                        if (!TextUtils.isEmpty(invalidSourceHost[0])) {
                            Response retryResponse = InvalidContentTypeRetry.retryRequest(mContext, originRequest, chain);
                            if (retryResponse != null) {
                                return retryResponse;
                            }
                        }

                        if (throwables[0] != null) {
                            checkHasException(url, throwables[0]);
                            if ("mobile.ximalaya.com".equals(host)) {
                                checkException(throwables[0]);
                            }
                            throw new IOException(throwables[0].toString() + "  URL:" + url, throwables[0]);
                        } else {
                            throw new IOException("application interceptor returned null" + "  URL:" + url);
                        }
                    }

                    return resultResponse;
                }
            }
        }
        /**
         * 主域名处理
         */
        Logger.i(TAG, "useOldPolicy " + url);
        boolean isRetry = false;
        try {
            Request.Builder builder = originRequest.newBuilder();
            builder.header("http_dns_address", "old,d_s=" + DNSCache.getInstance().isUseDnsCache()
                    + ";d_g=" + DNSCache.getInstance().getGatewaySwitch()
                    + ";d_f=" + DNSCache.getInstance().getFreeFlowSwitch()
                    + ";d_i=" + DNSCache.getInstance().getInitParamsSwitch()
                    + ";p=" + DnsAspect.getInstance().detectIfProxyExist(mContext)
                    + ";h=" + hasHanderByOther);
            resultResponse = chain.proceed(builder.build());
            if (resultResponse != null && resultResponse.headers() != null) {
                GatewaySwitchManager.parseProtocolSwitch(host, resultResponse.headers());
                GatewaySwitchManager.parseIDCSwitch(host, resultResponse.headers());
            }
        } catch (Throwable e) {
            if ("mobile.ximalaya.com".equals(host)) {
                checkException(e);
            }
            isRetry = true;
            network_exception = e;
            if (resultResponse != null) {
                resultResponse.close();
            }
            resultResponse = null;

            checkHasException(url, e);
        }
        if (resultResponse != null && resultResponse.isSuccessful()) {
            return resultResponse;
        }

        if (resultResponse == null) {

            //声音域名 cdn重试
            if (retryHttpRequest != null) {
                Response retryResponse = retryHttpRequest.retryRequest(mContext, originRequest, chain, network_exception);
                if (retryResponse != null) {
                    return retryResponse;
                }
            }

            if (network_exception != null) {
                throw new IOException(network_exception.toString() + "  URL:" + url, network_exception);
            } else {
                throw new IOException("application interceptor returned null" + "  URL:" + url);
            }
        }
        return resultResponse;
    }

    private void checkHasException(String url, Throwable e) {
        try {
            if (BaseUtil.isPlayerProcess(mContext) &&
                    ((playUrlMap.get(1) != null && url.contains(playUrlMap.get(1))) || (playUrlMap.get(3) != null && url.contains(playUrlMap.get(3))))
                    && XmPlayerService.getPlayerSrvice() != null
                    && XmPlayerService.getPlayerSrvice().getCurrPlayModel() != null) {
                String causeName = null;
                String message = null;
                String trace = null;
                String rootCauseName = null;
                String rootMessage = null;
                String rootTrace = null;
                if (e != null) {
                    causeName = e.getClass().getName();
                    message = e.getMessage();
                    trace = ExceptionUtil.getCauseTrace(e);
                    Throwable rootCause = ExceptionUtil.getRootCause(e);
                    if (rootCause != null) {
                        rootCauseName = rootCause.getClass().getName();
                        rootMessage = rootCause.getMessage();
                        rootTrace = ExceptionUtil.getCauseTrace(rootCause);
                    }
                }
                XmPlayErrorStatistic.getInstance().onBaseInfoError(XmPlayerService.getPlayerSrvice().getCurrPlayModel().getDataId(), 0, XmPlayErrorStatistic.getExceptionContent(e),
                        PlayerConstants.ERROR_FROM_BASE_INFO_3, causeName, message, trace, rootCauseName, rootMessage, rootTrace);
            } else if (BaseUtil.isPlayerProcess(mContext)
                    && playUrlMap.get(2) != null && url.contains(playUrlMap.get(2))
                    && XmPlayerService.getPlayerSrvice() != null
                    && XmPlayerService.getPlayerSrvice().getCurrPlayModel() != null) {
                String causeName = null;
                String message = null;
                String trace = null;
                String rootCauseName = null;
                String rootMessage = null;
                String rootTrace = null;
                if (e != null) {
                    causeName = e.getClass().getName();
                    message = e.getMessage();
                    trace = ExceptionUtil.getCauseTrace(e);
                    Throwable rootCause = ExceptionUtil.getRootCause(e);
                    if (rootCause != null) {
                        rootCauseName = rootCause.getClass().getName();
                        rootMessage = rootCause.getMessage();
                        rootTrace = ExceptionUtil.getCauseTrace(rootCause);
                    }
                }
                XmPlayErrorStatistic.getInstance().onPayInfoError(XmPlayerService.getPlayerSrvice().getCurrPlayModel().getDataId(), 0, XmPlayErrorStatistic.getExceptionContent(e),
                        PlayerConstants.ERROR_FROM_BASE_INFO_ANTI_2, causeName, message, trace, rootCauseName, rootMessage, rootTrace);
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }

    private Response getDnsCacheUrl(String url, Chain chain, Request originRequest, String host, Throwable[] throwables, String originProtocol, HttpEventListener httpEventListener, boolean isCdnUrl, String[] invalidSourceHost) {
        DomainInfoWrapper domainInfoWrapper = null;
        try {
            domainInfoWrapper = DNSCache.getInstance().getDomainServerIp(url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        int rTryCount = 0;
        Response resultResponse = null;
        List<XdcsEvent> eventList = new ArrayList<>();
        long requestTime = 0;
        boolean isIdcNull = false;
        String requestIp = "";
        boolean useHttpsForAudioRequest = false;

        String savedIp1 = null;
        String savedIp2 = null;
        String savedIp3 = null;
        String useIpString = "";
        String useHost = "";
        if (domainInfoWrapper != null) {
            List<DomainInfo> domainInfoList = domainInfoWrapper.domainInfos;
            if ("mobile.ximalaya.com".equals(host)) {
                if (needUseLastSavedIp) {
                    if (domainInfoList != null) {
                        List<DomainInfo> domainInfoListBackup = new ArrayList<>();
                        savedIp1 = getSavedIp("mobile.ximalaya.com", mContext);
                        if (!TextUtils.isEmpty(savedIp1)) {
                            String url1 = Tools.getIpUrl(url, host, savedIp1);
                            DomainInfo domainInfo1 = new DomainInfo(savedIp1, url1, host, "mobile.ximalaya.com");
                            domainInfoListBackup.add(domainInfo1);
                        }
                        Logger.i(TAG, "add saved ip1:" + savedIp1);

                        savedIp2 = getSavedIp("mobilehera.ximalaya.com", mContext);
                        if (!TextUtils.isEmpty(savedIp2)) {
                            String url2 = Tools.getIpUrl(url, host, savedIp2);
                            DomainInfo domainInfo2 = new DomainInfo(savedIp2, url2, host, "mobilehera.ximalaya.com");
                            domainInfoListBackup.add(domainInfo2);
                        }
                        Logger.i(TAG, "add saved ip2:" + savedIp2);

                        savedIp3 = getSavedIp("mobwsa.ximalaya.com", mContext);
                        if (!TextUtils.isEmpty(savedIp3)) {
                            String url3 = Tools.getIpUrl(url, host, savedIp3);
                            DomainInfo domainInfo3 = new DomainInfo(savedIp3, url3, host, "mobwsa.ximalaya.com");
                            domainInfoListBackup.add(domainInfo3);
                        }
                        Logger.i(TAG, "add saved ip3:" + savedIp3);

                        if(domainInfoListBackup.size() > 0) {
                            domainInfoList.addAll(0, domainInfoListBackup);
                        }
                    }
                    needUseLastSavedIp = false;
                }
            }
            if (domainInfoList != null && domainInfoList.size() > 0) {
                if (iRequestInterceptor != null) {
                    try {
                        //防止默认域名已经加入到了列表中，导致重复请求
                        boolean hasContainHost = false;
                        for (DomainInfo d : domainInfoList) {
                            if (d.newHost.equals(host)) {
                                hasContainHost = true;
                                break;
                            }
                        }
                        List<DomainInfo> domainInfoListTemp = new ArrayList<>(domainInfoList);
                        if (!hasContainHost) {
                            DomainInfo domainInfoTemp = new DomainInfo(host, url, host);
                            domainInfoListTemp.add(domainInfoTemp);
                        }
                        Response interceptorResponse = iRequestInterceptor.onHandleCallBack(chain, originRequest, domainInfoListTemp, originProtocol, httpEventListener);
                        if (interceptorResponse != null) {
                            return interceptorResponse;
                        }
                    } catch (Exception e) {
                        throwables[0] = e;
                        return null;
                    }
                }

                for (int index = 0; index < domainInfoList.size(); index++) {
                    DomainInfo domainInfo = domainInfoList.get(index);
                    rTryCount++;
                    requestTime = System.currentTimeMillis();
                    String realUrl = "";
                    try {
                        Request.Builder builder = originRequest.newBuilder();

                        String handleHost = domainInfo.newHost;
                        String handleUrl = domainInfo.url;
                        useIpString = domainInfo.httpDnsHost;
                        useHost = domainInfo.newHost;
                        DomainInfo domainInfoDns = replaceIpThroughDnsResolver(chain, domainInfo);
                        if (domainInfoDns != null) {
                            handleHost = domainInfoDns.newHost;
                            handleUrl = domainInfoDns.url;
                        }
                        if (!TextUtils.isEmpty(domainInfo.httpDnsHost) && domainInfo.httpDnsHost.contains(":")) {
                            builder.header("ipType", "1");
                            XmLogger.log("apm", "iptype", "{\"iptype\":1}");
                            Logger.i(TAG, "logipv6");
                        }
                        if (!TextUtils.isEmpty(domainInfo.httpDnsHost)) {
                            builder.header("http_dns_address", "use_http_dns," + domainInfo.httpDnsHost);
                        }
                        realUrl = handleUrl;
                        if (useHttpsForAudioRequest) {
                            handleUrl = handleUrl.replaceFirst("http", "https");
                        }
                        Logger.d(TAG, "HttpDNSInterceptor getDnsCacheUrl handleUrl=" + handleUrl);
                        builder.url(handleUrl);
                        builder.header("Host", handleHost);
                        if (rTryCount > 1) {
                            builder.header("xi-re", "true");
                        }
                        resultResponse = chain.proceed(builder.build());
                        if (resultResponse != null && resultResponse.headers() != null) {
                            GatewaySwitchManager.parseProtocolSwitch(domainInfo.newHost, resultResponse.headers());
                            GatewaySwitchManager.parseIDCSwitch(host, resultResponse.headers());
                        }
                        if (!Tools.isIP(Tools.getHostName(domainInfo.url))) {
                            requestIp = Tools.getUrlIp(domainInfo.url);
                        }
                    } catch (Throwable e) {
                        resultResponse = null;
                        if ("mobile.ximalaya.com".equals(host)) {
                            checkException(e);
                            removeSavedIp(useHost, useIpString);
                        }

                        throwables[0] = e;
                        Throwable cause = e.getCause();
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append(e.toString());
                        if (cause != null) {
                            stringBuilder.append(" ; cause = ");
                            stringBuilder.append(cause.toString());
                        }
                        DNSCache.getInstance().addToDarkRoom(domainInfo.httpDnsHost, System.currentTimeMillis(), host);

                        eventList.add(XDCSEventUtil.createXdcsEvent(domainInfo.url, XDCSEventUtil.RESULT_FAIL,
                                rTryCount + "", domainInfo.newHost, originRequest.header("user-agent"), requestTime + "",
                                stringBuilder.toString(), requestIp, "", originProtocol, originProtocol, e.getClass().getSimpleName()));

                        if (!url.contains("xdcs-collector") && !url.contains("mermaid.ximalaya.com")) {
                            HttpDnsErrorPostManager.getInstance().putErrorDomain(host);
                            if (HttpDnsErrorPostManager.getInstance().needCompress(host)) {
                                HttpDnsErrorPostManager.getInstance().postCompressData(host, e.getClass().getName());
                            }
                        }

                        if (e instanceof SocketTimeoutException) { //if read time out not retry, other exception all retry
                            String message = e.getMessage();
                            boolean isReadTimeout = false;
                            if (!TextUtils.isEmpty(message)) {
                                if (message.toLowerCase().contains("read timed out")) {
                                    isReadTimeout = true;
                                }
                            }
                            if (isReadTimeout) {
                                return null;
                            }
                        }
                    } finally {
                        DnsAspect.getInstance().removeData(chain.call().hashCode());
                    }
                    if (resultResponse != null) {
                        if (resultResponse.isSuccessful()) {
                            if (chain.call().request() != null
                                    && chain.call().request().url() != null
                                    && !TextUtils.isEmpty(chain.call().request().url().toString())) {
                                String oldUrl = chain.call().request().url().toString();
                                if (!TextUtils.isEmpty(realUrl)) {
                                    notifyDnsCacheListeners(oldUrl, realUrl);
                                }
                            }
                            if ("mobile.ximalaya.com".equals(host)) {
                                saveIp(domainInfo.newHost, domainInfo.httpDnsHost);
                                if (!TextUtils.isEmpty(useIpString)) {
                                    if (useIpString.equals(savedIp1) || useIpString.equals(savedIp2) || useIpString.equals(savedIp3)) {
                                        statToXDCSError("cxissuesuccess", "success:" + useIpString);
                                        Logger.i(TAG, "use_saved_ip_success," + useIpString);
                                    }
                                }
                            }
                            // 校验音频请求返回文件类型是否正确
                            List<String> contentTypeList = new ArrayList<>();
                            boolean ifAudioRequestContentTypeLegal = playResponseFileLegalCheck.checkAudioContentTypeLegal(realUrl, resultResponse, contentTypeList);
                            if (!ifAudioRequestContentTypeLegal) {
                                useHttpsForAudioRequest = true;
                                PlayExceptionModel playExceptionModel = new PlayExceptionModel();
                                playExceptionModel.playUrl = realUrl;
                                playExceptionModel.playResourceInvalid = true;
                                playExceptionModel.errorMsg = "content_type_ivalid";
                                if (contentTypeList != null && contentTypeList.size() > 0) {
                                    String contentType = contentTypeList.get(0);
                                    playExceptionModel.contentType = contentType;
                                    if (InvalidContentTypeRetry.CONTENT_TYPE_TEXT_PLAIN.equals(contentType)) {
                                        invalidSourceHost[0] = realUrl;
                                    }
                                }
                                PlayExceptionCollector.postData(playExceptionModel);
                                continue;
                            }

                            //校验播放文件的合法性  如果以上音频Content-Type不合法，则不需要检验文件合法性
                            if (ifAudioRequestContentTypeLegal) {
                                boolean isLegal = playResponseFileLegalCheck.checkAudioFileLegal(realUrl, iPlayResponseFileLegalCheck, resultResponse);
                                if (!isLegal) {
                                    PlayExceptionModel playExceptionModel = new PlayExceptionModel();
                                    playExceptionModel.playResourceInvalid = true;
                                    playExceptionModel.playUrl = realUrl;
                                    playExceptionModel.errorMsg = "audio_file_ivalid";
                                    PlayExceptionCollector.postData(playExceptionModel);
                                    DNSCache.getInstance().addToDarkRoom(domainInfo.httpDnsHost, System.currentTimeMillis(), host);
                                    continue;
                                }
                            }

                            Protocol protocol = resultResponse.protocol();
                            String newProtocol = originProtocol;
                            if (protocol == Protocol.HTTP_2) {
                                newProtocol = "http2";
                            }

                            //如果没有发生过错误，则不需要创建对象
                            if (eventList.size() > 0) {
                                eventList.add(XDCSEventUtil.createXdcsEvent(domainInfo.url, XDCSEventUtil.RESULT_SUCCESS,
                                        rTryCount + "", domainInfo.newHost, originRequest.header("user-agent"), requestTime + "",
                                        "", requestIp, resultResponse.code() + "", newProtocol, originProtocol, ""));
                            }

                            //xdcs mermaid 过滤上报
                            if (!url.contains("xdcs-collector") && !url.contains("mermaid.ximalaya.com")) {
                                if (eventList.size() > EVENT_LIST_NUM) {
                                    if (!HttpDnsErrorPostManager.getInstance().needCompress(host)) {
                                        XDCSEventUtil.sendHttpDnsEvent(eventList);
                                    }
                                }
                            }

                            return resultResponse;
                        } else {
                            //如果是cdn以及返回异常code的request则需要关进小黑屋，后面请求重试下一组。
                            if (!url.contains("xdcs-collector") && !url.contains("mermaid.ximalaya.com")) {
                                if (resultResponse.code() > 400) {
                                    HttpDnsErrorPostManager.getInstance().putErrorDomain(host);
                                    if (HttpDnsErrorPostManager.getInstance().needCompress(host)) {
                                        HttpDnsErrorPostManager.getInstance().postCompressData(host, resultResponse.code() + "");
                                    }
                                }
                            }

                            if (isCdnUrl || resultResponse.code() > 600) {
                                DNSCache.getInstance().addToDarkRoom(domainInfo.httpDnsHost, System.currentTimeMillis(), host);
                            }

                            if (isCdnUrl && resultResponse.code() != 403) { //如果是cdn请求并且不是403则直接重试
                                break;
                            }

                            //如果在用户在代理情况下且返回了5xx，则使用保底域名兜底。 ps线上发生过ip下返回502的特殊情况
                            boolean ifUserHasProxy = false;
                            if (!ConstantsOpenSdk.isDebug && DnsAspect.getInstance().detectIfProxyExist(mContext)) {
                                ifUserHasProxy = true;
                            }
                            if (ifUserHasProxy && resultResponse.code() < 600) {
                                DNSCache.getInstance().addToDarkRoom(domainInfo.httpDnsHost, System.currentTimeMillis(), host);
                                break;
                            }

                            //在大于600的情况下需要重试
                            if (resultResponse.code() < 600) {
                                return resultResponse;
                            }
                        }
                    }
                }
                //end for

            } else {
                isIdcNull = true;
            }
        } else {
            isIdcNull = true;
        }
        if (isIdcNull) {
            throwables[1] = new Exception("idc_is_null");
            return null;
        }
        try {
            rTryCount++;
            Request.Builder builder = originRequest.newBuilder();
            builder.url(url);
            builder.header("Host", host);
            builder.header("http_dns_address", "base_retry");
            resultResponse = chain.proceed(builder.build());
            if (resultResponse != null && resultResponse.headers() != null) {
                GatewaySwitchManager.parseProtocolSwitch(host, resultResponse.headers());
                GatewaySwitchManager.parseIDCSwitch(host, resultResponse.headers());
            }
            requestIp = Tools.getUrlIp(url);
        } catch (Throwable e) {
            resultResponse = null;
            throwables[0] = e;
            Throwable cause = e.getCause();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(e.toString());
            if (cause != null) {
                stringBuilder.append(" ; cause = ");
                stringBuilder.append(cause.toString());
            }

            if (!url.contains("xdcs-collector") && !url.contains("mermaid.ximalaya.com")) {
                HttpDnsErrorPostManager.getInstance().putErrorDomain(host);
                if (HttpDnsErrorPostManager.getInstance().needCompress(host)) {
                    HttpDnsErrorPostManager.getInstance().postCompressData(host, e.getClass().getName());
                }
            }

            eventList.add(XDCSEventUtil.createXdcsEvent(url, XDCSEventUtil.RESULT_FAIL,
                    rTryCount + "", host, originRequest.header("user-agent"), requestTime + "",
                    stringBuilder.toString(), requestIp, "", originProtocol, originProtocol, e.getClass().getSimpleName()));
        }

        if (resultResponse != null) {
            if (resultResponse.isSuccessful()) {
                if (rTryCount > 1) {

                    Protocol protocol = resultResponse.protocol();
                    String newProtocol = originProtocol;
                    if (protocol == Protocol.HTTP_2) {
                        newProtocol = "http2";
                    }
                    eventList.add(XDCSEventUtil.createXdcsEvent(url, XDCSEventUtil.RESULT_SUCCESS,
                            rTryCount + "", host, originRequest.header("user-agent"), requestTime + "",
                            "", requestIp, resultResponse.code() + "", newProtocol, originProtocol, ""));
                    if (!url.contains("xdcs-collector") && !url.contains("mermaid.ximalaya.com")) {
                        if (eventList.size() > EVENT_LIST_NUM) {
                            if (!HttpDnsErrorPostManager.getInstance().needCompress(host)) {
                                XDCSEventUtil.sendHttpDnsEvent(eventList);
                            }
                        }
                    }
                }
                return resultResponse;
            } else {
                return resultResponse;
            }
        } else {
            if (!url.contains("xdcs-collector") && !url.contains("mermaid.ximalaya.com")) {
                if (eventList.size() > EVENT_LIST_NUM) {
                    if (!HttpDnsErrorPostManager.getInstance().needCompress(host)) {
                        XDCSEventUtil.sendHttpDnsEvent(eventList);
                    }
                }
            }
        }
        return resultResponse;
    }

    private boolean isHttps(String url) {
        try {
            Uri uri = Uri.parse(url);
            return "https".equals(uri.getScheme());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private DomainInfo replaceIpThroughDnsResolver(Chain chain, DomainInfo domainInfo) {

        if (!GatewaySwitchManager.USE_TICKET) {
            SslCacheManager.clearSslCache();
        }

        if (GatewaySwitchManager.USE_TICKET && !DnsAspect.getInstance().detectIfProxyExist(mContext)) {
            if (domainInfo == null) {
                return null;
            }
            if (TextUtils.isEmpty(domainInfo.url)) {
                return null;
            }
            String currentHost = Tools.getHostName(domainInfo.url);
            if (TextUtils.isEmpty(currentHost)) {
                return null;
            }
            if (Tools.isIP(currentHost) || currentHost.contains(":")) {
                String host = domainInfo.newHost;
                String originHost = domainInfo.originHost;
                String httpDnsHost = domainInfo.httpDnsHost;
                String url = domainInfo.url;

                if (Tools.isIPv6(httpDnsHost)) {
                    String ip = "[" + httpDnsHost + "]";
                    url = Tools.getIpUrl(url, ip, host);
                } else {
                    url = Tools.getIpUrl(url, httpDnsHost, host);
                }
                DnsAspect.getInstance().putData(chain.call().hashCode(), host, httpDnsHost);
                return new DomainInfo(httpDnsHost, url, originHost, host);
            }
        }
        return null;
    }

    public synchronized static String getTraceId() {
        sIncrementId = sIncrementId + 1;
        return TRACE_UUID + sIncrementId;
    }

    public static void main(String args[]) {
        UUID uuid = UUID.randomUUID();
        System.out.println(uuid.toString());
    }

    private ConcurrentHashMap<String, String> successIpMap = new ConcurrentHashMap<>();

    //mobwsa.ximalaya.com
    //mobilehera.ximalaya.com
    //mobile.ximalaya.com
    public synchronized String getSavedIp(String host, Context context) {
        String ip = "";
        try {
            if (!TextUtils.isEmpty(host)) {
                ip = successIpMap.get(host);
                if (TextUtils.isEmpty(ip)) {
                    if (context != null) {
                        ip = MmkvCommonUtil.getInstance(context).getString(host, "");
                    }
                }
            }
        } catch (Throwable t) {
            t.printStackTrace();
        }
        Logger.i(TAG, "getSavedIp,host:" + host + ",ip:" + ip);
        return ip;
    }

    public synchronized void removeSavedIp(String host, String ip) {
        try {
            if (!TextUtils.isEmpty(ip) && !TextUtils.isEmpty(host) && mContext != null) {
                Logger.i(TAG, "removeSavedIp,host:" + host + ",ip:" + ip);
                successIpMap.remove(host);
                MmkvCommonUtil.getInstance(mContext).removeKey(host);
            }
        } catch (Throwable t) {
            t.printStackTrace();
        }
    }

    public synchronized void saveIp(String host, String ip) {
        try {
            if (!TextUtils.isEmpty(ip) && !TextUtils.isEmpty(host) && mContext != null) {
                if (Tools.isIP(ip) && !(ip.contains(":"))) {
                    Logger.i(TAG, "saveIp,host:" + host + ",ip:" + ip);
                    if ("mobwsa.ximalaya.com".equals(host) || "mobilehera.ximalaya.com".equals(host) || "mobile.ximalaya.com".equals(host)) {
                        if (successIpMap.containsKey(host)) {
                            String savedIp = successIpMap.get(host);
                            if (TextUtils.isEmpty(savedIp)) {
                                successIpMap.put(host, ip);
                                MmkvCommonUtil.getInstance(mContext).saveString(host, ip);
                            } else {
                                if (!(savedIp.equals(ip))) {
                                    successIpMap.put(host, ip);
                                    MmkvCommonUtil.getInstance(mContext).saveString(host, ip);
                                }
                            }
                        } else {
                            successIpMap.put(host, ip);
                            MmkvCommonUtil.getInstance(mContext).saveString(host, ip);
                        }
                    }
                }

            }
        } catch (Throwable t) {
            t.printStackTrace();
        }
    }
}
