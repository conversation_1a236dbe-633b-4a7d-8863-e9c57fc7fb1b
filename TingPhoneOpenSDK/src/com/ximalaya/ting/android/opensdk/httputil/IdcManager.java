package com.ximalaya.ting.android.opensdk.httputil;


import android.os.SystemClock;

import com.sina.util.dnscache.DNSCache;

/**
 * Created by jack.qin on 2019-10-22.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
class IdcManager {
    private static long sLastNoIdcTime = 0;
    static synchronized void idcSwitch(String idcString) {
        DNSCache.getInstance().setIdcString(idcString);
    }

    static synchronized void noIdcStringTime() {
        if (sLastNoIdcTime != 0) {
            long noIdcStringIntervalTime = SystemClock.elapsedRealtime() - sLastNoIdcTime;
            boolean isReachInterval = DNSCache.getInstance().ifReachNoIdcStringIntervalTime(noIdcStringIntervalTime);
            if(isReachInterval) {
                sLastNoIdcTime = SystemClock.elapsedRealtime();
            }
        } else {
            sLastNoIdcTime = SystemClock.elapsedRealtime();
        }

    }

    static synchronized void getIdcStringTime() {
        sLastNoIdcTime = 0;
    }
}
