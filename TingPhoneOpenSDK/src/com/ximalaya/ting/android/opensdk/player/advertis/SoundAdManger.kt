package com.ximalaya.ting.android.opensdk.player.advertis

import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList
import com.ximalaya.ting.android.opensdk.model.track.Track

abstract class SoundAdManger {

    companion object{
        const val TYPE_AI_INSERT = 0
        const val TYPE_ANCHOR_INSERT = 1
        const val TYPE_HEAD_INSERT = 2
        const val TYPE_TAIL_INSERT = 3
    }

    var lastTask: XmAdsManager.TaskWrapper? = null
    var dataHandle: IXmAdsDataHandle? = null
    var insertAdListener: OnInsertSoundAdListener? = null

    // 是否已经播放过中插广告，进入冷却期
    open var isInCoolingTime = false

    abstract fun startPlay(track: Track?)

    open fun clearData(){
        lastTask = null
        dataHandle = null
        insertAdListener = null
    }

    fun setOnInsertSoundAdListener(listener: OnInsertSoundAdListener?) {
        insertAdListener = listener
    }

    fun createAdvertisList(ad: Advertis, data: List<Advertis>): AdvertisList {
        val advertisList = AdvertisList()
        advertisList.advertisList = data
        advertisList.clientIp = ad.clientIp
        advertisList.responseId = ad.responseId
        return advertisList
    }

    fun getAdvertisFromList(list : AdvertisList?): Advertis?{
        list?.advertisList?.forEach {
            return it
        }
        return null
    }

    abstract fun getSoundAdType(): Int

    interface OnInsertSoundAdListener {
        fun onSoundAdLoaded(soundAdType : Int, tw: XmAdsManager.TaskWrapper?, ad: Advertis?)
    }
}