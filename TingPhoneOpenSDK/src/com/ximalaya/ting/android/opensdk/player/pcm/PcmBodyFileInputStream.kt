package com.ximalaya.ting.android.opensdk.player.pcm

import java.io.File
import java.io.FileInputStream
import java.io.InputStream

/**
 * <AUTHOR>
 * @date 2025/4/8 15:22
 */
class PcmBodyFileInputStream() : PcmBodyInputStream {
    private var mHasInit = false
    private var mFile: File? = null
    private var mInputStream: InputStream? = null
    fun init() {
        if (mHasInit) {
            return
        }
        mHasInit = true
        mFile = File("/sdcard/learn/1.pcm")
        mInputStream = FileInputStream(mFile)
    }

    override fun read(target: ByteArray, offset: Int, length: Int): Int {
        init()
        return mInputStream?.read(target, offset, length) ?: -1
    }

    override fun close() {
        mInputStream?.close()
    }
}