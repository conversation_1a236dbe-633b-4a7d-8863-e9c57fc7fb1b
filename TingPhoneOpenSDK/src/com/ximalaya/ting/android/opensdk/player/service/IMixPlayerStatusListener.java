package com.ximalaya.ting.android.opensdk.player.service;

/**
 * Created by hebin on 2019/3/26.
 */
public interface IMixPlayerStatusListener {

    void onMixStart();

    void onMixPause();

    void onMixStop();

    void onMixComplete();

    void onMixProgressUpdate(int percent);

    void onMixError(String url, int code, String msg);

    void onMixStatusChanged(double key, boolean isPlaying, String state, long curPosition);

    void onMixSoundComplete(double key);

    default void onMixTrackCleared() {

    }

    default void notifyMixProgressChanged(double key, int curPos, int duration) {
    }
}
