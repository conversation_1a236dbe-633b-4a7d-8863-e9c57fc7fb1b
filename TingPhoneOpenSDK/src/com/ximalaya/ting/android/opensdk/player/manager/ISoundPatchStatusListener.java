package com.ximalaya.ting.android.opensdk.player.manager;

import com.ximalaya.ting.android.opensdk.model.ad.SoundPatchInfo;

/**
 * Created by le.xin on 2020-01-19.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public interface ISoundPatchStatusListener {

    void onSoundPatchStartPlaySoundPatch(SoundPatchInfo soundPatchInfo);

    void onSoundPatchCompletePlaySoundPatch(SoundPatchInfo soundPatchInfo);

    void onSoundPatchError(SoundPatchInfo soundPatchInfo , int what, int extra);

}
