package com.ximalaya.ting.android.opensdk.player.advertis;

import android.annotation.TargetApi;
import android.content.Context;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.media.MediaPlayer.OnCompletionListener;
import android.media.MediaPlayer.OnErrorListener;
import android.media.audiofx.LoudnessEnhancer;
import android.net.Uri;
import android.os.Build;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.FileDescriptor;
/**
 * <AUTHOR>
 */
public class MiniPlayer implements IAdPlayer {
	public static final int STATE_MUTE = -2;	// 静音
	public static final int STATE_ERR = -1;
	public static final int STATE_IDLE = 0;
	public static final int STATE_PREPARED = 1;
	public static final int STATE_STARTED = 2;
	public static final int STATE_PAUSED = 3;
	public static final int STATE_STOPPED = 4;
	public static final int STATE_COMPLETED = 5;
	public static final int STATE_CLOSED = 6; // 提前关闭

	private static final String TAG = "MiniPlayer";

	private MediaPlayer mPlayer;
	private LoudnessEnhancer mLoudnessEnhancer = null;
	private int mGain = 0;

	@SuppressWarnings("unused")
	private int mStreamType = AudioManager.STREAM_MUSIC;

	private float mLeftVol = 1f;
	private float mRightVol = 1f;
	private boolean mLoop = false;

	private OnCompletionListener mCompletionListener;

	private PlayerStatusListener mPlayerStatusListener;

	private int mStatus = STATE_ERR;

	private Advertis mAdvertis;
	private AudioManager mAudioManager;

	public MiniPlayer() {
		resetPlayer();
	}

	public MediaPlayer getMediaPlayer(){
		return mPlayer;
	}

	public void setAudioStreamType(int type) {
		mStreamType = type;
	}

	@Override
	public void setOnCompletionListener(OnCompletionListener l) {
		mCompletionListener = l;
	}

	@TargetApi(Build.VERSION_CODES.GINGERBREAD)
	public int getSessionId() {
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.GINGERBREAD) {
			return mPlayer.getAudioSessionId();
		}
		return 0;
	}

	public void setLooping(boolean loop) {
		mLoop = loop;
		if (mStatus != STATE_ERR) {
			mPlayer.setLooping(loop);
		}
	}

	public void setCurDuration(int duration){
		mPlayer.seekTo(duration);
	}

    @Override
	public int getCurrPos() {
		switch (mStatus) {
		case STATE_IDLE:
		case STATE_PREPARED:
		case STATE_STARTED:
		case STATE_PAUSED:
		case STATE_STOPPED:
		case STATE_COMPLETED:
			if(mPlayer != null) {
				return mPlayer.getCurrentPosition();
			}

		default:
			break;
		}
		return 0;
	}

    @Override
	// 是否可以记录当前的位置
	public boolean canRecordPos() {
		if(mStatus == STATE_STARTED || mStatus == STATE_PAUSED || mStatus == STATE_COMPLETED) {
			return true;
		}
		return false;
	}

	public void setVolume(float left, float right) {
		mLeftVol = left;
		mRightVol = right;
		if (mStatus != STATE_ERR) {
			mPlayer.setVolume(mLeftVol, mRightVol);
		}
	}

    @Override
	public int getDuration() {
		switch (mStatus) {
		case STATE_STARTED:
		case STATE_PAUSED:
		case STATE_STOPPED:
		case STATE_COMPLETED:
		case STATE_PREPARED:
			return mPlayer.getDuration();
		case STATE_ERR:
		case STATE_IDLE:
		default:
			return 0;
		}
	}

	public void resetPlayer() {
		notifyCompleted = false;
		try {
			if (mPlayer == null) {
				mPlayer = new MediaPlayer();
				mStatus = STATE_IDLE;
				mPlayer.setOnErrorListener(new OnErrorListener() {

					@Override
					public boolean onError(MediaPlayer mp, int what, int extra) {
//						boolean errorReturn;
//						if(mStatus == STATE_PAUSED) {
//							errorReturn = true;
//						} else {
//							errorReturn = false;
//						}

						if(mStatus == STATE_STOPPED) {
							return true;
						}

						mStatus = STATE_ERR;
						if (mPlayerStatusListener != null) {
							mPlayerStatusListener.onError(null, what, extra);
						}

						notifyCompleted();

						//注意： Mediaplayer这个onerror回调一定要返回true，告诉系统你处理掉了，避免各种奇怪的问题，系统handleMediaPlayerError不可靠；
						// oppo手机，如果你没处理错误，他会自动帮你处理handleMediaPlayerError，然后走prepare；
						// 返回true后，发生错误，系统不会再回调onComplete
						return true;
					}
				});
				mPlayer.setOnCompletionListener(new OnCompletionListener() {
					@Override
					public void onCompletion(MediaPlayer mp) {
						mStatus = STATE_COMPLETED;
						notifyCompleted();
						if (mPlayerStatusListener != null) {
							mPlayerStatusListener.onComplete();
						}
					}
				});
				int audioSessionId = mPlayer.getAudioSessionId();
				if (audioSessionId > 0) {
					onAudioSessionId(audioSessionId);
				}
			}
			if (mStatus == STATE_STARTED) {
				mPlayer.stop();
				mStatus = STATE_STOPPED;
				if (mPlayerStatusListener != null) {
					mPlayerStatusListener.onStop();
				}
			}
			mPlayer.reset();
			// mPlayer.setAudioStreamType(mStreamType);
			mPlayer.setLooping(mLoop);
			mPlayer.setVolume(mLeftVol, mRightVol);
			mGain = 0;
			mStatus = STATE_IDLE;
		} catch (Exception e) {
			e.printStackTrace();
			mStatus = STATE_ERR;
			if (mPlayerStatusListener != null) {
				mPlayerStatusListener.onError(e, 0, 0);
			}
		}
	}

	private void onAudioSessionId(int audioSessionId) {
		if (!checkCanUseVolumeGain()) {
			return;
		}
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
			try {
				mLoudnessEnhancer = new LoudnessEnhancer(audioSessionId);
			} catch (Exception e) {
				mLoudnessEnhancer = null;
				e.printStackTrace();
			}
		}
	}

    public void volumeGain(double volumeGain) {
        if (!checkCanUseVolumeGain()) {
            return;
        }
        mGain = (int) (volumeGain * 100);
        if (mLoudnessEnhancer != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            try {
                int volumeEnhance = mGain;
                Logger.d(TAG, "MiniPlayer volumeGain: " + volumeEnhance);
                if (volumeEnhance != 0) {
                    mLoudnessEnhancer.setTargetGain(volumeEnhance);
                    mLoudnessEnhancer.setEnabled(true);//部分手机可能发生异常
                } else {
                    mLoudnessEnhancer.setEnabled(false);//部分手机可能发生异常
                }
            } catch (Exception e) {
                e.printStackTrace();
                mLoudnessEnhancer = null;
            }
        }
    }

	private boolean checkCanUseVolumeGain() {
		boolean canUseForMiniPlayer = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_USE_VOLUME_GAIN_FOR_MINI_PLAYER, true);
		Logger.d(TAG, "MiniPlayer checkCanUseVolumeGain: " + canUseForMiniPlayer);
		return canUseForMiniPlayer;
	}

	@Deprecated
	public void init(Context ctx, Uri uri, int offset, int length) {
		try {
			resetPlayer();
			mPlayer.setDataSource(ctx, uri);
			mPlayer.prepare();
			mStatus = STATE_PREPARED;
		} catch (Exception e) {
			e.printStackTrace();
			mStatus = STATE_ERR;
			notifyCompleted();
		}
	}

	@Deprecated
	public void init(Context ctx, Uri uri) {
		try {
			resetPlayer();
			mPlayer.setDataSource(ctx, uri);
			mPlayer.prepare();
			mStatus = STATE_PREPARED;
		} catch (Exception e) {
			e.printStackTrace();
			mStatus = STATE_ERR;
			notifyCompleted();
		}
	}

	public void restart() {
		try {
			switch (mStatus) {
			case STATE_STARTED:
			case STATE_PAUSED:
			case STATE_STOPPED:
				mPlayer.stop();
				mPlayer.prepare();
				mPlayer.start();
				mStatus = STATE_STARTED;
				if (mPlayerStatusListener != null) {
					mPlayerStatusListener.onStart();
				}
				break;
			case STATE_COMPLETED:
			case STATE_PREPARED:
				mPlayer.start();
				mStatus = STATE_STARTED;
				if (mPlayerStatusListener != null) {
					mPlayerStatusListener.onStart();
				}
				break;
			default:
				break;
			}
		} catch (Exception e) {
			e.printStackTrace();
			mStatus = STATE_ERR;
			if (mPlayerStatusListener != null) {
				mPlayerStatusListener.onError(e, 0, 0);
			}
		}
	}

    @Override
	public int getStatus() {
		return mStatus;
	}

	public boolean isStop() {
		return mStatus == STATE_STOPPED;
	}

    @Override
	public boolean isPlaying() {
		return mStatus == STATE_STARTED;
	}

	@Override
	public boolean isPaused() {
		return mStatus == STATE_PAUSED;
	}

	/**
	 *
	 * @param fd
	 * @param seek
	 *            offset in miliisecods
	 */

//	public void init(FileDescriptor fd, int seek){
//		init(fd,seek,null);
//	}


	public void init(FileDescriptor fd, int seek, final boolean[] isFinish) {
		Logger.e(TAG, "init seek " + seek);
		try {
			resetPlayer();
			mPlayer.setDataSource(fd);
			mPlayer.prepare();
			mPlayer.setOnSeekCompleteListener(new MediaPlayer.OnSeekCompleteListener() {
				@Override
				public void onSeekComplete(MediaPlayer mp) {
					if(isFinish!=null && isFinish.length>0){
						isFinish[0] = true;
					}
				}
			});
			if (seek > 0) {
				mPlayer.seekTo(seek);
			}
			mStatus = STATE_PREPARED;
		} catch (Exception e) {
			e.printStackTrace();
			notifyCompleted();
			mStatus = STATE_ERR;
			if(isFinish != null && isFinish.length>0)isFinish[0] = true;
		}
	}

	public void seekTo(int msec){
		if (mPlayer != null){
			mPlayer.seekTo(msec);
		}
	}

	public void init(FileDescriptor fd, long offset, long length) {
		Logger.e(TAG, "init offset " + offset + ", length " + length);
		try {
			resetPlayer();
			mPlayer.setDataSource(fd, offset, length);
			mPlayer.prepare();
			mStatus = STATE_PREPARED;
		} catch (Exception e) {
			notifyCompleted();
			e.printStackTrace();
			mStatus = STATE_ERR;
		}
	}

    @Override
	public void init(String path) throws Exception {
		// try
		// {
		resetPlayer();
		mPlayer.setDataSource(path);
		mPlayer.prepare();
		mStatus = STATE_PREPARED;

		// }
		// catch (Exception e)
		// {
		// e.printStackTrace();
		// mStatus = STATE_ERR;
		// if (mPlayerStatusListener != null)
		// {
		// mPlayerStatusListener.onError(e, 0, 0);
		// }
		// }
	}

    @Override
	public void init(String path ,Advertis advertis) throws Exception {
		resetPlayer();
		mPlayer.setDataSource(path);
		mPlayer.prepare();
		mStatus = STATE_PREPARED;
		mAdvertis = advertis;
		if(XmPlayerService.getPlayerSrvice() != null) {
			try {
				mAudioManager = (AudioManager) XmPlayerService.getPlayerSrvice().getSystemService(Context.AUDIO_SERVICE);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	private CountDownTimer mCountDownTimer;

	public void startPlay() {
		startPlay(true);
	}

    @Override
	public void startPlay(final boolean hasVolumeGradient) {
		if(Looper.getMainLooper() == Looper.myLooper()) {
			handleStart(hasVolumeGradient);
		} else {
			new Handler(Looper.getMainLooper()).post(new Runnable() {
				@Override
				public void run() {
					handleStart(hasVolumeGradient);
				}
			});
		}
	}

	private void handleStart(final boolean hasVolumeGradient) {
		try {

			if(mAdvertis != null && mAdvertis.getVolume() != 0 && hasVolumeGradient) {
		    	float willVolume = (float) mAdvertis.getVolume() / 100;
		    	if(mCountDownTimer != null) {
		    		mCountDownTimer.cancel();
				}

		    	Logger.log("MiniPlayer : willVolume " + willVolume);
		    	int totalTime = 3000;
		    	int timeInterval = 50;
		    	float startVolume = 0.2f;

				float v = (willVolume - startVolume) / totalTime;

				setVolume(startVolume, startVolume);

				mCountDownTimer = new CountDownTimer(totalTime ,timeInterval) {
					@Override
					public void onTick(long millisUntilFinished) {
						float left = v * (totalTime - millisUntilFinished) + startVolume;
						if (left > 1) {
							left = 1;
						}
						try {
							setVolume(left ,left);
						} catch (Exception e) {
							e.printStackTrace();
						}
					}

					@Override
					public void onFinish() {
						try {
							setVolume(willVolume , willVolume);
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				};
				mCountDownTimer.start();
			} else {
				setVolume(1, 1);
			}

			Logger.logToSd("playAd 1:");
			if (mStatus == STATE_PREPARED || mStatus == STATE_PAUSED
					|| mStatus == STATE_COMPLETED) {
				Logger.logToSd("playAd 2:");
				mPlayer.start();
				mStatus = STATE_STARTED;
				if (mPlayerStatusListener != null) {
					mPlayerStatusListener.onStart();
				}
			} else if (mStatus == STATE_STOPPED) {
				Logger.logToSd("playAd 3:");
				mPlayer.prepare();
				mPlayer.start();
				mStatus = STATE_STARTED;
				if (mPlayerStatusListener != null) {
					mPlayerStatusListener.onStart();
				}
			}
		} catch (Exception e) {
			Logger.logToSd("playAd 4:");
			e.printStackTrace();
			mStatus = STATE_ERR;
			if (mPlayerStatusListener != null) {
				mPlayerStatusListener.onError(e, 0, 0);
			}
		}
	}

    @Override
	public void pausePlay() {
		Logger.logToSd("Ad pausePlay 0:");
		if(Looper.getMainLooper() == Looper.myLooper()) {
			handlePause();
		} else {
			new Handler(Looper.getMainLooper()).post(new Runnable() {
				@Override
				public void run() {
					handlePause();
				}
			});
		}
	}

	private void handlePause() {
		try {
			if (mStatus == STATE_STARTED) {
				mPlayer.pause();
				mStatus = STATE_PAUSED;
				if (mPlayerStatusListener != null) {
					mPlayerStatusListener.onPause();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			mStatus = STATE_ERR;
			if (mPlayerStatusListener != null) {
				mPlayerStatusListener.onError(e, 0, 0);
			}
		}
	}

    @Override
	public void stopPlay() {
		Logger.logToSd("AD stopPlay 0:");
		try {
			mPlayer.reset();
			if (mStatus == STATE_STARTED) {
				mPlayer.stop();
				mStatus = STATE_STOPPED;
				if (mPlayerStatusListener != null) {
					mPlayerStatusListener.onStop();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			mStatus = STATE_ERR;
			if (mPlayerStatusListener != null) {
				mPlayerStatusListener.onError(e, 0, 0);
			}
		}

		mAdvertis = null;
	}

	public void release() {
		Logger.logToSd("AD release 0:");
		try {
			if (mPlayer != null) {
				if (mStatus == STATE_STARTED) {
					mPlayer.stop();
					if (mPlayerStatusListener != null) {
						mPlayerStatusListener.onStop();
					}
				}
				mPlayer.release();
			}
		} catch (Exception e) {
			e.printStackTrace();
			if (mPlayerStatusListener != null) {
				mPlayerStatusListener.onError(e, 0, 0);
			}
		}
		if (mLoudnessEnhancer != null) {
			mLoudnessEnhancer.release();
			mLoudnessEnhancer = null;
		}
		mAdvertis = null;
		mPlayer = null;
	}

	private boolean notifyCompleted = false;
	// 流程结束了
	private void notifyCompleted() {
		if(notifyCompleted) {
			return;
		}
		notifyCompleted = true;
		if (mCompletionListener != null) {
			mCompletionListener.onCompletion(mPlayer);
		}
	}

    @Override
	public Advertis getAdvertis() {
		return mAdvertis;
	}

	public void setAdvertis(Advertis advertis) {
		mAdvertis = advertis;
	}

    @Override
	public void setPlayerStatueListener(PlayerStatusListener l) {
		mPlayerStatusListener = l;
	}

}
