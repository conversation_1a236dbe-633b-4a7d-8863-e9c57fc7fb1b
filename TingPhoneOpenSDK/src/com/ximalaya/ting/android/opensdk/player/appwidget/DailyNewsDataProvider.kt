package com.ximalaya.ting.android.opensdk.player.appwidget

import android.content.Context
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManagerForPlayer
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService
import com.ximalaya.ting.android.routeservice.RouterServiceManager
import com.ximalaya.ting.android.routeservice.base.IMyDataCallBack
import com.ximalaya.ting.android.routeservice.service.ICommonBusiService
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForPlay
import com.ximalaya.ting.android.xmutil.Logger

/**
 * Author: <PERSON>
 * Email: <EMAIL>
 * Date: 2023/9/11
 * Description：
 */
object DailyNewsDataProvider {
    var mCommonTrackList: CommonTrackList<Track>? = null
    fun requestDataFromServer(refreshForce: Boolean, playingTrackId: Long, callBack: IMyDataCallBack<List<Track>>?) {
        Logger.e("sjc", "playingTrackId = $playingTrackId   refreshForce = $refreshForce")
        var refreshForceFinal = refreshForce
        val service = XmPlayerService.getPlayerSrvice()
        if (refreshForceFinal && service != null && service.currPlayModel is Track) {
            val track = service.currPlayModel as Track
            if (track.channelId == 80L && track.channelGroupId == 1L && service.isPlaying) {
                refreshForceFinal = false
            }
        }

        if (!refreshForceFinal && service != null && service.playListControl != null) {
            val list = mutableListOf<Track>()
            var currentIndex = -1L
            service.playListControl.playList?.forEach {
                if (it.dataId == playingTrackId && it.channelId == 80L && it.channelGroupId == 1L) {
                    // 在播热点  直接取播放器数据
                    list.add(it)
                    currentIndex++
                } else if (currentIndex == 0L) {
                    list.add(it)
                }
            }
            if (list.size > 0) {
                Logger.e("sjc", "get player cache")
                callBack?.onSuccess(list)
                return
            }
        }
        if (!refreshForceFinal && mCommonTrackList != null && mCommonTrackList!!.tracks != null) {
            var firstIndex = -1
            mCommonTrackList!!.tracks.forEachIndexed { index, track ->
                if (playingTrackId > 0 && playingTrackId == track.dataId) {
                    firstIndex = index
                }
            }
            if (firstIndex >= 0) {
                val list = mutableListOf<Track>()
                list.add(mCommonTrackList!!.tracks[firstIndex])
                if (firstIndex + 1 < mCommonTrackList!!.tracks.size) {
                    val nextTrack = mCommonTrackList!!.tracks[firstIndex + 1]
                    list.add(nextTrack)
                }
                Logger.e("sjc", "get local cache 1")
                callBack?.onSuccess(list)
                return
            } else if (playingTrackId == 0L && mCommonTrackList != null && mCommonTrackList!!.tracks != null && mCommonTrackList!!.tracks.size >= 2) {
                val list = mutableListOf<Track>()
                list.add(mCommonTrackList!!.tracks[0])
                list.add(mCommonTrackList!!.tracks[1])
                Logger.e("sjc", "get local cache 2")
                callBack?.onSuccess(list)
                return
            }
        }
        val iCommonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService::class.java)
        iCommonBusiService?.getCustomDailyNewsData(playingTrackId, 1, 80, null, object : IMyDataCallBack<CommonTrackList<Track>> {
            override fun onSuccess(data: CommonTrackList<Track>?) {
                if (data != null && data.tracks != null && data.tracks.size > 0) {
                    var firstIndex = -1
                    data.tracks.forEachIndexed { index, track ->
                        if (track.dataId == playingTrackId) {
                            firstIndex = index
                        }
                    }
                    if (firstIndex >= 0 && data.tracks[0].dataId != playingTrackId) {
                        // 置顶
                        val topTrack = data.tracks.removeAt(firstIndex)
                        data.tracks.add(0, topTrack)
                    }
                    mCommonTrackList = data
                    Logger.e("sjc", "get data from net")
                    callBack?.onSuccess(data.tracks)
                }
            }

            override fun onError(code: Int, message: String?) {
            }
        })
    }

    fun handleWidgetPlayClick(context: Context, trackId: Long) {
        val serviceInit = XmPlayerService.getPlayerSrvice()
        if (serviceInit == null) {
            val historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForPlay::class.java)
            historyManager?.addPlayListLoadListener {
                handleWidgetPlayClick(context, trackId)
            }
            XmPlayerManagerForPlayer.getInstance(context).init()
            return
        }
        val service = XmPlayerService.getPlayerSrvice() ?: return
        if (service.currPlayModel is Track) {
            val track = service.currPlayModel as Track
            if (trackId == track.dataId && track.channelId == 80L && track.channelGroupId == 1L) {
                // 在播热点  直接取播放器数据
                if (service.isPlaying) {
                    Logger.e("sjc", "在播热点  直接取播放器数据1")
                    service.pausePlay(false, PauseReason.Common.WIDGET_PAUSE_CLICK)
                } else {
                    Logger.e("sjc", "在播热点  直接取播放器数据2")
                    service.startPlay(true)
                }
                return
            }
            val nextTrack = service.getTrack(service.currIndex + 1)
            if (nextTrack != null && nextTrack.dataId == trackId && nextTrack.channelId == 80L && nextTrack.channelGroupId == 1L) {
                // 在播热点  直接取播放器数据
                service.playNext(true)
                Logger.e("sjc", "get playNext")
                return
            }
        }
        requestDataFromServer(false, trackId, object : IMyDataCallBack<List<Track>> {
            override fun onSuccess(data: List<Track>?) {
                if (mCommonTrackList != null && mCommonTrackList!!.tracks != null && mCommonTrackList!!.tracks.size > 0) {
                    mCommonTrackList!!.tracks.forEachIndexed { index, track ->
                        if (trackId == track.dataId) {
                            service.setPlayList(mCommonTrackList!!.params, mCommonTrackList!!.tracks)
                            service.play(index)
                            return
                        }
                    }
                }
            }

            override fun onError(code: Int, message: String?) {
            }

        })
    }
}