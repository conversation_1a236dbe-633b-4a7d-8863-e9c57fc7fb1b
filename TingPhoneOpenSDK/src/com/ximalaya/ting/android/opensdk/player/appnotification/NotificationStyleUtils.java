package com.ximalaya.ting.android.opensdk.player.appnotification;

import android.content.Context;
import android.os.Build;
import android.os.Build.VERSION;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationManagerCompat;

import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.BuildProperties;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.ManufacturUtil;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Locale;

public class NotificationStyleUtils {
    private static final String KEY_RO_BUILD_VERSION_OPPOROM = "ro.build.version.opporom";

    public static final String NOTIFICATION_STYLE = "notification_style";
    public static final int NOTIFICATION_STYLE_AUTO = 0;
    public static final int NOTIFICATION_STYLE_WHITE = 1;
    public static final int NOTIFICATION_STYLE_SYS = 2;
    private static final String TAG = "NotificationStyleUtils";
    private static final String APK_CHANNEL_FOR_OPPO = "and-d8";
    private static final String APK_CHANNEL_FOR_VIVO = "and-d12";
    private static final int NEW_VERSION_API_LEVEL = 27;
    public static int currStyle = getDefaultStyle(null);
    private static Boolean isHuaweiDevice;
    private static Boolean isOppoOs;
    private static Boolean isVivoDevice;
    private static Boolean isXiaomiDevice;
    private static Boolean isMotorolaDevice;
    private static Boolean isNioDevice;
    private static String emuiVersion;
    private static Boolean isOppoFlamingoDevice;

    public static void changeStyle(int style, Context context) {
        if (isHuaweiDevice() || isMotorolaDevice() || isNioDevice() || isOppoFlamingoDevice() || isHyperOs()) {
            currStyle = NOTIFICATION_STYLE_SYS;
            return;
        }
        boolean closeNoPermissionLocal = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_CLOSE_NO_PERMISSION_PLAYER_LOCAL, false);
        if (context != null && !closeNoPermissionLocal && !isSystemNotificationEnable(context)) {
            currStyle = NOTIFICATION_STYLE_SYS;
            return;
        }
        if (currStyle == style) {
            return;
        }

        currStyle = style;
    }

    public static boolean isSystemStyle() {
        return currStyle == NOTIFICATION_STYLE_SYS;
    }

    public static boolean isWhiteStyle() {
        return currStyle == NOTIFICATION_STYLE_WHITE;
    }

    public static int getDefaultStyle(Context context) {
        return isDefaultWithSystemStyle(context) ? NOTIFICATION_STYLE_SYS : NOTIFICATION_STYLE_AUTO;
    }

    private static boolean isDefaultWithSystemStyle(Context context) {
        if (isHuaweiDevice() || isMotorolaDevice() || isNioDevice() || isOppoFlamingoDevice() || isHyperOs()) {
            return true;
        }
        boolean closeNoPermissionLocal = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_CLOSE_NO_PERMISSION_PLAYER_LOCAL, false);
        if (context != null && !closeNoPermissionLocal && !isSystemNotificationEnable(context)) {
            return true;
        }
        boolean isUseCustom = MMKVUtil.getInstance()
                .getBoolean(PreferenceConstantsInOpenSdk.KEY_IS_USE_CUSTOM_NOTIFICATION, true);
        Logger.e("cf_test", "isUseCustom:____"+ isUseCustom);
        if (isUseCustom) {
            return false;
        }
        return isNeedUseMediaStyleForHuawei()
            || ((isNeedUseMediaStyleForOppo() || isNeedUseMediaStyleForMiui()) && VERSION.SDK_INT >= 21)
            || isNeedUseMediaStyleForVivo();
    }

    private static Boolean sHasNotificationPermission;
    public static boolean isSystemNotificationEnable(Context context) {
        if (sHasNotificationPermission != null) {
            return sHasNotificationPermission;
        }
        if (context == null) {
            return true;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) { //19以上可检测
            sHasNotificationPermission =  NotificationManagerCompat.from(context)
                    .areNotificationsEnabled();
            return sHasNotificationPermission;
        }
        sHasNotificationPermission = true;
        return true;
    }

    private static boolean isNeedUseMediaStyleForHuawei() {
        return isHuaweiDevice() && (isEmui111OrOver() || isHuaweiNewSystem() || VERSION.SDK_INT >= 28);
    }

    private static boolean isNeedUseMediaStyleForVivo() {
        return APK_CHANNEL_FOR_VIVO.equals(XmNotificationCreater.mApkChannel) && isVivoDevice() && VERSION.SDK_INT >= 28;
    }

    public static boolean isVivoDevice() {
        if (isVivoDevice != null) {
            return isVivoDevice;
        }
        String manufacturer = Build.MANUFACTURER;
        isVivoDevice =  !TextUtils.isEmpty(manufacturer)
            && ("vivo".equalsIgnoreCase(manufacturer) || manufacturer.toLowerCase(Locale.CHINA).contains("vivo"));
        return isVivoDevice;
    }

    public static boolean isXiaomiDevice() {
        if (isXiaomiDevice != null) {
            return isXiaomiDevice;
        }
        String manufacturer = Build.MANUFACTURER;
        isXiaomiDevice =  !TextUtils.isEmpty(manufacturer)
                && ("xiaomi".equalsIgnoreCase(manufacturer) || manufacturer.toLowerCase(Locale.CHINA).contains("xiaomi"));
        return isXiaomiDevice;
    }

    private static boolean isNeedUseMediaStyleForOppo() {
        return APK_CHANNEL_FOR_OPPO.equals(XmNotificationCreater.mApkChannel) && isOppoBrand();
    }

    public static boolean isOppoBrand() {
        String brand = android.os.Build.BRAND;
        return !TextUtils.isEmpty(brand) && brand.toLowerCase().contains("oppo");
    }

    private static boolean isNeedUseMediaStyleForMiui() {
        String miuiCode = BuildProperties.getSystemProperty("ro.miui.ui.version.code", "7");
        int miuiVersion = 0;
        try {
            miuiVersion = Integer.parseInt(miuiCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ManufacturUtil.isMiui10() || miuiVersion >= 8;
    }

    public static boolean isHyperOs() {
        String miuiCode = BuildProperties.getSystemProperty("ro.miui.ui.version.code", "7");
        int miuiVersion = 0;
        try {
            miuiVersion = Integer.parseInt(miuiCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return miuiVersion == 816;
    }

    /**
     * 是否华为设备
     */
    public static boolean isHuaweiDevice() {
        if (isHuaweiDevice != null) {
            return isHuaweiDevice;
        }
        String manufacturer = Build.MANUFACTURER;
        isHuaweiDevice = !TextUtils.isEmpty(manufacturer)
            && ("huawei".equalsIgnoreCase(manufacturer) || manufacturer.toLowerCase(Locale.CHINA).contains("huawei"));
        return isHuaweiDevice;
    }

    /**
     * 是否是摩托罗拉机型
     */
    public static boolean isMotorolaDevice () {
        if (isMotorolaDevice != null) {
            return isMotorolaDevice;
        }
        String manufacturer = Build.MANUFACTURER;
        isMotorolaDevice = !TextUtils.isEmpty(manufacturer)
                && ("Motorola".equalsIgnoreCase(manufacturer)
                || manufacturer.toLowerCase(Locale.CHINA).contains("motorola"));
        return isMotorolaDevice;
    }

    /**
     * 蔚来手机
     */
    public static boolean isNioDevice() {
        if (isNioDevice != null) {
            return isNioDevice;
        }
        String brand = android.os.Build.BRAND;
        isNioDevice = !TextUtils.isEmpty(brand) && ("NIO".equalsIgnoreCase(brand));
        return isNioDevice;
    }

    public static boolean isOppoFlamingoDevice() {
        if (isOppoFlamingoDevice != null) {
            return isOppoFlamingoDevice;
        }
        if (isOppoDevice()) {
            isOppoFlamingoDevice = isFlamingo();
        } else {
            isOppoFlamingoDevice = false;
        }
        return isOppoFlamingoDevice;
    }

    public static boolean isFlamingo() {
        boolean isSecondaryMiniApp = false;
        try {
            Class<?> cls = Class.forName("com.oplus.content.OplusFeatureConfigManager");

            Method instance = cls.getMethod("getInstance");
            Object configManager = instance.invoke(null);

            Method hasFeature = cls.getDeclaredMethod("hasFeature", String.class);
            Object object = hasFeature.invoke(configManager, "oplus.software.support_secondary_mini_app");
            if (object instanceof Boolean) {
                isSecondaryMiniApp = (boolean) object;
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }

        return isSecondaryMiniApp;
    }

    /**
     * 是否oppo设备
     */
    public static boolean isOppoDevice() {
        if (isOppoOs != null) {
            return isOppoOs;
        }
        String manufacturer = Build.MANUFACTURER;
        isOppoOs = !TextUtils.isEmpty(manufacturer)
            && ("oppo".equalsIgnoreCase(manufacturer) || manufacturer.toLowerCase(Locale.CHINA).contains("oppo"));
        return isOppoOs;
    }

    /**
     * 获取EMUI版本名称，格式为 EmotionUI_10.1.0
     */
    public static String getEmuiVersion() {
        if (emuiVersion != null) {
            return emuiVersion;
        }
        emuiVersion = BuildProperties.getSystemProperty("ro.build.version.emui");
        return emuiVersion;
    }

    /**
     * 是否EMUI11.1及以上版本
     */
    @NonNull
    public static Boolean isEmui111OrOver() {
        String buildVersion = getEmuiVersion();
        if (buildVersion == null || "".equals(buildVersion)) {
            return false;
        }

        String[] strArrays = buildVersion.split("_");
        if (strArrays == null || strArrays.length < 2) {
            Logger.i(TAG, "strArrays.length : " + strArrays.length);
            return false;
        }

        String[] versions = strArrays[1].split("\\.");
        if (versions == null || versions.length < 1) {
            Logger.i(TAG, "versions.length : " + versions.length);
            return false;
        }

        try {
            if (Integer.parseInt(versions[0]) > 11) {
                return true;
            }

            if (Integer.parseInt(versions[0]) == 11 && Integer.parseInt(versions[1]) > 0) {
                return true;
            }
        } catch (NumberFormatException e) {
            Logger.i(TAG, "NumberFormatException");
            return false;
        }

        return false;
    }

    /**
     * 是否华为新系统
     *
     * @return true/false
     */
    public static Boolean isHuaweiNewSystem() {
        int currentApiLevel;
        try {
            Class cls = Class.forName("android.os.SystemProperties");
            Method method = cls.getDeclaredMethod("get", new Class[] {String.class});
            currentApiLevel = Integer.parseInt((String) method.invoke(cls,
                new Object[] {"ro.build.hw_emui_api_level"}));
        } catch (ClassNotFoundException e) {
            Logger.i(TAG, "ClassNotFoundException");
            return false;
        } catch (NoSuchMethodException e) {
            Logger.e(TAG, "NoSuchMethodException");
            return false;
        } catch (IllegalAccessException e) {
            Logger.i(TAG, "NoSuchFieldException");
            return false;
        } catch (InvocationTargetException e) {
            Logger.e(TAG, "InvocationTargetException");
            return false;
        } catch (Exception e) {
            Logger.e(TAG, "Exception");
            return false;
        }

        Logger.i(TAG, "currentApiLevel = " + currentApiLevel);
        return currentApiLevel >= NEW_VERSION_API_LEVEL;
    }

}
