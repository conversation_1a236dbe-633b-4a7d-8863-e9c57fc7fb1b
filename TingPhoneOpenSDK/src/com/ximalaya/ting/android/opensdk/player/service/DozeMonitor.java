package com.ximalaya.ting.android.opensdk.player.service;

import android.content.Context;
import android.content.IntentFilter;
import android.os.Build;
import android.os.PowerManager;

import androidx.annotation.RequiresApi;

/**
 * Created by jack.qin on 2021/12/20.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class DozeMonitor {
    private boolean hasInit;

    protected DozeMonitor() {
    }

    private static class HolderClass {
        private final static DozeMonitor instance = new DozeMonitor();
    }

    public static DozeMonitor getInstance() {
        return HolderClass.instance;
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    public void init(Context context) {
        try {
            if (hasInit) {
                return;
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                DozeReceiver dozeReceiver = new DozeReceiver();
                IntentFilter filter = new IntentFilter();
                filter.addAction(PowerManager.ACTION_DEVICE_IDLE_MODE_CHANGED);
                context.registerReceiver(dozeReceiver, filter);
                hasInit = true;
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }
}
