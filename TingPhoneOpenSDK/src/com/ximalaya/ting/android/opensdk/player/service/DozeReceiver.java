package com.ximalaya.ting.android.opensdk.player.service;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.PowerManager;
import android.provider.Settings;
import android.text.TextUtils;

import androidx.annotation.RequiresApi;

import com.google.gson.Gson;
import com.ximalaya.ting.android.opensdk.model.power.DozeModel;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayErrorStatisticManager;
import com.ximalaya.ting.android.opensdk.util.SystemUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * Created by jack.qin on 2021/12/20.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class DozeReceiver extends BroadcastReceiver {
    private final static String DEVICE_IDLE_ACTION = "android.os.action.DEVICE_IDLE_MODE_CHANGED";
    private static boolean sHasPost = false;

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    public void onReceive(Context context, Intent intent) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (sHasPost) {
                return;
            }
            if (context != null) {
                try {
                    if (intent != null
                            && !TextUtils.isEmpty(intent.getAction())
                            && DEVICE_IDLE_ACTION.equals(intent.getAction())) {
                        XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
                        if (xmPlayerService != null) {
                            XmPlayerControl xmPlayerControl = xmPlayerService.getPlayControl();
                            if (xmPlayerControl != null) {
                                if (xmPlayerControl.getPlayerState() == PlayerConstants.STATE_STARTED) {
                                    PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
                                    if (powerManager != null && powerManager.isDeviceIdleMode()) {
                                        sHasPost = true;
                                        boolean isDeviceIdle = powerManager.isDeviceIdleMode();
                                        boolean isInteractive = powerManager.isInteractive();
                                        boolean isPowerSaveMode = SystemUtil.isPowerSaveMode(context);
                                        boolean isKeepNetSleepMode = SystemUtil.isKeepNetConnectInSleepMode(context);
                                        boolean isIgnoringBatteryOp = SystemUtil.isIgnoringBatteryOptimizations(context);

                                        String log = "device_idle_mode=" + isDeviceIdle
                                                + ",screen_status=" + isInteractive
                                                + ",process_status=" + SystemUtil.getProImportance(context)
                                                + ",power_save=" + isPowerSaveMode
                                                + ",net_sleep_policy=" + isKeepNetSleepMode
                                                + ",whitelist=" + isIgnoringBatteryOp;
                                        Logger.i("DozeReceiver", log);
                                        IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
                                        PlayErrorStatisticManager.setHasHappenedPlayErrorInBg();
                                        if (xdcsPost != null) {
                                            xdcsPost.statErrorToXDCS("doze_status_record", log);
                                        }
                                        DozeModel dozeModel = new DozeModel();
                                        dozeModel.isDeviceIdle = isDeviceIdle;
                                        dozeModel.isInteractive = isInteractive;
                                        dozeModel.isPowerSaveMode = isPowerSaveMode;
                                        dozeModel.isKeepNetSleepMode = isKeepNetSleepMode;
                                        dozeModel.isIgnoringBatteryOp = isIgnoringBatteryOp;
                                        XmLogger.log("apm", "doze_record", new Gson().toJson(dozeModel));
                                    }
                                }
                            }
                        }

                    }
                } catch (Throwable t) {
                    t.printStackTrace();
                }

            }
        }
    }
}
