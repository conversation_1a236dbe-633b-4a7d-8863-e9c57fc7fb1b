package com.ximalaya.ting.android.opensdk.player.ubt;

import android.content.Context;

import static com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk.isDebug;

/**
 * Created by <PERSON> on 2022/1/17 1:42 下午.
 *
 * <AUTHOR>
 */

public class UbtSourceEmptyCollector {

    private static class SingleTonHolder {
        private static final UbtSourceEmptyCollector INSTANCE = new UbtSourceEmptyCollector();
    }

    public static UbtSourceEmptyCollector getInstance() {
        return SingleTonHolder.INSTANCE;
    }

    private UbtSourceEmptyCollector() {

    }

    private boolean shouldCollect = true;
    private IStackCollector mCollector;

    public void collect(Context context) {
        if (isDebug) {
//            Runnable runnable = new Runnable() {
//                @Override
//                public void run() {
//                    Toast.makeText(context, "本次播放未捕获到对应资源位，请检查埋点相关逻辑", Toast.LENGTH_LONG).show();
//                }
//            };
//            if (Looper.myLooper() == Looper.myLooper()) {
//                runnable.run();
//            } else {
//                new Handler(Looper.getMainLooper()).post(runnable);
//            }
        }
        if (!shouldCollect || mCollector == null) {
            return;
        }
        mCollector.traceStack();
    }

    public void shouldCollect(boolean collect) {
        shouldCollect = collect;
    }

    public void setStackCollector(IStackCollector collector) {
        this.mCollector = collector;
    }

    public interface IStackCollector {

        void traceStack();

    }
}
