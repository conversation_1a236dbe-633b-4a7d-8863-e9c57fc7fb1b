package com.ximalaya.ting.android.opensdk.player.statistic;

/**
 * Created by q<PERSON><PERSON><PERSON><PERSON> on 2018/11/30.
 *
 * <AUTHOR>
 */
public interface IXmPlaySource {
    /***
     * 趣配音播放来源 playSource
     *
     * 1001: 发现 - 关注tab
     * 1002: 发现 - 推荐tab
     * 1003: 发现 - 趣配音tab
     * 1004: 发现 - 圈子tab
     * 1005: 主播个人页
     *
     * 2001: 趣配音个人页
     * 2002: 素材广场
     * 2003: 站外单人落地页 (H5)
     * 2004: 站外双人落地页 (H5)
     * 2005: 普通话题页
     * 2006: 挑战话题 - 最新tab
     * 2007: 挑战话题 - 我的作品
     * 2008: 挑战话题 - 挑战tab排行榜
     * 2009: 分享话题落地页 - 挑战tab
     * 2010: 分享话题落地页 - 最新tab
     * 2011: 动态视频站外落地页 (H5)
     * 2012: 普通话题站外落地页 (H5)
     * 2013: 圈子
     * 2014: 大家都在配
     * 2015: 我的趣配音作品页
     * 2018 feed流趣配音精选作品集
     * 2019 素材广场-> 素材详情页
     * 趣配音话题： (站内) 1001,1002,1003,2006,2007,2008（站外）2009,2010,2003
     * 趣配音业务：(站内) 1001,1002,1005,1003,2001,2002,2005（站外）2003,2004,
     * 动态：(站内) 1001,1002,1004,1005,2005,2013 (站外) 2011,2012
     *
     *
     * 钱佳丽
     **/

    int EVENT_PLAY_SOURCE_DETAIL = 6;

    int EVENT_DUB_PLAY_SOURCE_FEED_FOLLOW = 1001;
    int EVENT_DUB_PLAY_SOURCE_FEED_RECOMMEND = 1002;
    int EVENT_DUB_PLAY_SOURCE_FEED_DUB_TAB = 1003;
    int EVENT_DUB_PLAY_SOURCE_FEED_ZONE_TAB = 1004;
    int EVENT_DUB_PLAY_SOURCE_ANCHOR_SPACE = 1005;
    int EVENT_DUB_PLAY_SOURCE_FEED_VIDEO_TAB = 1006;

    int EVENT_DUB_PLAY_SOURCE_USER = 2001;
    int EVENT_DUB_PLAY_SOURCE_SQUARE = 2002;
    int EVENT_DUB_PLAY_SOURCE_LATEST_TOPIC = 2006;
    int EVENT_DUB_PLAY_SOURCE_MY_TOPIC_WORK = 2007;
    int EVENT_DUB_PLAY_SOURCE_TOPIC_CHALLENGE = 2008;
    int EVENT_DUB_PLAY_SOURCE_EVERYONE = 2014;
    int EVENT_DUB_PLAY_SOURCE_MY_SPACE = 2015;
    int EVENT_DUB_PLAY_SOURCE_CHOICE_ALBUM = 2018;
    int EVENT_DUB_PLAY_SOURCE_MATERIAL_LANDING = 2019;
}
