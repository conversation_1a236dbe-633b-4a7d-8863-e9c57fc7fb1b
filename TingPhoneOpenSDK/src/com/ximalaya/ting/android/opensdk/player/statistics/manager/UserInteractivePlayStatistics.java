package com.ximalaya.ting.android.opensdk.player.statistics.manager;

import android.content.Context;
import android.os.AsyncTask;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;
import androidx.core.os.TraceCompat;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.util.EasyConfigure;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.ICommonBusiService;
import com.ximalaya.ting.android.routeservice.service.version.IVersion;
import com.ximalaya.ting.android.statistic.audio.performance.PlayPerformanceModel;
import com.ximalaya.ting.android.statistic.audio.performance.XmPlayPerformanceStatistic;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

public class UserInteractivePlayStatistics {

    private static final String TAG = "UIPS_";
    private static final String KEY_IS_UNKNOWN_TRACK_ID = "KEY_IS_UNKNOWN_TRACK_ID_0802";
    private static final String KEY_UNKNOWN_TRACK_ID = "KEY_UNKNOWN_TRACK_ID_0802";

    private static final Map<Long, InteractivePlayInfo> sTrackClickPlayInfoMap = new ConcurrentHashMap<>();

    private static Boolean sIsOpen;
    private static Boolean sUploadExtra;
    private static Boolean sUploadTotalTime2;

    private static final Map<Long, Long> sTrackIdLastClickTimeMap = new ConcurrentHashMap<>();

    private static final Map<String, Long> sStepBeginTimeMap = new ConcurrentHashMap<>();
    //某个声音 ID 对应的中间步骤耗时
    private static final Map<Long, Map<String, Long>> sTrackIdStepCostTimeMap = new ConcurrentHashMap<>();

    private static Context sGlobalContext;

    //是否要切换播放的声音（切换上下首，这个时候不知道 TrackId，但还是要记录点击时间）
    private static boolean isPlayingUnknownTrack;
    //使用临时的时间戳代表 ID
    private static long unknownTrackId;

    public static void init(Context context) {
        if (!isOpen()) {
            return;
        }

        if (context != null) {
            sGlobalContext = context.getApplicationContext();
        }

        XmPlayPerformanceStatistic.getInstanse().setIUploadDataInterceptor(new XmPlayPerformanceStatistic.IUploadDataInterceptor() {
            @Override
            public PlayPerformanceModel onHandled(PlayPerformanceModel playPerformanceModel) {
                if (playPerformanceModel == null) {
                    return null;
                }
                if (!isOpen()) {
                    log("onHandled, not open ");
                    return playPerformanceModel;
                }

                //补充从用户点击到调用播放的数据
                log("onHandled, open , trackId: " + playPerformanceModel.trackId);
                InteractivePlayInfo playInfo = getPlayInfoSupportUnknownTrack(playPerformanceModel.trackId);

                if (playInfo == null) {
                    loge("onHandled, playInfo is null");
                    return playPerformanceModel;
                }

                if (playInfo.invalid()) {
                    loge("onHandled, playInfo invalid !");
                    return playPerformanceModel;
                }

                playPerformanceModel.sourceTotalTime = SystemClock.elapsedRealtime() - playInfo.clickPlayTime;
                playPerformanceModel.source = playInfo.source;
                playPerformanceModel.playerCallDuration = playInfo.playerCallDuration;

                boolean uploadExtra = uploadExtra();
                boolean uploadTotalTime2 = uploadTotalTime2();
                loge("onHandled, uploadExtra: " + uploadExtra + ", uploadTotalTime2: " + uploadTotalTime2);
                playPerformanceModel.uploadExtra = uploadExtra;

                //主进程调用播放器后，跨进程的时间
                long processCallDuration = playInfo.processCallDuration;
                //点击到播放器进程准备播放的时间
                long clickToPlayerProcessStartPlayDuration = playInfo.clickToPlayerProcessStartPlayDuration;

                playPerformanceModel.extra.put("processCallDuration", String.valueOf(processCallDuration));
                playPerformanceModel.extra.put("clickToPlayerProcessStartPlay", String.valueOf(clickToPlayerProcessStartPlayDuration));

                setStepCostTime(playInfo.stepCostTimeMap, playPerformanceModel.extra);

                if (uploadTotalTime2) {
                    long sourceTotalTime2 = playPerformanceModel.sourceTotalTime;
                    if (playPerformanceModel.soundAdShowTime > 0) {
                        sourceTotalTime2 = sourceTotalTime2 - playPerformanceModel.soundAdShowTime;
                    }
                    playPerformanceModel.extra.put("sourceTotalTime2", String.valueOf(sourceTotalTime2));
                }

                boolean optOpen = isOptOpen();
                playPerformanceModel.extra.put("optOpen", String.valueOf(optOpen));

                loge("onHandled, sourceTotalTime: " + playPerformanceModel.sourceTotalTime
                        + ", clickToStartPlay:" + clickToPlayerProcessStartPlayDuration + ", optOpen: " + optOpen);

                clearTrackInfo(playPerformanceModel.trackId);

                return playPerformanceModel;
            }
        });
    }

    private static void setStepCostTime(Map<String, Long> stepCostMap, Map<String, String> extra) {
        if (stepCostMap == null || stepCostMap.size() <= 0 || extra == null) {
            return;
        }

        for (Map.Entry<String, Long> entry : stepCostMap.entrySet()) {
            extra.put(entry.getKey(), String.valueOf(entry.getValue()));
        }

        if (ConstantsOpenSdk.isDebug) {
            loge("setStepCostTime, extra: " + extra);
        }
    }

    private static boolean uploadExtra() {
        if (sUploadExtra == null) {
            boolean open = EasyConfigure.getBoolean("UserInteractivePlayStatistics_uploadExtra_0806", false);
            Log.d(TAG, "sUploadExtra: " + open);
            sUploadExtra = open;
        }
        return sUploadExtra;
    }

    private static boolean uploadTotalTime2() {
        if (sUploadTotalTime2 == null) {
            boolean open = EasyConfigure.getBoolean("UIP_uploadTotalTime2_0903", true);
            Log.d(TAG, "sUploadTotalTime2: " + open);
            sUploadTotalTime2 = open;
        }
        return sUploadTotalTime2;
    }

    private static boolean isOpen() {
        if (sIsOpen == null) {
            boolean open = EasyConfigure.getBoolean("UserInteractivePlayStatistics_0626", false);
            Log.d(TAG, "isOpen: " + open);
            sIsOpen = open;
        }
        return sIsOpen;
    }

    /**
     * 点击 UI 播放，开始计时
     * @param trackId -1 的时候表示不知道要播放的声音 ID，比如切换上一首、下一首的时候
     */
    public static void clickPlay(long trackId, @NotNull String source) {
        if (!isOpen()) {
            return;
        }

        if (TextUtils.isEmpty(source)) {
            if (ConstantsOpenSdk.isDebug) {
                throw new IllegalStateException(TAG + " source is null");
            }
            return;
        }

        if (tooFrequently(trackId)) {
            log("clickPlay, too frequently");
            return;
        }

        // 检查是否正在播放这个声音，是的话就不记录数据
        if (BaseUtil.isMainProcess(sGlobalContext)) {
            boolean playing = XmPlayerManager.getInstance(sGlobalContext).isPlaying();
            PlayableModel currSound = XmPlayerManager.getInstance(sGlobalContext).getCurrSound();
            if (playing && currSound != null && currSound.getDataId() == trackId) {
                log("clickPlay, currSound is playing");
                return;
            }
        }

        log("clickPlay: " + trackId + ", " + source);

        updateIsPlayingUnknownTrack(trackId <= 0);
        if (isPlayingUnknownTrack) {
            trackId = unknownTrackId;
            log("clickPlay, unknownTrackId:" + unknownTrackId + ", " + source);
        }

        try {
            InteractivePlayInfo info = new InteractivePlayInfo(trackId, source);
            sTrackClickPlayInfoMap.put(trackId, info);
            MMKVUtil.getInstance().saveString(getKey(trackId), new Gson().toJson(info));
        } catch (Throwable throwable) {

            throwable.printStackTrace();
            loge("clickPlay  error: " + throwable);
        }
    }

    //未知的声音，保存一个值，播放进程要用
    private static void updateIsPlayingUnknownTrack(boolean isUnknownTrack) {
        MMKVUtil.getInstance().saveBoolean(KEY_IS_UNKNOWN_TRACK_ID, isUnknownTrack);
        isPlayingUnknownTrack = isUnknownTrack;
        if (isPlayingUnknownTrack) {
            unknownTrackId = SystemClock.elapsedRealtime();
        } else  {
            unknownTrackId = -1;
        }
        MMKVUtil.getInstance().saveLong(KEY_UNKNOWN_TRACK_ID, unknownTrackId);
    }

    private static boolean isPlayingUnknownTrack() {
        if (isPlayingUnknownTrack && unknownTrackId > 0) {
            return true;
        }
        boolean aBoolean = MMKVUtil.getInstance().getBoolean(KEY_IS_UNKNOWN_TRACK_ID, false);
        if (aBoolean) {
            unknownTrackId = MMKVUtil.getInstance().getLong(KEY_UNKNOWN_TRACK_ID);
        }
        return aBoolean;
    }

    private static boolean tooFrequently(long trackId) {
        Long lastClickTime = sTrackIdLastClickTimeMap.get(trackId);
        if (lastClickTime == null) {
            sTrackIdLastClickTimeMap.put(trackId, SystemClock.elapsedRealtime());
            return false;
        }
        //100ms内点击过，不记录
        return SystemClock.elapsedRealtime() - lastClickTime < 100;
    }

    private static void log(String s) {
        Log.w(TAG, s);
    }

    public static void loge(String s) {
        Log.e(TAG, s);
    }

    /**
     * 调用播放器开始播放
     * @param tracks
     */
    public static void callPlayerStart(List<Track> tracks, int startIndex) {
        if (!isOpen()) {
            return;
        }
        if (tracks == null || tracks.isEmpty() || startIndex < 0 || startIndex >= tracks.size()) {
            log("callPlayerStart return s1");
            return;
        }

        Track track = tracks.get(startIndex);

        log("callPlayerStart: " + tracks.size() + ", startIndex: " + startIndex);

        if (track == null) {
            log("callPlayerStart return s2");
            return;
        }

        log("callPlayerStart: " + track.getDataId() + ", " + track.getTrackTitle());
        callPlayerStart(track.getDataId());
    }

    /**
     * 开始调用播放器播放
     * @param trackId
     */
    public static void callPlayerStart(long trackId) {
        if (!isOpen()) {
            return;
        }
        long playerCallTime = SystemClock.elapsedRealtime();
        InteractivePlayInfo info = getPlayInfoSupportUnknownTrackMainProcess(trackId);

        if (info != null && info.clickPlayTime > 0) {
            info.playerCallDuration = playerCallTime - info.clickPlayTime;
            info.playerCallTime = playerCallTime;

            info.stepCostTimeMap = sTrackIdStepCostTimeMap.get(info.trackId);

            log("callPlayerStart save to mmkv, playerCallDuration: " +info.playerCallDuration
                    + ", stepCostTimeMap: " + info.stepCostTimeMap);

            MMKVUtil.getInstance().saveString(getKey(trackId), new Gson().toJson(info));

            //数据保存到 mmkv 以后，播放进程可以拿到，主进程的数据就没用了
            sTrackClickPlayInfoMap.clear();
            sStepBeginTimeMap.clear();
            sTrackIdStepCostTimeMap.clear();
        }
    }

    /**
     * 播放进程准备开始播放
     * @param trackId
     */
    public static void playerProcessStartPlay(long trackId) {
        if (!isOpen()) {
            return;
        }

        InteractivePlayInfo playInfo = getPlayInfoSupportUnknownTrack(trackId);

        log("playerProcessStartPlay: " + trackId + ", playInfo: " + playInfo);

        if (ConstantsOpenSdk.isDebug) {
            log("playerProcessStartPlay: " + trackId + Log.getStackTraceString(new Throwable()));
        }

        if (playInfo == null) {
            return;
        }

        long now = SystemClock.elapsedRealtime();

        long processCallDuration = -1;
        //有可能没拦截到主进程发起播放器调用的地方，没有的话就为 -1
        if (playInfo.playerCallTime > 0) {
            processCallDuration = now - playInfo.playerCallTime;
        }
        playInfo.processCallDuration = processCallDuration;
        playInfo.clickToPlayerProcessStartPlayDuration = now - playInfo.clickPlayTime;

        log("playerProcessStartPlay processCallDuration: " + processCallDuration + ", clickToPlayerProcessStartPlayDuration: " + playInfo.clickToPlayerProcessStartPlayDuration);
    }

    /**
     * 开始播放
     * @param trackId
     */
    public static void onPlayStart(long trackId) {
        if (!isOpen()) {
            return;
        }
        log("onPlayStart: " + trackId);

        InteractivePlayInfo playInfo = getPlayInfoSupportUnknownTrack(trackId);
        if (playInfo == null) {
            return;
        }

        playInfo.sourceTotalTime = SystemClock.elapsedRealtime() - playInfo.clickPlayTime;

        log("z_onPlayStart : " + playInfo.toString());

    }

    private static void clearTrackInfo(long trackId) {
        log("clearTrackInfo, trackId: " + trackId);

        //清理 map 和 mmkv 这条声音的数据，避免混乱
        //这里清除的是播放进程的，还得广播通知主进程清除

        clearMemoryTrackInfo(trackId);

        if (isPlayingUnknownTrack()) {
            clearMemoryTrackInfo(unknownTrackId);
        }

        updateIsPlayingUnknownTrack(false);

    }

    private static void clearMemoryTrackInfo(long trackId) {
        sTrackClickPlayInfoMap.remove(trackId);
        MMKVUtil.getInstance().saveString(getKey(trackId), "");

        sTrackIdStepCostTimeMap.remove(trackId);
    }

    //主进程，兼容播放未知声音
    private static InteractivePlayInfo getPlayInfoSupportUnknownTrackMainProcess(long trackId) {
        InteractivePlayInfo info = sTrackClickPlayInfoMap.get(trackId);
        if (info == null && isPlayingUnknownTrack()) {
            info = sTrackClickPlayInfoMap.get(unknownTrackId);
        }
        return info;
    }

    //播放进程，兼容播放未知声音，查不到去 mmkv 查询
    private static InteractivePlayInfo getPlayInfoSupportUnknownTrack(long trackId) {

        InteractivePlayInfo playInfo = getPlayInfo(trackId);
        if (playInfo == null && isPlayingUnknownTrack()) {
            playInfo = getPlayInfo(unknownTrackId);
        }
        return playInfo;
    }

    private static InteractivePlayInfo getPlayInfo(long trackId) {

        if (trackId < 0) {
            return null;
        }

        //内存中的数据，第一次为空
        InteractivePlayInfo playInfoFromMemory = sTrackClickPlayInfoMap.get(trackId);
        //mmkv 的数据，第一次有数据，放到内存中，清空掉
        InteractivePlayInfo playInfoFromMMKV = getPlayInfoFromMMKV(trackId);

        log("getPlayInfo s1 : " + trackId);
        log("getPlayInfo s1 playInfoFromMemory:" + playInfoFromMemory);
        log("getPlayInfo s1 playInfoFromMMKV:" + playInfoFromMMKV);

        if (playInfoFromMMKV != null) {
            sTrackClickPlayInfoMap.put(trackId, playInfoFromMMKV);
            MMKVUtil.getInstance().saveString(getKey(trackId), "");
            loge("getPlayInfo s2 playInfoFromMMKV replace playInfoFromMemory");
            return playInfoFromMMKV;
        }

        return playInfoFromMemory;
    }

    private static InteractivePlayInfo getPlayInfoFromMMKV(long trackId) {

        String json = MMKVUtil.getInstance().getString(getKey(trackId));

        log("getPlayInfo s2 : " + json);

        if (TextUtils.isEmpty(json)) {
            return null;
        }

        try {
            InteractivePlayInfo interactivePlayInfo = new Gson().fromJson(json, InteractivePlayInfo.class);

            log("getPlayInfo s3 : " + interactivePlayInfo);

            return interactivePlayInfo;
        } catch (Throwable throwable) {
            throwable.printStackTrace();

            loge("getPlayInfo s3 : " + throwable.getMessage());
        }

        return null;
    }

    private static String getKey(long trackId) {
        return "interactive_play_0719_" + trackId;
    }

    private static String getKey(String step) {
        return "interactive_play_step_cost_0923_" + step;
    }

    /**
     * 中间步骤开始
     * @param stepName
     */
    public static void stepBegin(String stepName) {
        sStepBeginTimeMap.put(stepName, SystemClock.elapsedRealtime());

        loge("stepBegin: " + stepName);
    }

    /**
     * 中间步骤结束
     * @param trackId
     * @param stepName
     */
    public static void stepEnd(long trackId, String stepName) {
        Long beginTime = sStepBeginTimeMap.get(stepName);
        if (beginTime == null) {
            return;
        }

        long cost = SystemClock.elapsedRealtime() - beginTime;
        Map<String, Long> stepCostTimeMap = sTrackIdStepCostTimeMap.get(trackId);
        if (stepCostTimeMap == null) {
            stepCostTimeMap = new HashMap<>();
            sTrackIdStepCostTimeMap.put(trackId, stepCostTimeMap);
        }
        stepCostTimeMap.put(stepName, cost);

        sStepBeginTimeMap.remove(stepName);

        loge("stepEnd: " + stepName + ", cost: " + cost + ", map: " + sTrackIdStepCostTimeMap);

        InteractivePlayInfo playInfo = getPlayInfoSupportUnknownTrack(trackId);
        if (playInfo != null) {
            if (playInfo.stepCostTimeMap == null) {
                playInfo.stepCostTimeMap = new HashMap<>();
            }
            playInfo.stepCostTimeMap.putAll(stepCostTimeMap);

            loge("stepEnd: " + stepName + ", playInfo.stepCostTimeMap: " + playInfo.stepCostTimeMap);
        }
    }

    private static Boolean isOptOpen;

    private static boolean isOptOpen() {
        if (isOptOpen != null) {
            return isOptOpen;
        }

        boolean switchOpen = EasyConfigure.getBoolean(Optimizer_IsPodCastAlbum.MMKV_KEY_SETTING, true);
        boolean abOpen = MMKVUtil.getInstance().getBoolean(Optimizer_IsPodCastAlbum.MMKV_KEY_AB, false);
        ICommonBusiService commonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
        boolean openForNet = true;
        if (commonBusiService != null && commonBusiService.isOpenNetDetect()) {
            openForNet = commonBusiService.openForAdRequestOpt();
        }
        IVersion version = RouterServiceManager.getInstance().getService(IVersion.class);
        boolean betaVersion = false;
        if (version != null) {
            betaVersion = !version.isReleaseVersion();
        }
        loge("Optimizer isOptOpen switchOpen: " + switchOpen + ", abOpen: " + abOpen + ",openForNet=" + openForNet + ",betaVersion=" + betaVersion);
        isOptOpen = switchOpen && abOpen && (betaVersion || openForNet);

        return isOptOpen;
    }

    public static void saveOptABValue(boolean abValue) {
        MMKVUtil.getInstance().saveBoolean(Optimizer_IsPodCastAlbum.MMKV_KEY_AB, abValue);
        loge("Optimizer saveABValue switchOpen: " + abValue);
    }

    public static boolean directPlayWhilePrepared() {
        return EasyConfigure.getBoolean("directPlayWhilePrepared_1112", true);
    }

    public static void logOpt(String s) {
        Log.w("Optimizer", s);
    }

    /**
     * 优化，iting 打开时不同步请求接口
     */
    public static class Optimizer_ItingDirectPlay {
        public static final String MMKV_KEY_AB = "START_PLAY_OPT_ITING_0930";
        private static Boolean sOptOpen;

        public static boolean enable() {
            if (sOptOpen == null) {
                sOptOpen = MMKVUtil.getInstance().getBoolean(Optimizer_ItingDirectPlay.MMKV_KEY_AB, false);
            }

            boolean optOpen = isOptOpen();
            loge("Optimizer_ItingDirectPlay isOptOpen: " + optOpen + ", sOptOpen: " + sOptOpen);
            return optOpen && sOptOpen;
        }

        public static void saveOptEnable(boolean enable) {
            sOptOpen = enable;
            MMKVUtil.getInstance().saveBoolean(Optimizer_ItingDirectPlay.MMKV_KEY_AB, enable);
            loge("Optimizer_ItingDirectPlay saveOptEnable switchOpen: " + enable);
        }
    }

    /**
     * 优化，广告请求时就开始播放
     */
    public static class Optimizer_StartPlayWhileAdRequest {
        public static final String MMKV_KEY_AB = "START_PLAY_OPT_WHILE_AD_REQUEST_0927";
        public static final String MMKV_KEY_AB_PAUSE_PLAY_WHILE_AD_MUTE = "MMKV_KEY_AB_PAUSE_PLAY_WHILE_AD_MUTE_1217";
        private static Boolean sStartPlayWhileAdRequest;
        private static Boolean sPausePlayWhileAdMute;

        public static void saveOptEnable(boolean enable) {
            MMKVUtil.getInstance().saveBoolean(Optimizer_StartPlayWhileAdRequest.MMKV_KEY_AB, enable);
            loge("Optimizer_StartPlayWhileAdRequest saveOptEnable switchOpen: " + enable);
            sStartPlayWhileAdRequest = enable;
        }

        public static void savePausePlayBugFixEnable(boolean enable) {
            MMKVUtil.getInstance().saveBoolean(Optimizer_StartPlayWhileAdRequest.MMKV_KEY_AB_PAUSE_PLAY_WHILE_AD_MUTE, enable);
            loge("Optimizer_StartPlayWhileAdRequest savePausePlayBugFixEnable switchOpen: " + enable);
            sPausePlayWhileAdMute = enable;
        }

        public static boolean enable() {

            if (sStartPlayWhileAdRequest == null) {
                sStartPlayWhileAdRequest = MMKVUtil.getInstance().getBoolean(Optimizer_StartPlayWhileAdRequest.MMKV_KEY_AB, false);
            }

            boolean optOpen = isOptOpen();
            loge("Optimizer_StartPlayWhileAdRequest isOptOpen: " + optOpen + ", sStartPlayWhileAdRequest: " + sStartPlayWhileAdRequest);
            return optOpen && sStartPlayWhileAdRequest;
        }

        /**
         *
         * @return 默认 false，用于有问题的时候下发关闭
         */
        public static boolean directPausePlay() {

            if (sPausePlayWhileAdMute == null) {
                sPausePlayWhileAdMute = MMKVUtil.getInstance().getBoolean(Optimizer_StartPlayWhileAdRequest.MMKV_KEY_AB_PAUSE_PLAY_WHILE_AD_MUTE, false);
            }

            loge("Optimizer_StartPlayWhileAdRequest directPausePlay, sPausePlayWhileAdMute: " + sPausePlayWhileAdMute);
            return enable() && !sPausePlayWhileAdMute;
        }
    }

    /**
     * 优化，判断是否播客专辑
     */
    public static class Optimizer_IsPodCastAlbum {
        public static final String TAG = "Optimizer";
        public static final String MMKV_KEY_CACHE = "Optimizer_ALBUM_TYPE_PODCAST_SHOW_NOTES_0924";
        public static final String MMKV_KEY_SETTING = "Opt_ALBUM_TYPE_PODCAST_0924";
        public static final String MMKV_KEY_AB = "START_PLAY_OPT_OPEN_ABTEST_0925";

        private static Map<Long, Boolean> sAlbumIdIsPodCastMap = null;
        private static AtomicBoolean isSavingToCache = new AtomicBoolean(false);

        private static void logO(String s) {
            logOpt(s);
        }

        /**
         * 保存专辑是否播客专辑的信息
         * @param albumId
         */
        public static void setAlbumLabelType(long albumId, boolean isPodCastAlbum) {

            boolean open = isOptOpen();
            if (!open) {
                logO("setAlbumLabelType return, not open.");
                return;
            }

            initMap();

            Boolean oldValue = sAlbumIdIsPodCastMap.get(albumId);

            if (oldValue != null && oldValue.equals(isPodCastAlbum)) {
                logO("setAlbumLabelType return, same value.");
                return;
            }

            //3: AlbumPodcastABManager.ALBUM_TYPE_PODCAST_SHOW_NOTES
            sAlbumIdIsPodCastMap.put(albumId, isPodCastAlbum);

            saveMapToMMKV();

            logO("setAlbumLabelType " + albumId + ",isPodCastAlbum: " + isPodCastAlbum);

            if (ConstantsOpenSdk.isDebug) {
                logO("setAlbumLabelType " + albumId + ",isPodCastAlbum: " + isPodCastAlbum + Log.getStackTraceString(new Throwable()));
            }
        }

        /**
         * 是否保存过这个专辑的播客专辑信息，没保存过返回 null
         * @param albumId
         * @return
         */
        public static Boolean knowIsPodCastAlbum(long albumId) {

            boolean open = isOptOpen();
            if (!open) {
                return null;
            }

            initMap();

            Boolean aBoolean = sAlbumIdIsPodCastMap.get(albumId);
            logO("knowIsPodCastAlbum " + albumId + ", isPodCastAlbum: " + aBoolean);

            return aBoolean;
        }

        private static void saveMapToMMKV() {

            boolean isSaving = isSavingToCache.get();
            if (isSaving) {
                logO("saveMapToMMKV return, is saving now.");
                return;
            }

            isSavingToCache.set(true);
            AsyncTask.execute(new Runnable() {
                @Override
                public void run() {
                    logO("saveMapToMMKV begin >>>");

                    try {
                        String json = new Gson().toJson(sAlbumIdIsPodCastMap, new TypeToken<Map<Long, Boolean>>() {
                        }.getType());

                        logO("saveMapToMMKV begin >>>" + json);

                        MMKVUtil.getInstance().saveString(MMKV_KEY_CACHE, json);
                    } catch (Throwable ignore) {}

                    isSavingToCache.set(false);

                    logO("saveMapToMMKV finish >>>");
                }
            });
        }

        private static void initMap() {
            if (sAlbumIdIsPodCastMap != null) {
                return;
            }

            try {
                String valueFromMMKV = MMKVUtil.getInstance().getString(MMKV_KEY_CACHE);
                sAlbumIdIsPodCastMap = new Gson().fromJson(valueFromMMKV, new TypeToken<Map<Long, Boolean>>() {}.getType());
            } catch (Throwable e) {
            }

            if (sAlbumIdIsPodCastMap == null) {
                sAlbumIdIsPodCastMap = new HashMap<>();
            }
        }
    }

    public static class Monitor {

        private static LinkedList<Pair<String, Long>> mTagStack = new LinkedList<>();

        public static void beginSection(String tag) {
            TraceCompat.beginSection(tag);
            printBegin(tag);
        }

        public static void endSection() {
            TraceCompat.endSection();
            printCost();
        }

        private static void printBegin(String tag) {
            mTagStack.push(new Pair<>(tag, System.nanoTime()));

            String msg = String.format(Locale.CHINA, "[%s] begin->> ", tag);
            log(msg);
        }

        private static void printCost() {
            long now = System.nanoTime();
            Pair<String, Long> lastBeginTrace = mTagStack.pop();
            if (lastBeginTrace != null && lastBeginTrace.second != null ) {
                String tag = lastBeginTrace.first;

                float cost = (now - lastBeginTrace.second)  / 1000000.0f;
                String msg = String.format(Locale.CHINA, "[%s] cost %.2f ms ", tag, cost);
                log(msg);
            }
        }

        private static void log(String msg) {
            Log.d(TAG, msg);
        }

    }
    public static class InteractivePlayInfo {
        public String source;
        public long trackId;

        //从用户点击到播放器调用耗时
        public long playerCallDuration;
        //从主进程播放器调用，到播放进程开始播放耗时
        public long processCallDuration;
        //从用户点击到播放进程调用播放耗时
        public long clickToPlayerProcessStartPlayDuration;
        //从点击到起播总耗时
        public long sourceTotalTime;

        //点击开始时间
        public long clickPlayTime;
        //播放器调用时间
        public long playerCallTime;

        public Map<String, Long> stepCostTimeMap;

        public InteractivePlayInfo(long trackId,String source) {
            this.trackId = trackId;
            this.source = source;
            this.clickPlayTime = SystemClock.elapsedRealtime();
        }

        @Override
        public String toString() {
            return "InteractivePlayInfo{" +
                    "source='" + source + '\'' +
                    ", trackId=" + trackId +
                    ", playerCallDuration=" + playerCallDuration +
                    ", processCallDuration=" + processCallDuration +
                    ", clickToPlayerProcessStartPlayDuration=" + clickToPlayerProcessStartPlayDuration +
                    ", sourceTotalTime=" + sourceTotalTime +
                    ", clickPlayTime=" + clickPlayTime +
                    ", playerCallTime=" + playerCallTime +
                    ", stepCostTimeMap=" + stepCostTimeMap +
                    '}';
        }

        /**
         * 数据是否异常
         * @return
         */
        public boolean invalid() {
            if (tooMuch(sourceTotalTime)) {
                return true;
            }
            if (tooMuch(clickToPlayerProcessStartPlayDuration)) {
                return true;
            }
            if (tooMuch(processCallDuration)) {
                return true;
            }
            if (tooMuch(playerCallDuration)) {
                return true;
            }
            return false;
        }

        private boolean tooMuch(long time) {
            //超过 1 分钟，认为是异常数据
            return time > 60 * 1000;
        }
    }


    public static String getDebugSystemProperty(String key, String defaultValue) {
        if (ConstantsOpenSdk.isDebug) {
            return getSystemProperty(key, defaultValue);
        }
        return defaultValue;
    }

    public static String getSystemProperty(String key, String defaultValue) {
        try {
            Class<?> SystemProperties = Class
                    .forName("android.os.SystemProperties");
            Method m = SystemProperties.getMethod("get", String.class,
                    String.class);
            String result = (String) m.invoke(null, key, defaultValue);
            return result;
        } catch (Exception e) {
            Logger.e(e);
        }
        return defaultValue;
    }

}
