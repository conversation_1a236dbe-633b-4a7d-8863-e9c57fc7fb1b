/**
 * XmMediaPlayerFactory.java
 * com.ximalaya.ting.android.opensdk.player.service
 * <p>
 * Function： TODO
 * <p>
 * ver     date      		author
 * ---------------------------------------
 * 2015-5-19 		chadwii
 * <p>
 * Copyright (c) 2015, chadwii All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.player.service;

import static com.ximalaya.ting.android.player.PlayerUtil.TAIHE_TAG;

import android.content.Context;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DataSpec;
import com.ximalaya.ting.android.api.AudioPlayerFactory;
import com.ximalaya.ting.android.api.IAudioPlayStatisticsCallback;
import com.ximalaya.ting.android.exoplayer.datasource.DataSourceInterceptor;
import com.ximalaya.ting.android.exoplayer.decode.TrackDecoderStrategy;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.CommonRequestForMain;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.httputil.XmCronetManager;
import com.ximalaya.ting.android.opensdk.player.manager.DataSourceChangeManager;
import com.ximalaya.ting.android.opensdk.player.pcm.PcmDataSourceForMp3;
import com.ximalaya.ting.android.routeservice.service.net.ICompositeConnectionCallback;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.opensdk.model.BluetoothStateModel;
import com.ximalaya.ting.android.opensdk.model.PlayExceptionModel;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.performance.XmLivePerformanceStatistic;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.manager.PcdnManager;
import com.ximalaya.ting.android.opensdk.player.manager.TrackUrlChooseManager;
import com.ximalaya.ting.android.opensdk.player.receive.FilterCarBluetoothDevice;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayExceptionCollector;
import com.ximalaya.ting.android.opensdk.util.ExceptionUtil;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.player.IDomainServerIpCallback;
import com.ximalaya.ting.android.player.PlayerUtil;
import com.ximalaya.ting.android.player.XMediaPlayerWrapper;
import com.ximalaya.ting.android.player.XMediaplayerImpl;
import com.ximalaya.ting.android.player.cdn.CdnUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.ICommonBusiService;
import com.ximalaya.ting.android.routeservice.service.freeflow.IFreeFlowService;
import com.ximalaya.ting.android.routeservice.service.version.IVersion;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.statistic.audio.error.XmPlayErrorStatistic;
import com.ximalaya.ting.android.util.ExoCrashUtil;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import okhttp3.CacheControl;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.Response;

/**
 * ClassName:XmMediaPlayerFactory Function: TODO ADD FUNCTION Reason: TODO ADD
 * REASON
 *
 * <AUTHOR>
 * @Date 2015-5-19 上午11:16:13
 * @see
 * @since Ver 1.1
 */
public class XmMediaPlayerFactory {

    private static boolean isUseSystemPlayer = false;
    private static boolean sRecordUseExoPlayer = false; // 是否已经上报了不是exoplayer
    private static boolean sRecordUseFFmepg = false; // 是否已经上报了不是ffmepg

    public static void setPlayerMode(boolean b) {
        isUseSystemPlayer = b;
    }

    public static XMediaplayerImpl getMediaPlayer(Context ctx) {
        return getMediaPlayer(ctx, null);
    }

    public static XMediaplayerImpl getMediaPlayer(Context ctx, DataSourceInterceptor interceptor) {
        XMediaplayerImpl impl;
        boolean useExo = isUseExo(ctx);
        if (useExo) {
            Logger.log("XmMediaPlayerFactory : 使用的是ExoPlayer ");
            ExoCrashUtil.fixExoPlayLibInfoField();
            DataSourceInterceptor dataSourceInterceptor = interceptor;
            if (interceptor == null) {
                dataSourceInterceptor = getDefaultDataSourceInterceptor();
            }
            impl = AudioPlayerFactory.createXExoMediaPlayer(ctx, dataSourceInterceptor, ConstantsOpenSdk.isDebug, new IAudioPlayStatisticsCallback() {
                @Override
                public long getCurPlayingTrackId() {
                    long trackId = -1;
                    XmPlayerService playService = XmPlayerService.getPlayerSrvice();
                    if (playService != null) {
                        PlayableModel playableModel = playService.getCurrPlayModel();
                        if (playableModel != null) {
                            return playableModel.getDataId();
                        }
                    }
                    return trackId;
                }

                @Override
                public String getCurPlayingUrl() {
                    XmPlayerService playService = XmPlayerService.getPlayerSrvice();
                    if (playService != null) {
                        PlayableModel playableModel = playService.getCurrPlayModel();
                        if (playableModel instanceof Track) {
                            return  TrackUrlChooseManager.getInstance().getTrackUrl((Track) playableModel, false);
                        }
                        return playService.getCurPlayUrl();
                    }
                    return null;
                }

                @Override
                public int getCurPlayType() {
                    XmPlayerService playService = XmPlayerService.getPlayerSrvice();
                    if (playService != null) {
                        PlayableModel playableModel = playService.getCurrPlayModel();
                        if (playableModel instanceof Track) {
                            return XmPlayListControl.getPlayType((Track) playableModel);
                        }
                    }
                    return 0;
                }

                @Override
                public void lagOccurred(String playUrl, List<Long> jankDuration, int lagCount) {
                    PcdnManager.getSingleInstance().lagOccurred(playUrl, jankDuration, lagCount);
                }

                @Override
                public Map<String, String> getExtra() {
                    Map<String, String> extra = null;
                    String playUrl = getCurPlayingUrl();
                    if (AudioPlayerFactory.sPlaybackStatsListener != null && !TextUtils.isEmpty(AudioPlayerFactory.sPlaybackStatsListener.mLastPlayUrl)) {
                        playUrl = AudioPlayerFactory.sPlaybackStatsListener.mLastPlayUrl;
                    }
                    if (playUrl != null && playUrl.contains(".flv")) {
                        extra = new HashMap<>();
                        extra.put("quic", XmCronetManager.Companion.getInstance().getUrlProtocolType(playUrl) == Protocol.QUIC ? "1" : "0");
                        extra.put("netStatus", XmCronetManager.Companion.getInstance().getNetworkRttType() + "");
                        extra.put("quicConfig", XmCronetManager.Companion.getInstance().getQuicConfig() + "");
                    }
                    ICommonBusiService commonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
                    if (commonBusiService != null) {
                        Map<String, String> result = commonBusiService.getResultForPlayer("lag");
                        if (result != null) {
                            if (extra != null) {
                                result.putAll(extra);
                            }
                            return result;
                        }
                    }
                    return extra;
                }
            });

            if (!sRecordUseFFmepg) {
                sRecordUseFFmepg = true;
                boolean useSoftDecoder = TrackDecoderStrategy.getInstance().useSoftDecoder(ctx);
                if (!useSoftDecoder) {
                    PlayExceptionModel playExceptionModel = new PlayExceptionModel();
                    playExceptionModel.useFFmpegDecode = false;
                    PlayExceptionCollector.postData(playExceptionModel);
                }
            }
        } else {
            if (!sRecordUseExoPlayer) {
                sRecordUseExoPlayer = true;
                PlayExceptionModel playExceptionModel = new PlayExceptionModel();
                playExceptionModel.useExoPlayer = false;
                PlayExceptionCollector.postData(playExceptionModel);
            }

            // FIXME 注意后面这个播放器完全去掉的时候需要将之前的缓存目录去掉
            Logger.log("XmMediaPlayerFactory : 使用的是XmPlayer ");
            impl = new XMediaPlayerWrapper(ctx, true, isUseSystemPlayer);
        }
        return impl;
    }

    public static boolean hasAssignValue = false;
    public static boolean ifUseExo = false;
    public static boolean isUseExo(Context ctx) {
        if(hasAssignValue) {
            return ifUseExo;
        }
        try {
            boolean useExoPlayer = MmkvCommonUtil.getInstance(ctx).getBoolean(PreferenceConstantsInOpenSdk.KEY_USE_EXO_PLAYER_FROM_NET, true);
            if (MmkvCommonUtil.getInstance(ctx).containsKey(PreferenceConstantsInOpenSdk.KEY_USE_EXO_PLAYER)) {
                useExoPlayer = MmkvCommonUtil.getInstance(ctx).getBoolean(PreferenceConstantsInOpenSdk.KEY_USE_EXO_PLAYER, true);
            }
            if (useExoPlayer || PlayerUtil.isX86Arch() || !PlayerUtil.isArmV7Plus()) {
                ifUseExo = true;
                hasAssignValue = true;
                return true;
            }
            hasAssignValue = true;
            recordUseNormalPlayReason(ctx);
        }catch (Throwable t) {
            t.printStackTrace();
        }
        return false;
    }

    private static void recordUseNormalPlayReason(Context ctx) {
        if (MmkvCommonUtil.getInstance(ctx).containsKey(PreferenceConstantsInOpenSdk.KEY_USE_EXO_PLAYER)) {
            if (!MmkvCommonUtil.getInstance(ctx).getBoolean(PreferenceConstantsInOpenSdk.KEY_USE_EXO_PLAYER, true)) {
                CdnUtil.statToXDCSError("normal_play_reason", "user_set");
                return;
            }
        }

        if (!MmkvCommonUtil.getInstance(ctx).getBoolean(PreferenceConstantsInOpenSdk.KEY_USE_EXO_PLAYER_FROM_NET, true)) {
            CdnUtil.statToXDCSError("normal_play_reason", "from_net");
        }
    }

    public static abstract class AbsDataSourceInterceptor implements DataSourceInterceptor {
        protected Handler mHandler = new Handler(Looper.getMainLooper());

        @Override
        public String getUserAgent() {
            return CommonRequestForMain.getUserAgent();
        }

        @Override
        public void flvDataOutput(int type, byte[] data) {
            if(XmPlayerService.getPlayerSrvice() != null) {
                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (XmPlayerService.getPlayerSrvice() == null) {
                            return;
                        }
                        XmPlayerControl playerControl = XmPlayerService.getPlayerSrvice().getPlayControl();
                        long bufferDuration = playerControl != null ? playerControl.getTotalBufferDuration() : 0;
                        mHandler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (XmPlayerService.getPlayerSrvice() != null) {
                                    XmPlayerService.getPlayerSrvice().flvDataOutputCallback(type, data, bufferDuration);
                                }
                            }
                        }, bufferDuration - 50 > 0? bufferDuration - 50 : bufferDuration);
                    }
                });

            }
        }

        @Override
        public boolean useFfmpegExtensionDecoder() {
            boolean useFfmpegExtensionDecoder = TrackDecoderStrategy.getInstance().useSoftDecoder(XmPlayerService.getPlayerSrvice());
            Logger.logToFile("XmMediaPlayerFactory : useFfmpegExtensionDecoder = " + useFfmpegExtensionDecoder);
            return useFfmpegExtensionDecoder;
        }

        @Override
        public boolean useFlacExtensionDecoder() {
            boolean useFlacExtensionDecoder = MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice())
                    .getBoolean(PreferenceConstantsInOpenSdk.KEY_USE_FLAC_SOFT_DECODER, false);
            Logger.logToFile("XmMediaPlayerFactory : useFlacExtensionDecoder = " + useFlacExtensionDecoder);
            return useFlacExtensionDecoder;
        }

        @Override
        public boolean openDumpInfo() {
            boolean openDumpInfo = MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice())
                    .getBoolean(PreferenceConstantsInOpenSdk.KEY_OPEN_PLAYER_DUMP_INFO, false);
            Logger.logToFile("XmMediaPlayerFactory : openDumpInfo = " + openDumpInfo);
            return openDumpInfo;
        }

        @Override
        public boolean retryPlayAfterFail() {
            return MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getBoolean(
                    PreferenceConstantsInOpenSdk.KEY_RETRY_PLAY_AFTER_FAIL, true);
        }

        @Override
        public boolean deleteFilesAfterDecodeFail() {
            return MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getBoolean(
                    PreferenceConstantsInOpenSdk.KEY_DELETE_FILES_AFTER_DECODE_FAIL, true);
        }

        @Override
        public void uploadInfo(String module, String dumpInfo) {
            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
            if (xdcsPost != null) {
                xdcsPost.statErrorToXDCS(module, dumpInfo);
            }
        }

        @Override
        public void exitPlayer() {
            // 这里用户每次触发，都会上报，也就是这里和exoLoadable数量是一样的，但是因为一天只能杀一次进程，
            // 因此杀进程上报后播放上报的数量是不一致的
            // 这里需要统计的是上报后以及播放上报的数量是否是一致的
            PlayExceptionModel playExceptionModel = new PlayExceptionModel();
            playExceptionModel.unlimitedLoading = true;
            PlayExceptionCollector.postData(playExceptionModel);
            XmLogger.flush(true);
            Logger.i("XmMediaPlayerFactory", "exitPlayer");

            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getBoolean(
                            PreferenceConstantsInOpenSdk.KEY_KILL_PROCESS_FOR_UNLIMITED_LOADING,
                            false)) {
                        long lastKillTime = MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getLong(
                                PreferenceConstantsInOpenSdk.KEY_KILL_PROCESS_FOR_UNLIMITED_LOADING_TIME,
                                -1);
                        long curKillTime = System.currentTimeMillis();
                        // 相差时间大于一天才会杀进程
                        if (lastKillTime < curKillTime && curKillTime - lastKillTime > 24 * 3600 * 1000) {
                            // 保存杀进程的时间
                            MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).saveLong(
                                    PreferenceConstantsInOpenSdk.KEY_KILL_PROCESS_FOR_UNLIMITED_LOADING_TIME,
                                    curKillTime);

                            Logger.i("XmMediaPlayerFactory", "kill process play");
                            android.os.Process.killProcess(android.os.Process.myPid());
                            System.exit(0);
                        }
                    }
                }
            }, 200);
        }

        @Override
        public int getIntConfig(@ConfigIntType String key, int defaultVal) {
            return MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getInt(key, defaultVal);
        }

        @Override
        public boolean getBooleanConfig(@ConfigBooleanType String key, boolean defaultVal) {
            return MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getBoolean(key, defaultVal);
        }

        @Override
        public String getStringConfig(@ConfigStringType String key, String defaultVal) {
            return MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getString(key, defaultVal);
        }



        @Override
        public void onTransferInitializing(Uri url) {
           if (checkCurrPlayModelIsLive()) {
               XmLivePerformanceStatistic.getInstance().onTransferInitializing();
           }
        }

        @Override
        public void onTransferStart(Uri url) {
            if (checkCurrPlayModelIsLive()) {
                XmLivePerformanceStatistic.getInstance().onTransferStart();
            }
        }

        @Override
        public void onStartReadFlvHeader() {
            if (checkCurrPlayModelIsLive()) {
                XmLivePerformanceStatistic.getInstance().onStartReadFlvHeader();
            }
        }

        @Override
        public void onReadFlvHeaderFinished() {
            if (checkCurrPlayModelIsLive()) {
                XmLivePerformanceStatistic.getInstance().onReadFlvHeaderFinished();
            }
        }

        @Override
        public void onStartReadFlvScriptTag() {
            if (checkCurrPlayModelIsLive()) {
                XmLivePerformanceStatistic.getInstance().onStartReadFlvScriptTag();
            }
        }

        @Override
        public void onReadFlvScriptTagFinished(int width, int height, double audiobitrate, double videobitrate) {
            if (checkCurrPlayModelIsLive()) {
                XmLivePerformanceStatistic.getInstance().onReadFlvScriptTagFinished();
            }
            if (XmPlayerService.getPlayerSrvice() != null) {
                XmPlayerService.getPlayerSrvice().flvMetaDataInfoCallback(width, height, audiobitrate, videobitrate);
            }
        }


        @Override
        public void onStartReadAudioHeader() {
            if (checkCurrPlayModelIsLive()) {
                XmLivePerformanceStatistic.getInstance().onStartReadAudioHeader();
            }
        }

        @Override
        public void onReadAudioHeaderFinished() {
            if (checkCurrPlayModelIsLive()) {
                XmLivePerformanceStatistic.getInstance().onReadAudioHeaderFinished();
            }
        }

        @Override
        public void onStartReadFlvVideoTag(int frameCount) {
            if (checkCurrPlayModelIsLive()) {
                if (frameCount == 1) {
                    XmLivePerformanceStatistic.getInstance().onStartReadFirstVideoTag();
                } else if (frameCount == 2) {
                    XmLivePerformanceStatistic.getInstance().onStartReadSecondVideoTag();
                }
            }
        }

        @Override
        public void onReadFlvVideoTagFinished(int frameCount) {
            if (checkCurrPlayModelIsLive()) {
                if (frameCount == 1) {
                    XmLivePerformanceStatistic.getInstance().onReadFirstVideoTagFinished();
                } else if (frameCount == 2) {
                    XmLivePerformanceStatistic.getInstance().onReadSecondVideoTagFinished();
                }
            }
        }


        private boolean checkCurrPlayModelIsLive() {
            XmPlayerService playService = XmPlayerService.getPlayerSrvice();
            if (playService != null) {
                PlayableModel playableModel = playService.getCurrPlayModel();
                if (playableModel instanceof Track && playableModel.isKindOfLive()) {
                    return true;
                }
            }
            return false;
        }
    }

    public static DataSourceInterceptor getDefaultDataSourceInterceptor() {
        return new AbsDataSourceInterceptor() {
            @Override
            public String getCacheDir() {
                return "new_player_cache";
            }

            @Override
            public DataSource getResponseForPcm(DataSpec dataSpec) {
                Logger.i("Ai-Agent-PcmDataSourceForMp3", "getResponseForPcm >>> "+ dataSpec.uri);
                return new PcmDataSourceForMp3(dataSpec);
            }

            @Override
            public Response getResponse(String range, boolean allowGzip, Uri originUri, long dataId, boolean skipOpt, boolean firstResponse) throws IOException {
                Uri url = originUri;
                if (!firstResponse) {
                    try {
                        String originUrl = originUri.toString();
                        String path = originUrl.replaceFirst(originUri.getHost(), PcdnManager.getSingleInstance().getPCDNDomain(originUri.getHost(), 2).first);
                        url = Uri.parse(path);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                final Uri finalUri = url;
                ICommonBusiService commonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
                if (commonBusiService != null && commonBusiService.gotoCompositeConnection()) {
                    return commonBusiService.getCompositeConnectionResponse(range, allowGzip, finalUri, dataId, skipOpt, new ICompositeConnectionCallback() {
                        @Override
                        public Response getOriResponse() throws IOException {
                            return getResponseReal(range, allowGzip, finalUri, false, dataId);
                        }
                    });
                } else {
                    return getResponseReal(range, allowGzip, finalUri, false, dataId);
                }
            }

            private Response getResponseReal(String range, boolean allowGzip, Uri uri, boolean isRetry, long dataId) throws IOException {
                if (uri == null) {
                    throw new IOException("uri 为null");
                }

                String urlStr = uri.toString();
                boolean isFlv = false;
                boolean isThirdPath = false;
                if (uri.getPath() != null) {
                    String lowerPath = uri.getPath().toLowerCase();
                    isFlv = lowerPath.contains(".flv");
                    isThirdPath = lowerPath.contains(TAIHE_TAG);
                }

                OkHttpClient okHttpClient;

                if (isFlv || isThirdPath) {
                    IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
                    if(freeFlowService != null) {
                        okHttpClient = freeFlowService.getOkHttpClientNotProxy();
                    } else {
                        okHttpClient = new OkHttpClient();
                    }
                } else {
                    URL url = null;
                    try {
                        url = new URL(urlStr);
                    } catch (MalformedURLException e) {
                        e.printStackTrace();
                    }
                    okHttpClient = BaseCall.getInstanse().getOkHttpClient(url);
                }

                Request.Builder builder = new Request.Builder().url(urlStr);
                if (!TextUtils.isEmpty(range)) {
                    builder.addHeader("Range", range);
                }
                IVersion versionCallback = RouterServiceManager.getInstance().getService(IVersion.class);
                if (versionCallback != null && versionCallback.addUniqueIdForCdn()) {
                    builder.addHeader("xmuniqueid", versionCallback.uniqueId());
                }
                builder.addHeader("User-Agent", getUserAgent());
                builder.addHeader("playType", "exo");
                if (!allowGzip) {
                    builder.addHeader("Accept-Encoding", "identity");
                    CacheControl.Builder cacheBuilder = new CacheControl.Builder();
                    cacheBuilder.noCache();
                    cacheBuilder.noStore();
                    CacheControl cacheControl = cacheBuilder.build();
                    builder.cacheControl(cacheControl);
                }
                builder.get();
                Response response = null;
                int responseCode = 0;
                Request request;
                long startTime = System.currentTimeMillis();
                try {
                    request = builder.build();
                    response = okHttpClient.newCall(request).execute();
                    responseCode = response.code();

                    Logger.logToFile("getResponse url=" + urlStr + ", range=" + range + ", dataId=" + dataId + ", isRetry=" + isRetry + ", responseCode=" + responseCode);
                } catch (Exception e) {
                    long duration = Math.abs(System.currentTimeMillis() - startTime);
                    if (duration > 15_000) {
                        Logger.logToFile("XmMediaPlayerRequest XmMediaPlayerFactory getResponseReal duration=" + (duration / 1000.0));
                    }

                    String causeName = null;
                    String message = null;
                    String trace = null;
                    String rootCauseName = null;
                    String rootMessage = null;
                    String rootTrace = null;
                    if (e != null) {
                        causeName = e.getClass().getName();
                        message = e.getMessage();
                        trace = ExceptionUtil.getCauseTrace(e);
                        Throwable rootCause = ExceptionUtil.getRootCause(e);
                        if (rootCause != null) {
                            rootCauseName = rootCause.getClass().getName();
                            rootMessage = rootCause.getMessage();
                            rootTrace = ExceptionUtil.getCauseTrace(rootCause);
                        }
                    }

                    if (XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().getCurrPlayModel() != null) {
                        XmPlayErrorStatistic.getInstance().onTrackPlayError(XmPlayerService.getPlayerSrvice().getCurrPlayModel().getDataId(), 603, XmPlayErrorStatistic.getExceptionContent(e),
                                PlayerConstants.ERROR_FROM_CDN_ERROR, causeName, message, trace, rootCauseName, rootMessage, rootTrace);
                    }

                    Logger.logToFile("getResponse exception=" + e.getMessage());

                    throw e;
                }

                if (!(responseCode >= 200 && responseCode < 400)) {
                    if (XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().getCurrPlayModel() != null) {
                        String message = null;
                        if (response != null) {
                            message = response.message();
                        }
                        XmPlayErrorStatistic.getInstance().onTrackPlayError(XmPlayerService.getPlayerSrvice().getCurrPlayModel().getDataId(), responseCode, null,
                                PlayerConstants.ERROR_FROM_CDN_ERROR_2, String.valueOf(responseCode), message, null, String.valueOf(responseCode), message, null);
                    }
                }

                if (responseCode == 403 && !isRetry) {
                    Response newResponse = updateChargePlayUrl(range, allowGzip, urlStr, dataId);
                    if (newResponse == null) {
                        Logger.logToFile("getResponse exception=更新地址失败");
                        throw new IOException("更新地址失败");
                    }
                    return newResponse;
                }

                return response;
            }

            private Response updateChargePlayUrl(String range, boolean allowGzip, String url, long dataId) throws IOException {
                if (XmPlayerService.getPlayerSrvice() != null) {
                    IDomainServerIpCallback callback = XmPlayerService.getPlayerSrvice().getIDomainServerIpCallback();
                    if (callback != null) {
                        String cTrackUrl = callback.getNewDownloadUrlIfExpire(dataId, url);

                        if (!TextUtils.isEmpty(cTrackUrl)) {
                            Logger.logToFile("updateChargePlayUrl cTrackUrl=" + cTrackUrl + ", range=" + range + ", allowGzip=" + allowGzip + ", url=" + url + ", dataId=" + dataId);
                            return getResponseReal(range, allowGzip, Uri.parse(cTrackUrl), true, dataId);
                        }
                    }
                }

                return null;
            }

            @Override
            public String getBluetoothState() {
                BluetoothStateModel model = FilterCarBluetoothDevice.getBluetoothStateModel();
                if (model == null) {
                    return null;
                }
                return model.toString();
            }

            @Override
            public boolean isHostInOptHostList(String host) {
                return DataSourceChangeManager.getInstance().isHostInOptHostList(host);
            }

            @Override
            public boolean useOptDataSource() {
                return DataSourceChangeManager.getInstance().useOptDataSource();
            }

            @Override
            public boolean isDolbySupport() {
                ICommonBusiService commonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
                if (commonBusiService != null) {
                    return commonBusiService.isDolbySupport();
                }
                return false;
            }
        };
    }

}
