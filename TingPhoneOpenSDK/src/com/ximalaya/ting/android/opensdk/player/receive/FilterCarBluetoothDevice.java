package com.ximalaya.ting.android.opensdk.player.receive;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothClass;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.ximalaya.ting.android.opensdk.model.BluetoothStateModel;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.usetrace.UseTraceCollector;
import com.ximalaya.ting.android.player.cdn.CdnUtil;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;

/**
 * Created by le.xin on 2021/9/24.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class FilterCarBluetoothDevice {
    private static BluetoothStateBroadcastReceiver mBluetoothStateBroadcastReceiver;
    private static long lastCarBluetoothConnectTime;
    private static BluetoothStateModel sBluetoothStateModel = new BluetoothStateModel();

    public interface IBluetoothStateChange {
        void onBluetoothResultCallBack(boolean isCarBlueDevice);
    }

    public static BluetoothStateModel getBluetoothStateModel() {
        return sBluetoothStateModel;
    }

    public static boolean canPlayByBluetoothCar() {
        long fromLastConnectTime = System.currentTimeMillis() - lastCarBluetoothConnectTime;
        UseTraceCollector.log("FilterCarBluetoothDevice_____fromLastConnectTime___"+fromLastConnectTime);
        return fromLastConnectTime > MMKVUtil.getInstance(PlayerConstants.FILENAME_XM_PLAYER_AUDIO_FOCUS_DATA).getInt(PlayerConstants.TINGMAIN_KEY_CAR_BLUETOOTH_CONTROL_PLAY_TIME, 3000);
    }

    public static void register(Context context) {
        if (mBluetoothStateBroadcastReceiver == null) {
            mBluetoothStateBroadcastReceiver = new BluetoothStateBroadcastReceiver();
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
            intentFilter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
            intentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
            intentFilter.addAction("android.bluetooth.BluetoothAdapter.STATE_OFF");
            intentFilter.addAction("android.bluetooth.BluetoothAdapter.STATE_ON");
            if (context != null) {
                try {
                    context.registerReceiver(mBluetoothStateBroadcastReceiver, intentFilter);
                } catch (Exception exception) {
                    exception.printStackTrace();
                }
            }
        }
    }

    public static void unRegister(Context context) {
        if (mBluetoothStateBroadcastReceiver != null) {
            try {
                context.unregisterReceiver(mBluetoothStateBroadcastReceiver);
            } catch (Exception exception) {
                exception.printStackTrace();
            }
            mBluetoothStateBroadcastReceiver = null;
        }
    }

    public static void saveCarBluetooth(String name) {
        if (TextUtils.isEmpty(name)) {
            return;
        }

        UseTraceCollector.log("FilterCarBluetoothDevice : saveCarBluetooth " + name);
        Logger.logToFile("FilterCarBluetoothDevice : saveCarBluetooth " + name);
        MMKVUtil mmkvUtil =
                MMKVUtil.getInstance(PlayerConstants.FILENAME_XM_PLAYER_AUDIO_FOCUS_DATA);
        String carBlueToothList = mmkvUtil.getString(PlayerConstants.TINGMAIN_KEY_CAR_BLUETOOTH_LIST, "");

        if(carBlueToothList == null || !carBlueToothList.contains(name)) {
            carBlueToothList += name;
            mmkvUtil.saveString(PlayerConstants.TINGMAIN_KEY_CAR_BLUETOOTH_LIST, carBlueToothList);
        }
    }

    private static boolean isCarBluetoothFromLocal(String name) {
        if (TextUtils.isEmpty(name)) {
            return false;
        }

        MMKVUtil mmkvUtil =
                MMKVUtil.getInstance(PlayerConstants.FILENAME_XM_PLAYER_AUDIO_FOCUS_DATA);
        String carBlueToothList = mmkvUtil.getString(PlayerConstants.TINGMAIN_KEY_CAR_BLUETOOTH_LIST, "");
        boolean isCardBluetooth = carBlueToothList != null && carBlueToothList.contains(name);
        UseTraceCollector.log("FilterCarBluetoothDevice________ : isCardBluetooth " + isCardBluetooth + "   name=" + name);
        return isCardBluetooth;
    }

    //检查已连接的蓝牙设备
    public static void getBluetoothConnectState(final Context context, IBluetoothStateChange bluetoothStateChange) {
        try {
            BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
                if (bluetoothStateChange != null) {
                    bluetoothStateChange.onBluetoothResultCallBack(false);
                }
                return;
            }
            int a2dp = bluetoothAdapter.getProfileConnectionState(BluetoothProfile.A2DP);
            int headset = bluetoothAdapter.getProfileConnectionState(BluetoothProfile.HEADSET);
            int health = bluetoothAdapter.getProfileConnectionState(BluetoothProfile.HEALTH);
            int flag = -1;
            if (a2dp == BluetoothProfile.STATE_CONNECTED) {
                flag = a2dp;
            } else if (headset == BluetoothProfile.STATE_CONNECTED) {
                flag = headset;
            } else if (health == BluetoothProfile.STATE_CONNECTED) {
                flag = health;
            }

            Runnable connectTimeout = new Runnable() {
                @Override
                public void run() {
                    if (bluetoothStateChange != null) {
                        bluetoothStateChange.onBluetoothResultCallBack(false);
                    }
                }
            };

            Handler handler = new Handler(Looper.getMainLooper());
            if (flag != -1) {
                bluetoothAdapter.getProfileProxy(context, new BluetoothProfile.ServiceListener() {
                    @Override
                    public void onServiceDisconnected(int profile) {
                    }

                    @Override
                    public void onServiceConnected(int profile, BluetoothProfile proxy) {
                        handler.removeCallbacks(connectTimeout);
                        List<BluetoothDevice> connectedDevices = null;
                        try {
                            connectedDevices = proxy.getConnectedDevices();
                        } catch (Exception exception) {
//                            exception.printStackTrace();
                        }
                        bluetoothAdapter.closeProfileProxy(profile, proxy);
                        try {
                            if (connectedDevices != null && connectedDevices.size() > 0) {
                                for (BluetoothDevice device : connectedDevices) {
                                    if (device != null) {
                                        UseTraceCollector.log("FilterCarBluetoothDevice __deviceList__ " + device.getName());
                                        if (isCarBluetoothFromLocal(device.getName())) {
                                            if (bluetoothStateChange != null) {
                                                bluetoothStateChange.onBluetoothResultCallBack(true);
                                            }
                                            return;
                                        }

                                        int deviceClass = device.getBluetoothClass().getDeviceClass();
                                        if (BluetoothClass.Device.AUDIO_VIDEO_CAR_AUDIO == deviceClass) {
                                            CdnUtil.statToXDCSError("CarBlueCheckError", "deviceName=" + device.getName());
                                            return;
                                        }
                                    }
                                }
                            }
                        } catch (Exception exception) {
                            exception.printStackTrace();
                        }

                        if (bluetoothStateChange != null) {
                            bluetoothStateChange.onBluetoothResultCallBack(false);
                        }
                    }
                }, flag);
            }

            handler.postDelayed(connectTimeout, 500);
        } catch (Exception exception) {
            exception.printStackTrace();

            if (bluetoothStateChange != null) {
                bluetoothStateChange.onBluetoothResultCallBack(false);
            }
        }
    }

    private static class BluetoothStateBroadcastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            updateBluetoothStateModel(intent);

            String action = intent.getAction();
            if (action == null) {
                return;
            }
            BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
            if (device == null) {
                return;
            }
            switch (action) {
                case BluetoothDevice.ACTION_ACL_CONNECTED: // 蓝牙设备已连接
                    UseTraceCollector.log("FilterCarBluetoothDevice_____蓝牙设备已连接");
                    Logger.logToFile("FilterCarBluetoothDevice_____蓝牙设备已连接");
                    try {
                        if (isCarBluetoothFromLocal(device.getName())) {
                            lastCarBluetoothConnectTime = System.currentTimeMillis();
                            UseTraceCollector.log("FilterCarBluetoothDevice_____蓝牙设备已连接___"+lastCarBluetoothConnectTime);
                            Logger.logToFile("FilterCarBluetoothDevice_____蓝牙设备已连接___" + lastCarBluetoothConnectTime);
                        }
                    } catch (Exception exception) {
                        exception.printStackTrace();
                    }
                    break;
                case BluetoothDevice.ACTION_ACL_DISCONNECTED: // 蓝牙设备已断开
                    break;
                case BluetoothAdapter.ACTION_STATE_CHANGED: // 蓝牙状态发生变化
                    int blueState = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, 0);
                    switch (blueState) {
                        case BluetoothAdapter.STATE_OFF: // 蓝牙已关闭
                            break;
                        case BluetoothAdapter.STATE_ON: // 蓝牙已开启
                            break;
                    }
                    break;
            }
        }
    }

    private static void updateBluetoothStateModel(Intent intent) {
        if (intent == null) {
            return;
        }
        String action = intent.getAction();
        if (action == null) {
            return;
        }
        BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
        String deviceName = "";
        String bluetoothType = "";
        if (device != null) {
            deviceName = device.getName();
            bluetoothType = String.valueOf(device.getType());
        }
        sBluetoothStateModel.bluetoothType = bluetoothType;
        sBluetoothStateModel.deviceName = deviceName;
        sBluetoothStateModel.time = System.currentTimeMillis();
        switch (action) {
            case BluetoothDevice.ACTION_ACL_CONNECTED: // 蓝牙设备已连接
                sBluetoothStateModel.type = BluetoothStateModel.CONNECTED;
                break;
            case BluetoothDevice.ACTION_ACL_DISCONNECTED: // 蓝牙设备已断开
                sBluetoothStateModel.type = BluetoothStateModel.DISCONNECTED;
                break;
            case BluetoothAdapter.ACTION_STATE_CHANGED: // 蓝牙状态发生变化
                int blueState = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, 0);
                switch (blueState) {
                    case BluetoothAdapter.STATE_TURNING_OFF: // 蓝牙已关闭
                    case BluetoothAdapter.STATE_OFF:
                        sBluetoothStateModel.type = BluetoothStateModel.STATE_CHANGED_OFF;
                        break;
                    case BluetoothAdapter.STATE_TURNING_ON: // 蓝牙已开启
                    case BluetoothAdapter.STATE_ON:
                        sBluetoothStateModel.type = BluetoothStateModel.STATE_CHANGED_ON;
                        break;
                    default:
                        sBluetoothStateModel.type = blueState;
                        break;
                }
                break;
        }
    }

}
