package com.ximalaya.ting.android.opensdk.player.check;

/**
 * <AUTHOR>
 * @date 2024/6/21 16:58
 */
public class PauseReason {
    public static final int NONE = 0;
    public static final int STOP_NONE = 1;

    public static final class Common {
        // 切歌暂停
        public static final int ON_SOUND_SWITCH = 10000;
        // 定时暂停
        public static final int PLAN_PAUSE = 10001;
        // 定时暂停（和上面一个业务）
        public static final int PLAN_TERMINATE_PAUSE = 10002;
        // 声音设置暂停
        public static final int PLAN_PAUSE_TRACK = 10003;
        // 儿童保护模式暂停部分声音起播
        public static final int CHILD_PROTECT_PAUSE = 10004;
        // xiaoai服务调用
        public static final int XIAOAI_ACTION_PAUSE = 10005;

        // 关闭通知栏
        public static final int NOTIFICATION_CLOSE = 10006;
        // 通知栏暂停
        public static final int NOTIFICATION_PAUSE_MAIN = 10007;
        public static final int NOTIFICATION_PAUSE = 10008;

        // 按键暂停
        public static final int KEYCODE_MEDIA_PAUSE = 10009;
        public static final int KEYCODE_MEDIA_PAUSE_2 = 10010;
        public static final int KEYCODE_MEDIA_PLAY_PAUSE = 10011;
        public static final int MEDIA_SESSION_PAUSE = 10012;

        // Widget
        public static final int WIDGET_PAUSE_CLICK = 10013;
        // 用不太到
        public static final int PAUSE_AND_CLOSE_NOTIFICATION = 10014;
        public static final int RELEASE = 10015;

        // 权限类
        public static final int CODE_927 = 10016;
        public static final int CODE_929 = 10017;
        public static final int CODE_707 = 10018;
        // 没有版权
        public static final int NO_COPYRIGHT = 10019;
        // 已下架
        public static final int HAS_REMOVED = 10020;
        public static final int CHILD_PROTECT_PAUSE_MAIN = 10021;
        public static final int NET_CONFIRM = 10022;
        public static final int NET_CONFIRM_2 = 10023;
        public static final int SubscribeFreeAuthorized = 10024;

        public static final int CarBundle_WeLink = 10025;

        //有声广告正在播放
        public static final int SOUND_AD_PLAYING = 10026;
    }

    public static final class AudioFocus {
        // 失去音频焦点
        public static final int AUDIOFOCUS_LOSS = 20001;
        public static final int AUDIOFOCUS_LOSS_TRANSIENT = 20002;

        public static final int AUDIOFOCUS_LOSS_BY_OUTSIDE = 20005;
        public static final int AUDIOFOCUS_LOSS_TRANSIENT_BY_OUTSIDE = 20006;

        public static final int AUDIOFOCUS_LOSS_INNER = 20007;
        public static final int AUDIOFOCUS_LOSS_TRANSIENT_INNER = 20008;
        public static final int AUDIOFOCUS_LOSS_INNER_PLAYER = 20009;
        public static final int AUDIOFOCUS_LOSS_TRANSIENT_INNER_PLAYER = 20010;

        // 断开耳机
        public static final int ACTION_AUDIO_BECOMING_NOISY = 20003;
        // 响铃
        public static final int CALL_STATE = 20004;
    }

    public static final class Ad {
        // 声音播放过程中插入广告
        public static final int PLAY_AD_IN_PROGRESS = 30001;
        // 声音播放过程中插入广告2
        public static final int PLAY_AD_IN_PROGRESS_2 = 30002;
        // 畅听贴片（提前1h提示音贴）
        public static final int FREE_LISTEN_TEN_MINUTE_LEFT_V2 = 30003;
        // 畅听贴片（时长用完）
        public static final int FREE_LISTEN_NO_TIME_LEFT = 30004;
        // 畅听贴片V2（时长用完）
        public static final int FREE_LISTEN_NO_TIME_LEFT_V2 = 30005;
        // 头贴广告
        public static final int HEAD_SOUND_AD = 30006;
        // 头贴广告2
        public static final int HEAD_SOUND_AD_2 = 30007;
        // 时长解锁
        public static final int FREE_TIME_UNLOCK_NO_TIME_LEFT = 30008;
        // 全站畅听5期
        public static final int AD_UNLOCK_VIP_TIME = 30009;
        // 看视频免广告
        public static final int LockRewardFreeAd = 30010;
        // 全屏视频广告页面
        public static final int FullVideoAdPage = 30011;
    }

    public static final class SoundPatch {
        // 起播贴片
        public static final int SOUND_PATCH_START = 40001;
        // 中插贴片
        public static final int SOUND_PATCH_MIDDLE = 40002;
        // 本地贴片
        public static final int IMMEDIATE_SOUND_PATCH = 40003;
        public static final int NET_PATCH_COMPLETE = 40004;
    }

    public static final class KidMode {
        // 时长达到一定值
        public static final int TIME_DURATION_REACHED = 50001;
        // 达到指定时间段（22-6）
        public static final int TIME_SLOT_REACHED = 50002;
    }

    public static final class Business {
        // 播客待播
        public static final int PODCAST_PLAYLIST = 60001;
        // 待播
        public static final int TO_LISTEN_PLAYLIST = 60002;
        public static final int TO_LISTEN_PLAYLIST_1 = 60003;

        // 首页爆款运营内容大卡（庆余年）
        public static final int RecommendExplosiveContent = 60004;
        public static final int RecommendExplosiveContent_Old = 60005;
        // 首页声音条
        public static final int RecommendTrackNew = 60006;
        // 首页视频条
        public static final int RecommendVideoCard = 60007;
        // 首页视频条small
        public static final int RecommendVideoSmallCard = 60008;
        // 专辑页视频
        public static final int AlbumShortVideo = 60009;
        // 个人页-最近收听模块
        public static final int AnchorSpaceRecentListen = 60010;
        // 分类页-直播条
        public static final int ChannelLiveVideo = 60011;
        // 推荐卡片页面
        public static final int RecommendAlbumCard2023 = 60012;
        // 精彩片段制作页面
        public static final int SoundHightsProduce = 60013;
        // Kacha
        public static final int Kacha = 60014;
        // VoiceWake
        public static final int VoiceWake = 60015;
        // 自制页
        public static final int UniversalCustomAlbum = 60016;
        // 声音签名录制页面
        public static final int CreateVoiceSignature = 60017;
        // NewShowNotes
        public static final int NewShowNotesDetail = 60018;
        // 播放页ai文稿
        public static final int PlayAiDoc = 60019;
        // 播放页-视频
        public static final int VideoPlayDetail = 60020;
        public static final int VideoPlayDetail_Y = 60021;
        // 视频播放页 Old
        public static final int VideoPlayDetail_Old = 60022;
        // 播放页-沉浸式视频模式
        public static final int ImmersiveModeVideo = 60023;
        public static final int ImmersiveModeVideo_Detail = 60024;
        public static final int ImmersiveModeVideo_Switch = 60025;
        // 播放页-播客进度条
        public static final int TrackMarkProgress = 60026;
        // 分类聚合榜声音适配器
        public static final int CategoryRanTrackItem = 60027;
        // 新版话题详情页
        public static final int TagContent = 60028;
        // 我的喜欢页面
        public static final int MyLikeV2 = 60029;
        // 听更新
        public static final int EveryDayUpdate = 60030;
        // 文档阅读器
        public static final int DocReadTtsGuide = 60031;
        // 搜索精选页
        public static final int SearchListen = 60032;
        public static final int SearchTrackCard = 60033;
        // 搜索头部广告
        public static final int SearchTopBrandAdNew = 60034;
        // 搜索AI 内容
        public static final int SearchAiContent = 60035;
        // 老年模式搜索声音条
        public static final int SearchTrackElderlyMode = 60036;
        // chatxmly
        public static final int ChatXmly = 60037;
        // VideoTest
        public static final int VideoTest = 60038;
        // GaiaX pause
        public static final int GaiaXPause = 60039;
        // QQ小游戏
        public static final int QQGameAction = 60040;
        // 爸爸妈妈讲故事
        public static final int ChildAiSound = 60041;
        // vip精选页banner
        public static final int VipPublicityVipBanner = 60042;
        // 会员页预约模块
        public static final int SimpleAuditionVipReservation = 60043;
        // 动态页视频
        public static final int DynamicShortVideoDetail = 60044;
        // 短音频
        public static final int ShortAudio = 60045;
        // ShortVideo
        public static final int ShortVideo = 60046;
        // TopicVideo
        public static final int TopicVideo = 60047;
        // FeedTrackItem
        public static final int FeedTrackItem = 60048;
        // CalabashLineGotoLive
        public static final int CalabashLineGotoLive = 60049;
        public static final int AnchorSpace_TrackComment = 60050;
        public static final int AnchorSpace_MyTrackComment = 60051;
        public static final int RecommendTrack_1 = 60052;
        public static final int RecommendTrackNew_1 = 60053;
        public static final int NewDailyRecommend = 60054;
        // 专辑页
        public static final int AlbumNewList2 = 60055;
        public static final int AlbumNewList = 60056;
        public static final int AnchorTrackComment = 60057;
        public static final int NewRecommendTrack = 60058;
        public static final int RecommendTrackAndVideo = 60059;
        public static final int ScenerySpotTrack = 60060;
        public static final int CategoryRecommendFeed = 60061;
        public static final int CategoryRecommendNewSoundGroup = 60062;
        public static final int CategoryRecommendSoundGroup = 60063;
        public static final int CategoryRecommendTopicTrack = 60064;
        public static final int SelectedHotCommentCard = 60065;
        public static final int MyLikeTrack = 60066;
        public static final int TingListDetail = 60067;
        public static final int DubbingPlayNew = 60068;
        public static final int DubFeedAutoPlay = 60069;
        public static final int ImageDubView = 60070;
        public static final int FindRecommendStream = 60071;
        public static final int AIChatHomePage = 60072;
        public static final int DailySignPage = 60073;
        public static final int RecommendAlbumCard = 60074;
        public static final int ChildProtectionSetting = 60075;
        public static final int ChildProtectionPassWord = 60076;
        public static final int KidsProtectionPassWord = 60077;
        public static final int AlarmSetting = 60078;
        public static final int SleepAutoTerminate = 60079;
        public static final int WifiConnect = 60080;
        public static final int KachaPostShare = 60081;
        public static final int AlbumDailyRecommend = 60082;
        public static final int DlnaPush = 60083;
        public static final int ITingGotoPlayPagePause = 60084;
        public static final int PlayletDetail = 60085;
        public static final int DailNews4Normal = 60086;
        public static final int TrackCommentDetail = 60087;
        public static final int OneKeyNewPlus = 60088;
        public static final int AudioPlayStartKaCha = 60089;
        public static final int AudioPlayPage_InteractComponent = 60090;
        public static final int AudioPlayPage_SeekBarComponent = 60091;
        public static final int AudioPlayV2StartKaCha = 60092;
        public static final int AudioPlayPageV2_InteractComponent = 60093;
        public static final int AudioPlayPageV2_SeekBarComponent = 60094;
        public static final int AudioPlayPage_PlayManuscriptTab = 60095;
        public static final int AudioPlayPage_PlayTtsTab = 60096;
        public static final int AudioPlayPage_VideoPlayTab = 60097;
        public static final int AudioPlayPageX_PlayAd = 60098;
        public static final int AudioPlayPageX_PlayAdForAnchor = 60099;
        public static final int AudioPlayPageY_PlayAd = 60100;
        public static final int AudioPlayPageY_PlayAdForAnchor = 60101;
        public static final int AudioPlayPage_BottomView = 60102;
        public static final int AudioPlayPage_BottomViewV2 = 60103;
        public static final int AudioPlayPage_GaiaXVideoView = 60104;
        public static final int SearchPaidTrack = 60105;
        public static final int SearchTopBrandAd = 60106;
        public static final int SearchDirect = 60107;
        public static final int SearchPlayAlbum = 60108;
        public static final int VideoController = 60109;
        public static final int CartonVideoController = 60110;
        public static final int DubVideoController = 60111;
        public static final int DynamicDetailVideoController = 60112;
        public static final int PlayTabVideoController = 60113;
        public static final int AlarmScreenActivity = 60114;
        public static final int BasePlayActivity = 60115;
        public static final int AbstractAlbumTrack = 60116;
        public static final int AbstractTrack = 60117;
        public static final int CommentEmotionSelector = 60118;
        public static final int DriveModeFindV3 = 60119;
        public static final int DriveModeActivityV2 = 60120;
        public static final int DriveModeActivityV3 = 60121;
        public static final int JsAudioModule = 60122;
        public static final int AdHybridFragment = 60123;
        public static final int BaseBackgroundAudioAction = 60124;
        public static final int ListenEarnRewardCoinAction = 60125;
        public static final int JsSdkGPlayerPauseAction = 60126;
        public static final int ElderlyModePause = 60127;
        public static final int SimpleMediaPlayer = 60128;
        public static final int UserInfoManagerPause = 60129;
        public static final int ForwardVideo = 60130;
        public static final int AdGameCenterPlayerFloatView = 60131;
        public static final int AdUnlockPaidManager = 60132;
        public static final int AdUnlockPaidManagerV2 = 60133;
        public static final int AdUnlockPaidManagerV3 = 60134;
        public static final int ChildProtectManager = 60135;
        public static final int FreeFlowService = 60136;
        public static final int AdMakeVipLocalmanager = 60137;
        public static final int PaidVoiceAlert = 60138;
        public static final int NetworkChangeDownloadAndPause = 60139;
        public static final int ShortVideoVoicePause = 60140;
        public static final int AlarmReceiverPause = 60141;
        public static final int AdVideoView = 60142;
        public static final int CommentTimeView = 60143;
        public static final int EmotionSelector = 60144;
        public static final int RadioLivePlayBtnClick = 60145;
        public static final int RecommendTrackInRecommendCard = 60146;
        public static final int RecommendAlbumCardC = 60147;
        public static final int RecommendAlbumCardNew = 60148;
        public static final int RecommendTrackRecommendCard = 60149;
        public static final int OneKeyPlayList = 60150;
        public static final int OneKeyPlayListNew = 60151;
        public static final int AbstractPodCastModule = 60152;
        public static final int RecommendCardListTrack = 60153;
        public static final int WholeAlbumTrack = 60154;
        public static final int CommendAdView = 60155;
        public static final int CommendAdViewX = 60156;
        public static final int AlbumDetailView2 = 60157;
        public static final int PPTPlayController = 60158;
        public static final int AiChatPlayerComponent = 60159;
        public static final int PodCastImmersiveAudioPlayer = 60160;
        public static final int NewUserRecommend = 60161;
        public static final int OneKeyPlayDetail = 60162;
        public static final int DlnaAction = 60163;
        public static final int MoreAction = 60164;
        public static final int AudioPlayPage_FloatingControlBarComponent = 60165;
        public static final int AudioPlayPage_CheckSubscribeUnlock = 60166;
        public static final int AudioPlayPage_LoadData = 60167;
        public static final int SearchSuggestWord = 60168;
        public static final int SearchAdView = 60169;
        public static final int PlayBarNew = 60170;
        public static final int VideoPlayManager_OpenVoice = 60171;
        public static final int AlbumReminder = 60172;
        public static final int ToBePlayedWithMine = 60173;
        public static final int ToBePlayedWithPlayer = 60174;
        public static final int RecommendChasingForUpdateList_SocialListAlbum = 60175;
        public static final int RecommendRankList = 60176;
        public static final int RecommendSocialListenList = 60177;
        public static final int PodCastTagList = 60178;
        public static final int PodPlayHelper = 60179;
        public static final int ChannelTrackListenerCell = 60180;
        public static final int WholeAlbumTrackDetail = 60181;
        public static final int PlayCompleteRecommend = 60182;
        public static final int DailyNew3 = 60183;
        public static final int DailyNew4Child = 60184;
        public static final int TopicDetail = 60185;
        public static final int SearchQuestionAnswerCard = 60186;
        public static final int ChatXmlyPopupDialog = 60187;
        public static final int ChatXmlyPopupWindow = 60188;
        public static final int ChatXmlySpeaker = 60189;
        public static final int ChatXmlyTrack = 60190;
        public static final int VipFeedTrackItem = 60191;
        public static final int VipFeedBackRealTime = 60192;
        public static final int FeedPlayPanel = 60193;
        public static final int DailyNews3PanelForManuscript = 60194;
        public static final int DailyNewsPlayOrPauseView = 60195;
        public static final int CommentInputPlayBar = 60196;
        public static final int CreateFindDynamicNew = 60197;
        public static final int CreateFindDynamic = 60198;
        public static final int PPTPlayerView = 60199;
        public static final int PPTPlayerViewNew = 60200;
        public static final int FeedPlayCard = 60201;
        public static final int DailyNewsDetail = 60202;
        public static final int CommentList = 60203;
        public static final int AudioPlayUtil_PlayOrPause = 60204;
        public static final int AudioPlayUtilV2_PlayOrPause = 60205;
        public static final int TingReadFloatingConComponent = 60206;
        public static final int PlayTtsBottomControl = 60207;
        public static final int AdSdkJumpStrategy = 60208;
        public static final int LiveMediaPlayer = 60209;
        public static final int LiveAudienceHeader = 60210;
        public static final int VideoShareUtil = 60211;
        public static final int PlayerBridgeModule = 60212;
        public static final int LiveUtil = 60213;
        public static final int LiveStartUtil = 60214;
        public static final int BaseRoomFragment = 60215;
        public static final int LiveFunctionActionImpl = 60216;
        public static final int RnBusinessModule = 60217;
        public static final int RnPlayerModule = 60218;
        public static final int BusinessInfoModule = 60219;
        public static final int SearchPaidTrackAdapterV2 = 60220;
        public static final int LiveAudienceRoomFragment = 60221;
        public static final int LiveDiscoverContainerFragment = 60222;
        public static final int RecommendIpNewContainerAdapter = 60223;
        public static final int ChannelTrackRow = 60224;
        public static final int SearchAlbumCard2024 = 60225;
        public static final int LiveRoomBaseFragment = 60226;
        public static final int CreateXimaLiveFragment = 60227;
        public static final int RadioFragmentNew = 60228;
        public static final int SearchAlbumAdapter2024 = 60229;
        public static final int VideoFullscreenDialogFragment = 60230;
        public static final int VIDEO_CATEGORY_V2_FRAGMENT = 60231;
        public static final int SeatchAiTabPage = 60232;
        public static final int ANCHOR_ALL_TRACK = 60233;
        public static final int CategorySecondPage = 60234;
        public static final int MiniDrama = 60235;
        public static final int ClientFlow = 60236;
        public static final int AI_RADIO = 60236;
    }

    public static final class StopBusiness {
        public static final int TingLocal_FreeListenTimeRunOut = 70000;
        public static final int TingLocal_NO_AUTHORIZED = 70001;
        public static final int TingLocal_NO_CHILD_VIP = 70002;
        public static final int TingLocal_NO_AUTHORIZED_OLD = 70003;
        public static final int TingLocal_NO_PERMISSION = 70004;
        public static final int RnPlayerModule = 70005;
        public static final int XiaoAiControl = 70006;
        public static final int XiaoAiControlUtil = 70007;
        public static final int XmPlayServiceDestroy = 70008;
        public static final int RadioStop = 70009;
        public static final int TingLocal_CHECK_DOWNLOAD = 70010;
    }
}
