package com.ximalaya.ting.android.opensdk.player.manager

import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.routeservice.RouterServiceManager
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForPlay

/**
 * Created by WolfXu on 2021/9/20.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
object LivePutIntoHistoryManager {

    private var mLastPlayingLive: Track? = null
    private var mHistoryManager: IHistoryManagerForPlay? = null

    fun recordLastPlayingLive(playableModel: PlayableModel?) {
        val track = playableModel as? Track
        if (track?.isKindOfLive == true) {
            mLastPlayingLive = track
        } else {
            mLastPlayingLive = null
        }
    }

    fun putLastPlayingLiveIntoHistoryIfNeeded(lastPlayModel: PlayableModel?, currPlayingModel: PlayableModel?) {
        // 产品需求直播只有在退出的时候才进播放历史
        // 然后在有新的播放内容过来时，判断下还是不是直播，不是就把前面记录的直播加到播放历史了
        // 9.0.36直播不再入播放历史，转到浏览历史
//        if (currPlayingModel?.isKindOfLive != true) {
//            if (lastPlayModel is Track && lastPlayModel.isKindOfLive) {
//                getHistoryManager()?.putSound(lastPlayModel)
//                mLastPlayingLive = null
//            } else if (mLastPlayingLive != null) {
//                getHistoryManager()?.putSound(mLastPlayingLive)
//                mLastPlayingLive = null
//            }
//        }
    }

    private fun getHistoryManager(): IHistoryManagerForPlay? {
        if (mHistoryManager == null) {
            mHistoryManager = RouterServiceManager.getInstance().getService(IHistoryManagerForPlay::class.java)
        }
        return mHistoryManager
    }
}