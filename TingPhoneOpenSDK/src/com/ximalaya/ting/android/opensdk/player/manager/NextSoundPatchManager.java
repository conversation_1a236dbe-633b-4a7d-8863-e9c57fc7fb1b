package com.ximalaya.ting.android.opensdk.player.manager;

import android.content.Context;

import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.ICommonBusiService;

/**
 * Created by WolfXu on 2023/12/11.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class NextSoundPatchManager {

    private static final int MIN_DURATION_DEFAULT = 5 * 60;

    private long mLastRequestPatchId;
    private int mGap = 0;
    private int mMinDuration = MIN_DURATION_DEFAULT;
    private boolean mHasLoadConfig;
    private long mLastRequestPatchTime = 0;

    private NextSoundPatchManager() {
    }

    public void onSoundSwitch(PlayableModel curModel, PlayableModel lastModel) {
        mLastRequestPatchId = 0;
    }

    private static class NextSoundPatchManagerHolder {
        static NextSoundPatchManager sInstance = new NextSoundPatchManager();
    }

    public static NextSoundPatchManager getInstance() {
        return NextSoundPatchManagerHolder.sInstance;
    }

    public void onPlayProgress(Context context, PlayableModel curData, int currPos, int duration) {
        if (curData instanceof Track && (duration - currPos) < 3000 && PlayableModel.KIND_TRACK.equals(curData.getKind()) &&
                mLastRequestPatchId != curData.getDataId() && checkGapAndDuration(context, (Track) curData)
                && !SoundPatchManager.getInstance().curPlaySoundHasPatch()) {
            mLastRequestPatchTime = System.currentTimeMillis();
            mLastRequestPatchId = curData.getDataId();
            XmPlayerService service = XmPlayerService.getPlayerSrvice();
            if (service != null && service.mListControl != null) {
                Track nextTrack = service.mListControl.getNextPlayTrack();
                if (nextTrack != null) {
                    long nextSoundAlbumId = 0;
                    String nextSoundTitle = nextTrack.getTrackTitle();
                    if (nextTrack.getAlbum() != null) {
                        nextSoundAlbumId = nextTrack.getAlbum().getAlbumId();
                    }
                    ICommonBusiService iCommonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
                    if (iCommonBusiService != null) {
                        iCommonBusiService.registerNextSoundPatch(curData.getDataId(), nextSoundAlbumId, nextSoundTitle);
                    }
                }
            }
        }
    }

    private boolean checkGapAndDuration(Context context, Track track) {
        if (!mHasLoadConfig) {
            mHasLoadConfig = true;
            mGap = MmkvCommonUtil.getInstance(context).getInt(PreferenceConstantsInOpenSdk.KEY_NEXT_SOUND_PATCH_GAP, 0);
            mMinDuration = MmkvCommonUtil.getInstance(context).getInt(PreferenceConstantsInOpenSdk.KEY_NEXT_SOUND_MIN_DURATION, MIN_DURATION_DEFAULT);
        }
        if (track.getDuration() < mMinDuration) {
            return false;
        }
        return (System.currentTimeMillis() - mLastRequestPatchTime) > mGap * 60000L;
    }
}
