package com.ximalaya.ting.android.opensdk.player.pcm

import android.net.Uri
import com.google.android.exoplayer2.C
import com.google.android.exoplayer2.upstream.DataSource
import com.google.android.exoplayer2.upstream.DataSpec
import com.google.android.exoplayer2.upstream.TransferListener
import com.ximalaya.ting.android.routeservice.RouterServiceManager
import com.ximalaya.ting.android.routeservice.service.agent.IAgentService
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmutil.Logger
import java.io.File
import java.io.RandomAccessFile
import java.nio.ByteBuffer

class PcmDataSourceForMp3(private val dataSpec: DataSpec) : DataSource {
    companion object {
        private const val BUFFER_SIZE = 1024 * 8
        private const val TAG = "Ai-Agent-PcmDataSourceForMp3"
    }

    private var raf: RandomAccessFile? = null
    private var readPosition = 0L
    private var lastFileSize = 0L
    private var readBuffer: ByteBuffer = ByteBuffer.allocate(BUFFER_SIZE)
    private var bufferPosition = 0
    private var bufferLimit = 0
    private var partId = 0
    private var messageId = ""
    private var filePath = ""
    private val lock = Any()
    private var agentService: IAgentService? = null

    init {
        agentService = RouterServiceManager.getInstance().getService(IAgentService::class.java)
        open(dataSpec)
    }

    override fun open(dataSpec: DataSpec): Long {
        synchronized(lock) {
            dlog("Opening new file: ${dataSpec.uri}")
            val params = dataSpec.uri.toString()
                .removePrefix("pcm://")
                .split('&')
                .associate { it.split('=', limit = 2).let { (k, v) -> k to v } }

            filePath = params["url"] ?: ""
            partId = params["partId"]?.toIntOrNull() ?: 0
            messageId = params["messageId"]?: ""

            dlog("File path: $filePath, partId: $partId")

            try {
                val file = File(filePath)
                if (!file.exists()) {
                    dlog("File not found: $filePath")
                    file.parentFile?.mkdirs()
                    file.createNewFile()
                }
                raf = RandomAccessFile(file, "r")
                lastFileSize = file.length()
                readPosition = 0
                dlog("File opened - size: $lastFileSize, initial position: $readPosition")
                return C.LENGTH_UNSET.toLong()
            } catch (e: Exception) {
                dlog("Failed to open file: ${e.message}")
                close()
                return 0
            }
        }
    }

    override fun read(target: ByteArray, offset: Int, length: Int): Int {
//        dlog("Read - offset: $offset, length: $length")
        val currentRaf = raf ?: return C.RESULT_END_OF_INPUT

        synchronized(lock) {
            lastFileSize = File(filePath).length()
            if (readPosition >= lastFileSize) {
                val writeSplit =
                    MMKVUtil.getInstance().getString("key_ai_radio_part_write_finish_id", "")
                        .split("&")
                val finishMessageId = writeSplit.getOrNull(0)?:""
                val finishPartId = writeSplit.getOrNull(1)?.toIntOrNull() ?: -1

                if (finishMessageId == messageId && (finishPartId == -1 || partId <= finishPartId)) {
                    dlog("Read finished - position: $readPosition, size: $lastFileSize, finishPartId: $finishPartId")
                    return C.RESULT_END_OF_INPUT
                }
//                dlog("read no data")
                return 0
            }

            var totalRead = 0
            var remaining = length
            var currentOffset = offset

            while (remaining > 0 && readPosition < lastFileSize) {
                if (bufferPosition >= bufferLimit) {
                    val bytesToRead = minOf(BUFFER_SIZE, (lastFileSize - readPosition).toInt())
                    if (bytesToRead <= 0) break

                    currentRaf.seek(readPosition)
                    readBuffer.clear()
                    val actualRead = currentRaf.read(readBuffer.array(), 0, bytesToRead)
                    if (actualRead <= 0) break

//                    dlog("Buffer refilled - bytesRead: $actualRead, position: $readPosition")

                    bufferPosition = 0
                    bufferLimit = actualRead
                    readPosition += actualRead
                }

                val bytesToCopy = minOf(remaining, bufferLimit - bufferPosition)
                System.arraycopy(
                    readBuffer.array(),
                    bufferPosition,
                    target,
                    currentOffset,
                    bytesToCopy
                )

                bufferPosition += bytesToCopy
                currentOffset += bytesToCopy
                remaining -= bytesToCopy
                totalRead += bytesToCopy
            }
//            dlog("Read - totalRead: $totalRead, position: $readPosition, size: $lastFileSize")
            return totalRead
        }
    }

    override fun getUri(): Uri? = dataSpec.uri

    override fun close() {
        synchronized(lock) {
            try {
                dlog("close file: $filePath")
                raf?.close()
                raf = null
                readBuffer.clear()
                bufferPosition = 0
                bufferLimit = 0
            } catch (e: Exception) {
                dlog("Close error: ${e.message}")
            }
        }
    }

    override fun addTransferListener(transferListener: TransferListener) {}

    private fun dlog(msg: String) {
        Logger.i(TAG, msg)
        agentService?.log(TAG, msg)
    }
}