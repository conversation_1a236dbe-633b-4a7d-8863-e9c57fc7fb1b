package com.ximalaya.ting.android.opensdk.player;

import android.content.ComponentName;
import android.content.Context;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.IBinder;

import com.ximalaya.ting.android.opensdk.player.service.IXmPlayer;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;

import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Created by jack.qin on 2017/6/12.
* <AUTHOR>
 */

public class XmPlayerManagerForPlayer {
    private volatile static XmPlayerManagerForPlayer sInstance;
    private Context mAppCtx;
    private boolean mBindRet = false;
    private IXmPlayer mPlayerStub;
    private CopyOnWriteArrayList<IConnectListener> connectListeners = new CopyOnWriteArrayList<>();

    public static XmPlayerManagerForPlayer getInstance(Context context) {
        if (sInstance == null) {
            synchronized (XmPlayerManagerForPlayer.class) {
                if (sInstance == null)
                    sInstance = new XmPlayerManagerForPlayer(context);
            }
        }
        return sInstance;

    }

    private XmPlayerManagerForPlayer(Context context) {
        this.mAppCtx = context.getApplicationContext();
    }

    private ServiceConnection mConn = new ServiceConnection() {

        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            mPlayerStub = IXmPlayer.Stub.asInterface(service);
            if (connectListeners != null) {
                for (IConnectListener iConnectListener : connectListeners) {
                    iConnectListener.onConnected();
                }
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {

        }
    };

    private void addOnConnectedListerner(IConnectListener l) {
        if (l != null && !connectListeners.contains(l)) {
            connectListeners.add(l);
        }
    }

    public void init(IConnectListener l) {
        addOnConnectedListerner(l);
        init();
    }

    public static void unBind() {
        if (sInstance != null && sInstance.mAppCtx != null && sInstance.mConn != null && sInstance.mPlayerStub != null
                && sInstance.mPlayerStub.asBinder() != null
                && sInstance.mPlayerStub.asBinder().isBinderAlive()) {
            sInstance.mAppCtx.unbindService(sInstance.mConn);
        }
    }

    public void init() {
        try {

            boolean isStartForeground = false;

            if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {

                mAppCtx.startForegroundService(XmPlayerService.getIntent(mAppCtx,true));

                isStartForeground = true;
            }else{

                mAppCtx.startService(XmPlayerService.getIntent(mAppCtx,false));
            }

            mBindRet = mAppCtx.bindService(XmPlayerService.getIntent(mAppCtx,isStartForeground),
                    mConn, Context.BIND_AUTO_CREATE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public interface IConnectListener {
        public void onConnected();
    }

    public static void release() {
        if (sInstance != null && sInstance.connectListeners != null) {
            sInstance.connectListeners.clear();
        }
    }
}
