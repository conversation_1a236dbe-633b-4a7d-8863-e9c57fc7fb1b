package com.ximalaya.ting.android.opensdk.player.receive

import android.content.Intent
import androidx.annotation.Nullable
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants
import com.ximalaya.ting.android.opensdk.datatrasfer.CommonRequestForMain
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.push.PushGuardPlayerManager
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService

/**
 * Created by nali on 2022/3/3.
 * <AUTHOR>
 */
object NotificationLikeManager {
    private var isRequesting = false
    private var mLastRequestTrackId: Long = 0

    fun changeLikeState() {
        val currPlayModel = PushGuardPlayerManager.getInstance().currentPlayableModel
        if (currPlayModel is Track && PlayableModel.KIND_TRACK == currPlayModel.getKind() && CommonRequestForMain.hasLogin()) {
            if (currPlayModel.getDataId() == mLastRequestTrackId && isRequesting) {
                return
            }
            mLastRequestTrackId = currPlayModel.getDataId()
            isRequesting = true

            CommonRequestForMain.traceNotifyLike(currPlayModel.isLike)

            CommonRequestForMain.likeSound(
                currPlayModel.getDataId(),
                !currPlayModel.isLike,
                object : IDataCallBack<Boolean?> {
                    override fun onSuccess(data: Boolean?) {
                        isRequesting = false
                        if (data != null && data) {
                            currPlayModel.isLike =
                                !currPlayModel.isLike
                            onTrackLikeStateChange()
                        }
                    }

                    override fun onError(code: Int, message: String) {
                        isRequesting = false
                    }
                })
        }
    }

    fun onTrackLikeStateChange() {
        updateNotifyOnLikeStateChange()
        val track = PushGuardPlayerManager.getInstance().currentPlayableModel
        if (track is Track && PlayableModel.KIND_TRACK == track.getKind()) {
            notifyLikeStateChange(track)
        }
    }

    private fun updateNotifyOnLikeStateChange() {
        val service = XmPlayerService.getPlayerSrvice() ?: return
        service.updateRemoteViewOnLikeStateChange()
    }

    private fun notifyLikeStateChange(track: Track?) {
        if (XmPlayerService.getPlayerSrvice() != null && track != null) {
            val intent = Intent(DTransferConstants.ACTION_LIKE_STATE_CHANGE)
            intent.putExtra(DTransferConstants.PARAM_LIKE_TRACK_ID, track.dataId)
            intent.putExtra(DTransferConstants.PARAM_LIKE_TRACK_STATE, track.isLike)
            XmPlayerService.getPlayerSrvice()?.sendBroadcast(intent)

            XmPlayerService.getPlayerSrvice()?.sendPlayLikeStateChange()
        }
    }

    fun checkLikeStateChange(@Nullable oldTrack: Track,@Nullable newTrack: Track) : Boolean {
        if (oldTrack == null || newTrack == null) {
            return false
        }

        return oldTrack.isLike != newTrack.isLike
    }

    fun onUseLoginCheckIsLike() {
        val playerSrvice = XmPlayerService.getPlayerSrvice()
        if (playerSrvice != null && playerSrvice.currPlayModel is Track && PlayableModel.KIND_TRACK == playerSrvice.currPlayModel.kind) {
            val trackId = playerSrvice.currPlayModel.dataId
            CommonRequestForMain.getTrackInfo(playerSrvice.currPlayModel as Track, object : IDataCallBack<Track> {
                override fun onSuccess(data: Track?) {
                    if(data == null) {
                        return
                    }

                    val playerSrvice = XmPlayerService.getPlayerSrvice()
                    if (playerSrvice != null && playerSrvice.currPlayModel is Track && playerSrvice.currPlayModel.dataId == trackId) {
                        var track = playerSrvice.currPlayModel as Track
                        if(track.isLike != track.isLike) {
                            track.isLike = data.isLike
                            onTrackLikeStateChange()
                        }
                    }
                }

                override fun onError(code: Int, message: String?) {

                }
            })
        }
    }
}