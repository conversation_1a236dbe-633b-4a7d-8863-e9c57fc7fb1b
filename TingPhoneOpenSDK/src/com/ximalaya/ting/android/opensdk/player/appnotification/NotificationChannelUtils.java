package com.ximalaya.ting.android.opensdk.player.appnotification;

import android.annotation.TargetApi;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.util.HashSet;
import java.util.List;

/**
 * Created by kevin.fang on 2019/1/28
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @tel 15671641463
 */
public class NotificationChannelUtils {

    //notification channel
    private static final String PLAYER_CHANNEL_VERSION = "#2";
    private static final String PLAYER_CHANNEL_VERSION_FOR_OPPO = "#3";
    private static final String PLAYER_CHANNEL_ID = "player" + PLAYER_CHANNEL_VERSION;
    private static final String PLAYER_CHANNEL_ID_FOR_OPPO = "player" + PLAYER_CHANNEL_VERSION_FOR_OPPO;
    private static final String PLAYER_CHANNEL_NAME = "播放器控制栏";
    private static final int PLAYER_CHANNEL_IMPORTANCE = NotificationManagerCompat.IMPORTANCE_LOW;
    private static final int PLAYER_CHANNEL_IMPORTANCE_FOR_OPPO = NotificationManagerCompat.IMPORTANCE_DEFAULT;
    private static final String DEFAULT_CHANNEL_ID = "default_channel";
    private static final String DEFAULT_CHANNEL_NAME = "默认通知";
    public static final int DEFAULT_CHANNEL_IMPORTANCE = NotificationManagerCompat.IMPORTANCE_LOW;

    public static void createPersonalNotificationChannel(Context context) {
        try {
            createNotificationChannel(context,DEFAULT_CHANNEL_ID,DEFAULT_CHANNEL_NAME,DEFAULT_CHANNEL_IMPORTANCE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * create a notification builder with channel
     *
     * @param context
     * @return
     */
    public static NotificationCompat.Builder newNotificationBuilder(Context context) {

        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O){

            try {
                createNotificationChannel(context,DEFAULT_CHANNEL_ID,DEFAULT_CHANNEL_NAME,DEFAULT_CHANNEL_IMPORTANCE);
            } catch (Exception e) {
                e.printStackTrace();
            }

            NotificationCompat.Builder builder = new NotificationCompat.Builder(context,DEFAULT_CHANNEL_ID);

            builder.setVibrate(new long[]{0});

            builder.setSound(null);

            builder.setDefaults(0);

            builder.setVisibility(NotificationCompat.VISIBILITY_PUBLIC);

            return builder;
        }else{

            return new NotificationCompat.Builder(context);
        }

    }

    public static NotificationCompat.Builder newPlayerNotificationBuilder(Context context) {
        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            String playerChannelId = PLAYER_CHANNEL_ID;
            int importance = PLAYER_CHANNEL_IMPORTANCE;
            boolean defaultEnable = MmkvCommonUtil.getInstance(context).getBoolean(XmNotificationCreater.ANDROID_NOTIFICATION_OPPO_PRIORITY, false);
            if (defaultEnable && NotificationStyleUtils.isOppoDevice()) {
                // 配置中心开启后，oppo设备的优先级提高成default
                playerChannelId = PLAYER_CHANNEL_ID_FOR_OPPO;
                importance = PLAYER_CHANNEL_IMPORTANCE_FOR_OPPO;
            }
            try {
                createNotificationChannel(context, playerChannelId, PLAYER_CHANNEL_NAME, importance);
            } catch (Exception e) {
                e.printStackTrace();
            }
            NotificationCompat.Builder builder = new NotificationCompat.Builder(context, playerChannelId);
            builder.setVibrate(new long[]{0});
            builder.setSound(null);
            builder.setDefaults(0);
            builder.setVisibility(NotificationCompat.VISIBILITY_PUBLIC);
            return builder;
        } else {
            return new NotificationCompat.Builder(context);
        }
    }

    public static NotificationCompat.Builder newChatXmlyNotificationBuilder(Context context) {
        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            String playerChannelId = PLAYER_CHANNEL_ID;
            try {
                createNotificationChannel(context, playerChannelId, "波波球",  NotificationManagerCompat.IMPORTANCE_LOW);
            } catch (Exception e) {
                e.printStackTrace();
            }
            NotificationCompat.Builder builder = new NotificationCompat.Builder(context, playerChannelId);
            builder.setVibrate(new long[]{0});
            builder.setSound(null);
            builder.setDefaults(0);
            builder.setVisibility(NotificationCompat.VISIBILITY_PUBLIC);
            return builder;
        } else {
            return new NotificationCompat.Builder(context);
        }
    }

    public static NotificationCompat.Builder newNotificationBuilder(Context context, String channelId, String channelName, int importance) {
        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (TextUtils.isEmpty(channelId)) {
                channelId = "default";
            }
            if (TextUtils.isEmpty(channelName)) {
                channelName = "默认通知";
            }
            if ("default".equalsIgnoreCase(channelId)) {
                importance = DEFAULT_CHANNEL_IMPORTANCE;
            }
            try {
                createNotificationChannel(context, channelId,channelName, importance);
            } catch (Exception e) {
                e.printStackTrace();
            }
            NotificationCompat.Builder builder = new NotificationCompat.Builder(context, channelId);
            builder.setVibrate(new long[]{0, 200, 0, 200});
            builder.setSound(null);
            builder.setDefaults(NotificationCompat.DEFAULT_ALL);
            builder.setVisibility(NotificationCompat.VISIBILITY_PUBLIC);
            return builder;
        } else {
            return new NotificationCompat.Builder(context);
        }
    }

    private static HashSet<String> mChannelMap = new HashSet<>();

    @TargetApi(Build.VERSION_CODES.O)
    private static void createNotificationChannel(Context context,String channelId,String channelName,int importance){

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            return;
        }

        if(context == null){
            return;
        }

        if(mChannelMap.contains(channelId)){
            return;
        }

        NotificationManager manager = SystemServiceManager.getNotificationManager(context);

        if(manager == null){
            return;
        }

        List<NotificationChannel> channels = manager.getNotificationChannels();

        NotificationChannel old = null;

        if(channels != null && !channels.isEmpty()) {

            for (NotificationChannel nc : channels) {

                if (nc == null) {

                    return;
                }

                if(TextUtils.equals(channelName,nc.getName())){

                    old = nc;
                    break;
                }
            }
        }

        //Channel已经创建
        if(old != null && TextUtils.equals(channelId,old.getId())){

            return;
        }

        //已经创建该渠道，但是版本不同，需要先删除旧的渠道。
        if(old != null && !TextUtils.equals(channelId,old.getId())) {
            manager.deleteNotificationChannel(old.getId());
        }

        NotificationChannel channel = new NotificationChannel(channelId,
                channelName,
                importance);

        if (importance < NotificationManagerCompat.IMPORTANCE_HIGH) {
            channel.enableVibration(false);
            channel.enableLights(false);
            channel.setVibrationPattern(new long[]{0});
            channel.setSound(null,null);
        } else {
            channel.enableVibration(true);
            channel.enableLights(true);
            channel.setVibrationPattern(new long[]{0, 200, 0, 200});
            channel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
        }

        if (PLAYER_CHANNEL_NAME.equals(channelName)) {
            channel.setShowBadge(false);
            channel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            channel.enableVibration(false);
            channel.enableLights(false);
            channel.setVibrationPattern(new long[]{0});
            channel.setSound(null,null);
        }

        manager.createNotificationChannel(channel);

        mChannelMap.add(channelId);

    }
}
