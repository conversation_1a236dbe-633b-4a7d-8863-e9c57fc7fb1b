package com.ximalaya.ting.android.opensdk.player.advertis

import android.content.Context
import com.ximalaya.ting.android.opensdk.model.advertis.AdBreakPoint

object SoundAdManagerFactory {

    private val TAG = "SoundAdManagerFactory"

    fun createSoundAdManager(context: Context, adBreakPoints: MutableList<AdBreakPoint>?) : SoundAdManger{
        return if (adBreakPoints == null || adBreakPoints.isEmpty()) {
            AiInsertSoundAdManager(context)
        } else {
            AnchorInsertSoundAdManager(context)
        }
    }
}