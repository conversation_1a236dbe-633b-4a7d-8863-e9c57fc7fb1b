package com.ximalaya.ting.android.opensdk.player.simplePlayer.base;

import android.content.Context;

import com.ximalaya.ting.android.opensdk.player.service.XmMediaPlayerFactory;

/**
 * Created by hebin on 2019/3/19.
 */
public enum SimpleMediaPlayerFactory {
    INS;

    public enum PlayerType {
        // exoplayer
        MEDIA_TYPE_DEFAULT,
        // mediaplayer
        MEDIA_TYPE_SIMPLE
    }


    public final IMediaPlayer createPlayer(Context context, PlayerType type) {
        switch (type) {
            case MEDIA_TYPE_SIMPLE:
                return new SimplePlayer();
            default:
                return new SimpleMixPlayer(context, XmMediaPlayerFactory.getDefaultDataSourceInterceptor());
        }
    }

}
