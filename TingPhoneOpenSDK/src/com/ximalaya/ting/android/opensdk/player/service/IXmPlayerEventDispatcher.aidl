/***********************************************************************
 * Module:  IXmPlayerEventDispather.aidl
 * Author:  chadwii
 * Purpose: Defines the Interface IXmPlayerEventDispather
 ***********************************************************************/
package com.ximalaya.ting.android.opensdk.player.service;

import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.model.live.radio.Radio;

interface IXmPlayerEventDispatcher
{
	
	void onPlayStart();

	void onPlayPause();

	void onPlayStop();

	void onSoundPlayComplete();

	void onSoundPrepared();

	void onSoundSwitch(in Track lastTrack, in Track curTrack);

	void onBufferingStart();
	
	void onBufferingStop();

	void onBufferProgress(int percent);

	void onPlayProgress(int currPos, int duration);

	void onError(inout XmPlayerException exception);

	void onRequestPlayUrlBegin();

	void onRequestPlayUrlSuccess();

	void onRequestPlayUrlError(int code ,String message);

	void onAudioAuditionOver(inout Track lastTrack);

	void onRenderingStart();

    void onVideoSizeChanged(int width, int height);

    void onRotationChanged(int rotationAngle);

    void onChildAiTimbreUrlGet(int success, int isDownload, String type);
}