package com.ximalaya.ting.android.opensdk.player.mediacontrol;

import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.os.SystemClock;
import android.support.v4.media.MediaMetadataCompat;
import android.support.v4.media.RatingCompat;
import android.support.v4.media.session.MediaSessionCompat;
import android.support.v4.media.session.PlaybackStateCompat;
import android.telephony.TelephonyManager;
import android.view.KeyEvent;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.opensdk.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.appnotification.NotificationStyleUtils;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationCreater;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationPendingIntentCreateUtils;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.receive.NotificationLikeManager;
import com.ximalaya.ting.android.opensdk.player.receive.WireControlReceiver;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerConfig;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.mixplay.MixPlayerService;
import com.ximalaya.ting.android.opensdk.player.ubt.UbtSourceProvider;
import com.ximalaya.ting.android.opensdk.push.PushGuardPlayerManager;
import com.ximalaya.ting.android.opensdk.util.FileUtilBase;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.stat.IPushGuardUbtTrace;
import com.ximalaya.ting.android.util.OsUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

/**
 * <AUTHOR>
 *
 * MediaControl兼容版本
 */
@Keep
public class MediaControlManagerCompat implements IMediaControl {

    public static final String TAG = "MediaControlManagerCompat";
    private static final int STATE_UPDATE_PROCESS = 1;
    private static final int STATE_RELEASE = 2;
    private static final int STATE_START_PLAY = 3;
    private static final int STATE_PAUSE_PLAY = 4;
    private static final int STATE_STOP_PLAY = 5;
    private static final int STATE_INIT = 6;
    private static final int STATE_SOUND_PREPARED = 7;
    private static final int STATE_SOUND_SWITCH = 8;
    private static final int STATE_SOUND_LIKE_STATE_CHANGE = 9;
    private static final int STATE_PUSH_NOTIFICATION_HARMONY = 10;

    private MediaSessionCompat mSession;
    private Context mContext;
    private WireControlReceiver wireControlReceiver;
    private HandlerThread mHandlerThread;
    private Handler mHandler;
    private float mTempo = 1.0f;
    private volatile boolean mIsAdPlaying = false;
    private volatile boolean mIsRadioPlaying = false;
    private volatile boolean mIsLivePlaying = false;
    private volatile boolean mIsSketchVideoPlaying = false;

    private int mLastState = PlaybackStateCompat.STATE_NONE;
    @Nullable
    private MediaMetadataCompat.Builder mMediaMetadataCompatBuilder;
    private boolean isWireControl = true; //播放逻辑是否受线控耳机操作影响

    public MediaControlManagerCompat(Context context) {
        mContext = context;
        wireControlReceiver = new WireControlReceiver();

        mHandlerThread = new HandlerThread("MediaControlManagerCompat");
        mHandlerThread.start();
        mHandler = new Handler(mHandlerThread.getLooper()) {
            @Override
            public void handleMessage(Message msg) {
                if (msg == null) {
                    return;
                }
                PlaybackStateCompat state;
                try {
                    switch (msg.what) {
                        case STATE_INIT:
                            mSession = new MediaSessionCompat(mContext, "MusicService");
                            Logger.d(TAG, "new___mSession");

                            if (mSession != null) {
                                mSession.setCallback(new MediaSessionCompat.Callback() {
                                    @Override
                                    public void onCustomAction(String action, Bundle extras) {
                                        super.onCustomAction(action, extras);
                                        XmPlayerService service = XmPlayerService.getPlayerSrvice();
                                        if (service == null) {
                                            return;
                                        }
                                        if (XmNotificationCreater.ACTION_CONTROL_LESS_TIME.equals(action)) {
                                            traceNotificationClick("快退");
                                            int seekToPos = service.getPlayCurrPosition() - 15000;
                                            seekToPos = Math.max(seekToPos, 0);
                                            service.seekTo(seekToPos);
                                            service.mediaSessionSeek(seekToPos);
                                        } else if (XmNotificationCreater.ACTION_CONTROL_PLUS_TIME.equals(action)) {
                                            traceNotificationClick("快进");
                                            int seekToPos = service.getPlayCurrPosition() + 15000;
                                            service.seekTo(seekToPos);
                                            service.mediaSessionSeek(seekToPos);
                                        }
                                    }

                                    @Override
                                    public void onSetRating(RatingCompat rating) {
                                        super.onSetRating(rating);
                                        traceNotificationClick("点赞");
                                        boolean played = PushGuardPlayerManager.getInstance().playListWithRecommend(false, 0);
                                        if (played) {
                                            PushGuardPlayerManager.getInstance().getHandler().postDelayed(() -> {
                                                Logger.log("MediaControlManagerCompat : onSetRating 1");
                                                NotificationLikeManager.INSTANCE.changeLikeState();
                                                XmPlayerService service = XmPlayerService.getPlayerSrvice();
                                                if (service != null) {
                                                    PushGuardPlayerManager.getInstance().traceGuardRealClick(true, true, "like");
                                                }
                                            }, 500);
                                            return;
                                        }
                                        Logger.log("MediaControlManagerCompat : onSetRating 1");
                                        NotificationLikeManager.INSTANCE.changeLikeState();
                                        XmPlayerService service = XmPlayerService.getPlayerSrvice();
                                        if (service != null) {
                                            PushGuardPlayerManager.getInstance().traceGuardRealClick(true, true, "like");
                                        }
                                    }

                                    @Override
                                    public boolean onMediaButtonEvent(Intent mediaButtonIntent) {
                                        Logger.logToFile("MediaControlManagerCompat : onMediaButtonEvent " + mediaButtonIntent);
                                        if (isWireControl) {
                                            //线控逻辑交给wireControlReceiver来处理
                                            wireControlReceiver.onReceive(mContext, mediaButtonIntent);
                                        }

                                        //以下的返回逻辑与super.onMediaButtonEvent()里的保持一致
                                        KeyEvent keyEvent = mediaButtonIntent.getParcelableExtra(Intent.EXTRA_KEY_EVENT);
                                        if (keyEvent == null || keyEvent.getAction() != KeyEvent.ACTION_DOWN) {
                                            return false;
                                        }
                                        int keyCode = keyEvent.getKeyCode();
                                        return KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE == keyCode
                                                || KeyEvent.KEYCODE_MEDIA_PAUSE == keyCode
                                                || KeyEvent.KEYCODE_MEDIA_STOP == keyCode
                                                || KeyEvent.KEYCODE_HEADSETHOOK == keyCode
                                                || KeyEvent.KEYCODE_MEDIA_PLAY == keyCode
                                                || KeyEvent.KEYCODE_MEDIA_NEXT == keyCode
                                                || KeyEvent.KEYCODE_MEDIA_PREVIOUS == keyCode;
                                    }

                                    @Override
                                    public void onPlay() {
                                        traceHarmonyFirstPlayNotificationClick("播放");
                                        Logger.logToFile("MediaControlManagerCompat : onPlay ");
                                        traceNotificationClick("播放");
                                        XmPlayerService service = XmPlayerService.getPlayerSrvice();
                                        if (service == null){
                                            WireControlReceiver.initAndStartPlay(context);
                                        } else {
                                            if (service.isVideoMode()) {
                                                Logger.log("mediaSession video play");
                                                Intent intentVideo = new Intent(PlayerConstants.ACTION_BROADCAST_VIDEO_PLAY_PAUSE);
                                                intentVideo.putExtra(PlayerConstants.EXTRA_VIDEO_PLAY_EVENT, "video_play");
                                                context.sendBroadcast(intentVideo);
                                                return;
                                            }
                                            if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null) {
                                                MixPlayerService.getMixService().start();
                                                return;
                                            }
                                            boolean played = PushGuardPlayerManager.getInstance().playListWithRecommend(true, 0);
                                            try {
                                                UbtSourceProvider.getInstance().setUbtSource(service.getPlayableModel());
                                            } catch (Exception e) {
                                                Logger.logToFile("WireControlReceiver : ubtSource error");
                                            }
                                            if (!played) {
                                                WireControlReceiver.startPlay(service);
                                            }
                                            PushGuardPlayerManager.getInstance().traceGuardRealClick(true, true, "play");
                                        }
                                    }

                                    @Override
                                    public void onPause() {
                                        traceHarmonyFirstPlayNotificationClick("暂停");
                                        traceNotificationClick("暂停");
                                        Logger.logToFile("MediaControlManagerCompat : onPause ");
                                        XmPlayerService service = XmPlayerService.getPlayerSrvice();
                                        if (service != null) {
                                            if (service.getCurrPlayModel() != null && service.getCurrPlayModel().isKindOfLive()) {
                                                //直播不受线控逻辑控制
                                                return;
                                            }
                                            if (mIsSketchVideoPlaying) {
                                                // 短剧
                                                return;
                                            }
                                            if (service.isVideoMode()) {
                                                Logger.log("mediaSession video pause");
                                                Intent intentVideo = new Intent(PlayerConstants.ACTION_BROADCAST_VIDEO_PLAY_PAUSE);
                                                intentVideo.putExtra(PlayerConstants.EXTRA_VIDEO_PLAY_EVENT, "video_pause");
                                                context.sendBroadcast(intentVideo);
                                            }
                                            if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null) {
                                                MixPlayerService.getMixService().pause();
                                                return;
                                            }
                                            if (!service.isPlaying()) {
                                                // 修复华为反馈的问题
                                                pausePlay();
                                            }
                                            service.pausePlay(false, PauseReason.Common.MEDIA_SESSION_PAUSE);
                                        }
                                    }

                                    @Override
                                    public void onSkipToNext() {
                                        traceHarmonyFirstPlayNotificationClick("下一首");
                                        traceNotificationClick("下一首");
                                        Logger.logToFile("MediaControlManagerCompat : onSkipToNext ");
                                        XmPlayerService service = XmPlayerService.getPlayerSrvice();
                                        boolean played = PushGuardPlayerManager.getInstance().playListWithRecommend(true, 1);
                                        if (service != null) {
                                            if (service.isVideoMode()) {
                                                service.playNext(false);
                                            } else if (!played && !mIsSketchVideoPlaying) {
                                                service.playNext();
                                            }
                                            PushGuardPlayerManager.getInstance().traceGuardRealClick(true, true, "next");
                                        }
                                    }

                                    @Override
                                    public void onSkipToPrevious() {
                                        traceHarmonyFirstPlayNotificationClick("上一首");
                                        traceNotificationClick("上一首");
                                        Logger.logToFile("MediaControlManagerCompat : onSkipToPrevious ");
                                        XmPlayerService service = XmPlayerService.getPlayerSrvice();
                                        boolean played = PushGuardPlayerManager.getInstance().playListWithRecommend(true, -1);

                                        if (service != null) {
                                            if (service.isVideoMode()) {
                                                service.playPre(false);
                                            } else if (!played && !mIsSketchVideoPlaying) {
                                                service.playPre();
                                            }
                                            PushGuardPlayerManager.getInstance().traceGuardRealClick(true, true, "before");
                                        }
                                    }

                                    @Override
                                    public void onSeekTo(long pos) {
                                        Logger.logToFile("MediaControlManagerCompat : onSeekTo " + pos);
                                        XmPlayerService service = XmPlayerService.getPlayerSrvice();
                                        if (service != null) {
                                            service.seekTo((int) pos != pos ? Integer.MAX_VALUE : (int) pos);
                                            service.mediaSessionSeek((int) pos != pos ? Integer.MAX_VALUE : (int) pos);
                                            if (mSession != null) {
                                                PlaybackStateCompat.Builder builder = new PlaybackStateCompat.Builder()
                                                        .setActions(getMediaSessionActions())
                                                        .setState(PlaybackStateCompat.STATE_NONE, pos, mTempo,
                                                                SystemClock.elapsedRealtime());
                                                mSession.setPlaybackState(addCustomTimeAction(builder).build());
                                            }
                                        }
                                    }
                                });
                                mSession.setFlags(MediaSessionCompat.FLAG_HANDLES_MEDIA_BUTTONS
                                        | MediaSessionCompat.FLAG_HANDLES_TRANSPORT_CONTROLS);
                                mSession.setSessionActivity(getPlayOutIntent());
                                //----为了华为hicar车载模式切换不使用这个session，需要添加一个字段hicar.media.bundle.NOT_FOR_HICAR
                                Bundle bundle = new Bundle();
                                bundle.putBoolean("hicar.media.bundle.NOT_FOR_HICAR", true);
                                // 设置单次快进时长，仅支持10,15,30
                                bundle.putInt("fast_forward_interval", 15);
                                bundle.putInt("backward_interval", 15);
                                mSession.setExtras(bundle);
                                mSession.setActive(true);
                                XmPlayerService service = XmPlayerService.getPlayerSrvice();
                                if (service != null) {
                                    service.onSessionCreated(mSession);
                                }
                            }
                            break;
                        case STATE_RELEASE:
                            Logger.d(TAG, "mSession.setPlaybackState________STATE_RELEASE");
                            if (mSession != null) {
                                mSession.setActive(false);
                                mSession.release();
                                mIsAdPlaying = false;
                                mIsRadioPlaying = false;
                                mIsLivePlaying = false;
                                mIsSketchVideoPlaying = false;
                            }
                            break;
                        case STATE_SOUND_SWITCH:
                            Logger.d(TAG, "mSession.setPlaybackState________STATE_SOUND_SWITCH");
                            if (mSession != null) {
                                PlaybackStateCompat.Builder builder = new PlaybackStateCompat.Builder()
                                        .setActions(getLeastActions())
                                        .setState(PlaybackStateCompat.STATE_NONE, 0, mTempo, SystemClock.elapsedRealtime());;
                                mSession.setPlaybackState(addCustomTimeAction(builder).build());
                            }
                            break;
                        case STATE_SOUND_PREPARED:
                            Logger.d(TAG, "mSession.setPlaybackState________STATE_SOUND_PREPARED");
                            if (mSession != null) {
                                state = addCustomTimeAction(new PlaybackStateCompat.Builder()
                                        .setActions(getMediaSessionActions())
                                        .setState(getStateOnPrepared(), 0, mTempo,
                                                SystemClock.elapsedRealtime())).build();

                                mSession.setPlaybackState(state);
                            }
                            break;
                        case STATE_START_PLAY:
                            Logger.d(TAG, "mSession.setPlaybackState________start_play");
                            if (mSession != null || mIsAdPlaying) {
                                PushGuardPlayerManager.getInstance().mFromHarmonyAutoNotificationFirstTime = false;
                                PushGuardPlayerManager.getInstance().mHasPlayedForHarmony = true;
                                state = addCustomTimeAction(new PlaybackStateCompat.Builder()
                                        .setActions(getMediaSessionActions())
                                        .setState(PlaybackStateCompat.STATE_PLAYING, mIsAdPlaying ? 0 : msg.arg1, mTempo,
                                                SystemClock.elapsedRealtime())).build();
                                mLastState = PlaybackStateCompat.STATE_PLAYING;

                                register(mSession);
                                mSession.setActive(true);
                                Logger.d(TAG, "mSession.setPlaybackState________start_play_1");
                                mSession.setPlaybackState(state);
                                if (msg.arg2 == 1) {
                                    XmPlayerService service = XmPlayerService.getPlayerSrvice();
                                    if (service != null) {
                                        service.updateViewStateAtPause();
                                    }
                                }
                            }
                            break;
                        case STATE_PUSH_NOTIFICATION_HARMONY:
                            Logger.d(TAG, "mSession.setPlaybackState________huwei");
                            if (mSession != null) {
                                // 会抢占焦点
                                state = addCustomTimeAction(new PlaybackStateCompat.Builder()
                                        .setActions(getMediaSessionActions())
                                        .setState(PlaybackStateCompat.STATE_PLAYING, msg.arg1, mTempo,
                                                SystemClock.elapsedRealtime())).build();
                                mSession.setPlaybackState(state);
                                register(mSession);
                                mSession.setActive(true);

                                state = addCustomTimeAction(new PlaybackStateCompat.Builder()
                                        .setActions(getMediaSessionActions())
                                        .setState(PlaybackStateCompat.STATE_PAUSED, msg.arg1, mTempo)).build();
                                mSession.setPlaybackState(state);
                                mLastState = PlaybackStateCompat.STATE_PAUSED;
                                Logger.d(TAG, "mSession.setPlaybackState________huwei_1");
                            }
                            break;
                        case STATE_PAUSE_PLAY:
                            Logger.d(TAG, "mSession.setPlaybackState________pause_play");
                            if (isMediaSessionInValid()) {
                                return;
                            }
                            state = addCustomTimeAction(new PlaybackStateCompat.Builder()
                                    .setActions(getMediaSessionActions())
                                    .setState(PlaybackStateCompat.STATE_PAUSED, msg.arg1, mTempo)).build();
                            mLastState = PlaybackStateCompat.STATE_PAUSED;
                            Logger.d(TAG, "mSession.setPlaybackState________pause_play_1");
                            mSession.setPlaybackState(state);
                            if (msg.arg2 == 1) {
                                XmPlayerService service = XmPlayerService.getPlayerSrvice();
                                if (service != null) {
                                    service.updateViewStateAtPause();
                                }
                            }
                            break;
                        case STATE_STOP_PLAY:
                            Logger.d(TAG, "mSession.setPlaybackState________stop_play");
                            if (isMediaSessionInValid()) {
                                return;
                            }

                            state = addCustomTimeAction(new PlaybackStateCompat.Builder()
                                    .setActions(getMediaSessionActions())
                                    .setState(PlaybackStateCompat.STATE_STOPPED, 0, mTempo)).build();
                            mLastState = PlaybackStateCompat.STATE_STOPPED;
                            Logger.d(TAG, "mSession.setPlaybackState________stop_play_1");
                            mSession.setPlaybackState(state);
                            break;
                        case STATE_UPDATE_PROCESS:
                            Logger.d(TAG, "mSession.setPlaybackState________STATE_UPDATE_PROCESS");
                            if (isMediaSessionInValid() || mIsLivePlaying || mIsSketchVideoPlaying) {
                                return;
                            }

                            state = addCustomTimeAction(new PlaybackStateCompat.Builder()
                                    .setActions(getMediaSessionActions())
                                    .setState(mLastState, mIsAdPlaying ? 0 : msg.arg1, mTempo))
                                    .build();
                            mSession.setPlaybackState(state);
                            break;
                        case STATE_SOUND_LIKE_STATE_CHANGE:
                            if (isMediaSessionInValid()) {
                                return;
                            }

                            if (msg.obj instanceof Boolean && mMediaMetadataCompatBuilder != null) {
                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                                    mMediaMetadataCompatBuilder.putRating(MediaMetadataCompat.METADATA_KEY_USER_RATING,
                                            RatingCompat.newHeartRating((Boolean) msg.obj));
                                    mMediaMetadataCompatBuilder.putRating(MediaMetadataCompat.METADATA_KEY_RATING,
                                            RatingCompat.newHeartRating((Boolean) msg.obj));
                                    mSession.setMetadata(mMediaMetadataCompatBuilder.build());
                                }
                            }

                            break;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    if (ConstantsOpenSdk.isDebug) {
                        throw new RuntimeException("MediaControlManagerCompat 有异常" + e);
                    }
                }
            }
        };
    }

    private int getStateOnPrepared() {
        if (wireControlReceiver != null && wireControlReceiver.mKeyCode == KeyEvent.KEYCODE_MEDIA_PLAY) {
            wireControlReceiver.mKeyCode = -1;
            return PlaybackStateCompat.STATE_PLAYING;
        }
        return PlaybackStateCompat.STATE_PAUSED;
    }

    private PendingIntent getPlayOutIntent() {
        try {
            Class clazz = Class.forName(XmPlayerService.TARGET_CLASS_NAME, false, MediaControlManagerCompat.class.getClassLoader());
            Intent intent = new Intent(mContext, clazz);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.putExtra(XmNotificationCreater.NOTIFICATION_EXTRY_KEY, XmNotificationCreater.NOTIFICATION_EXTYR_DATA);
            intent.setAction(XmNotificationCreater.ACTION_PULL_UP_AND_START);
            return PendingIntent.getActivity(mContext, 0, intent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public void initMediaControl() {
        IntentFilter inFilter = new IntentFilter(
                "android.intent.action.MEDIA_BUTTON");
        inFilter.setPriority(10000);

        try {
            mContext.registerReceiver(wireControlReceiver, inFilter);
        } catch (Exception e) {
            e.printStackTrace();
        }

        mHandler.sendEmptyMessage(STATE_INIT);

        registerLikeSoundBroadCast(mContext);
    }

    @Override
    public void release() {

        mHandler.sendEmptyMessage(STATE_RELEASE);

        if (wireControlReceiver != null && mContext != null) {
            try {
                mContext.unregisterReceiver(wireControlReceiver);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        unRegisterLikeSoundReceiver(mContext);
    }

    @Override
    public void updateProcess(int progress) {
        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        if (service == null) {
            return;
        }
        Message obtain = Message.obtain();
        obtain.what = STATE_UPDATE_PROCESS;
        obtain.arg1 = progress;
        mHandler.sendMessage(obtain);
    }

    @Override
    public void onSoundPrepared() {
        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        if (service == null) {
            return;
        }
        mIsRadioPlaying = service.isPlayingRadio();
        mIsLivePlaying = service.isPlayingLive();
        mIsSketchVideoPlaying = service.isSketchVideo();
        mHandler.sendEmptyMessage(STATE_SOUND_PREPARED);
    }

    @Override
    public void onSoundSwitch() {
        mMediaMetadataCompatBuilder = null;
        mHandler.sendEmptyMessage(STATE_SOUND_SWITCH);
    }

    @Override
    public void onBufferingStart() {
        if (!OsUtil.isHarmonyOs()) {
            return;
        }
        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        if (service == null || !service.isPlaying()) {
            return;
        }
        Message message = Message.obtain();
        message.what = STATE_PAUSE_PLAY;
        Logger.logToFile("MediaControlManagerCompat onBufferingStart");
        message.arg1 = service.getPlayCurrPosition();
        message.arg2 = 1;
        mHandler.sendMessage(message);
        if (!NotificationStyleUtils.isSystemStyle()) {
            addMetaData(service);
        }
    }

    @Override
    public void onBufferingStop() {
        if (!OsUtil.isHarmonyOs()) {
            return;
        }
        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        if (service == null) {
            return;
        }
        Message message = Message.obtain();
        message.what = service.isPlaying() ? STATE_START_PLAY : STATE_PAUSE_PLAY;
        Logger.logToFile("MediaControlManagerCompat onBufferingStop() = " + message.what);
        message.arg1 = service.getPlayCurrPosition();
        message.arg2 = 1;
        mHandler.sendMessage(message);
        if (!NotificationStyleUtils.isSystemStyle()) {
            addMetaData(service);
        }
    }

    private void register(MediaSessionCompat mediaSession) {
        if (mediaSession == null) {
            return;
        }

        Intent mediaButtonIntent = new Intent(Intent.ACTION_MEDIA_BUTTON);
        try {
            ComponentName mediaButtonReceiverComponent = new ComponentName(mContext,
                    WireControlReceiver.class);
            mediaButtonIntent.setComponent(mediaButtonReceiverComponent);
            PendingIntent mediaPendingIntent = PendingIntent
                    .getBroadcast(mContext.getApplicationContext(), 0, mediaButtonIntent, 0);
            mediaSession.setMediaButtonReceiver(mediaPendingIntent);
            mediaSession.setPlaybackToLocal(AudioManager.STREAM_MUSIC);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void startPlay() {
        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        if (service == null) {
            return;
        }
        Message message = Message.obtain();
        message.what = STATE_START_PLAY;
        Logger.logToFile("MediaControlManagerCompat startPlay()");
        message.arg1 = service.getPlayCurrPosition();
        mHandler.sendMessage(message);
        if (!NotificationStyleUtils.isSystemStyle()) {
            addMetaData(service);
        }
    }


    @Override
    public void openHarmonyOsNotification(Context context) {
        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        if (service == null) {
            return;
        }
        IPushGuardUbtTrace pushGuardUbtTrace = RouterServiceManager.getInstance().getService(IPushGuardUbtTrace.class);
        if (PushGuardPlayerManager.getInstance().getCurrentPlayableModel() == null) {
            if (pushGuardUbtTrace != null) {
                pushGuardUbtTrace.traceHarmonyOsNotify(3);
            }
            return;
        }
        TelephonyManager tm = SystemServiceManager.getTelephonyManager(context);
        if (tm != null && (TelephonyManager.CALL_STATE_OFFHOOK == tm.getCallState() || TelephonyManager.CALL_STATE_RINGING == tm.getCallState())) {
            // 通话中，不调起通知栏
            if (pushGuardUbtTrace != null) {
                pushGuardUbtTrace.traceHarmonyOsNotify(1);
            }
            return;
        }
        if (service.mPlayerAudioFocusControl != null && service.mPlayerAudioFocusControl.getAudioManager() != null) {
            boolean isMusicActive = service.mPlayerAudioFocusControl.getAudioManager().isMusicActive();
//            service.mPlayerAudioFocusControl.setAudioFocusAtStopState();
            if (isMusicActive) {
                if (pushGuardUbtTrace != null) {
                    pushGuardUbtTrace.traceHarmonyOsNotify(2);
                }
                return;
            }
        }
        if (!PushGuardPlayerManager.getInstance().mFromHarmonyAutoNotification) {
            PushGuardPlayerManager.getInstance().mFromHarmonyAutoNotificationFirstTime = true;
        }
        PushGuardPlayerManager.getInstance().mFromHarmonyAutoNotification = true;
        if (pushGuardUbtTrace != null) {
            pushGuardUbtTrace.traceHarmonyOsNotify(0);
        }
        Message message = Message.obtain();
        message.what = STATE_PUSH_NOTIFICATION_HARMONY;
        message.arg1 = service.getPlayCurrPosition();
        mHandler.sendMessage(message);
        if (!NotificationStyleUtils.isSystemStyle()) {
            addMetaData(service);
        }
    }

    private void addMetaData(XmPlayerService service) {
        final Track track = (Track) service.getPlayListControl().getCurrentPlayableModel();

        if (track == null) {
            return;
        }

        FileUtilBase.getBitmapByUrl(XmNotificationCreater.useSmallBitmapForRemote(), mContext, track, 0, 0,
                bitmap -> MyAsyncTask.execute(() -> {
                    try {
                        MediaMetadataCompat.Builder metadataBuilder = new MediaMetadataCompat.Builder();
                        boolean showBgView = true;
                        XmPlayerConfig playerConfig = XmPlayerConfig.getInstance(mContext);
                        if (playerConfig != null) {
                            showBgView = playerConfig.isShowMediaSessionBgView();
                        }
                        if (bitmap != null && !bitmap.isRecycled()) {
                            if (showBgView) {
                                metadataBuilder.putBitmap(MediaMetadataCompat.METADATA_KEY_ART, bitmap);
                            }
                            metadataBuilder.putBitmap(MediaMetadataCompat.METADATA_KEY_DISPLAY_ICON, bitmap);
                        }
                        if (track.getAlbum() != null) {
                            metadataBuilder.putString(MediaMetadataCompat.METADATA_KEY_ALBUM, track.getAlbum().getAlbumTitle());
                        }
                        if (track.getAnnouncer() != null) {
                            metadataBuilder.putString(MediaMetadataCompat.METADATA_KEY_ARTIST, track.getAnnouncer().getNickname());
                            metadataBuilder.putString(MediaMetadataCompat.METADATA_KEY_DISPLAY_SUBTITLE, track.getAnnouncer().getNickname());
                        }

                        metadataBuilder.putString(MediaMetadataCompat.METADATA_KEY_TITLE, track.getTrackTitle());
                        metadataBuilder.putString(MediaMetadataCompat.METADATA_KEY_DISPLAY_TITLE, track.getTrackTitle());
                        metadataBuilder.putLong(MediaMetadataCompat.METADATA_KEY_DURATION, service.getDuration());
                        metadataBuilder.putString(MediaMetadataCompat.METADATA_KEY_ALBUM_ART_URI, track.getValidCover());

                        if (PlayableModel.KIND_TRACK.equals(track.getKind())) {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                                metadataBuilder.putRating(MediaMetadataCompat.METADATA_KEY_USER_RATING,
                                        RatingCompat.newHeartRating(track.isLike()));
                                metadataBuilder.putRating(MediaMetadataCompat.METADATA_KEY_RATING,
                                        RatingCompat.newHeartRating(track.isLike()));
                            }
                        }

                        if (isMediaSessionInValid()) {
                            return;
                        }
                        mMediaMetadataCompatBuilder = metadataBuilder;
                        MediaMetadataCompat build = metadataBuilder.build();
                        mSession.setMetadata(build);
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                }));
    }

    @Override
    public void pausePlay() {
        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        if (service == null) {
            return;
        }

        Message message = Message.obtain();
        message.what = STATE_PAUSE_PLAY;
        Logger.logToFile("MediaControlManagerCompat pausePlay()");
        message.arg1 = service.getPlayCurrPosition();
        mHandler.sendMessage(message);
    }

    @Override
    public void stopPlay() {
        mHandler.sendEmptyMessage(STATE_STOP_PLAY);
    }

    @Override
    public void setTempo(float tempo) {
        this.mTempo = tempo;
    }

    @Override
    public void setAdsPlayStatus(boolean isAdPlaying) {
        Logger.d(TAG, "mIsAdPlaying = " + mIsAdPlaying);
        this.mIsAdPlaying = isAdPlaying;
        if (mSession != null && mIsAdPlaying) {
            mSession.setPlaybackState(new PlaybackStateCompat.Builder()
                    .setActions(getMediaSessionActions())
                    .setState(PlaybackStateCompat.STATE_PAUSED, 0, 1.0f,
                            SystemClock.elapsedRealtime()).build());
            mLastState = PlaybackStateCompat.STATE_PAUSED;
        }
    }

    public void traceNotificationClick(String item) {
        IPushGuardUbtTrace pushGuardUbtTrace = RouterServiceManager.getInstance().getService(IPushGuardUbtTrace.class);
        if (pushGuardUbtTrace != null) {
            pushGuardUbtTrace.traceNotificationClick(true, true, item);
        }
    }

    public void traceHarmonyFirstPlayNotificationClick(String item) {
        if (PushGuardPlayerManager.getInstance().mFromHarmonyAutoNotificationFirstTime && OsUtil.isHarmonyOs() &&
                !PushGuardPlayerManager.getInstance().mHasPlayedForHarmony) {
            PushGuardPlayerManager.getInstance().mFromHarmonyAutoNotificationFirstTime = false;
            IPushGuardUbtTrace pushGuardUbtTrace = RouterServiceManager.getInstance().getService(IPushGuardUbtTrace.class);
            if (pushGuardUbtTrace != null) {
                pushGuardUbtTrace.traceHarmonyFirstPlayNotificationClick(item);
            }
        }
    }

    @Override
    public void setIsWireControl(boolean isWireControl) {
        this.isWireControl = isWireControl;
    }

    public MediaSessionCompat getSession() {
        return mSession;
    }

    private boolean isMediaSessionInValid() {
        return mSession == null || !mSession.isActive();
    }

    private long getMediaSessionActions() {
        long actions = getLeastActions();

        XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();
        boolean videoMode = false;
        if (playerSrvice != null) {
            videoMode = playerSrvice.isVideoMode();
        }
        if (mIsAdPlaying || mIsRadioPlaying || mIsLivePlaying || videoMode || mIsSketchVideoPlaying) {
            return actions;
        }

        return actions | PlaybackStateCompat.ACTION_SEEK_TO;
    }

    private long getLeastActions() {
        if (mIsLivePlaying || mIsSketchVideoPlaying) {
            // 鸿蒙通知栏直播置灰所有按钮
            return 0;
        }
        if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null) {
            return PlaybackStateCompat.ACTION_PLAY | PlaybackStateCompat.ACTION_PAUSE | PlaybackStateCompat.ACTION_PLAY_PAUSE;
        }
        return PlaybackStateCompat.ACTION_PLAY
                | PlaybackStateCompat.ACTION_PAUSE
                | PlaybackStateCompat.ACTION_REWIND
                | PlaybackStateCompat.ACTION_FAST_FORWARD
                | PlaybackStateCompat.ACTION_PLAY_PAUSE
                | PlaybackStateCompat.ACTION_SKIP_TO_NEXT
                | PlaybackStateCompat.ACTION_PLAY_FROM_MEDIA_ID
                | PlaybackStateCompat.ACTION_SKIP_TO_PREVIOUS
                | PlaybackStateCompat.ACTION_STOP;
    }

    private void registerLikeSoundBroadCast(Context context) {
        if (context == null) {
            return;
        }

        IntentFilter intentFilter = new IntentFilter(DTransferConstants.ACTION_LIKE_STATE_CHANGE);
        context.registerReceiver(mLikeSoundBroadCast, intentFilter);
    }

    private void unRegisterLikeSoundReceiver(Context context) {
        if (context == null) {
            return;
        }
        context.unregisterReceiver(mLikeSoundBroadCast);
    }

    private LikeSoundBroadCast mLikeSoundBroadCast = new LikeSoundBroadCast();

    private class LikeSoundBroadCast extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null
                    && DTransferConstants.ACTION_LIKE_STATE_CHANGE.equals(intent.getAction())
                    && intent.getLongExtra(DTransferConstants.PARAM_LIKE_TRACK_ID, 0) > 0) {
                onLikeStateChange(intent.getBooleanExtra(DTransferConstants.PARAM_LIKE_TRACK_STATE, false));
            }
        }
    }

    public PlaybackStateCompat.Builder addCustomTimeAction(PlaybackStateCompat.Builder builder) {
        if (mIsLivePlaying || mIsSketchVideoPlaying) {
            // 鸿蒙通知栏直播置灰所有按钮
            return builder;
        }
        if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null) {
            return builder;
        }
        PlaybackStateCompat.CustomAction rewindAction = new PlaybackStateCompat.CustomAction.Builder(
                XmNotificationCreater.ACTION_CONTROL_LESS_TIME,
                "减15s",
                R.drawable.bg_white_notify_btn_media_style_less_selector
        ).build();
        PlaybackStateCompat.CustomAction forwardAction = new PlaybackStateCompat.CustomAction.Builder(
                XmNotificationCreater.ACTION_CONTROL_PLUS_TIME,
                "加15s",
                R.drawable.bg_white_notify_btn_media_style_plus_selector
        ).build();
//        if (NotificationStyleUtils.isHyperOs()) {
            return builder
                    .addCustomAction(rewindAction)
                    .addCustomAction(forwardAction);
//        } else {
//            return builder
//                    .addCustomAction(forwardAction);
//        }
    }

    public void onLikeStateChange(boolean isLiked) {
        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        if (service == null) {
            return;
        }

        Message message = Message.obtain();
        message.what = STATE_SOUND_LIKE_STATE_CHANGE;
        message.obj = isLiked;
        mHandler.sendMessage(message);
    }
}
