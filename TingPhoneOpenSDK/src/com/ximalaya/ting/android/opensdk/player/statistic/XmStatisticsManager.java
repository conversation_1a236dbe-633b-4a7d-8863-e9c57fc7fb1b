package com.ximalaya.ting.android.opensdk.player.statistic;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.history.XmPlayRecord;
import com.ximalaya.ting.android.opensdk.model.statistic.RecordModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.manager.TrackUrlChooseManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmCommonBusinessDispatcher;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerControl;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.mixplay.MixPlayerService;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.util.MMKVRaiseUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


/**
 * 该类用来统计应用内的声音播放数据。
 * 一条声音的播放时长包括在音频播放器中的播放时长和视频播放器中的播放时长，
 * 比如现在音频播放器中播放了1分钟，再到视频播放器中播放了该声音的视频版2分钟，那么这条声音的总播放时长为3分钟
 */
/**
 * <AUTHOR>
 */
public class XmStatisticsManager {
	private static final String TAG = "XmStatisticsManager";
	private volatile static XmStatisticsManager sInstance;
	private static byte[] sLock = new byte[0];
	@Nullable
	private IXmPlayStatisticUploader mUploader;

	@Nullable
	private IXmPlayStatisticUploader mDubPlayStatisticsUploader;

	private Set<IXmUserOneDateListener> mUserOneDateListener = new CopyOnWriteArraySet<>();
	private Map<Double, IXmPlayStatisticUploader> mMixPlayerUploaders = new HashMap<>();
	private RecordModel mRecordModel = null;
	private boolean mIsShouldStatistic = false;
	private boolean mIsSeek;

	private Context mContext;
	public String xmCurPlayResource;
	public String xmUploadPlayResource;
	public Map<String,String> mXmResourceMap;
	private long mStartTime;
	
	private XmStatisticsManager() {
		init();
	}

	private void init() {
		XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
		if(xmPlayerService != null) {
			XmPlayerControl xmPlayerControl = xmPlayerService.getPlayControl();
			if(xmPlayerControl != null) {
				xmPlayerControl.resetDuration();
			}
		}
	}

	public void setContext(Context context) {
		this.mContext = context;
	}
	public static XmStatisticsManager getInstance() {
		if (sInstance == null) {
			synchronized (sLock) {
				if (sInstance == null) {
					sInstance = new XmStatisticsManager();
				}
			}
		}
		return sInstance;
	}

	public void addListenDateListener(IXmUserOneDateListener userOneDateListener) {
		if(userOneDateListener != null) {
			mUserOneDateListener.add(userOneDateListener);
		}
	}

	public void updatePlayId(long trackId, String playId) {
		if (mUploader != null && playId != null) {
			String uploaderTrackId = mUploader.getTrackId();
			if (uploaderTrackId != null && uploaderTrackId.equals(String.valueOf(trackId))) {
				mUploader.setPlayId(playId);
			}
		}
	}

	private int mCurrPos1 = 0;

	public boolean checkIsSeek(int currPos,int duration){
		int currPosLost = currPos - mCurrPos1;
		mCurrPos1 = currPos;
		if (Math.abs(currPosLost) > 1200 ) {
			mIsSeek = true;
		} else {
			mIsSeek = false;
		}
		return mIsSeek;
	}


	private long mBlockTime1;
	private long mBlockTime2;
	private boolean mIsBlocked;

	public void stuckStatistics(int percent,int duration,int currposition){
		int mBufferCurr = 0;
		if (percent != 0) {
			mBufferCurr = (percent * duration) / 100;
		}
		long mLastBlockTime =0;
		int playLost = currposition - mBufferCurr ;

		if (currposition > (15 * 1000) && currposition < duration - (15 * 1000) && percent > 5) {
			if (currposition >= mBufferCurr) {
				mIsBlocked = true;// 现在卡了
				if (!mIsSeek) {
					mBlockTime1 = System.currentTimeMillis();
				}
			} else if (currposition < mBufferCurr + 3000) {
				if (mIsBlocked) {
					mBlockTime2 = System.currentTimeMillis();
					if (mBlockTime1 != 0) {
						mLastBlockTime = mBlockTime2 - mBlockTime1;
						if(mUploader != null){
							mUploader.onEvent(IXmPlayStatisticUploader.EVENT_ADD_BLOCK_DURATION, mLastBlockTime);
						}
						mIsBlocked = false;
					}
				}
			}
		}
	}

	@Nullable
	private Track mPlayingTrack;  //音频播放器正在播放的数据的track

	private boolean mIsPlayTrack;  //音频播放器是否正在播放点播声音



	private void onVideoPlayStart(Track track){

	}


	//视频播放时长，只有当视频播放器和音频播放器播放同一个声音时该值才有效
	private int mVideoPlayDurationSec;

	public void onVideoPlayEnd(Track track, int breakSec, int playDurationSec){
		if(mPlayingTrack != null && mPlayingTrack.getDataId() == track.getDataId()){
			mVideoPlayDurationSec += playDurationSec;  //累加，而不是直接赋值，防止用户一直播同一个视频，时长却只记了最后一个
			Logger.d(TAG, "000 title: " + track.getTrackTitle() + ", playDurationSec: " + playDurationSec + ", mVideoPlayDurationSec: " + mVideoPlayDurationSec);
		}else{
			IXmPlayStatisticUploader trackStatisticsUploader = PlayStatisticsUploaderManager.
					getInstance().
					newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_TRACK_PLAY_STATISTICS, track);

			if(trackStatisticsUploader != null){
				if (track.getRelatedId() != null) {
					trackStatisticsUploader.setPlayId(track.getRelatedId());
					track.setRelatedId(null);
				}
				trackStatisticsUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_BREAK_SECOND, breakSec);
				trackStatisticsUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_DURATION_SEC, playDurationSec);
				trackStatisticsUploader.upload();
			}
			Logger.d(TAG, "001 title: " + track.getTrackTitle() + ", playDurationSec: " + playDurationSec + ", breakSec: " + breakSec);
		}
	}

	public void onSwitchInAudio(int switchInSec){
		if(mUploader != null){
			mUploader.onEvent(IXmPlayStatisticUploader.EVENT_ADD_SWITCH_IN_SEC, switchInSec);
		}
	}

	public Map<String, String> getDubPlayStatistics(){
		if(mDubPlayStatisticsUploader != null){
			return mDubPlayStatisticsUploader.getParams();
		}

		return null;
	}

	public void onSwitchOutAudio(int switchOutSec){
		if(mUploader != null){
			mUploader.onEvent(IXmPlayStatisticUploader.EVENT_ADD_SWITCH_OUT_SEC, switchOutSec);
		}
	}

	public void onPlayTrack(Track track, boolean online, int position, @Nullable String currentPlayUrl,
							int currPlayMethod) {
		if (track == null) {
			return;
		}

		mStartTime = System.currentTimeMillis();
		mIsShouldStatistic = true;
		if (mPlayingTrack == null ||
				mUploader == null ||
				track.getDataId() != mPlayingTrack.getDataId() ||
				(track.getDataId() == mPlayingTrack.getDataId() &&
						XmPlayerService.getPlayerSrvice()!=null &&
						XmPlayerService.getPlayerSrvice().getXmPlayMode() == XmPlayListControl.PlayMode.PLAY_MODEL_SINGLE_LOOP)) {
			mPlayingTrack = track;

			mIsPlayTrack = false;

			//清空卡顿统计信息
			mIsBlocked = false;
			mBlockTime1 = 0;
			mBlockTime2 = 0;

			XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
			if(xmPlayerService != null) {
				XmPlayerControl xmPlayerControl = xmPlayerService.getPlayControl();
				if(xmPlayerControl != null) {
					xmPlayerControl.resetDuration();
				}
			}

			String playId = null;

			if (PlayableModel.KIND_SCHEDULE.equals(track.getKind())
					|| PlayableModel.KIND_RADIO.equals(track.getKind())) {
				if(track.getExtra()){//现场直播
					mUploader = PlayStatisticsUploaderManager.getInstance().
							newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_ACTIVITY_PLAY_STATISTICS, track);

				}else {
					String time = track.getStartTime() + "-"
							+ track.getEndTime();
					if (BaseUtil.isInTime(time) != 0) {  //广播回听作为声音处理
						mIsPlayTrack = true;

//						mUploader = PlayStatisticsUploaderManager.
//								getInstance().
//								newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_TRACK_PLAY_STATISTICS, track);
					}
					mUploader = PlayStatisticsUploaderManager.
							getInstance().
							newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_RADIO_PLAY_STATISTICS, track);
				}
            } else if (PlayableModel.KIND_TRACK.equals(track.getKind())
                    || PlayableModel.KIND_TTS.equals(track.getKind())
					|| PlayableModel.KIND_MODE_SLEEP.equals(track.getKind())) {
				mIsPlayTrack = true;

				playId = PlayIdManager.INSTANCE.generatePlayId();

				mUploader = PlayStatisticsUploaderManager.
						getInstance().
						newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_TRACK_PLAY_STATISTICS, track);

				if (mUploader != null) {
					mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_PLAY_ID, playId);
				}

			}else if (PlayableModel.KIND_LIVE_FLV.equals(track.getKind())){
				mIsPlayTrack = true;

				mUploader = PlayStatisticsUploaderManager.
						getInstance().
						newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_LIVE_PLAY_STATISTICS, track);

			} else if (PlayableModel.KIND_MYCLUB_REPLAY_TRACK.equals(track.getKind())) {
				mIsPlayTrack = true;
				mUploader = PlayStatisticsUploaderManager.getInstance()
						.newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_MYCLUB_REPLAY_STATISTICS, track);
			}

			try {
				if (mUploader != null) {
					mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_PLAY_TYPE,
							online ? XmPlayRecord.PLAY_TYPE_ONLINE : XmPlayRecord.PLAY_TYPE_LOCAL);

					mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_START_TIME, mStartTime);

					if (!TextUtils.isEmpty(currentPlayUrl)) {
						mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_PLAY_URL, currentPlayUrl);
					}

					boolean lockScreenShowing = false;
					if(xmPlayerService != null) {
						IXmCommonBusinessDispatcher iXmCommonBusinessDispatcher =
								xmPlayerService.getIXmCommonBusinessDispatcher();
						if(iXmCommonBusinessDispatcher != null) {
							lockScreenShowing = iXmCommonBusinessDispatcher.lockScreenActivityIsShowing();
						}
					}

					mUploader.onEvent(IXmPlayStatisticUploader.EVENT_IS_CLOSE_SCREEN, !(BaseUtil.isAppForeground(mContext) && !lockScreenShowing));
				}
			}catch (Exception e){  //多线程会导致空指针,临时catch
				e.printStackTrace();
			}

			if(mUploader != null) {
				mUploader.onEvent(IXmPlayStatisticUploader.EVENT_NEXT_TYPE ,
						currPlayMethod == PlayerConstants.PLAY_METHOD_MANUAL_PLAY ? 4 : currPlayMethod);
			}

			if (statTrackNow(track)) {
				IXmPlayStatisticUploader uploader = PlayStatisticsUploaderManager.getInstance().
						newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_TRACK_PLAY_COUNT, track);
				if(uploader != null) {
					if (playId != null) {
						uploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_PLAY_ID, playId);
					}
					uploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_START_TIME, mStartTime);
					uploader.upload();
				}
			} else if (statRadioNow(track)) {
				IXmPlayStatisticUploader uploader = PlayStatisticsUploaderManager.getInstance().
						newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_RADIO_PLAY_COUNT, track);
				if(uploader != null) {
					uploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_START_TIME, mStartTime);
					uploader.upload();
				}
			} else if (statActivityNow(track)) {
				IXmPlayStatisticUploader uploader = PlayStatisticsUploaderManager.getInstance().
						newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_ACTIVITY_PLAY_COUNT, track);
				if(uploader != null) {
					uploader.upload();
				}
			} else if (statMyclubReplayNow(track)) {
				IXmPlayStatisticUploader uploader = PlayStatisticsUploaderManager.getInstance().
						newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_MYCLUB_REPLAY_PLAY_COUNT, track);
				if (uploader != null) {
					uploader.upload();
				}
			}
		}
		if(!mIsPlayTrack) {
			XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
			if(xmPlayerService != null) {
				XmPlayerControl xmPlayerControl = xmPlayerService.getPlayControl();
				if(xmPlayerControl != null) {
					xmPlayerControl.setLastPosition(System.currentTimeMillis());
				}
			}
		}else {
			XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
			if(xmPlayerService != null) {
				XmPlayerControl xmPlayerControl = xmPlayerService.getPlayControl();
				if(xmPlayerControl != null) {
					// 下面的代码是设置错了,但是因为线上已经运行很长时间了,目前先不做改动
					xmPlayerControl.setPlayedDuration(position);
					Logger.i("statisticsXmManager", "setPlayedDuration " + position);
				}
			}
		}
	}
	private long mTotalPlayMilliSec = 0L;  //本次启动应用的播放总时长, 单位毫秒

	public void updatePlayDuration(long playDurationMilliSec, float natureDurationMilliSec, long vagueDuration){
		if(mUploader != null) {
			try {
				mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_DURATION_SEC, (int) (playDurationMilliSec / 1000));
				mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_NATURE_DURATION_SEC, (int) (natureDurationMilliSec / 1000));
				mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_VAGUE_DURATION, (int)(vagueDuration / 1000));

				if (Logger.isDebug) {
					Logger.d("playSourceTraceOut", "XmStatisticManager updatePlayDuration, mDuration: " + (int) (playDurationMilliSec / 1000) + ", mListenedDuration: " + (int) (natureDurationMilliSec / 1000));
//					Logger.d("playSourceTraceWithStackTrace", "XmStatisticManager updatePlayDuration, mDuration: " + (int) (playDurationMilliSec / 1000) + ", mListenedDuration: " + (int) (natureDurationMilliSec / 1000) + Log.getStackTraceString(new Throwable()));
				}
			}catch (Exception e){  //多线程会导致空指针 https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/8214485?pid=1
				e.printStackTrace();
			}
		}
	}

	public void onStopTrack(Track track, int breakMillisecond) {
		Logger.d(TAG, "onStopTrack breakSecond" + (breakMillisecond / 1000));
 		if (track == null || mUploader == null) {
			Logger.d(TAG, "onStopTrack -------2");
			return;
		}
		if(!mIsShouldStatistic) {
			return;
		}
		Logger.d(TAG, "onStopTrack " + track.getTrackTitle() + "-------3");
		mIsShouldStatistic = false;

		long startTime = 0;
		long endTime = 0;

		if(!mIsPlayTrack) {
			SimpleDateFormat sdf = new SimpleDateFormat("yy:MM:dd:HH:mm", Locale.getDefault());
			if (!TextUtils.isEmpty(track.getStartTime())
					&& !TextUtils.isEmpty(track.getEndTime())) {
				try {
					startTime = sdf.parse(track.getStartTime()).getTime();
					// 因为云历史中定义的endTime 表示用户离开播放时的时间
					endTime = System.currentTimeMillis();
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}
		}

		if(PlayableModel.KIND_SCHEDULE.equals(track.getKind()) || PlayableModel.KIND_RADIO.equals(track.getKind())) {
			mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_PROGRAM_ID, track.getProgramId());
			mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_PROGRAM_SCHEDULE_ID, track.getScheduleId());
		}

		//先获取已经播放的时间，升级版本清零播放时长，存在跨进程同步的问题
		mTotalPlayMilliSec = MMKVRaiseUtil.getInstance(mContext).getPlayTimeMs(mTotalPlayMilliSec);

		//计算时间
		int playDurationMilliSec = 0;
		XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
		if(xmPlayerService != null) {
			XmPlayerControl xmPlayerControl = xmPlayerService.getPlayControl();
			if(xmPlayerControl != null) {
				playDurationMilliSec = (int) xmPlayerControl.getPlayedDuration();
			}
		}
		mTotalPlayMilliSec += playDurationMilliSec;
		MMKVRaiseUtil.getInstance(mContext).savePlayTimeMs(mTotalPlayMilliSec);

		mUploader.onEvent(IXmPlayStatisticUploader.EVENT_ON_END_PLAY, ((long)playDurationMilliSec / 1000) + mVideoPlayDurationSec);   //参数必须是long型
		mVideoPlayDurationSec = 0;

		mUploader.onEvent(IXmPlayStatisticUploader.EVENT_PLAY_TONE_QUALITY,
				track.getTrackCurPlayQualityLevel() >= 0 ? track.getTrackCurPlayQualityLevel() : TrackUrlChooseManager.getInstance().getRealTrackQuality(track));

		//修复云历史播放顺序更新错误，服务端根据endAt字段来对播放记录排序，
		// 当播放新的声音时，客户端会将当前正在播放的声音停止，
		// 并将该声音的停止时间发送至服务端，
		// 但真正传输的参数是发送网络请求时的System.currentMillions；
		// 同时新声音的播放记录也会传输到服务端，该记录的endAt与startAt相同，
		// 这将导致有一定概率结束的endAt大于新开始的endAt，所以历史记录异常

		if(endTime == 0){
			try{
				SimpleDateFormat sdf = new SimpleDateFormat("yy:MM:dd:HH:mm", Locale.getDefault());
				endTime = sdf.parse(track.getEndTime()).getTime();
			}catch (Exception e){
				endTime = System.currentTimeMillis();
			}
		}
		mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_END_TIME, endTime);

		int playedDurationPercent = 0;
		if(mIsPlayTrack) {
			if(track.getDuration() != 0) {
				playedDurationPercent = (playDurationMilliSec) / track.getDuration();
			}
		}else {
			if((endTime - startTime) != 0) {
				playedDurationPercent = (int)(playDurationMilliSec / (endTime - startTime));
			}
		}
		long clientTraffic = playedDurationPercent*track.getDownloadSize() / 1000;
		mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_CLIENT_TRAFFIC, clientTraffic);

		if(!TextUtils.isEmpty(track.getRecSrc())){
			mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_REC_SRC, track.getRecSrc());
			mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_REC_TRACK, track.getRecTrack());
		}else if(track.getAlbum() != null){
			mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_REC_SRC, track.getAlbum().getRecSrc());
			mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_REC_TRACK, track.getAlbum().getRecTrack());
        }

		if (Logger.isDebug) {
			Logger.d("playSourceTraceOut", "XmStatisticsManager playSource: " + track.getPlaySource() + ", id: " + track.getDataId() + ", type: " + track.getType());
//			Logger.d("playSourceTraceWithStackTrace", "XmStatisticsManager playSource: " + track.getPlaySource() + ", id: " + track.getDataId() + ", type: " + track.getType() + ", " + Log.getStackTraceString(new Throwable()));
		}
        mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_PLAY_SOURCE, track.getPlaySource());

		if (mRecordModel != null) {
			mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_CONNECT_TYPE, mRecordModel.getType());
			mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_CONNECT_DEVICE, mRecordModel.getDevice());
			mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_CONNECT_DEVICE_NAME, mRecordModel.getDeviceName());
		}


		if(TextUtils.isEmpty(xmUploadPlayResource)){
			xmUploadPlayResource = xmCurPlayResource;
		}

		mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_XM_UPLOAD_PLAY_RESOURCE, xmUploadPlayResource);

		mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_BREAK_SECOND, breakMillisecond / 1000);
		updateXmResource();
		if(mUploader != null) {
			if(track.getType() == Track.TYPE_DUBSHOW){ //配音秀的播放数据不通过声音统计接口上传
				mDubPlayStatisticsUploader = mUploader;
				PlayStatisticsUploaderManager.getInstance().asyncRemoveUploader(mDubPlayStatisticsUploader);
			}else {
				mUploader.upload();
			}
			mUploader = null;  //上传之后置空，防止重复上传
		}
		updateXmPlayResource();
		if(xmPlayerService != null) {
			XmPlayerControl xmPlayerControl = xmPlayerService.getPlayControl();
			if(xmPlayerControl != null) {
				xmPlayerControl.resetDuration();
			}
		}
	}

	private void updateXmPlayResource() {
		if(TextUtils.equals(xmCurPlayResource,xmUploadPlayResource))return;
		if(!TextUtils.isEmpty(xmCurPlayResource)){
			xmUploadPlayResource = xmCurPlayResource;
		}
	}
	private void updateXmResource(){
		if(mXmResourceMap == null)return;
		if(mUploader != null && mXmResourceMap.containsKey("tid")){
			mUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_TID, mXmResourceMap.get("tid"));
		}
	}

	public void release() {
		synchronized (sLock) {
			XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
			if(xmPlayerService != null) {
				XmPlayerControl xmPlayerControl = xmPlayerService.getPlayControl();
				if(xmPlayerControl != null) {
					xmPlayerControl.resetDuration();
					xmPlayerControl.setPlayedDuration(0);
				}
			}
			if(mUserOneDateListener != null) {
				for (IXmUserOneDateListener listener : mUserOneDateListener) {
					listener.release();
				}
			}
		}
	}

	public void setRecordModel(RecordModel model) {
		mRecordModel = model;
	}
	private boolean statTrackNow(Track track){
        if (track == null || (!PlayableModel.KIND_TRACK.equals(track.getKind())
                && !PlayableModel.KIND_TTS.equals(track.getKind())
				&& !PlayableModel.KIND_MODE_SLEEP.equals(track.getKind()))) {
            return false;
        } else {
            return true;
        	}
	}
	private boolean statRadioNow(Track track){
        	if (track == null || !PlayableModel.KIND_SCHEDULE.equals(track.getKind())) {
        	    return false;
        	} else {
        	    return true;
        	}
	}

	private boolean statActivityNow(Track track){
		if (track == null || !PlayableModel.KIND_RADIO.equals(track.getKind()) || !track.getExtra()) {
			return false;
		} else {
			return true;
		}
	}

	private boolean statMyclubReplayNow(Track track) {
		return track != null && PlayableModel.KIND_MYCLUB_REPLAY_TRACK.equals(track.getKind());
	}

	public void setmIsSeek(boolean mIsSeek) {
		this.mIsSeek = mIsSeek;
	}

	public void onSeekComplete(int seekToPosition) {
		if(mUserOneDateListener != null) {
			for (IXmUserOneDateListener listener : mUserOneDateListener) {
				listener.onSeekComplete(seekToPosition);
			}
		}
	}

	public void onPlayStart(int progress ,boolean isRadio) {
		if (mUserOneDateListener != null) {
			for (IXmUserOneDateListener listener : mUserOneDateListener) {
				listener.onPlayStart(progress, isRadio);
			}
		}
	}

	public void onPlayProgress(int currPos,int duration ,boolean isRadio) {
		if(mUserOneDateListener != null) {
			for (IXmUserOneDateListener listener : mUserOneDateListener) {
				listener.onPlayProgress(currPos, duration, isRadio);
			}
		}
	}

	public void onSoundSwitch() {
		if(mUserOneDateListener != null) {
			for (IXmUserOneDateListener listener : mUserOneDateListener) {
				listener.onSoundSwitch();
			}
		}
	}

	public void onPlayStop() {
		if(mUserOneDateListener != null) {
			for (IXmUserOneDateListener listener : mUserOneDateListener) {
				listener.onPlayStop();
			}
		}
	}

	public void onMixStatusChanged(double key, boolean isPlaying, long curPosition) {
		Map<String, Long> info = getMixRecordParams(key);
		if (info == null || info.size() <= 1) {
			Log.w(TAG, "onMixStatusChanged: getMixRecordParams trackId albumId is empty " );
			return;
		}
		IXmPlayStatisticUploader mixPlayerUploader = getUploader(key, info);
		if (mixPlayerUploader != null) {
			if (isPlaying) {
//				Log.i(TAG, "onMixStatusChanged: EVENT_ON_START_PLAY " + key);
				mixPlayerUploader.onEvent(IXmPlayStatisticUploader.EVENT_ON_START_PLAY, null);
			} else {
				// 因为会同时上报，我这里添加playId逻辑
				String playId = PlayIdManager.INSTANCE.generatePlayId();

				mixPlayerUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_PLAY_ID, playId);
				mixPlayerUploader.onEvent(IXmPlayStatisticUploader.EVENT_ON_END_PLAY, curPosition / 1000);
				mixPlayerUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_BREAK_SECOND, (int)(curPosition / 1000));
//				Log.i(TAG, "onMixStatusChanged: EVENT_ON_END_PLAY " + key);
				mixPlayerUploader.upload();
				mMixPlayerUploaders.remove(key);

				IXmPlayStatisticUploader uploader = PlayStatisticsUploaderManager.getInstance().
						newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_SLEEP_MODE_PLAY_COUNT, info);
				if (uploader != null) {
					uploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_PLAY_ID, playId);
					uploader.upload();
				}
			}
		}
	}

	private IXmPlayStatisticUploader getUploader(double key, @NonNull Map<String, Long> info) {
		IXmPlayStatisticUploader mixPlayerUploader;
		if (!mMixPlayerUploaders.containsKey(key)) {
			mixPlayerUploader = PlayStatisticsUploaderManager.getInstance().
					newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_SLEEP_MODE_PLAY_STATISTICS, info);
			mMixPlayerUploaders.put(key, mixPlayerUploader);
		} else {
			mixPlayerUploader = mMixPlayerUploaders.get(key);
		}
		return mixPlayerUploader;
	}

	public void onMixSoundComplete(double key) {
		Map<String, Long> info = getMixRecordParams(key);
		if (info == null || info.size() <= 1) {
			Log.w(TAG, "onMixStatusChanged: getMixRecordParams trackId albumId is empty " );
			return;
		}
		// 一次播放完成上报
		IXmPlayStatisticUploader mixPlayerUploader = PlayStatisticsUploaderManager.getInstance().
				newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_SLEEP_MODE_PLAY_STATISTICS, info);
		if (mixPlayerUploader != null) {
			long curPosition = MixPlayerService.getMixService().getCurPosition(key);
			long duration = MixPlayerService.getMixService().getDuration(key);
			mixPlayerUploader.onEvent(IXmPlayStatisticUploader.EVENT_ON_START_PLAY, System.currentTimeMillis() - duration);
			mixPlayerUploader.onEvent(IXmPlayStatisticUploader.EVENT_ON_END_PLAY, curPosition / 1000);
			mixPlayerUploader.onEvent(IXmPlayStatisticUploader.EVENT_SET_BREAK_SECOND, (int)(curPosition / 1000));
//			Log.i(TAG, "onMixSoundComplete:  START_END " + key);
			mixPlayerUploader.upload();
		}
		IXmPlayStatisticUploader uploader = PlayStatisticsUploaderManager.getInstance().
				newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_SLEEP_MODE_PLAY_COUNT, info);
		if (uploader != null) {
			uploader.upload();
		}
		IXmPlayStatisticUploader statisticUploader = getUploader(key, info);
		if (statisticUploader != null) {
			statisticUploader.onEvent(IXmPlayStatisticUploader.EVENT_ON_START_PLAY, null);
		}
	}

	public static Map<String, Long> getMixRecordParams(double key) {
		Map<String, Object> map = MixPlayerService.getMixService().getDataSourceInfo(key);
		Map<String, Long> params = new HashMap<>();
		if (map == null) {
			Log.e(TAG, "onMixStatusChanged: map == null ");
			return null;
		}
		if (map.containsKey("trackId")) {
			Object ob1 = map.get("trackId");
			if (ob1 instanceof Long) {
				params.put("trackId", (Long) ob1);
			}
		}
		if (map.containsKey("albumId")) {
			Object ob2 = map.get("albumId");
			if (ob2 instanceof Long) {
				params.put("albumId", (Long) ob2);
			}
		}
		params.put("playSource", 3002L);
		return params;
	}

}
