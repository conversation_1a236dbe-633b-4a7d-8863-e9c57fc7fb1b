package com.ximalaya.ting.android.opensdk.player.manager;

import android.content.Context;

import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.BaseInfoOnErrorModel;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerBaseInfoRequestListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmSimplePlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.player.XMediaplayerJNI;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;

/**
 * Created by WolfXu on 5/10/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class TempoManagerForPlayer {
    private TempoManagerForPlayer() {

    }

    private static class TempoManagerForPlayerHolder {
        static TempoManagerForPlayer sInstance = new TempoManagerForPlayer();
    }

    public static TempoManagerForPlayer getInstance() {
        return TempoManagerForPlayerHolder.sInstance;
    }

    public void init() {
        XmPlayerService.addPlayerStatusListenerOnPlayProcees(mPlayerStatusListener);
        SendPlayStaticManager.FunctionForPlayProgress.addBaseInfoRequestListener(mXmPlayerBaseInfoRequestListener);
    }

    public void release() {
        XmPlayerService.removePlayerStatusListenerOnPlayProcess(mPlayerStatusListener);
        SendPlayStaticManager.FunctionForPlayProgress.removeBaseInfoRequestListener(mXmPlayerBaseInfoRequestListener);
    }

    private final IXmPlayerStatusListener mPlayerStatusListener = new IXmSimplePlayerStatusListener() {
        @Override
        public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
            XmPlayerService service = XmPlayerService.getPlayerSrvice();
            if (service == null) {
                return;
            }
            // 儿童模式有自己的逻辑，不要走下面的逻辑
            if (isInKisMode(service)) {
                return;
            }

            // 快听
            if (QuickListenForPlayProcessUtil.isQuickListen()) {
                service.setQuickListenTempo(QuickListenForPlayProcessUtil.getQuickListenTempo(service));
                return;
            }

            // 一起听不要倍速
            if (curModel instanceof Track && PlayableModel.KIND_TRACK.equals(curModel.getKind()) &&
                    ((Track) curModel).getPlaySource() != ConstantsOpenSdk.PLAY_FROM_LISTEN_SCENE) {
                Track track =  (Track) curModel;
                float tempo = 0;
                if (track.getAlbum() != null) {
                    long albumId = track.getAlbum().getAlbumId();
                    if (albumId > 0) {
                        tempo = MMKVUtil.getInstance(PreferenceConstantsInOpenSdk.TINGMAIN_FILENAME_ALBUM_TEMPO_SETTINGS).
                                getFloat(String.valueOf(albumId), 0);
                    }
                }
                if (tempo <= 0) {
                    tempo = MmkvCommonUtil.getInstance(service.getApplicationContext()).getFloat(
                            PreferenceConstantsInOpenSdk.KEY_PLAY_TEMPO_FOR_PLAY,
                            XMediaplayerJNI.TEMPO_DEFAULT);
                }
                if (tempo <= 0) {
                    tempo = XMediaplayerJNI.TEMPO_DEFAULT;
                }
                if (service.getPlayControl() != null && service.getPlayControl().getTempo() != tempo) {
                    service.setTempo(tempo);
                }
            }
        }
    };

    private final IXmPlayerBaseInfoRequestListener mXmPlayerBaseInfoRequestListener = new IXmPlayerBaseInfoRequestListener() {
        @Override
        public void onTrackBaseInfoBackSuccess(Track track) {
            if (track == null) {
                return;
            }
            XmPlayerService service = XmPlayerService.getPlayerSrvice();
            if (service == null) {
                return;
            }
            // 儿童模式有自己的逻辑，不要走下面的逻辑
            if (isInKisMode(service)) {
                return;
            }

            // 快听
            if (QuickListenForPlayProcessUtil.isQuickListen()) {
                service.setQuickListenTempo(QuickListenForPlayProcessUtil.getQuickListenTempo(service));
                return;
            }

            // 一起听不要倍速
            if (PlayableModel.KIND_TRACK.equals(track.getKind()) &&
                    track.getPlaySource() != ConstantsOpenSdk.PLAY_FROM_LISTEN_SCENE) {
                float tempo = 0;
                if (track.getAlbum() != null) {
                    long albumId = track.getAlbum().getAlbumId();
                    if (albumId > 0) {
                        tempo = MMKVUtil.getInstance(PreferenceConstantsInOpenSdk.TINGMAIN_FILENAME_ALBUM_TEMPO_SETTINGS).
                                getFloat(String.valueOf(albumId), 0);
                    }
                }
                if (tempo <= 0) {
                    tempo = MmkvCommonUtil.getInstance(service.getApplicationContext()).getFloat(
                            PreferenceConstantsInOpenSdk.KEY_PLAY_TEMPO_FOR_PLAY,
                            XMediaplayerJNI.TEMPO_DEFAULT);
                }
                if (tempo <= 0) {
                    tempo = XMediaplayerJNI.TEMPO_DEFAULT;
                }
                if (service.getPlayControl() != null && service.getPlayControl().getTempo() != tempo) {
                    service.setTempo(tempo);
                }
            }
        }

        @Override
        public void onTrackBaseInfoBackError(BaseInfoOnErrorModel baseInfoOnErrorModel) {
        }
    };

    private static boolean isInKisMode(Context context) {
        return MmkvCommonUtil.getInstance(context).getBoolean(PreferenceConstantsInOpenSdk.KIDS_KEY_IS_IN_KIDS_MODE, false);
    }
}
