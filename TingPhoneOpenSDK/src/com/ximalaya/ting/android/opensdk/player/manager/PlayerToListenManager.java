package com.ximalaya.ting.android.opensdk.player.manager;

import android.os.RemoteException;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.service.IXmCommonBusinessDispatcher;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.PlayListMMKVUtil;
import com.ximalaya.ting.android.opensdk.util.ToListenUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.ICommonBusiService;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2023/9/1
 * Description：
 */
public class PlayerToListenManager {
    public boolean mContinueToPlayToListenTrack;
    public boolean ignorePlayerToListenLogic;
    private int mPlayCompleteLoop;

    private static final class HOLDER {
        static final PlayerToListenManager INSTANCE = new PlayerToListenManager();
    }

    public static PlayerToListenManager getInstance() {
        return PlayerToListenManager.HOLDER.INSTANCE;
    }

    /**
     * 1.点击下一首的时候，当前声音会被删除
     * 2.自动播到推荐列表，按照列表顺序播放
     * 3.手动启播推荐列表的声音，会置顶为第一条
     * */
    public void handleOnSoundSwitch(PlayableModel curModel, PlayableModel lastModel) {
        if (MMKVUtil.getInstance().getBoolean(ToListenUtil.TO_LISTEN_ANDROID_NEW_CLOSE)) {
            return;
        }
        if (curModel == null && lastModel instanceof Track && !((Track) lastModel).isAuthorized()) {
            Logger.logToFile("PlayerToListenManager no isAuthorized track");
            ignorePlayerToListenLogic = true;
        }
        if (curModel == null) {
            mPlayCompleteLoop++;
        } else {
            mPlayCompleteLoop = 0;
        }
        if (mPlayCompleteLoop > 5) {
            Logger.logToFile("PlayerToListenManager mPlayCompleteLoop over 3");
            return;
        }
        boolean isCurrentToListen = false;
        boolean isLastToListen = false;
        XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
        if (xmPlayerService == null) {
            return;
        }
        boolean isPlayingPodCastList = xmPlayerService.isPlayingPodCastList();
        if (curModel instanceof Track) {
            isCurrentToListen = ((Track) curModel).getIsFromToListenTrack() == 1;
            if (!isCurrentToListen) {
                xmPlayerService.clearNormalData();
            }
        }
        if (lastModel instanceof Track) {
            isLastToListen = ((Track) lastModel).getIsFromToListenTrack() == 1 || ((Track) lastModel).getIsLastModelFromToListenTrack() == 1;
        }
        if (curModel == null || lastModel == null) {
            Logger.logToFile("PlayerToListenManager curModel is null = " + (curModel == null) + ", lastModel null = " + (lastModel == null));
        }
        if (XmPlayListControl.PlayMode.PLAY_MODEL_SINGLE_LOOP == xmPlayerService.getPlayMode()) {
            Logger.logToFile("PlayerToListenManager PLAY_MODEL_SINGLE_LOOP");
            ignorePlayerToListenLogic = true;
        } else {
            if (isCurrentToListen && !isLastToListen) {
                xmPlayerService.setPlayMode(XmPlayListControl.PlayMode.PLAY_MODEL_LIST);
            } else if (!isCurrentToListen && isLastToListen) {
                int originalPlayMode = PlayListMMKVUtil.getInstance(xmPlayerService.getContext()).getInt(
                        PreferenceConstantsInOpenSdk.TINGMAIN_KEY_PLAY_MODE, XmPlayListControl.PlayMode.PLAY_MODEL_LIST.ordinal());
                xmPlayerService.setPlayMode(XmPlayListControl.PlayMode.getIndex(originalPlayMode));
            }
        }
        if (curModel == null && isLastToListen) {
            Logger.logToFile("PlayerToListenManager reset ignorePlayerToListenLogic");
            ignorePlayerToListenLogic = false;
        }

        if (lastModel instanceof Track) {
            Track lastSound = (Track) lastModel;
            if ((isLastToListen || xmPlayerService.isPlayingPodCastList()) && ToListenUtil.isListenComplete(true, xmPlayerService.getContext(), lastSound)) {
                ToListenUtil.deleteTrackDataById(lastSound.getDataId());
//                ToListenUtil.getRecommendMMKVUtil().removeByKey(lastSound.getDataId() + "");
                Logger.logToFile("PlayerToListenManager delete last complete");
            }
            Logger.logToFile("PlayerToListenManager isCurrentToListen = " + isCurrentToListen + ", isLastToListen = " + isLastToListen +
                    "  ignorePlayerToListenLogic = " + ignorePlayerToListenLogic +  "，isPlayingPodCastList = " + isPlayingPodCastList);
            if (!ignorePlayerToListenLogic) {
                if (MmkvCommonUtil.getInstance(xmPlayerService.getContext()).getBoolean(PreferenceConstantsInOpenSdk.KEY_OPEN_TO_LISTEN_AUTO_LOGIC, true)) {
                    boolean isTimerStoppedForPlay = PlanTerminateManagerForPlay.getInstance().isTimerStoppedForPlay() && PlanTerminateManagerForPlay.getInstance().isLastSeriesType();
                    if (isLastToListen && curModel == null) {
                        // 待播播完，播放普通声音
                        Logger.logToFile("PlayerToListenManager 待播播完，播放普通声音 play original list， isTimerStoppedForPlay = " + isTimerStoppedForPlay);
                        playToListenListAfterPlayNormalTrack(xmPlayerService, false, true,
                                !isTimerStoppedForPlay && TextUtils.equals(ToListenUtil.getToListenOriginalPlayListMode(), DTransferConstants.PARAM_SUB_PLAY_LIST_TO_LISTEN), false);
                    } else if ((mContinueToPlayToListenTrack || isPlayingPodCastList) && !isLastToListen && !isCurrentToListen &&
                            ToListenUtil.getMMKVUtil().getAllKeys() != null && ToListenUtil.getMMKVUtil().getAllKeys().length > 0) {
                        // 一首普通声音播完，准备自动播待播声音
                        boolean deleteCurrent = curModel == null && isPlayingPodCastList;
                        Logger.logToFile("PlayerToListenManager 一首普通声音播完，准备自动播待播声音， deleteCurrent = " + deleteCurrent + "  ,isTimerStoppedForPlay = " + isTimerStoppedForPlay);
                        playToListenListAfterPlayNormalTrack(xmPlayerService, true, false, !isTimerStoppedForPlay, deleteCurrent);
                    }
                }
            }
        }
        ignorePlayerToListenLogic = false;
        mContinueToPlayToListenTrack = false;
    }

    private long lastRequestPatchId;
    public void onPlayProgress(PlayableModel curData, int currPos, int duration) {
        // 提前4秒设置前贴
        if ((duration - currPos) < 4000 && PlayableModel.KIND_TRACK.equals(curData.getKind()) && lastRequestPatchId != curData.getDataId()) {
            lastRequestPatchId = curData.getDataId();
            XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
            if (xmPlayerService == null) {
                return;
            }
            if (xmPlayerService.isPlayingPodCastList() && curData instanceof Track && ((Track) curData).getIsFromToListenTrack() != 1) {
                String[] toListenArray = ToListenUtil.getMMKVUtil().getAllKeys();
                if (toListenArray != null && toListenArray.length > 0) {
                    ToListenUtil.getToListenTracksForPlayService(new IDataCallBack<List<Track>>() {
                        @Override
                        public void onSuccess(@Nullable List<Track> data) {
                            xmPlayerService.getTimeHander().post(() -> {
                                if (data != null && data.size() > 0) {
                                    ICommonBusiService iCommonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
                                    if (iCommonBusiService != null) {
                                        iCommonBusiService.registerAndPlayToListenSoundPatch(data.get(0).getDataId());
                                    }
                                }
                            });
                        }

                        @Override
                        public void onError(int code, String message) {

                        }
                    });
                }
            }
        }
    }

    public void playToListenListAfterPlayNormalTrack(XmPlayerService xmPlayerService, boolean saveNormalList,
                                                     boolean playOriginalList, boolean autoPlay, boolean deleteCurrentNormalTrack) {
        // 播完一首非待播的声音，保存原来列表和index，并且开播待播列表+推荐列表
        String[] toListenArray = ToListenUtil.getMMKVUtil().getAllKeys();
        if (!playOriginalList && toListenArray != null && toListenArray.length > 0) {
            if (saveNormalList) {
                xmPlayerService.saveOriginPlayListBeforeToListen(deleteCurrentNormalTrack);
            }
            ToListenUtil.getToListenTracksForPlayService(new IDataCallBack<List<Track>>() {
                @Override
                public void onSuccess(@Nullable List<Track> data) {
                    xmPlayerService.getTimeHander().post(() -> {
                        if (xmPlayerService.mListControl != null && data != null && data.size() > 0 && xmPlayerService.getPlayerImpl() != null) {
                            try {
                                if (xmPlayerService.getPlayMode() != XmPlayListControl.PlayMode.PLAY_MODEL_LIST) {
                                    xmPlayerService.setPlayMode(XmPlayListControl.PlayMode.PLAY_MODEL_LIST);
                                }
                                xmPlayerService.setPlayList(null, data);
                                xmPlayerService.lastRequestTime = xmPlayerService.lastRequestTime -
                                MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getInt(PreferenceConstantsInOpenSdk.KEY_INTERVAL_SOUND_PATCH_REQUEST_TIME, 1000);
                                if (autoPlay) {
                                    xmPlayerService.play(0);
                                } else {
                                    xmPlayerService.setPlayIndex(0);
                                }
                                Logger.logToFile("PlayerToListenManager 播完一首非待播的声音，开播待播列表, autoPlay = " + autoPlay);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }

                @Override
                public void onError(int code, String message) {

                }
            });
        } else if (playOriginalList) {
            ToListenUtil.playOriginalList(xmPlayerService, null, 0, autoPlay);
        }
    }

    public void handleOnPlayNext(PlayableModel curModel) {
        if (MMKVUtil.getInstance().getBoolean(ToListenUtil.TO_LISTEN_ANDROID_NEW_CLOSE)) {
            return;
        }
        XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
        if (xmPlayerService == null) {
            return;
        }
        if (!(curModel instanceof Track)) {
            return;
        }
        boolean isCurrentToListen = ((Track) curModel).getIsFromToListenTrack() == 1;
        // 这一首是待播或播客内容  点击下一首直接删除
        if (isCurrentToListen && PlayerToListenManager.getInstance().isPlayingToListenTrack()) {
            IXmCommonBusinessDispatcher iXmCommonBusinessDispatcher = xmPlayerService.getIXmCommonBusinessDispatcher();
            Logger.logToFile("PlayerToListenManager  这一首是待播或播客内容  点击下一首直接删除");
            try {
                if (iXmCommonBusinessDispatcher != null) {
                    iXmCommonBusinessDispatcher.deleteLastUnCompleteToListen(true, curModel.getDataId());
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    public boolean isPlayingToListenTrack() {
        XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
        if (xmPlayerService == null) {
            return false;
        }
        PlayableModel curModel = xmPlayerService.getCurrPlayModel();
        if (!(curModel instanceof Track)) {
            return false;
        }
        Track track = (Track) curModel;
        return track.getIsFromToListenTrack() == 1;
    }

    public void handleOnPlayStart(PlayableModel curModel) {
        if (MMKVUtil.getInstance().getBoolean(ToListenUtil.TO_LISTEN_ANDROID_NEW_CLOSE)) {
            return;
        }
        XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
        if (xmPlayerService == null) {
            return;
        }
        if (curModel instanceof Track) {
            Track track = (Track) curModel;
            boolean isPlayingPodCastList = xmPlayerService.isPlayingPodCastList();
            if (track.getIsFromToListenTrack() == 1 || isPlayingPodCastList) {
                MMKVUtil.getInstance().saveLong("last_play_to_listen_time", System.currentTimeMillis());
            }
            boolean shouldAddNormalList = false;
            if (isPlayingPodCastList && ToListenUtil.getRecommendMMKVUtil().getAllKeys() == null) {
                shouldAddNormalList = true;
            } else if (track.getIsFromToListenTrack() == 1 && ToListenUtil.getMMKVUtil().getAllKeys() == null) {
                shouldAddNormalList = true;
            }
            if (shouldAddNormalList) {
                Logger.logToFile("PlayerToListenManager 边界情况处理");
                // 边界情况处理：从首页待播进入播放页，从未播过其他声音，播完待播 待播列表为空，此时再点击播放按钮，要将声音加入普通播放列表；
                track.setToListenRecommend(false);
                track.setIsFromToListenTrack(-1);
                List<Track> trackList = new ArrayList<>();
                trackList.add(track);
                xmPlayerService.setPlayList(null, trackList);
                xmPlayerService.lastRequestTime = xmPlayerService.lastRequestTime -
                        MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getInt(PreferenceConstantsInOpenSdk.KEY_INTERVAL_SOUND_PATCH_REQUEST_TIME, 1000);
                xmPlayerService.play(0, true);
            }

//            if (shouldAddToListen) {
//                IXmCommonBusinessDispatcher iXmCommonBusinessDispatcher = xmPlayerService.getIXmCommonBusinessDispatcher();
//                if (iXmCommonBusinessDispatcher != null) {
//                    try {
//                        iXmCommonBusinessDispatcher.addToListenForLastTrack(track);
//                    } catch (RemoteException e) {
//                        e.printStackTrace();
//                    }
//                }
//            }
        }
    }
}
