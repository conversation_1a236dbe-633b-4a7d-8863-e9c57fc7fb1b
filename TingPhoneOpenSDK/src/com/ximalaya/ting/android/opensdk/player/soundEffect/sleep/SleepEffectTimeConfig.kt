package com.ximalaya.ting.android.opensdk.player.soundEffect.sleep

import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import java.util.Calendar
import java.util.Locale

object SleepEffectTimeConfig {
    private val CONFIG_TIME_SWITCH = "SLEEP_EFFECT_CONFIG_TIME_SWITCH"
    private val CONFIG_TIME_START = "SLEEP_EFFECT_CONFIG_TIME_START"
    private val CONFIG_TIME_END = "SLEEP_EFFECT_CONFIG_TIME_END"
    private val CONFIG_TIME_LAST = "SLEEP_EFFECT_CONFIG_TIME_LAST"

    private val DEFAULT_START = 21*60 + 0L
    private val DEFAULT_END = 6*60 + 0L

    fun isActivate(): Boolean {
        return MMKVUtil.getInstance().getBoolean(CONFIG_TIME_SWITCH, false)
    }

    fun setActivate(activate: Boolean) {
        MMKVUtil.getInstance().saveBoolean(CONFIG_TIME_SWITCH, activate)
    }

    fun getStart(): Long {
        return MMKVUtil.getInstance().getLong(CONFIG_TIME_START, DEFAULT_START)
    }

    fun saveStart(start: Long) {
        MMKVUtil.getInstance().saveLong(CONFIG_TIME_START, start)
    }

    private fun getLastActivateTs(): Long {
        return MMKVUtil.getInstance().getLong(CONFIG_TIME_LAST, -1L)
    }

    fun saveLastActivateTs(last: Long) {
        MMKVUtil.getInstance().saveLong(CONFIG_TIME_LAST, last)
    }

    fun getStartStr(): String {
        val start = getStart()
        val hour = start / 60
        val min = start % 60
        return String.format(Locale.US, "%02d:%02d", hour, min)
    }

    fun getEnd(): Long {
        return MMKVUtil.getInstance().getLong(CONFIG_TIME_END, DEFAULT_END)
    }

    fun saveEnd(end: Long) {
        MMKVUtil.getInstance().saveLong(CONFIG_TIME_END, end)
    }

    fun getEndStr(): String {
        val start = getEnd()
        val hour = start / 60
        val min = start % 60
        return String.format(Locale.US, "%02d:%02d", hour, min)
    }

    @JvmStatic
    fun inTime(): Boolean {
        if (!isActivate()) {
            return false
        }

        val current = System.currentTimeMillis()
        val cal = Calendar.getInstance()
        cal.timeInMillis = current
        val hour = cal.get(Calendar.HOUR_OF_DAY)
        val min = cal.get(Calendar.MINUTE)
        val nowMin = hour * 60 + min


        val configuredStart = getStart()
        val configuredEnd = getEnd()

        //in one day
        if (configuredEnd > configuredStart) {
            return nowMin >= configuredStart && nowMin <= configuredEnd
        } else {
            return nowMin >= configuredStart || nowMin <= configuredEnd
        }
    }

    @JvmStatic
    fun hasEverStartInRange(): Boolean {
        val last = getLastActivateTs()
        if (last <= 0) return false

        val todayCal = Calendar.getInstance()
        todayCal.timeInMillis = System.currentTimeMillis()
        val today = todayCal.get(Calendar.DAY_OF_MONTH)
        val todayMin = todayCal.get(Calendar.HOUR_OF_DAY) * 60 + todayCal.get(Calendar.MINUTE)

        val cal = Calendar.getInstance()
        cal.timeInMillis = last
        val lastDay = cal.get(Calendar.DAY_OF_MONTH)
        val lastHour = cal.get(Calendar.HOUR_OF_DAY)
        val lastMin = cal.get(Calendar.MINUTE)
        val lastMinZ = lastHour * 60 + lastMin


        val configuredStart = getStart()
        val configuredEnd = getEnd()

        //in one day
        if (configuredEnd > configuredStart) {
            val inRange = lastMinZ in configuredStart..configuredEnd
            return inRange && lastDay == today
        }


        //down half day
        if (lastMinZ >= configuredStart && todayMin >= configuredStart) {
            return lastDay == today
        }

        //up half day
        if (todayMin <= configuredEnd) {
            if (lastMinZ <= configuredEnd) {
                return lastDay == today
            }

            if (lastMinZ >= configuredStart) {
                return lastDay == today - 1
            }
        }

        return false
    }
}