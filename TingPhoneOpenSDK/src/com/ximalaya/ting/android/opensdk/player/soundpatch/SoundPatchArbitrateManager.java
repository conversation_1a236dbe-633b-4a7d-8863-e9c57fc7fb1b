package com.ximalaya.ting.android.opensdk.player.soundpatch;

import android.os.RemoteCallbackList;
import android.os.RemoteException;

import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.SoundPatchConstants;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.soundpatch.BaseSoundPatch;
import com.ximalaya.ting.android.opensdk.model.soundpatch.ImmediateSoundPatch;
import com.ximalaya.ting.android.opensdk.model.soundpatch.NotPlayingSoundPatch;
import com.ximalaya.ting.android.opensdk.model.soundpatch.SimpleSoundPatchInfo;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.manager.ISoundPatchStatusDispatcher;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.mixplay.MixPlayerService;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Created by 5Greatest on 2021.04.27
 * <p>
 * 声音贴片的客户端播放顺序仲裁管理器
 *
 * <AUTHOR>
 * On 2021/4/27
 */
public class SoundPatchArbitrateManager implements IXmPlayerStatusListener, IImmediateSoundPatchCheck {
    private static final String TAG = SoundPatchConstants.TAG;

    private static boolean isForceStop = false;

    public static void setForceStop(boolean flag) {
        isForceStop = flag;
    }

    public static boolean isForceStop() {
        return isForceStop;
    }

    private static final Comparator COMPARATOR = new Comparator<BaseSoundPatch>() {
        @Override
        public int compare(BaseSoundPatch p1, BaseSoundPatch p2) {
            if (null == p1 || null == p2) {
                return 0;
            }
            // 降序排列
            return p2.getPriority() - p1.getPriority();
        }
    };

    /**
     * 播放期贴片事件
     */
    public static void onSoundPatchEvent(int type) {
        XmPlayerService playerService = XmPlayerService.getPlayerSrvice();
        if (playerService != null) {
            try {
                RemoteCallbackList<ISoundPatchStatusDispatcher> soundPatchDispatcher =
                        playerService.getSoundPatchDispatcher();
                int N = soundPatchDispatcher.beginBroadcast();
                for (int i = 0; i < N; i++) {
                    ISoundPatchStatusDispatcher broadcastItem = soundPatchDispatcher
                            .getBroadcastItem(i);
                    try {
                        broadcastItem.onCommercialSoundPatchStatusChange(type);
                    } catch (RemoteException err) {
                        err.printStackTrace();
                    }
                }
                soundPatchDispatcher.finishBroadcast();
            } catch (Exception err) {
                err.printStackTrace();
            }
        }
    }

    public static SoundPatchArbitrateManager getInstance() {
        return SingletonHolder.INSTANCE;
    }

    private final Map<Integer, BaseSoundPatch> mSoundPatchInstanceMap;
    private final List<ImmediateSoundPatch> mOrderedImmediateSoundPatchList;
    private final List<NotPlayingSoundPatch> mOrderedNotPlayingSoundPatch;

    // 在一次播放任何Track前的非播放期贴片的统计
    private final List<NotPlayingSoundPatch> mPlayedPatchBeforeAnyTrack;

    private final CurrentTrackWithSoundPatchInfo mCurrentInfo;
    private int mMode = SoundPatchConstants.MODE_DEFAULT;

    private SoundPatchArbitrateManager() {
        mSoundPatchInstanceMap = new ConcurrentHashMap<>();
        mOrderedImmediateSoundPatchList = new ArrayList<>();
        mOrderedNotPlayingSoundPatch = new CopyOnWriteArrayList<>();
        mPlayedPatchBeforeAnyTrack = new CopyOnWriteArrayList<>();
        mCurrentInfo = new CurrentTrackWithSoundPatchInfo();
    }

    /**
     * 注册播放状态监听器
     */
    public void init() {
        XmPlayerService.addPlayerStatusListenerOnPlayProcees(this);
        XmPlayerService playerService = XmPlayerService.getPlayerSrvice();
        if (playerService != null) {
            try {
                RemoteCallbackList<ISoundPatchStatusDispatcher> soundPatchDispatcher =
                        playerService.getSoundPatchDispatcher();
                int N = soundPatchDispatcher.beginBroadcast();
                for (int i = 0; i < N; i++) {
                    ISoundPatchStatusDispatcher broadcastItem = soundPatchDispatcher
                            .getBroadcastItem(i);
                    try {
                        broadcastItem.onCommercialSoundPatchNeedReRegister();
                    } catch (RemoteException err) {
                        err.printStackTrace();
                    }
                }
                soundPatchDispatcher.finishBroadcast();
            } catch (Exception err) {
                err.printStackTrace();
            }
        }
    }

    /**
     * 注销播放状态监听器
     */
    public void release() {
        XmPlayerService.removePlayerStatusListenerOnPlayProcess(this);
    }

    /**
     * 注册声音贴片
     */
    public void registerSoundPatch(int type, SimpleSoundPatchInfo simpleInfo) {
        if (null == simpleInfo) {
            return;
        }
        BaseSoundPatch instance = simpleInfo.createSoundPatch();
        registerSoundPatch(type, instance);
    }

    public void registerSoundPatch(int type, BaseSoundPatch instance) {
        if (null == instance || !instance.isValid()) {
            return;
        }
        mSoundPatchInstanceMap.put(type, instance);
        instance.releaseSoundPatch();
        if (SoundPatchConstants.TYPE_LOCAL_SOUND_PATCH == instance.getBasicType()
                && instance instanceof ImmediateSoundPatch) {
            mOrderedImmediateSoundPatchList.add((ImmediateSoundPatch) instance);
            Collections.sort(mOrderedImmediateSoundPatchList, COMPARATOR);
        }
        if (SoundPatchConstants.TYPE_NOT_PLAYING_SOUND_PATCH == instance.getBasicType()
                && instance instanceof NotPlayingSoundPatch) {
            mOrderedNotPlayingSoundPatch.add((NotPlayingSoundPatch) instance);
            List<NotPlayingSoundPatch> temp = new ArrayList<>(mOrderedNotPlayingSoundPatch);
            Collections.sort(temp, COMPARATOR);
            mOrderedNotPlayingSoundPatch.clear();
            mOrderedNotPlayingSoundPatch.addAll(temp);
        }
    }

    /**
     * 设置工作环境
     */
    public void setWorkMode(int mode) {
        mMode = mode;
    }

    /////////////////////////////////////////////////////////
    ///////////////       功能性方法区       //////////////////
    ///////////////          start         //////////////////
    /////////////////////////////////////////////////////////

    /**
     * 判断是否是不可播放声音贴片的情景
     * 包括：助眠模式, 一起听模式
     */
    private boolean isNoSoundPatchCircumstance(BaseSoundPatch soundPatch) {
        PlayableModel playableModel = null == XmPlayerService.getPlayerSrvice() ? null : XmPlayerService.getPlayerSrvice().getCurrPlayModel();

        //助眠模式
        if (MixPlayerService.getMixService().getPlaySource() != null
                || (null != playableModel
                && PlayableModel.KIND_MODE_SLEEP.equals(playableModel.getKind()))) {
            return true;
        }

        // 儿童模式
        if (SoundPatchConstants.MODE_KIDS == mMode
                || (null != playableModel
                && (playableModel instanceof Track)
                && (ConstantsOpenSdk.PLAY_FROM_KID_MODE_PICTURE_BOOK == ((Track) playableModel).getPlaySource()
                || ConstantsOpenSdk.PLAY_FROM_KID_MODE_TRACK == ((Track) playableModel).getPlaySource()))) {
            // 目前屏蔽儿童模式下的任何贴片
            return SoundPatchConstants.ENVIRONMENT_KIDS != soundPatch.getEnvironment();
        }

        // 一起听模式
        if (null != playableModel
                && (playableModel instanceof Track)
                && (ConstantsOpenSdk.PLAY_FROM_LISTEN_SCENE == ((Track) playableModel).getPlaySource())) {
            return true;
        }

        // 高德链接的时候,不能播放音贴
        if (SoundPatchConstants.isRemoteServiceConnected && MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_GAODE_REMOVE_AD, true)) {
            Logger.log("PromotionCenterService : remoteSerivceConnectd  return ");
            return true;
        }

        return false;
    }

    /**
     * 确认优先级并设置这一个track对应的贴片
     */
    private void tryToPlay(long trackId, BaseSoundPatch soundPatch) {
        if (isNoSoundPatchCircumstance(soundPatch)) {
            return;
        }
        if (null == soundPatch) {
            // 参数错误
            return;
        }

        if (SoundPatchConstants.TYPE_NOT_PLAYING_SOUND_PATCH == soundPatch.getBasicType()
                && soundPatch instanceof NotPlayingSoundPatch) {
            // 非播放时期贴片，不与播放时期贴片的优先级做判断
            if (((NotPlayingSoundPatch) soundPatch).getAllowTolerance() >= mPlayedPatchBeforeAnyTrack.size()) {
                mPlayedPatchBeforeAnyTrack.add((NotPlayingSoundPatch) soundPatch);
                soundPatch.playSoundPatch();
            }
            return;
        }

        PlayableModel currentTrack = null == XmPlayerService.getPlayerSrvice() ? null : XmPlayerService.getPlayerSrvice().getCurrPlayModel();
        if (null == currentTrack) {
            return;
        } else {
            if (currentTrack.getDataId() != trackId) {
                // 贴片过期
                return;
            }
        }

        if (mCurrentInfo.isEmpty()) {
            // 初次播放贴片
            mCurrentInfo.setInfo(trackId, soundPatch);
            soundPatch.playSoundPatch();
            Logger.d(TAG, "mCurrentInfo is Empty: " + soundPatch);
            return;
        }
        if (!mCurrentInfo.isCurrentInfo(trackId)) {
            BaseSoundPatch expired = mCurrentInfo.soundPatch;
            if (null != expired) {
                expired.releaseSoundPatch();
            }
            // 记录中的贴片是过期的，或记录错误
            mCurrentInfo.setInfo(trackId, soundPatch);
            soundPatch.playSoundPatch();
            Logger.d(TAG, "mCurrentInfo is CurrentInfo: " + soundPatch);
            return;
        }
        if (soundPatch.shouldPlayOnlyOncePerTrack()) {
            if (soundPatch.getPriority() <= mCurrentInfo.soundPatch.getPriority()) {
                soundPatch.releaseSoundPatch();
                // 判断已记录的贴片的优先级是否低于传入参数中的
                Logger.d(TAG, "mCurrentInfo is priority release: " + soundPatch + " " + soundPatch.getPriority() + " " + mCurrentInfo.soundPatch + " " + mCurrentInfo.soundPatch.getPriority());
                return;
            }
            if (mCurrentInfo.soundPatch.isPlaying() || mCurrentInfo.soundPatch.isComplete()) {
                soundPatch.releaseSoundPatch();
                // 若当前低优先级的贴片已经在播放或者已经播放过，为了保证一个声音只有一个贴片的原则，放弃这个高优先级的贴片
                // 如果设置了canPlayMoreThanOnce，则不受限制
                Logger.d(TAG, "mCurrentInfo drop for only " + soundPatch);
                return;
            }
            if (mCurrentInfo.soundPatch instanceof ImmediateSoundPatch && ((ImmediateSoundPatch) mCurrentInfo.soundPatch).isPaused()) {
                // 若当前低优先级的贴片处在暂停阶段，为了保证一个声音只有一个贴片的原则，放弃这个高优先级的贴片
                return;
            }
        }
        // 传入参数中的高优先级贴片替代之前的低优先级贴片
        mCurrentInfo.soundPatch.removeSoundPatch();
        mCurrentInfo.setInfo(trackId, soundPatch);
        soundPatch.playSoundPatch();
        Logger.d(TAG, "soundPatch playSoundPatch");
    }

    /**
     * 检测是否该声音贴片被屏蔽
     * 以及本类贴片是否可以播放
     *
     * @return null 被屏蔽
     * 非null 未被屏蔽
     */
    private BaseSoundPatch isBlockedByHighPriorityPatched(int currentPatchType) {
        BaseSoundPatch currentPatch = null;
        if (Util.isEmptyMap(mSoundPatchInstanceMap)
                || null == (currentPatch = mSoundPatchInstanceMap.get(currentPatchType))) {
            return null;
        }

        if (SoundPatchConstants.TYPE_NOT_PLAYING_SOUND_PATCH == currentPatch.getBasicType()) {
            // 非播放时期贴片，不与播放时期贴片的优先级做判断
            if (Util.isEmptyCollects(mOrderedNotPlayingSoundPatch)
                    || !mOrderedNotPlayingSoundPatch.contains(currentPatch)) {
                return null;
            }
            for (NotPlayingSoundPatch notPlayingSoundPatch : mOrderedNotPlayingSoundPatch) {
                if (null == notPlayingSoundPatch) {
                    continue;
                }
                if (notPlayingSoundPatch == currentPatch) {
                    Map<String, Object> param = new HashMap<>();
                    param.put(SoundPatchConstants.KEY_SELF, null);
                    return notPlayingSoundPatch.isAbleToBlockLowPriorities(param) ? currentPatch : null;
                }
                if (notPlayingSoundPatch.isAbleToBlockLowPriorities(new HashMap<>())) {
                    // 更高优先级
                    return null;
                }
            }
            return null;
        }

        PlayableModel model = null == XmPlayerService.getPlayerSrvice() ? null : XmPlayerService.getPlayerSrvice().getCurrPlayModel();
        if (null == model) {
            // 播放器无声音对象，错误！！
            return null;
        }

        if (Util.isEmptyCollects(mOrderedImmediateSoundPatchList)) {
            return currentPatch;
        }
        Map<String, Object> requirementParam = new HashMap<>();
        requirementParam.put(SoundPatchConstants.KEY_CURRENT_PLAYABLEMODEL, model);
        for (ImmediateSoundPatch immediateSoundPatch : mOrderedImmediateSoundPatchList) {
            if (null == immediateSoundPatch) {
                continue;
            }
            if (currentPatch == immediateSoundPatch) {
                // 检测到相同类型时
                requirementParam.put(SoundPatchConstants.KEY_SELF, null);
                return immediateSoundPatch.isAbleToBlockLowPriorities(requirementParam) ? currentPatch : null;
            }
            if (immediateSoundPatch.isAbleToBlockLowPriorities(requirementParam)) {
                return null;
            }
        }
        return currentPatch;
    }

    /**
     * 通过对应贴片类名来播放声音贴片
     *
     * @param soundPatchType 声音贴片类的编号
     */
    public void playSoundPatchByType(int soundPatchType, String paramsJsonString) {
        this.playSoundPatchByType(null, soundPatchType, paramsJsonString);
    }

    public void playSoundPatchByType(PlayableModel currentModel, int soundPatchType, String paramsJsonString) {
        if (!mSoundPatchInstanceMap.containsKey(soundPatchType)) {
            Logger.e(TAG, "SoundPatchHostManager: 声音贴片类（编号为："
                    + soundPatchType
                    + "）未被注册在管理器中，请检查类编号是否正确，以及是否已经进行了注册");
            return;
        }
        Logger.d(TAG, "SoundPatchArbitrateManager: playSoundPatchByType 执行， 声音贴片类（编号为："
                + soundPatchType + "）");
        BaseSoundPatch currentSoundPatch = null;
        if (null == (currentSoundPatch = isBlockedByHighPriorityPatched(soundPatchType))) {
            return;
        }

        if (!currentSoundPatch.isReadyToPlay()) {
            return;
        }

        PlayableModel model = (null == currentModel)
                ? (null == XmPlayerService.getPlayerSrvice() ? null : XmPlayerService.getPlayerSrvice().getCurrPlayModel())
                : currentModel;
        Logger.d(TAG, "playSoundPatchByType trackId " + (null != model ? model.getDataId() : 0));
        if (SoundPatchConstants.TYPE_NOT_PLAYING_SOUND_PATCH != currentSoundPatch.getBasicType()
                && null == model) {
            return;
        }

        BaseSoundPatch source = mSoundPatchInstanceMap.get(soundPatchType);
        if (null == source) {
            return;
        }

        // 添加参数
        BaseSoundPatch target = source.cloneSoundPatch(paramsJsonString);
        Logger.d(TAG, "SoundPatchArbitrateManager playSoundPatchByType: " + target);

        tryToPlay(model.getDataId(), target);
    }

    /**
     * 判断是否在播放声音贴片
     *
     * @return true：在播放中
     * false：没有在播放
     */
    public boolean isPlayingSoundPatch() {
        if (null != mCurrentInfo
                && !mCurrentInfo.isEmpty()
                && null != mCurrentInfo.soundPatch) {
            if (mCurrentInfo.soundPatch.isPlaying()) {
                return true;
            }
        }
        for (NotPlayingSoundPatch notPlayingSoundPatch : mPlayedPatchBeforeAnyTrack) {
            if (null != notPlayingSoundPatch && notPlayingSoundPatch.isPlaying()) {
                return true;
            }
        }
        return false;
    }

    public boolean needBlockPlayNextOrPrevBtn() {
        if (null != mCurrentInfo
                && !mCurrentInfo.isEmpty()
                && null != mCurrentInfo.soundPatch) {
            if (mCurrentInfo.soundPatch.isPlaying()) {
                return mCurrentInfo.soundPatch.blockPlayNextOrPrevBtn();
            }
        }
        for (NotPlayingSoundPatch notPlayingSoundPatch : mPlayedPatchBeforeAnyTrack) {
            if (null != notPlayingSoundPatch && notPlayingSoundPatch.isPlaying()) {
                return notPlayingSoundPatch.blockPlayNextOrPrevBtn();
            }
        }
        return false;
    }

    /**
     * 停止播放声音贴片（所有类型）
     */
    public void stopSoundPatch() {
        Logger.logToFile("SoundPatchManager stopSoundPatch mCurrentInfo=" + mCurrentInfo + ",soundPath=" + (mCurrentInfo != null ? mCurrentInfo.soundPatch : "null"));
        if (null != mCurrentInfo
                && !mCurrentInfo.isEmpty()
                && null != mCurrentInfo.soundPatch) {
            mCurrentInfo.soundPatch.stopSoundPatch();
        }
        for (NotPlayingSoundPatch notPlayingSoundPatch : mPlayedPatchBeforeAnyTrack) {
            if (null != notPlayingSoundPatch) {
                notPlayingSoundPatch.stopSoundPatch();
            }
        }
    }

    /**
     * 广告阻塞并重置片头贴片
     */
    public void resetOnVideoAdPlay() {
        if (0 != mPlayedPatchBeforeAnyTrack.size()) {
            NotPlayingSoundPatch toResetOne = mPlayedPatchBeforeAnyTrack.get(mPlayedPatchBeforeAnyTrack.size() - 1);
            if (null != toResetOne && toResetOne.resetOnVideoAdPlay()) {
                mPlayedPatchBeforeAnyTrack.remove(toResetOne);
            }
        }
    }

    ///////////////////////////////////////////////////////////////////////////
    ///////////////                功能性方法区                //////////////////
    ///////////////                    end                   //////////////////
    ///////////////////////////////////////////////////////////////////////////

    ///////////////////////////////////////////////////////////////////////////
    ///////////////           与SoundPatch协调的方法区          //////////////////
    ///////////////                   start                  //////////////////
    ///////////////////////////////////////////////////////////////////////////

    @Override
    public boolean isImmediateSoundPatchPlaying() {
        return mCurrentInfo.isImmediateSoundPatchPlaying();
    }

    @Override
    public boolean isImmediateSoundPatchPaused() {
        return mCurrentInfo.isImmediateSoundPatchPaused();
    }

    @Override
    public boolean pauseImmediateSoundPatch() {
        return mCurrentInfo.pauseImmediateSoundPatch();
    }

    @Override
    public boolean resumeImmediateSoundPatch() {
        return mCurrentInfo.resumeImmediateSoundPatch();
    }

    @Override
    public void stopAndClearImmediateSoundPatch() {
        mCurrentInfo.stopAndClearImmediateSoundPatch();
    }


    ///////////////////////////////////////////////////////////////////////////
    ///////////////           与SoundPatch协调的方法区          //////////////////
    ///////////////                    end                   //////////////////
    ///////////////////////////////////////////////////////////////////////////

    ///////////////////////////////////////////////////////////////////////////
    ///////////////   interface: IXmPlayerStatusListener     //////////////////
    ///////////////                  start                   //////////////////
    ///////////////////////////////////////////////////////////////////////////

    private boolean hasChangedTrack = false;

    private boolean skipHandlerOnPlayStart = false;

    public void setSkipHandlerOnPlayStart(boolean skipHandlerOnPlayStart) {
        this.skipHandlerOnPlayStart = skipHandlerOnPlayStart;
    }

    @Override
    public void onPlayStart() {
        if (skipHandlerOnPlayStart) {
            skipHandlerOnPlayStart = false;
            return;
        }

        Logger.logToFile("SoundPatchManager onPlayStart");
        stopSoundPatch();
//        if (hasChangedTrack) {
//            hasChangedTrack = false;
//            mCurrentInfo.clear();
//        }
        if (mPlayedPatchBeforeAnyTrack != null) {
            for (NotPlayingSoundPatch soundPatch : mPlayedPatchBeforeAnyTrack) {
                if (null != soundPatch) {
                    soundPatch.releaseSoundPatch();
                }
            }
            mPlayedPatchBeforeAnyTrack.clear();
        }
        setForceStop(false);
    }

    @Override
    public void onPlayPause() {

    }

    @Override
    public void onPlayStop() {

    }

    @Override
    public void onSoundPlayComplete() {
        Logger.d(TAG, "onSoundPlayComplete");
        // 每次播完，移除该声音相关的贴片信息
        mCurrentInfo.clear();
    }

    @Override
    public void onSoundPrepared() {

    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
        Logger.d(TAG, "onSoundSwitch " + (null == lastModel ? -1 : lastModel.getDataId()) + "   " + (null == curModel ? -1 : curModel.getDataId()));
        if (null != curModel) {
            hasChangedTrack = true;
        }
        /*for (NotPlayingSoundPatch soundPatch : mPlayedPatchBeforeAnyTrack) {
            if (null != soundPatch) {
                soundPatch.stopSoundPatch();
                *//*if (!soundPatch.isComplete()) {
                    // 如果还未播放，则移除，不占用播放名额，保证还能播放其他贴片
                    mPlayedPatchBeforeAnyTrack.remove(soundPatch);
                }*//*
            }
        }*/
    }

    @Override
    public void onBufferingStart() {

    }

    @Override
    public void onBufferingStop() {

    }

    @Override
    public void onBufferProgress(int percent) {

    }

    @Override
    public void onPlayProgress(int currPos, int duration) {

    }

    @Override
    public boolean onError(XmPlayerException exception) {
        return false;
    }

    ///////////////////////////////////////////////////////////////////////////
    ///////////////   interface: IXmPlayerStatusListener     //////////////////
    ///////////////                   end                    //////////////////
    ///////////////////////////////////////////////////////////////////////////


    private static class CurrentTrackWithSoundPatchInfo implements IImmediateSoundPatchCheck {
        public long trackId;
        public BaseSoundPatch soundPatch;

        public void setInfo(long trackId, BaseSoundPatch soundPatch) {
            this.trackId = trackId;
            this.soundPatch = soundPatch;
        }

        public boolean isCurrentInfo(long trackId) {
            return this.trackId == trackId;
        }

        public boolean isEmpty() {
            return 0 == this.trackId || null == this.soundPatch;
        }

        public void clear() {
            trackId = 0;
            /*if (null != soundPatch) {
                soundPatch.releaseSoundPatch();
            }*/
            soundPatch = null;
        }

        @Override
        public boolean isImmediateSoundPatchPlaying() {
            BaseSoundPatch currentSoundPatch = soundPatch;
            if (currentSoundPatch instanceof ImmediateSoundPatch) {
                return currentSoundPatch.isPlaying();
            }
            return false;
        }

        @Override
        public boolean isImmediateSoundPatchPaused() {
            BaseSoundPatch currentSoundPatch = soundPatch;
            if (currentSoundPatch instanceof ImmediateSoundPatch) {
                return ((ImmediateSoundPatch) currentSoundPatch).isPaused();
            }
            return false;
        }

        @Override
        public boolean pauseImmediateSoundPatch() {
            BaseSoundPatch currentSoundPatch = soundPatch;
            if (currentSoundPatch instanceof ImmediateSoundPatch) {
                return ((ImmediateSoundPatch) currentSoundPatch).pause();
            }
            return false;
        }

        @Override
        public boolean resumeImmediateSoundPatch() {
            BaseSoundPatch currentSoundPatch = soundPatch;
            if (currentSoundPatch instanceof ImmediateSoundPatch) {
                return ((ImmediateSoundPatch) currentSoundPatch).resumePlay();
            }
            return false;
        }

        @Override
        public void stopAndClearImmediateSoundPatch() {
            BaseSoundPatch currentSoundPatch = soundPatch;
            if (currentSoundPatch instanceof ImmediateSoundPatch) {
                currentSoundPatch.stopSoundPatch();
            }
        }
    }

    private static class SingletonHolder {
        private static final SoundPatchArbitrateManager INSTANCE = new SoundPatchArbitrateManager();
    }

    private static class Util {
        public static <T> boolean isEmptyCollects(Collection<T> collection) {
            return collection == null || collection.isEmpty();
        }

        public static <T> boolean isEmptyMap(Map map) {
            return map == null || map.isEmpty();
        }

    }
}
