/**
 * PlayerConfig.java
 * com.ximalaya.ting.android.opensdk.player.service
 * <p>
 * Function： TODO
 * <p>
 * ver     date      		author
 * ---------------------------------------
 * 2015-5-19 		chadwii
 * <p>
 * Copyright (c) 2015, chadwii All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.player.service;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.os.Build;

import androidx.annotation.FloatRange;

import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;

/**
 * ClassName:PlayerConfig Function: TODO ADD FUNCTION Reason: TODO ADD REASON
 *
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015-5-19 上午11:26:27
 *
 * @see
 */
public class XmPlayerConfig {
    private static final int PREFERENCE_MODE = Context.MODE_PRIVATE;

    private static XmPlayerConfig sInstance;
    private static byte[] sLock = new byte[0];
    private Context mAppCtx;
    private SharedPreferences mPreferences;
    private int soundPatchTimeoutMs;

    private XmPlayerConfig(Context ctx) {
        mAppCtx = ctx.getApplicationContext();
        init();
    }

    public static XmPlayerConfig getInstance(Context ctx) {
        if (sInstance == null) {
            synchronized (sLock) {
                if (sInstance == null) {
                    sInstance = new XmPlayerConfig(ctx);
                }
            }
        }
        return sInstance;
    }

    private void init() {
        mPreferences =
				mAppCtx.getSharedPreferences(PreferenceConstantsInOpenSdk.OPENSDK_FILENAME_XMPLAYER_CONFIG
                , PREFERENCE_MODE);

        if (mPreferences.contains(PreferenceConstantsInOpenSdk.OPENSDK_KEY_BREAKPOINT_RESUME)) {
            MmkvCommonUtil.getInstance(mAppCtx).saveBoolean(PreferenceConstantsInOpenSdk.OPENSDK_KEY_BREAKPOINT_RESUME,
                    mPreferences.getBoolean(PreferenceConstantsInOpenSdk.OPENSDK_KEY_BREAKPOINT_RESUME, true));
            apply(mPreferences.edit().remove(PreferenceConstantsInOpenSdk.OPENSDK_KEY_BREAKPOINT_RESUME));
        }
    }

    public boolean isBreakpointResume() {
        return MmkvCommonUtil.getInstance(mAppCtx).getBoolean(PreferenceConstantsInOpenSdk.OPENSDK_KEY_BREAKPOINT_RESUME, true);
    }

    public void setBreakpointResume(boolean isBreakpointResume) {
        MmkvCommonUtil.getInstance(mAppCtx).saveBoolean(PreferenceConstantsInOpenSdk.OPENSDK_KEY_BREAKPOINT_RESUME, isBreakpointResume);
    }

    public boolean isShowMediaSessionBgView() {
        return mPreferences.getBoolean(PreferenceConstantsInOpenSdk.OPENSDK_KEY_MEDIA_SESSION_BG_VIEW_SHOW, true);
    }

    public void setMediaSessionBgView(boolean show) {
        apply(mPreferences.edit().putBoolean(PreferenceConstantsInOpenSdk.OPENSDK_KEY_MEDIA_SESSION_BG_VIEW_SHOW, show));
    }

    public boolean isUseRadioHighBitrate() {
        return mPreferences.getBoolean(PreferenceConstantsInOpenSdk.OPENSDK_KEY_USE_RADIO_HIGH_BITRATE, false);
    }

    public void setUseRadioHighBitrate(boolean value) {
        apply(mPreferences.edit().putBoolean(PreferenceConstantsInOpenSdk.OPENSDK_KEY_USE_RADIO_HIGH_BITRATE, value));
    }

    public boolean isUseTrackHighBitrate() {
        return mPreferences.getBoolean(PreferenceConstantsInOpenSdk.OPENSDK_KEY_USE_TRACK_HIGH_BITRATE, false);
    }

    public void setUseTrackHighBitrate(boolean value) {
        apply(mPreferences.edit().putBoolean(PreferenceConstantsInOpenSdk.OPENSDK_KEY_USE_TRACK_HIGH_BITRATE, value));
    }


    @TargetApi(Build.VERSION_CODES.GINGERBREAD)
    private void apply(Editor editor) {
        // 大于等于android2.3
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.GINGERBREAD) {
            editor.apply();
        } else {
            editor.commit();
        }
    }

    public void release() {
        synchronized (sLock) {
            sInstance = null;
        }
    }

    public int getSoundPatchTimeoutMs() {
        return soundPatchTimeoutMs;
    }

    public void setSoundPatchTimeoutMs(int soundPatchTimeoutMs) {
        this.soundPatchTimeoutMs = soundPatchTimeoutMs;
    }
}
