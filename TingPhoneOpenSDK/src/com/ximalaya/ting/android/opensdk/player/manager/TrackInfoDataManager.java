package com.ximalaya.ting.android.opensdk.player.manager;

import android.os.SystemClock;
import androidx.annotation.Nullable;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.CommonRequestForMain;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.receive.NotificationLikeManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmSimplePlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.TrackUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.ICommonBusiService;
import com.ximalaya.ting.android.routeservice.service.version.IVersion;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.app.XmAppHelper;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023/8/15 21:26
 */
public class TrackInfoDataManager {
    private static final String TAG = "TrackInfoDataManager";
    private final Map<Long, RequestModel> mRequestMap = new ConcurrentHashMap<>();
    private Boolean mOpen = null;
    private boolean mNoOptStrategyOpen = false;
    private long mIntervalDuration = 5_000;
    private static final Object sLock = new Object();

    private static final class SingletonHolder {
        private static final TrackInfoDataManager INSTANCE = new TrackInfoDataManager();
    }

    public static TrackInfoDataManager getInstance() {
        return SingletonHolder.INSTANCE;
    }

    private TrackInfoDataManager() {
        XmPlayerService.addPlayerStatusListenerOnPlayProcees(mSimplePlayerStatusListener);
    }

    private final IXmSimplePlayerStatusListener mSimplePlayerStatusListener = new IXmSimplePlayerStatusListener() {
        @Override
        public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
//            Logger.logToFile(TAG, "onSoundSwitch sRequestMap clear " + Log.getStackTraceString(new Throwable()));
//            mRequestMap.clear();
        }
    };

    public Track getCachedTrack() {
        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        if (service == null) {
            return null;
        }
        PlayableModel playableModel = service.getCurrPlayModel();
        if (playableModel == null) {
            return null;
        }
        RequestModel model = mRequestMap.get(playableModel.getDataId());
        if (model == null) {
            return null;
        }
        return model.responseData;
    }

    public void getTrackInfo(Track track, IDataCallBack<Track> callback) {
        if (track == null) {
            return;
        }
        CommonRequestForMain.getTrackInfo(track, new IDataCallBack<Track>() {
            @Override
            public void onSuccess(Track object) {
                if (object != null) {
                    track.clearDirectPlayUrl();  // 把Track中直接带的url清掉，可能是无效的
                    boolean likeChanged = NotificationLikeManager.INSTANCE.checkLikeStateChange(track, object);
                    track.updateBaseInfoByTrack(object);
                    if (likeChanged) {
                        NotificationLikeManager.INSTANCE.onTrackLikeStateChange();
                    }
                }
                if (callback != null) {
                    callback.onSuccess(object);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (callback != null) {
                    callback.onError(code, message);
                }
            }
        });
    }

    private boolean isOpen() {
        if (mOpen == null) {
            synchronized (sLock) {
                if (mOpen == null) {
                    boolean optOpen = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEM_BASEINFO_REQUEST_OPT, false);
                    boolean abOpen = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_ITEM_AB_BASEINFO_REQUEST_OPT, false);
                    IVersion versionCallback = RouterServiceManager.getInstance().getService(IVersion.class);
                    boolean betaVersion = false;
                    if (versionCallback != null) {
                        betaVersion = !versionCallback.isReleaseVersion();
                    }
                    ICommonBusiService commonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
                    boolean noOptStrategyOpen = false;
                    if (commonBusiService != null && commonBusiService.isOpenNetDetect()) {
                        noOptStrategyOpen = commonBusiService.noOptStrategy();
                        mIntervalDuration = noOptStrategyOpen ? 5_000 : 10_000;
                    }
                    mNoOptStrategyOpen = noOptStrategyOpen;
                    mOpen = optOpen && (betaVersion || abOpen);
                    Logger.logToFile(TAG, "optOpen=" + optOpen + ", abOpen=" + abOpen + ", betaVersion=" + betaVersion + ",noOptStrategyOpen=" + noOptStrategyOpen + ",mIntervalDuration=" + mIntervalDuration);
                }
            }
        }
        return mOpen;
    }

    public void clear() {
        mRequestMap.clear();
        Logger.logToFile(TAG, "onSoundSwitch sRequestMap clear");
    }

    public void getTrackInfoCanGetFromCache(boolean isFromPreload, Track track, int from, IDataCallBack<Track> callback) {
//        if (ConstantsOpenSdk.isDebug) {
//            Logger.d(TAG, "getTrackInfoCanGetFromCache=" + TrackUtil.trackToStr(track) + ",trace=" + Log.getStackTraceString(new Throwable()));
//        }
        final long startTime = System.currentTimeMillis();
        if (!isOpen()) {
            Logger.logToFile(TAG, "getTrackInfoCanGetFromCache from=" + from + ",trace=" + TrackUtil.trackToStr(track));
            CommonRequestForMain.getTrackInfo(track, callback);
            return;
        }
        if (callback == null) {
            return;
        }
        final long trackId = track.getDataId();
        XmAppHelper.runOnWorkThread(new Runnable() {
            @Override
            public void run() {
                if (mNoOptStrategyOpen) {
                    Logger.logToFile(TAG, "getTrackInfoCanGetFromCache no_opt from=" + from + "," + TrackUtil.trackToStr(track));
                    requestTrackInfo(trackId, track, callback);
                    return;
                }
                RequestModel model = mRequestMap.get(trackId);
                Logger.logToFile(TAG, "getTrackInfoCanGetFromCache 1 trackId=" + trackId + ",from=" + from + ",model=" + model);
                if (model != null && model.responseData != null && Math.abs(model.responseTime - SystemClock.elapsedRealtime()) < mIntervalDuration) {
                    Logger.logToFile(TAG, "getTrackInfoCanGetFromCache 2");
                    callback.onSuccess(model.responseData);
                    return;
                }
                if (model == null) {
                    model = new RequestModel();
                    model.from = from;
                    mRequestMap.put(trackId, model);
                    model.callBacks.add(callback);
                } else {
                    model.callBacks.add(callback);
                }

                if (!model.requesting) {
                    model.requesting = true;
                    model.requestTime = SystemClock.elapsedRealtime();
                    RequestModel finalModel = model;
                    Logger.logToFile(TAG, "getTrackInfoCanGetFromCache 3 requestTrackInfo trackId=" + trackId);
                    requestTrackInfo(trackId, track, new IDataCallBack<Track>() {
                        @Override
                        public void onSuccess(@Nullable Track data) {
                            Logger.logToFile(TAG, "getTrackInfoCanGetFromCache 4 " + TrackUtil.trackToStr(data));
                            finalModel.requesting = false;
                            finalModel.responseData = data;
                            finalModel.responseTime = SystemClock.elapsedRealtime();
                            for (int i = 0; i < finalModel.callBacks.size(); i++) {
                                long callbackTime = System.currentTimeMillis();
                                finalModel.callBacks.get(i).onSuccess(data);
                                long time = System.currentTimeMillis();
                                Logger.logToFile(TAG, "getTrackInfoCanGetFromCache 4-1 callback_time=" + (time - callbackTime) + ",cost_time=" + (time - startTime) + "," + TrackUtil.trackToStr(data));
                            }
                            finalModel.callBacks.clear();
                        }

                        @Override
                        public void onError(int code, String message) {
                            Logger.logToFile(TAG, "getTrackInfoCanGetFromCache 5 code=" + code + ",message=" + message);
                            finalModel.requesting = false;
                            for (int i = 0; i < finalModel.callBacks.size(); i++) {
                                finalModel.callBacks.get(i).onError(code, message);
                            }
                            mRequestMap.remove(trackId);
                        }
                    });
                }
            }
        });
    }

    private void requestTrackInfo(final long trackId, Track track, IDataCallBack<Track> callBack) {
        long startTime = System.currentTimeMillis();
        CommonRequestForMain.getTrackInfo(track, new IDataCallBack<Track>() {
            @Override
            public void onSuccess(@Nullable Track data) {
                Logger.logToFile(TAG, "requestTrackInfo time=" + (System.currentTimeMillis() - startTime) + "," + TrackUtil.trackToStr(data));
                XmAppHelper.runOnWorkThread(new Runnable() {
                    @Override
                    public void run() {
                        callBack.onSuccess(data);
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                XmAppHelper.runOnWorkThread(new Runnable() {
                    @Override
                    public void run() {
                        callBack.onError(code, message);
                    }
                });
            }
        });
    }

    private static class RequestModel {
        boolean requesting = false;
        long requestTime;
        long responseTime;
        Track responseData;
        int from;
        List<IDataCallBack<Track>> callBacks = new LinkedList<>();

        @Override
        public String toString() {
            return "RequestModel{" +
                    "requesting=" + requesting +
                    ", requestTime=" + requestTime +
                    ", from=" + from +
                    ", responseTime=" + responseTime +
                    ", responseData=" + TrackUtil.trackToStr(responseData) +
                    ", callBacks=" + callBacks.size() +
                    '}';
        }
    }
}
