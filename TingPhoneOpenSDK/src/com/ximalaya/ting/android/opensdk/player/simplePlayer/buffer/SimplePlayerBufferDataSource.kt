package com.ximalaya.ting.android.opensdk.player.simplePlayer.buffer

import android.net.Uri
import com.google.android.exoplayer2.upstream.DataSource
import com.google.android.exoplayer2.upstream.DataSpec
import com.google.android.exoplayer2.upstream.TransferListener
import java.util.Collections

class SimplePlayerBufferDataSource(val uid: String, val bufferManager: IMediaDataCallback) : DataSource {

    override fun open(dataSpec: DataSpec): Long {
        return dataSpec.length
    }

    override fun getUri(): Uri? {
        return Uri.EMPTY
    }

    override fun close() {
    }

    override fun addTransferListener(transferListener: TransferListener) {

    }

    override fun read(target: ByteArray, offset: Int, length: Int): Int {
        return bufferManager.readData(uid, target, offset, length)
    }

    override fun getResponseHeaders(): MutableMap<String, MutableList<String>> {
        return Collections.emptyMap()
    }

}