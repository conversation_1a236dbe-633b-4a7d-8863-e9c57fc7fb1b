package com.ximalaya.ting.android.opensdk.player.receive;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiManager;
import android.os.Parcelable;

import com.ximalaya.ting.android.opensdk.player.statistic.TrafficStatisticManager;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;

/**
 * <AUTHOR>
 */
public class NetWorkChangeReceiver extends BroadcastReceiver {
    private static final String TAG = "SNetWorkChangeReceiver";
    public static boolean isMobileType = false;

    @Override
    public void onReceive(Context context, Intent intent) {
        if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
            NetworkType.NetWorkType type = NetworkType.getNetWorkType(context);
            if (type == null) {
                return;
            }
            switch (type) {
                case NETWORKTYPE_WIFI: {
                    if (isMobileType) {
                        Logger.i(TAG, "endTrafficStatistic");
                        TrafficStatisticManager.getInstance().endTrafficStatistic();
                    }
                    Logger.e(TAG, "connect to wifi");
                    isMobileType = false;
                    break;
                }
                case NETWORKTYPE_INVALID: {
                    break;
                }
                default: {
                    if (!isMobileType) {
                        Logger.i(TAG, "startTrafficStatistic");
                        TrafficStatisticManager.getInstance().startTrafficStatistic();
                    }
                    Logger.e(TAG, "connect to mobile");
                    isMobileType = true;
                    break;
                }
            }
        }


        if (WifiManager.NETWORK_STATE_CHANGED_ACTION.equals(intent.getAction())) {
            Parcelable parcelableExtra = intent
                    .getParcelableExtra(WifiManager.EXTRA_NETWORK_INFO);
            if (null != parcelableExtra) {
                NetworkInfo networkInfo = (NetworkInfo) parcelableExtra;
                NetworkInfo.State state = networkInfo.getState();
                boolean isConnected = state == NetworkInfo.State.CONNECTED;
                if (isConnected) {
                    if (networkInfo.getType() == ConnectivityManager.TYPE_WIFI) {
                        if (isMobileType) {
                            Logger.i(TAG, "endTrafficStatistic");
                            TrafficStatisticManager.getInstance().endTrafficStatistic();
                        }
                        Logger.e(TAG, "connect to wifi");
                        isMobileType = false;
                    }
                }
            }
        }
    }
}
