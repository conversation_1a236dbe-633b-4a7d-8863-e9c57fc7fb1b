package com.ximalaya.ting.android.opensdk.player.receive;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class ScreenStatusReceiver extends BroadcastReceiver {
    private static List<IScreenStatusListener> mScreenStatusListeners;
    private static boolean mIsInitialized;

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (Intent.ACTION_SCREEN_ON.equals(action)) { // 开屏
            notifyListeners(IScreenStatusListener.SCREEN_ON);
        } else if (Intent.ACTION_SCREEN_OFF.equals(action)) { // 锁屏
            notifyListeners(IScreenStatusListener.SCREEN_OFF);
        }
    }

    public void register(Context context) {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Intent.ACTION_SCREEN_ON);
        intentFilter.addAction(Intent.ACTION_SCREEN_OFF);
        context.registerReceiver(this, intentFilter);
    }

    public synchronized static void init(Context context){
        if (mIsInitialized) {
            return;
        }
        ScreenStatusReceiver screenStatusReceiver = new ScreenStatusReceiver();
        screenStatusReceiver.register(context);
        mIsInitialized = true;
    }

    private void notifyListeners(int screenStatus){
        if (mScreenStatusListeners == null || mScreenStatusListeners.size() == 0) {
            return;
        }
        for (IScreenStatusListener screenStatusListener : mScreenStatusListeners) {
            screenStatusListener.onScreenStatusChange(screenStatus);
        }
    }
    public static void addScreenStatusListener(IScreenStatusListener screenStatusListener) {
        if (screenStatusListener == null) {
            return;
        }
        if (mScreenStatusListeners == null) {
            mScreenStatusListeners = new CopyOnWriteArrayList<>();
        }
        if (mScreenStatusListeners.contains(screenStatusListener)) {
            return;
        }
        mScreenStatusListeners.add(screenStatusListener);
    }

    public static void removeScreenStatusListener(IScreenStatusListener screenStatusListener) {
        if (screenStatusListener == null) {
            return;
        }
        if (mScreenStatusListeners == null) {
            return;
        }
        if (mScreenStatusListeners.contains(screenStatusListener)) {
            mScreenStatusListeners.remove(screenStatusListener);
        }
    }

    public interface IScreenStatusListener {
        int SCREEN_DEFAULT = 0;
        int SCREEN_ON = 1;
        int SCREEN_OFF = 2;
        void onScreenStatusChange(int screenStatus);
    }
}
