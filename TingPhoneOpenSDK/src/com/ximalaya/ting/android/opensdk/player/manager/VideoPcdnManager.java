package com.ximalaya.ting.android.opensdk.player.manager;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import androidx.collection.ArrayMap;
import com.google.android.exoplayer2.ExoPlaybackException;
import com.google.android.exoplayer2.upstream.HttpDataSource;
import com.sina.util.dnscache.DNSCache;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.opensdk.model.VideoPCdnErrorModel;
import com.ximalaya.ting.android.opensdk.util.SystemUtil;
import com.ximalaya.ting.android.routeservice.service.freeflow.IFreeFlowService;
import com.ximalaya.ting.android.util.TraceUtil;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;

import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/25 15:21
 */
public class VideoPcdnManager {
    private static final String TAG = "VideoPcdnManager";

    private static final String PCDN = ".pcdn.";

    private static final String PCDN_PREFIX = "video@";

    public static Context sContext;
    private volatile static VideoPcdnManager instance;
    private boolean gateWaySwitch;
    private boolean sGateWaySwitch = true;
    private boolean abSwitch;

    private boolean mNoMoreUsePCDN = false;
    private boolean mUsePcdnForLocal = true;

    private int mFailTimeWindow = 10;
    private int mFailCountWindow = 5;
    private Map<Long, String> mFailTimeMap = new ArrayMap<>();

    private Map<String, String> mWhiteMap = new ArrayMap<>();

    private Map<String, String> mWhiteCodeMap = new ArrayMap<>();

    private VideoPcdnManager() {
        abSwitch = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEM_VIDEO_PCDN_SWITCH_OPEN, false);
        mUsePcdnForLocal = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_REPLACE_VIDEO_DOMAIN_TOGGLE, true);
        mFailCountWindow = MMKVUtil.getInstance().getInt(PreferenceConstantsInOpenSdk.ITEM_VIDEO_PCDN_FAIL_COUNT_IN_WINDOW, 5);
        mFailTimeWindow = MMKVUtil.getInstance().getInt(PreferenceConstantsInOpenSdk.ITEM_VIDEO_PCDN_FAIL_TIME_IN_WINDOW, 10);
        mFailTimeWindow = Math.max(mFailTimeWindow, 1);
        mFailCountWindow = Math.max(mFailCountWindow, 1);
        Logger.d(TAG, "trace=" + Log.getStackTraceString(new Throwable()));
        Logger.d(TAG, "VideoPcdnManager abSwitch: " + abSwitch + ", failCountWindow: " + mFailCountWindow + ", failTimeWindow: " + mFailTimeWindow);

        String whiteList = MMKVUtil.getInstance().getString(PreferenceConstantsInOpenSdk.ITEM_VIDEO_PCDN_WHITE_DOMAIN_LIST, null);
        setDomainWhiteList(whiteList);

        String codeWhiteList = MMKVUtil.getInstance().getString(PreferenceConstantsInOpenSdk.KEY_WHITE_RESPONSE_CODE_LIST_FOR_VIDEO, null);
        setResponseCodeWhiteList(codeWhiteList);
    }

    public static VideoPcdnManager getSingleInstance() {
        if (instance == null) {
            synchronized (VideoPcdnManager.class) {
                if (instance == null) {
                    instance = new VideoPcdnManager();
                }
            }
        }
        return instance;
    }

    // 不管url是否相同 只要是pcdn的 都会添加到时间窗口中
    public void addFailCount(ExoPlaybackException error, Throwable cause, String playUrl) {
        if (playUrl == null || !playUrl.contains(PCDN)) {
            return;
        }
        if (mNoMoreUsePCDN) {
            Logger.d(TAG, "VideoPcdnManager addFailCount: " + mNoMoreUsePCDN + ", " + mFailTimeMap.size());
            return;
        }
        if (cause == null || error == null || error.getCause() == null) {
            return;
        }
        int responseCode = 0;
        if (cause instanceof HttpDataSource.InvalidResponseCodeException) {
            responseCode = ((HttpDataSource.InvalidResponseCodeException) cause).responseCode;
            if (responseCode > 0 && mWhiteCodeMap.containsKey(String.valueOf(responseCode))) {
                Logger.d(TAG, "VideoPcdnManager addFailCount in codeMap code=" + responseCode);
                return;
            }
        }

        long curTime = System.currentTimeMillis();
        mFailTimeMap.put(curTime, playUrl);
        Iterator<Map.Entry<Long, String>> it = mFailTimeMap.entrySet().iterator();
        long mThresholdTime = curTime - mFailTimeWindow * 60_000L;
        while (it.hasNext()) {
            Map.Entry<Long, String> v = it.next();
            if (v != null && v.getKey() != null && Math.abs(v.getKey() - mThresholdTime) > mFailTimeWindow * 60_000L) {
                it.remove();
            }
        }

        // 这里会做上报
        VideoPCdnErrorModel model = new VideoPCdnErrorModel(playUrl);
        model.reachedMaxFailCount = mFailTimeMap.size() >= mFailCountWindow;
        model.responseCode = responseCode;

        model.rootCauseName = cause.getClass().getName();
        model.rootMessage = cause.getMessage();
        model.rootTrace = TraceUtil.getTrace(cause.getStackTrace());

        Throwable exception = error.getCause();
        model.causeName = exception.getClass().getName();
        model.message = exception.getMessage();
        model.trace = TraceUtil.getTrace(exception.getStackTrace());

        String jsonStr = model.toJson();
        Logger.d(TAG, "VideoPcdnManager addFailCount set true, " + jsonStr);
        XmLogger.log("apm", "videoPcdnErrorRecord", jsonStr);

        if (mFailTimeMap.size() >= mFailCountWindow) {
            mNoMoreUsePCDN = true;
            Logger.d(TAG, "VideoPcdnManager addFailCount set true");
        }
        Logger.d(TAG, "VideoPcdnManager addFailCount: end");
    }

    public void setGateWaySwitch(boolean gateWaySwitch) {
        this.gateWaySwitch = gateWaySwitch;
    }

    public void setGateWaySwitchOff() {
        sGateWaySwitch = false;
    }

    public void setAbSwitch(boolean abSwitch) {
        this.abSwitch = abSwitch;
    }

    public void setFailCountAndTimeWindow(int failCountWindow, int failTimeWindow) {
        this.mFailCountWindow = failCountWindow;
        this.mFailTimeWindow = failTimeWindow;
        mFailTimeWindow = Math.max(mFailTimeWindow, 1);
        mFailCountWindow = Math.max(mFailCountWindow, 1);
    }

    public void setResponseCodeWhiteList(String codeWhiteList) {
        Logger.d(TAG, "VideoPcdnManager setResponseCodeWhiteList: " + codeWhiteList);
        try {
            if (codeWhiteList != null) {
                String[] list = codeWhiteList.split(";");
                if (list != null) {
                    int len = list.length;
                    for (int i = 0; i < len; i++) {
                        String item = list[i];
                        if (item != null && item.length() > 0) {
                            mWhiteCodeMap.put(item, null);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setDomainWhiteList(String domainWhiteList) {
        if (domainWhiteList != null) {
            String[] list = domainWhiteList.split(";");
            if (list != null) {
                int len = list.length;
                for (int i = 0; i < len; i++) {
                    mWhiteMap.put(list[i], null);
                }
            }
            if (Logger.isDebug) {
                StringBuilder sb = new StringBuilder();
                for (String key : mWhiteMap.keySet()) {
                    sb.append(key).append(", ");
                }
                Logger.d(TAG, "VideoPcdnManager setDomainWhiteList: " + sb.toString());
            }
        }
    }

    public boolean canUse() {
        if (!gateWaySwitch || !abSwitch || !netCheckCanUsing() || !sGateWaySwitch) {
            return false;
        }
        if (mNoMoreUsePCDN) {
            return false;
        }
        if (!mUsePcdnForLocal) {
            return false;
        }
        return true;
    }

    public String getPCDNDomain(String domain) {
        Logger.d(TAG, "VideoPcdnManager getPCDNDomain: " + domain + ", " + gateWaySwitch + ", " + abSwitch + ", " + sGateWaySwitch + ", " + mNoMoreUsePCDN);
        if (TextUtils.isEmpty(domain)) {
            return domain;
        }
        if (!canUse()) {
            Logger.d(TAG, "VideoPcdnManager getPCDNDomain canNotUse");
            return domain;
        }
        if (!mWhiteMap.containsKey(domain)) {
            Logger.d(TAG, "VideoPcdnManager getPCDNDomain whiteMap has no such key");
            return domain;
        }
        Map<String, String> map = DNSCache.getInstance().getPcdnMap();
        if (map != null && map.containsKey(PCDN_PREFIX + domain)) {
            String replaceDomain = map.get(PCDN_PREFIX + domain);
            Logger.d(TAG, "getPCDNDomain replaceDomain: " + replaceDomain);
            if (replaceDomain != null && replaceDomain.length() > 0) {
                return replaceDomain;
            }
        }
//        boolean ignore = ConstantsOpenSdk.isDebug && "1".equals(SystemUtil.getSystemProperty("debug.xmly.ignoreDnsMap", "0"));
//        if (ignore && domain != null && domain.equals("vod.ali.xmcdn.com")) {
//            return "vod.pcdn.xmcdn.com";
//        }
        Logger.d(TAG, "getPCDNDomain map not contain");
        return domain;
    }

    private boolean netCheckCanUsing() {
        NetworkType.NetWorkType netWorkType = NetworkType.getNetWorkType(sContext);
        Logger.d(TAG, "netCheckCanUsing " + netWorkType);
        if (netWorkType == NetworkType.NetWorkType.NETWORKTYPE_WIFI) {
            return true;
        }
        if (netWorkType == NetworkType.NetWorkType.NETWORKTYPE_4G
                || netWorkType == NetworkType.NetWorkType.NETWORKTYPE_5G) {
            IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
            if (freeFlowService == null || !freeFlowService.isUsingFreeFlow()) {
                Log.i(TAG, "netCheckCanUsing: 4g or 5g use freeflow");
                return true;
            }
        }
        Log.i(TAG, "netCheckCanUsing: false");
        return false;
    }
}
