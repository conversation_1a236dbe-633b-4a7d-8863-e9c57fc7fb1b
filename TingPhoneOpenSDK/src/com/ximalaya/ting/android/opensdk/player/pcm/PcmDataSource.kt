package com.ximalaya.ting.android.opensdk.player.pcm

import android.net.Uri
import com.google.android.exoplayer2.upstream.DataSource
import com.google.android.exoplayer2.upstream.DataSpec
import com.google.android.exoplayer2.upstream.TransferListener
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * <AUTHOR>
 * @date 2025/4/8 14:08
 */
private const val WAV_HEADER_SIZE: Int = 44

class PcmDataSource(private val dataSpec: DataSpec) : DataSource {
    private val mObj = Any()
    private var mHasInit = false
    private var mHeaderArr: ByteArray? = null
    private var mPcmInputStream: PcmBodyInputStream? = null
    private var mPosition = 0

    override fun read(target: ByteArray, offset: Int, length: Int): Int {
        if (!mHasInit) {
            synchronized(mObj) {
                mHeaderArr = createWavHeader(Int.MAX_VALUE, 16000, 1, 16)
                mPcmInputStream = PcmBodyFileInputStream()
                mPosition = 0
                mHasInit = true
            }
        }
        val header = mHeaderArr
        val inputStream = mPcmInputStream
        if (header == null || header.isEmpty()) {
            throw RuntimeException("wav header missing")
        }
        if (inputStream == null) {
            throw RuntimeException("pcm input stream read exception")
        }
        if (mPosition < header.size) {
            val bytesReadFromHeader = minOf(length, header.size - mPosition)
            System.arraycopy(header, mPosition, target, offset, bytesReadFromHeader)
            mPosition += bytesReadFromHeader

            if (bytesReadFromHeader < length) {
                val bytesReadFromStream = inputStream.read(
                    target,
                    offset + bytesReadFromHeader,
                    length - bytesReadFromHeader
                )
                if (bytesReadFromStream > 0) {
                    mPosition += bytesReadFromStream
                    return bytesReadFromHeader + bytesReadFromStream
                }
            }
            return bytesReadFromHeader
        } else {
            val bytesReadFromStream = inputStream.read(target, offset, length)
            if (bytesReadFromStream > 0) {
                mPosition += bytesReadFromStream
            }
            return bytesReadFromStream
        }
    }

    private fun createWavHeader(
        pcmDataLength: Int,
        sampleRate: Int,
        channels: Int,
        bitsPerSample: Int
    ): ByteArray? {
        // 计算数据块大小
        val byteRate = sampleRate * channels * bitsPerSample / 8
        val blockAlign = channels * bitsPerSample / 8

        // 创建WAV头
        val header = ByteBuffer.allocate(WAV_HEADER_SIZE)
        header.order(ByteOrder.LITTLE_ENDIAN)

        // RIFF头
        header.put("RIFF".toByteArray())
        header.putInt(36 + pcmDataLength) // 文件总大小
        header.put("WAVE".toByteArray())

        // fmt块
        header.put("fmt ".toByteArray())
        header.putInt(16) // fmt块大小
        header.putShort(1.toShort()) // 音频格式（1表示PCM）
        header.putShort(channels.toShort()) // 声道数
        header.putInt(sampleRate) // 采样率
        header.putInt(byteRate) // 字节率
        header.putShort(blockAlign.toShort()) // 数据块对齐
        header.putShort(bitsPerSample.toShort()) // 位深度

        // data块
        header.put("data".toByteArray())
        header.putInt(pcmDataLength) // 数据大小

        return header.array()
    }

    override fun addTransferListener(transferListener: TransferListener) {
    }

    override fun open(dataSpec: DataSpec): Long {
        return -1L
    }

    override fun getUri(): Uri? {
        return dataSpec.uri
    }

    override fun close() {
        mPcmInputStream?.close()
    }
}