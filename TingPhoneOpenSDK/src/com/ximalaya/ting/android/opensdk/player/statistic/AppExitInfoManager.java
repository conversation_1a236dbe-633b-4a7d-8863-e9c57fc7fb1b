package com.ximalaya.ting.android.opensdk.player.statistic;

import android.app.ActivityManager;
import android.app.ApplicationExitInfo;
import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.ProcessStateSummary;
import com.ximalaya.ting.android.opensdk.model.XmAppExitInfo;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.manager.BgPlayOptManager;
import com.ximalaya.ting.android.opensdk.player.receive.ScreenStatusReceiver;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SystemUtil;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.ProcessUtil;
import com.ximalaya.ting.android.xmutil.app.XmAppHelper;

import java.util.List;

public class AppExitInfoManager implements IXmPlayerStatusListener {
    private final static String TAG = "AppExitInfoManager";
    private final static String sLastExitTimeKey = "last_exit_time_key";
    private final static String sPlayerLastExitTimeKey = "player_last_exit_time_key";
    public final static String sAbTestIdKey = "is_bg_play_opt_open";
    private final static int sMaxUploadSize = 2;
    private Runnable mRecordOomAdj;
    private Context mContext;
    private boolean mIsMainProcess;
    private ProcessStateSummary mProcessStateSummary;
    private int mScreenStatues;

    private ScreenStatusReceiver.IScreenStatusListener mIScreenStatusListener = new ScreenStatusReceiver.IScreenStatusListener() {
        @Override
        public void onScreenStatusChange(int screenStatus) {
            mScreenStatues = screenStatus;
            recordOomAdj(mContext);
        }
    };

    private AppExitInfoManager() {
    }
    private static class AppExitInfoManagerHolder {
        private static AppExitInfoManager sAppExitInfoManager = new AppExitInfoManager();
    }

    public static AppExitInfoManager getSingleInstance() {
        return AppExitInfoManagerHolder.sAppExitInfoManager;
    }

    public static String reasonCodeToString(int reason) {
        switch (reason) {
            case ApplicationExitInfo.REASON_EXIT_SELF:
                return "EXIT_SELF";
            case ApplicationExitInfo.REASON_SIGNALED:
                return "SIGNALED";
            case ApplicationExitInfo.REASON_LOW_MEMORY:
                return "LOW_MEMORY";
            case ApplicationExitInfo.REASON_CRASH:
                return "APP CRASH(EXCEPTION)";
            case ApplicationExitInfo.REASON_CRASH_NATIVE:
                return "APP CRASH(NATIVE)";
            case ApplicationExitInfo.REASON_ANR:
                return "ANR";
            case ApplicationExitInfo.REASON_INITIALIZATION_FAILURE:
                return "INITIALIZATION FAILURE";
            case ApplicationExitInfo.REASON_PERMISSION_CHANGE:
                return "PERMISSION CHANGE";
            case ApplicationExitInfo.REASON_EXCESSIVE_RESOURCE_USAGE:
                return "EXCESSIVE RESOURCE USAGE";
            case ApplicationExitInfo.REASON_USER_REQUESTED:
                return "USER REQUESTED";
            case ApplicationExitInfo.REASON_USER_STOPPED:
                return "USER STOPPED";
            case ApplicationExitInfo.REASON_DEPENDENCY_DIED:
                return "DEPENDENCY DIED";
            case ApplicationExitInfo.REASON_OTHER:
                return "OTHER KILLS BY SYSTEM";
            default:
                return "UNKNOWN";
        }
    }

    public void initialize(Context context) {
        mContext = context;
        mIsMainProcess = ProcessUtil.isMainProcess(context);
        if (mIsMainProcess) {
            if (XmPlayerManager.getInstance(context).isConnected()) {
                XmPlayerManager.getInstance(context).addPlayerStatusListener(this);
            } else {
                XmPlayerManager.getInstance(context).addOnConnectedListerner(new XmPlayerManager.IConnectListener() {
                    @Override
                    public void onConnected() {
                        XmPlayerManager.getInstance(context).addPlayerStatusListener(AppExitInfoManager.this);
                    }
                });
            }
        } else {
            XmPlayerService.addPlayerStatusListenerOnPlayProcees(this);
        }
        onNeedCheckAppExitInfo(mContext);
        mProcessStateSummary = new ProcessStateSummary();
        if(mIsMainProcess) {
            ScreenStatusReceiver.init(mContext);
        }
        ScreenStatusReceiver.addScreenStatusListener(mIScreenStatusListener);
        mScreenStatues = SystemUtil.isScreenOn(mContext)
                ? ScreenStatusReceiver.IScreenStatusListener.SCREEN_ON
                : ScreenStatusReceiver.IScreenStatusListener.SCREEN_OFF;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                ((ActivityManager) (context.getSystemService(Context.ACTIVITY_SERVICE))).setProcessStateSummary(null);
            }
        } catch (Throwable e) {
        }
    }

    private String getLastExitTimeKey(){
        return mIsMainProcess ? sLastExitTimeKey : sPlayerLastExitTimeKey;
    }

    public void onNeedCheckAppExitInfo(Context context) {
        if (context == null) {
            return;
        }
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
            return;
        }
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ApplicationExitInfo> applicationExitInfos = null;
        try {
            applicationExitInfos = activityManager.getHistoricalProcessExitReasons("", 0, 16);
        } catch (Throwable throwable){
        }
        if (applicationExitInfos == null || applicationExitInfos.isEmpty()) {
            return;
        }
        long lastTimeStamp = MMKVUtil.getInstance().getLong(getLastExitTimeKey(), 0);
        long newExitTime = applicationExitInfos.get(0).getTimestamp();
        if (newExitTime <= lastTimeStamp) {
            return;
        }
        MMKVUtil.getInstance().saveLong(getLastExitTimeKey(), newExitTime);
        for (ApplicationExitInfo applicationExitInfo : applicationExitInfos) {
            if (applicationExitInfo == null) {
                continue;
            }
            String processName = applicationExitInfo.getProcessName();
            if (TextUtils.isEmpty(processName)) {
                continue;
            }
            if ((mIsMainProcess && !processName.equals("com.ximalaya.ting.android"))
                         || (!mIsMainProcess && !processName.equals("com.ximalaya.ting.android:player"))) {
                continue;
            }
            int reasonCode = applicationExitInfo.getReason();
            String description = applicationExitInfo.getDescription();
            if (!TextUtils.isEmpty(description) && description.contains("installPackage")) {
                continue;
            }
            if (reasonCode == ApplicationExitInfo.REASON_OTHER) {
                if (!TextUtils.isEmpty(description) && (description.contains("REQUEST_INSTALL_PACKAGES") || description.contains("SwipeUpClean"))) {
                    continue;
                }
            }
            if (applicationExitInfo.getTimestamp() <= lastTimeStamp) {
                return;
            }
            MyAsyncTask.execute(() -> {
                try {
                    Gson gson = new Gson();
                    String processStateSummaryString = null;
                    ProcessStateSummary processStateSummary = null;
                    try {
                        processStateSummaryString = new String(applicationExitInfo.getProcessStateSummary());
                        if (TextUtils.isEmpty(processStateSummaryString)) {
                            return;
                        }
                        processStateSummary = gson.fromJson(processStateSummaryString, ProcessStateSummary.class);
                        if (processStateSummary == null) {
                            return;
                        }
                        if (processStateSummary.getScreenStatus() == ScreenStatusReceiver.IScreenStatusListener.SCREEN_ON
                                && (reasonCode == ApplicationExitInfo.REASON_USER_STOPPED || reasonCode == ApplicationExitInfo.REASON_USER_REQUESTED)) {
                            return;
                        }
                        if ((PlayErrorStatisticManager.PlayerStatus.PLAYER_IS_PAUSE+"").equals(processStateSummary.getPlayStatus())) {
                            return;
                        }
                    } catch (Throwable throwable) {
                        return;
                    }
                    String applicationExitInfoString = gson.toJson(getFromAppExitInfo(applicationExitInfo, processStateSummary));
                    Logger.i(TAG, "applicationExitInfoString:" + applicationExitInfoString);
                    MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInOpenSdk.KEY_APP_LOW_MEMORY_KILL_BY_NOTIFICATION, true);
                    XmLogger.log("apm", "appExitInfo", applicationExitInfoString);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
    }

    private XmAppExitInfo getFromAppExitInfo(ApplicationExitInfo applicationExitInfo, ProcessStateSummary processStateSummary) {
        XmAppExitInfo xmAppExitInfo = new XmAppExitInfo();
        xmAppExitInfo.setDescription(applicationExitInfo.getDescription());
        xmAppExitInfo.setImportance(applicationExitInfo.getImportance());
        xmAppExitInfo.setProcessName(applicationExitInfo.getProcessName());
        xmAppExitInfo.setReason(applicationExitInfo.getReason() + " (" + reasonCodeToString(applicationExitInfo.getReason()) + ")");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            xmAppExitInfo.setTimestamp(applicationExitInfo.getTimestamp()+"");
        }
        xmAppExitInfo.setPss(applicationExitInfo.getPss() + "");
        xmAppExitInfo.setRss(applicationExitInfo.getRss() + "");
        xmAppExitInfo.setStatus(applicationExitInfo.getStatus()+"");
        xmAppExitInfo.setInWhiteList(SystemUtil.isIgnoringBatteryOptimizations(mContext));
        try {
            if (processStateSummary != null) {
                int playStatus = processStateSummary.getPlayStatus();
                if (playStatus > 0 && System.currentTimeMillis() - applicationExitInfo.getTimestamp() < 24 * 60 * 60 *1000 ){
                    BgPlayOptManager.getSingleInstance().onKillBySystemWhenPlayingAndroid11(mContext);
                }
                xmAppExitInfo.setPlayStatus(processStateSummary.getPlayStatus() + "");
                xmAppExitInfo.setOomAdj(processStateSummary.getOomAdj());
                xmAppExitInfo.setScreenStatus(processStateSummary.getScreenStatus());
            }
        } catch (Exception e) {
        }

        return xmAppExitInfo;
    }

    public void recordOomAdj(Context context) {
        if (context == null) {
            return;
        }
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
            return;
        }
        if (mRecordOomAdj == null) {
            mRecordOomAdj = new Runnable() {
                @Override
                public void run() {
                    int playerStatusIndex = MMKVUtil.getInstance(PlayErrorStatisticManager.TAG)
                                        .getInt(PlayErrorStatisticManager.PLAYER_STATUS_KEY
                                                , PlayErrorStatisticManager.PlayerStatus.PLAYER_IS_PAUSE.ordinal());
                    if (mProcessStateSummary == null) {
                        return;
                    }
                    mProcessStateSummary.setPlayStatus(playerStatusIndex);
                    mProcessStateSummary.setOomAdj(SystemUtil.getProImportance(context)+"");
                    mProcessStateSummary.setScreenStatus(mScreenStatues);
                    String stateString = new Gson().toJson(mProcessStateSummary);
                    Logger.i(TAG, "stateString:____" + stateString);
                    ((android.app.ActivityManager) (context.getSystemService(Context.ACTIVITY_SERVICE))).setProcessStateSummary(stateString.getBytes());
                }
            };
        }
        XmAppHelper.removeTaskInOnWorkThread(mRecordOomAdj);
        XmAppHelper.runOnOnWorkThreadDelayed(mRecordOomAdj,200);
    }

    private boolean isPlaying() {
        if (mContext == null) {
            return false;
        }
        if (ProcessUtil.isMainProcess(mContext)) {
            return XmPlayerManager.getInstance(mContext).isPlaying();
        } else {
            return XmPlayerService.getPlayerSrvice().isPlaying();
        }
    }

    @Override
    public void onPlayStart() {
        AppExitInfoManager.getSingleInstance().recordOomAdj(mContext);

    }

    @Override
    public void onPlayPause() {
        AppExitInfoManager.getSingleInstance().recordOomAdj(mContext);
    }

    @Override
    public void onPlayStop() {
        AppExitInfoManager.getSingleInstance().recordOomAdj(mContext);
    }

    @Override
    public void onSoundPlayComplete() {
        AppExitInfoManager.getSingleInstance().recordOomAdj(mContext);
    }

    @Override
    public void onSoundPrepared() {

    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {

    }

    @Override
    public void onBufferingStart() {

    }

    @Override
    public void onBufferingStop() {

    }

    @Override
    public void onBufferProgress(int percent) {

    }

    @Override
    public void onPlayProgress(int currPos, int duration) {

    }

    @Override
    public boolean onError(XmPlayerException exception) {
        AppExitInfoManager.getSingleInstance().recordOomAdj(mContext);
        return false;
    }

}
