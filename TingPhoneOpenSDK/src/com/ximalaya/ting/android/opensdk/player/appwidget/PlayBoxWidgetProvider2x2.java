/**
 * WidgetProvider.java
 * com.ximalaya.ting.android.opensdk.player.appwidget
 * <p/>
 * Function： TODO
 * <p/>
 * ver     date      		author
 * ──────────────────────────────────
 * 2015-9-10 		jack.qin
 * <p/>
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.player.appwidget;

import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.RemoteViews;

import androidx.collection.LruCache;

import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.appnotification.NotificationStyleUtils;
import com.ximalaya.ting.android.opensdk.player.appnotification.PendingIntentRequestConstants;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationCreater;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationPendingIntentCreateUtils;
import com.ximalaya.ting.android.opensdk.player.receive.PlayerReceiver;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.push.PushGuardPlayerManager;
import com.ximalaya.ting.android.opensdk.util.AsyncGson;
import com.ximalaya.ting.android.opensdk.util.FileUtilBase;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForPlay;
import com.ximalaya.ting.android.routeservice.service.push.IWidgetService;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.app.XmAppHelper;

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2023/4/17
 * Description：
 */
public class PlayBoxWidgetProvider2x2 extends BaseAppWidgetProvider {
    private AppWidgetManager mAppWidgetManager;
    private boolean mFirstUpdate = true;

    public static int id_appwidget_playOrPause;
    public static int id_appwidget_pre;
    public static int id_appwidget_next;
    public static int id_appwidget_iv_cover;
    public static int id_appwidget_status;
    public static int id_appwidget_title;
    public static int id_appwidget_iv_blur;
    public static int id_appwidget_iv_color;
    public static int id_widget_root;
    public static int layout_host_reflect_appwidget_layout;
    public static int layout_host_reflect_appwidget_layout_match;
    public static int drawable_host_reflect_widget_pause;
    public static int drawable_host_reflect_widget_play;
    public static int drawable_host_widget_default_album;
    public static int drawable_host_widget_play_box_2x2_bg;
    public static int string_host_widget_status_playing;
    public static int string_host_widget_status_continue_play;
    public static final String TRACE_TAG = "PlayBoxWidgetProvider2x2";
    private static LruCache<String, Bitmap> mBitmapLruCache = new LruCache<>(16);
    private static LruCache<String, Bitmap> mBgLruCache = new LruCache<>(16);

    @Override
    public void onReceive(Context context, Intent intent) {
        mIsPlayWidget = true;
        super.onReceive(context, intent);
    }

    private int getLayoutId() {
        return BaseWidgetUtil.useMatchParentLayout() ? layout_host_reflect_appwidget_layout_match : layout_host_reflect_appwidget_layout;
    }

    private RemoteViews bindWidgetPendingIntent(Context context) {
        try {
            PendingIntent broPendingIntent = null;
            PendingIntent openPendingIntent = null;
            Intent broIntent = null;
            Intent openIntent = null;
            RemoteViews remoteViews = new RemoteViews(context.getPackageName(), getLayoutId());
            if (mAppWidgetManager == null) {
                mAppWidgetManager = AppWidgetManager.getInstance(context);
            }
            if (remoteViews != null) {
                // play
                String actionName = XmNotificationCreater.ACTION_CONTROL_START_PAUSE;
                if (context.getPackageName().equals("com.ximalaya.ting.android")) {
                    actionName = XmNotificationCreater.ACTION_CONTROL_START_PAUSE_MAIN;
                }
                broIntent = new Intent(context.getApplicationContext(), PlayerReceiver.class);
                broIntent.setAction(actionName);
                broIntent.putExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER, true);
                broIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME, TRACE_TAG);
                broPendingIntent = PendingIntent.getBroadcast(context, PendingIntentRequestConstants.WIDGET_PLAY2X2_REQUEST_PLAY,
                        broIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
                Logger.i("WidgetProvider", "setOnClickPendingIntent_" + actionName);
                remoteViews.setOnClickPendingIntent(id_appwidget_playOrPause, broPendingIntent);

                if (context.getPackageName().equals("com.ximalaya.ting.android")) {
                    // PRE
                    broIntent = new Intent(context.getApplicationContext(), PlayerReceiver.class);
                    broIntent.setAction(XmNotificationCreater.ACTION_CONTROL_PLAY_PRE_MAIN);
                    broIntent.putExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER, true);
                    broIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME, TRACE_TAG);
                    broPendingIntent = PendingIntent.getBroadcast(context, PendingIntentRequestConstants.WIDGET_PLAY2X2_REQUEST_PPR,
                            broIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
                    remoteViews.setOnClickPendingIntent(id_appwidget_pre, broPendingIntent);

                    // NEXT
                    broIntent = new Intent(context.getApplicationContext(), PlayerReceiver.class);
                    broIntent.setAction(XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT_MAIN);
                    broIntent.putExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER, true);
                    broIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME, TRACE_TAG);
                    broPendingIntent = PendingIntent.getBroadcast(context, PendingIntentRequestConstants.WIDGET_PLAY2X2_REQUEST_NEXT,
                            broIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
                    remoteViews.setOnClickPendingIntent(id_appwidget_next, broPendingIntent);
                }

                openIntent = new Intent(Intent.ACTION_VIEW);
                openIntent.setData(Uri.parse("iting://open?msg_type=11&guiyin=no_ggzs&only_show=true&source=widget"));
                openIntent.putExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER, true);
                openIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME, TRACE_TAG);
                openIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_FROM_OTHER_AREA, "其他");
                openPendingIntent = PendingIntent.getActivity(context, PendingIntentRequestConstants.WIDGET_PLAY2X2_REQUEST_ROOT, openIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
                remoteViews.setOnClickPendingIntent(id_widget_root, openPendingIntent);
                Logger.i("WidgetProvider", "bindWidgetPendingIntent");
            }
            return remoteViews;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void updateWidget(Context context) {
        RemoteViews remoteViews = bindWidgetPendingIntent(context);
        if (remoteViews == null) {
            return;
        }
        Logger.i("WidgetProvider", "updateWidget");
        setDataToView(context, remoteViews);
        boolean isAgress = MmkvCommonUtil.getInstance(context).getBooleanCompat(
                PreferenceConstantsInOpenSdk.TINGMAIN_KEY_PRIVACY_POLICY_AGREED_NEW);
        if (!isAgress && BaseWidgetUtil.useMatchParentLayout()) {
            remoteViews.setTextColor(id_appwidget_status, Color.parseColor("#ffffff"));
            remoteViews.setTextViewText(id_appwidget_status, "请授权“喜马拉雅”\n以使用此小组件");
            remoteViews.setViewVisibility(id_appwidget_title, View.GONE);
        }
        commitUpdate(context, remoteViews);
    }

    private void commitUpdate(Context context, RemoteViews remoteViews) {
        if (mAppWidgetManager == null) {
            return;
        }
        int[] appWidgetIds = new int[0];
        try {
            appWidgetIds = mAppWidgetManager.getAppWidgetIds(new ComponentName(context, getClass()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (appWidgetIds == null || appWidgetIds.length == 0) {
            return;
        }
        Logger.i("WidgetProvider", "commitUpdate");
        try {
            mAppWidgetManager.updateAppWidget(appWidgetIds, remoteViews);
        } catch (Exception e) {
            e.printStackTrace();
            //https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/1362?pid=1
        }
    }

    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager,
                         int[] appWidgetIds) {
        super.onUpdate(context, appWidgetManager, appWidgetIds);
        mAppWidgetManager = appWidgetManager;
        boolean isAgress = MmkvCommonUtil.getInstance(context).getBooleanCompat(
                PreferenceConstantsInOpenSdk.TINGMAIN_KEY_PRIVACY_POLICY_AGREED_NEW);
        if (!isAgress) {
            updateWidget(context);
            return;
        }
        if (appWidgetIds.length > 0) {
            BaseWidgetUtil.traceWidgetNewUpdate(TRACE_TAG);
        }
        PlayableModel playableModel = PushGuardPlayerManager.getInstance().getCurrentPlayableModel();
        if (mFirstUpdate && playableModel == null) {
            mFirstUpdate = false;
            Logger.d("WidgetProvider", "onUpdate1");

            String string = MmkvCommonUtil.getInstance(context).getStringCompat("lasttrack");
            if (TextUtils.isEmpty(string)) {
                IHistoryManagerForPlay historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForPlay.class);
                if(historyManager != null) {
                    Logger.d("WidgetProvider", "onUpdate2");
                    historyManager.addPlayListLoadListener(playIndex -> onUpdate(context, appWidgetManager, appWidgetIds));
                }
            } else {
                Logger.d("WidgetProvider", "onUpdate3");
                new AsyncGson<Track>().fromJson(string, Track.class, new AsyncGson.IResult<Track>() {
                    @Override
                    public void postResult(Track result) {
                        Logger.d("WidgetProvider", "onUpdate4");
                        onInitUI(context, result);
                    }

                    @Override
                    public void postException(Exception e) {

                    }
                });
            }
            return;
        }
        Logger.d("WidgetProvider", "onUpdate5 = " + (playableModel == null));
        mFirstUpdate = false;
        try {
            int n = appWidgetIds.length;
            if (n > 0) {
                PendingIntent broPendingIntent = null;
                PendingIntent openPendingIntent = null;
                Intent broIntent = null;
                Intent openIntent = null;
                RemoteViews views = new RemoteViews(context.getPackageName(), getLayoutId());
                if (views != null) {
                    // play
                    String actionName = XmNotificationCreater.ACTION_CONTROL_START_PAUSE;
                    if (context.getPackageName().equals("com.ximalaya.ting.android")) {
                        actionName = XmNotificationCreater.ACTION_CONTROL_START_PAUSE_MAIN;
                    }
                    broIntent = new Intent(context.getApplicationContext(), PlayerReceiver.class);
                    broIntent.putExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER, true);
                    broIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME, TRACE_TAG);
                    broIntent.setAction(actionName);
//				broIntent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
                    broPendingIntent = PendingIntent.getBroadcast(context, PendingIntentRequestConstants.WIDGET_PLAY2X2_REQUEST_PLAY,
                            broIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
                    Logger.i("WidgetProvider", "setOnClickPendingIntent_" + actionName);
                    views.setOnClickPendingIntent(id_appwidget_playOrPause, broPendingIntent);

                    if (context.getPackageName().equals("com.ximalaya.ting.android")) {
                        // PRE
                        broIntent = new Intent(context.getApplicationContext(), PlayerReceiver.class);
                        broIntent.setAction(XmNotificationCreater.ACTION_CONTROL_PLAY_PRE_MAIN);
                        broIntent.putExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER, true);
                        broIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME, TRACE_TAG);
                        broPendingIntent = PendingIntent.getBroadcast(context, PendingIntentRequestConstants.WIDGET_PLAY2X2_REQUEST_PPR,
                                broIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
                        views.setOnClickPendingIntent(id_appwidget_pre, broPendingIntent);

                        // NEXT
                        broIntent = new Intent(context.getApplicationContext(), PlayerReceiver.class);
                        broIntent.setAction(XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT_MAIN);
                        broIntent.putExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER, true);
                        broIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME, TRACE_TAG);
                        broPendingIntent = PendingIntent.getBroadcast(context, PendingIntentRequestConstants.WIDGET_PLAY2X2_REQUEST_NEXT,
                                broIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
                        views.setOnClickPendingIntent(id_appwidget_next, broPendingIntent);
                    }

                    openIntent = new Intent(Intent.ACTION_VIEW);
                    if (context.getPackageName().equals("com.ximalaya.ting.android")) {
                        openIntent.setData(Uri.parse("iting://open?msg_type=11&guiyin=no_ggzs&only_show=true"));
                        openIntent.putExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER, true);
                        openIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME, TRACE_TAG);
                        openIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_FROM_OTHER_AREA, "其他");
                        openPendingIntent = PendingIntent.getActivity(context, PendingIntentRequestConstants.WIDGET_PLAY2X2_REQUEST_ROOT,
                                openIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
                        views.setOnClickPendingIntent(id_widget_root, openPendingIntent);
                    }
                    setDataToView(context, views);
                    try {
                        mAppWidgetManager.updateAppWidget(appWidgetIds, views);
                    } catch (Exception e) {
                        e.printStackTrace();
                        //https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/1362?pid=1
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Track mLastTrack, mCurrentTrack;

    @Override
    public void onDeleted(Context context, int[] appWidgetIds) {
        super.onDeleted(context, appWidgetIds);
        // 当控件被删除的时候调用该方法
        Logger.i("WidgetProvider", "onDeleted");
    }

    @Override
    public void onDisabled(Context context) {
        super.onDisabled(context);
        // 最后一个widget从屏幕移除
        traceWidget("卸载");
        MMKVUtil.getInstance().saveInt("default_widget_number_key_box_2x2", 0);
        Logger.i("WidgetProvider", "onDisabled");
        mBitmapLruCache = null;
        mBgLruCache = null;
    }

    @Override
    public void onEnabled(Context context) {
        super.onEnabled(context);
        mBitmapLruCache = new LruCache<>(16);
        mBgLruCache = new LruCache<>(16);
        // 第一个加入到屏幕上
        int num = MMKVUtil.getInstance().getInt("default_widget_number_key_box_2x2", 0);
        if (num == 0) {
            num++;
            MMKVUtil.getInstance().saveInt("default_widget_number_key_box_2x2", num);
        }
        traceWidget("添加");
        String string = MmkvCommonUtil.getInstance(context).getStringCompat("lasttrack");
        if (TextUtils.isEmpty(string)) {
            return;
        }
        new AsyncGson<Track>().fromJson(string, Track.class, new AsyncGson.IResult<Track>() {
            @Override
            public void postResult(Track result) {
                onInitUI(context, result);
            }

            @Override
            public void postException(Exception e) {

            }
        });
        Logger.i("WidgetProvider", "onEnabled");
    }

    public static int dp2px(Context context, float dipValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dipValue * scale + 0.5f);
    }

    @Override
    protected void onUpdateWidget(Context context) {
        updateWidget(context);
        Logger.i("WidgetProvider", "onUpdateWidget");
    }

    @Override
    public void onInitUI(Context context, Track track) {
        RemoteViews remoteViews = bindWidgetPendingIntent(context);
        if (remoteViews == null) {
            return;
        }
        if (XmPlayerService.getPlayerSrvice() != null) {
            if (XmPlayerService.getPlayerSrvice().isPlaying()) {
                remoteViews.setImageViewResource(
                        id_appwidget_playOrPause,
                        drawable_host_reflect_widget_pause);
                remoteViews.setTextViewText(id_appwidget_status, context.getResources().getString(string_host_widget_status_playing));
            } else {
                remoteViews.setImageViewResource(
                        id_appwidget_playOrPause,
                        drawable_host_reflect_widget_play);
                remoteViews.setTextViewText(id_appwidget_status, context.getResources().getString(string_host_widget_status_continue_play));
            }
        }
        updateTrackText(context, remoteViews, track);
        commitUpdate(context, remoteViews);
        Logger.i("WidgetProvider", "onInitUI");
        mCurrentTrack = track;
        if (!PushGuardPlayerManager.getInstance().mIsFromPushGuard && !mCurrentTrack.equals(mLastTrack)) {
            new AsyncGson<String>().toJson(mCurrentTrack, new AsyncGson.IResult<String>() {
                @Override
                public void postResult(String result) {
                    if (TextUtils.isEmpty(result)) {
                        return;
                    }
                    MmkvCommonUtil.getInstance(context).saveString("lasttrack", result);
                }

                @Override
                public void postException(Exception e) {

                }
            });
        }
        mLastTrack = mCurrentTrack;
        updateRemoteBitmap(context, track, remoteViews);
    }

    private void setDataToView(Context context, RemoteViews remoteViews) {
        PlayableModel playableModel = PushGuardPlayerManager.getInstance().getCurrentPlayableModel();
        if (!(playableModel instanceof Track)) {
            Logger.e("WidgetProvider", "WidgetProvider null");
            return;
        }
        Track track = (Track) playableModel;
        if (context == null || remoteViews == null) {
            return;
        }
        try {
            if (XmPlayerService.getPlayerSrvice() != null) {
                if (XmPlayerService.getPlayerSrvice().isPlaying()) {
                    remoteViews.setTextViewText(id_appwidget_status, context.getResources().getString(string_host_widget_status_playing));
                    remoteViews.setImageViewResource(
                            id_appwidget_playOrPause,
                            drawable_host_reflect_widget_pause);
                } else {
                    remoteViews.setTextViewText(id_appwidget_status, context.getResources().getString(string_host_widget_status_continue_play));
                    remoteViews.setImageViewResource(
                            id_appwidget_playOrPause,
                            drawable_host_reflect_widget_play);
                }
            }
            Logger.i("WidgetProvider", "setDataToView");
            updateTrackText(context, remoteViews, track);
            mCurrentTrack = track;
            if (!PushGuardPlayerManager.getInstance().mIsFromPushGuard && !mCurrentTrack.equals(mLastTrack)) {
                new AsyncGson<String>().toJson(mCurrentTrack, new AsyncGson.IResult<String>() {
                    @Override
                    public void postResult(String result) {
                        if (TextUtils.isEmpty(result)) {
                            return;
                        }
                        MmkvCommonUtil.getInstance(context).saveString("lasttrack", result);
                    }

                    @Override
                    public void postException(Exception e) {

                    }
                });
            }
            mLastTrack = mCurrentTrack;
            updateRemoteBitmap(context, track, remoteViews);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void updateTrackText(Context context, RemoteViews remoteViews, Track track) {
        String modelDetail = track.getTrackTitle();
        remoteViews.setTextViewText(id_appwidget_title, modelDetail);
    }

    private void updateRemoteBitmap(final Context context, Track track, final RemoteViews remoteViews) {
        if (context == null || TextUtils.isEmpty(context.getPackageName())
                || !context.getPackageName().equals("com.ximalaya.ting.android")) {
            return;
        }
        String coverUrl = FileUtilBase.getLargeImgUrl(track);
        if (coverUrl != null && mBitmapLruCache != null &&
                mBitmapLruCache.get(coverUrl) != null && mBgLruCache != null &&
                mBgLruCache.get(coverUrl) != null) {
            Bitmap cacheMap = mBitmapLruCache.get(coverUrl);
            Bitmap bgMap = mBgLruCache.get(coverUrl);
            if (remoteViews != null && cacheMap != null && !cacheMap.isRecycled() && bgMap != null && !bgMap.isRecycled()) {
                remoteViews.setImageViewBitmap(id_appwidget_iv_cover, cacheMap);
                if (!NotificationStyleUtils.isVivoDevice()) {
                    remoteViews.setImageViewBitmap(id_appwidget_iv_blur, bgMap);
                }
                commitUpdate(context, remoteViews);
                return;
            }
        }
        int width = dp2px(context, 48);
        try {
            int finalWidth = width;
            FileUtilBase.getBitmapByUrl(true, context, track, width, width, bitmap ->
                    XmAppHelper.runOnWorkThread(() -> {
                        if (bitmap != null && !bitmap.isRecycled()) {
                            if (remoteViews != null) {
                                long curTime = System.currentTimeMillis();
                                Bitmap cornerImage = null;
                                try {
                                    cornerImage = BaseWidgetUtil.toRoundCornerImage(bitmap, dp2px(context, 4), finalWidth, finalWidth);
                                    if (mBitmapLruCache != null && coverUrl != null) {
                                        mBitmapLruCache.put(coverUrl, cornerImage);
                                    }
                                } catch (Exception e) {
                                    cornerImage = bitmap;
                                    e.printStackTrace();
                                }
                                remoteViews.setImageViewBitmap(id_appwidget_iv_cover, cornerImage);
                                try {
                                    Bitmap blurBitmap = BaseWidgetUtil.fastBlur(context, bitmap, 20, 50);
                                    blurBitmap = BaseWidgetUtil.toRoundCornerImage(blurBitmap, dp2px(context, 16), dp2px(context, 148), dp2px(context, 148));
                                    if (mBgLruCache != null && coverUrl != null) {
                                        mBgLruCache.put(coverUrl, blurBitmap);
                                    }
                                    if (!NotificationStyleUtils.isVivoDevice()) {
                                        remoteViews.setImageViewBitmap(id_appwidget_iv_blur, blurBitmap);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                Logger.log("WidgetProvider : time = " + (System.currentTimeMillis() - curTime));
                            }
                        } else {
                            if (remoteViews != null) {
                                remoteViews.setInt(id_appwidget_iv_cover, "setImageResource", drawable_host_widget_default_album);
                                remoteViews.setInt(id_appwidget_iv_blur, "setImageResource", drawable_host_widget_play_box_2x2_bg);
                            }
                        }
                        if (remoteViews != null) {
                            commitUpdate(context, remoteViews);
                        }
                    }));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void traceWidget(String type) {
        IWidgetService iWidgetService = RouterServiceManager.getInstance().getService(IWidgetService.class);
        if (iWidgetService != null) {
            iWidgetService.traceWidgetNewClick(TRACE_TAG, type);
        }
    }

    public static Bitmap getBackground(int bgColor) {
        try {
            Bitmap.Config config = Bitmap.Config.ARGB_8888; // Bitmap.Config.ARGB_8888 Bitmap.Config.ARGB_4444 to be used as these two config constant supports transparency
            Bitmap bitmap = Bitmap.createBitmap(2, 2, config); // Create a Bitmap
            Canvas canvas =  new Canvas(bitmap); // Load the Bitmap to the Canvas
            canvas.drawColor(bgColor); //Set the color
            return bitmap;
        } catch (Exception e) {
            return null;
        }
    }
}