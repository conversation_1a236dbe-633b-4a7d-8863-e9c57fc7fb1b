package com.ximalaya.ting.android.opensdk.player.appnotification;

/**
 * 播控、小组件pendingIntent的requestCode列表
 * */
public class PendingIntentRequestConstants {
    public static int NOTIFICATION_REQUEST_ROOT = 7000;
    public static int NOTIFICATION_REQUEST_PLAY = 7001;
    public static int NOTIFICATION_REQUEST_NEXT = 7002;
    public static int NOTIFICATION_REQUEST_PRE = 7003;
    public static int NOTIFICATION_REQUEST_NOTHING = 7004;
    public static int NOTIFICATION_REQUEST_CLOSE = 7005;
    public static int NOTIFICATION_REQUEST_LIKE = 7006;
    public static int NOTIFICATION_REQUEST_LIST = 7007;
    public static int NOTIFICATION_REQUEST_SIGN = 7008;
    public static int NOTIFICATION_REQUEST_PLUS = 7009;
    public static int NOTIFICATION_REQUEST_LESS = 7010;
    public static int NOTIFICATION_REQUEST_GROUP_CHAT = 7011;
    public static int NOTIFICATION_REQUEST_CLOSE_CHAT_XMLY_MVP = 7012;
    public static int NOTIFICATION_REQUEST_MUTE_CHAT_XMLY_MVP = 7013;
    public static int NOTIFICATION_REQUEST_ROOT_CHAT_XMLY_MVP = 7014;

    public static int WIDGET_PLAY4X2_REQUEST_PLAY = 7101;
    public static int WIDGET_PLAY4X2_REQUEST_PPR = 7102;
    public static int WIDGET_PLAY4X2_REQUEST_NEXT = 7103;
    public static int WIDGET_PLAY4X2_REQUEST_HOME = 7104;
    public static int WIDGET_PLAY4X2_REQUEST_RANK = 7105;
    public static int WIDGET_PLAY4X2_REQUEST_SUBSCRIBE = 7106;
    public static int WIDGET_PLAY4X2_REQUEST_HISTORY = 7107;
    public static int WIDGET_PLAY4X2_REQUEST_ROOT = 7108;

    public static int WIDGET_PLAY2X2_REQUEST_PLAY = 7201;
    public static int WIDGET_PLAY2X2_REQUEST_PPR = 7202;
    public static int WIDGET_PLAY2X2_REQUEST_NEXT = 7203;
    public static int WIDGET_PLAY2X2_REQUEST_ROOT = 7204;

    public static int WIDGET_SIGN2X2_REQUEST_ROOT = 7301;

    public static int WIDGET_SEARCH2X2_REQUEST_SEARCH_HINT = 7401;
    public static int WIDGET_SEARCH2X2_REQUEST_SEARCH_RESULT = 7402;

    public static int WIDGET_PLAY4X1_REQUEST_PLAY = 7501;
    public static int WIDGET_PLAY4X1_REQUEST_PPR = 7502;
    public static int WIDGET_PLAY4X1_REQUEST_NEXT = 7503;
    public static int WIDGET_PLAY4X1_REQUEST_LIKE = 7504;
    public static int WIDGET_PLAY4X1_REQUEST_ROOT = 7505;

    public static int WIDGET_NEWS4X2_REQUEST_ROOT = 7601;
    public static int WIDGET_NEWS4X2_REQUEST_PLAY1 = 7602;
    public static int WIDGET_NEWS4X2_REQUEST_PLAY2 = 7603;
    public static int WIDGET_NEWS4X2_REQUEST_TITLE1 = 7604;
    public static int WIDGET_NEWS4X2_REQUEST_TITLE2 = 7605;

    public static int WIDGET_AUTO_STOP_4X2_REQUEST_PLAY = 7701;
    public static int WIDGET_AUTO_STOP_4X2_REQUEST_PPR = 7702;
    public static int WIDGET_AUTO_STOP_4X2_REQUEST_NEXT = 7703;
    public static int WIDGET_AUTO_STOP_4X2_REQUEST_ROOT = 7704;
    public static int WIDGET_AUTO_STOP_4X2_REQUEST_SET_ONE = 7705;
    public static int WIDGET_AUTO_STOP_4X2_REQUEST_SET_THREE = 7706;
    public static int WIDGET_AUTO_STOP_4X2_REQUEST_SET_TEN_MIN = 7707;
    public static int WIDGET_AUTO_STOP_4X2_REQUEST_SET_HALF_HOUR = 7708;
}