/**
 * IXmPlayerStatusListener.java
 * com.ximalaya.ting.android.player.service
 * <p>
 * <p>
 * ver     date      		author
 * ---------------------------------------
 * 2015-4-3 		chadwii
 * <p>
 * Copyright (c) 2015, chadwii All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.player.service;

import com.ximalaya.ting.android.opensdk.model.track.Track;

/**
 * <AUTHOR>
 * @Date 2015-4-3 上午11:04:59
 * @see
 * @since Ver 1.1
 */
public interface IXmPlayerStatusListenerExtension extends IXmPlayerStatusListener {

    void onRequestPlayUrlBegin();

    void onRequestPlayUrlSuccess();

    void onRequestPlayUrlError(int code, String message);

    // 试听结束
    void onAudioAuditionOver(Track track);

    default void onChildAiTimbreUrlGet(boolean success, boolean isDownload, String type) {
    }
}
