package com.ximalaya.ting.android.opensdk.player.statistics.manager

import android.content.Context
import android.util.Log
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.statistic.RecordModel
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.manager.QuickListenForPlayProcessUtil.getQuickListenSkipHead
import com.ximalaya.ting.android.opensdk.player.manager.SkipHeadTailManager
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService
import com.ximalaya.ting.android.opensdk.player.statistics.event.*
import com.ximalaya.ting.android.opensdk.player.statistics.model.PlayStatisticsRecord
import com.ximalaya.ting.android.opensdk.player.statistics.util.PlayStatisticsUtil
import com.ximalaya.ting.android.routeservice.RouterServiceManager
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmutil.Logger

/**
 * <AUTHOR>
 * @date 2023/3/7 18:17
 */
private const val TAG = "XmPlayStatisticsManager"
private const val TAG_TRACE = "XmPlayStatisticsTrace"
private const val OPEN_STACKTRACE = false

class XmPlayStatisticsManager private constructor() {
    var mContext: Context? = null
    var mCurPlayRecord: PlayStatisticsRecord? = null
        private set
    private var mRecordFromFile = false
    private var mLastStatus = STATUS_NONE
    private var mRecordModel: RecordModel? = null

    companion object {
        private val sXmPlayStatisticsManager = XmPlayStatisticsManager()
        fun getInstance() = sXmPlayStatisticsManager
    }

    init {
        val curPlayRecord = PlayStatisticsRecordManager.getInstance().getLastPlayRecord()
        Logger.d(TAG, "XmPlayStatisticsManager init 1 $curPlayRecord")
        if (curPlayRecord != null) {
            Logger.d(TAG, "XmPlayStatisticsManager init 2 ${curPlayRecord.lastStatus}")
            // 从文件中恢复，如果上次是播放状态，这里补充杀进程埋点。如果是暂停状态，因为之前上报过，这里会恢复到之前的播放状态
            if (curPlayRecord.lastStatus == STATUS_STARTED) {
                // 上一次播放状态是正在播放的状态，这时候需要上报杀进程->续播或者杀进程结束播放埋点
                mCurPlayRecord = curPlayRecord
                mRecordFromFile = curPlayRecord != null
                Logger.d(TAG, "XmPlayStatisticsManager init 3")
                EventManager.getInstance().processKilled(curPlayRecord)
                Logger.d(
                    "XmPlayStatisticsInit", "3-1 listenedDuration=" + curPlayRecord.listenedDuration
                            + ", playedDuration=" + curPlayRecord.playedDuration
                            + ", totalPlayedDuration=" + curPlayRecord.totalPlayedDuration
                            + ", totalListenedDuration=" + curPlayRecord.totalListenedDuration
                            + ", lastPlayedDuration=" + curPlayRecord.lastPlayedDuration
                            + ", lastListenedDuration=" + curPlayRecord.lastListenedDuration
                )
                curPlayRecord.resetDuration() // 重置时间
            } else {
                // EventManager.getInstance().processStarted(curPlayRecord)
                // 这里将record赋值，同时不认为是从文件恢复，可以认为复原上次的播放状态
                mCurPlayRecord = curPlayRecord
                val uploadSoundSwitchOnStart = MMKVUtil.getInstance().getBoolean(
                    PreferenceConstantsInOpenSdk.KEY_PLAY_TRACE_SOUND_SWITCH_UPLOAD, false
                )
                Logger.d(TAG, "init uploadSoundSwitchOnStart=$uploadSoundSwitchOnStart")
                if (uploadSoundSwitchOnStart) {
                    mRecordFromFile = false
                } else {
                    mRecordFromFile = curPlayRecord != null
                }
                mLastStatus = curPlayRecord.lastStatus
                // 后续这里就是切歌和暂停->起播的操作
                Logger.d(
                    "XmPlayStatisticsInit", "4-1 listenedDuration=" + curPlayRecord.listenedDuration
                            + ", playedDuration=" + curPlayRecord.playedDuration
                            + ", totalPlayedDuration=" + curPlayRecord.totalPlayedDuration
                            + ", totalListenedDuration=" + curPlayRecord.totalListenedDuration
                            + ", lastPlayedDuration=" + curPlayRecord.lastPlayedDuration
                            + ", lastListenedDuration=" + curPlayRecord.lastListenedDuration
                )
                curPlayRecord.resetDuration() // 重置时间
            }
        }
        Logger.d(TAG, "XmPlayStatisticsManager init 4")
    }

    fun init(ctx: Context) {
        mContext = ctx
        // 在构造方法中执行即可
    }

    fun setRecordModel(model: RecordModel?) {
        mRecordModel = model
    }

    private fun isTrack(track: Track): Boolean {
        return PlayableModel.KIND_TRACK == track.kind || PlayableModel.KIND_TTS == track.kind || PlayableModel.KIND_MODE_SLEEP == track.kind || PlayableModel.KIND_CHAT_XMLY == track.kind
    }

    private fun isWillNotPlay(): Boolean {
        return XmPlayerService.getPlayerSrvice()?.willNotPlay() ?: false
    }

    fun updateUbtSource() {
        mCurPlayRecord?.updateUbtSource(2)
        PlayStatisticsRecordManager.getInstance().savePlayRecord(mCurPlayRecord)
    }

    // 起播时，需要存储下
    fun onPlayStart(curModel: PlayableModel?, online: Boolean, position: Int, playUrl: String?, nextType: Int, bitrate: Int, contentLen: Long) {
        if (OPEN_STACKTRACE) {
            Logger.d(TAG_TRACE, "onPlayStart ${Log.getStackTraceString(Throwable())}")
        }
//        if (isWillNotPlay()) {
//            Logger.d(TAG, "onPlayStart 0 willNotPlay")
//            return
//        }
        if (curModel !is Track) {
            mCurPlayRecord = null
            Logger.d(TAG, "onPlayStart 1 curModel is not Track")
            return
        }
        if (!isTrack(curModel)) {
            // 这里认为是暂停，后面再触发续播逻辑
            Logger.d(TAG, "onPlayStart 2")
            val record = mCurPlayRecord
            if (record != null && record.lastStatus == STATUS_STARTED) {
                // 这里发生了暂停
                EventManager.getInstance().startToPause(record, position)
            }
            return
        }
        Logger.d(
            TAG,
            "onPlayStart 2 ${curModel.trackTitle}, position=$position, curPlayMethod=$nextType, mLastStatus=$mLastStatus"
        )
        if (mLastStatus == STATUS_STARTED) {
            // 这里过滤多余的状态回调，在单曲循环下，重复播放会出现onPlayStart触发多次
            Logger.d(TAG, "onPlayStart 3")
            return
        }
        val lastStatus = mLastStatus
        mLastStatus = STATUS_STARTED
        EventManager.getInstance().mXdcsPlayedPosition = position.toLong()
        // 如何判断是首次播放
        // 1. mCurPlayRecord == null
        // 2. 和上次播放的id不一致
        // 3. 单曲模式下id一致，需要考虑多次暂停起播的情况
        if (mCurPlayRecord == null || mCurPlayRecord?.trackId != curModel.dataId
            || (mCurPlayRecord?.trackId == curModel.dataId
                    && (lastStatus == STATUS_COMPLETED || lastStatus == STATUS_STOPPED)
                    && inPlayMode(XmPlayListControl.PlayMode.PLAY_MODEL_SINGLE_LOOP))
        ) {
            if (mRecordFromFile) {
                Logger.d(TAG, "onPlayStart 4-1 杀进程，切歌播放")
                // 判断是否是杀进程 -> 切歌播放
                val startFromLastProcess = mRecordFromFile
                mRecordFromFile = false
                // 杀进程结束播放埋点上报
                // 这里就不上报了，要不然会重复上报
                val uploadSoundSwitchTrace = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_PLAY_TRACE_SOUND_SWITCH_UPLOAD, false)
                EventManager.getInstance().soundSwitch(mCurPlayRecord, null, uploadSoundSwitchTrace)
                // EventManager.getInstance().processKilled(mCurPlayRecord)
            }

            PlayStatisticsUtil.getStatisticsCallback()?.clearUploadTime()

            mCurPlayRecord = PlayStatisticsRecord(curModel, online, nextType, playUrl, mRecordModel, bitrate, contentLen)
            // 起播的判定开始
            Logger.d(TAG, "onPlayStart 4-2 起播")
            var head = getQuickListenSkipHead(XmPlayerService.getPlayerSrvice(), curModel)
            Logger.d(TAG, "onPlayStart 4-2-1 起播  head=$head")
            if (head == -1) {
                head = getSkipHead(curModel.album?.albumId)
            }
            Logger.d(TAG, "onPlayStart 4-2-2 起播  head=$head")

            val curPlayStartSkipHead = head > 0 && Math.abs(position - head) < 5_00
            // 这个只有单曲循环才生效
            // val headSkip = SkipHeadTailManager.getInstance().isHeadSkipped
            Logger.d(TAG, "onPlayStart 4-3 起播，当前是否跳过开头 $curPlayStartSkipHead")
            mCurPlayRecord?.headSkip = curPlayStartSkipHead
            mCurPlayRecord?.head = head
            mCurPlayRecord?.lastStatus = STATUS_STARTED

            EventManager.getInstance().onPlayStart(mCurPlayRecord, position)
        } else {
            if (mRecordFromFile) {
                Logger.d(TAG, "onPlayStart 5-1 杀进程，继续播放")
                // 判断是否是杀进程 -> 继续播放
                val startFromLastProcess = mRecordFromFile
                mRecordFromFile = false
                mCurPlayRecord?.lastStatus = STATUS_STARTED
                // 杀进程继续播放埋点上报
                EventManager.getInstance().processKilledToStart(mCurPlayRecord, position)
            } else if (mCurPlayRecord?.lastStatus == STATUS_COMPLETED) {
                // 同一个声音播放，且上一个声音播放结束，说明需要重复播放

                PlayStatisticsUtil.getStatisticsCallback()?.clearUploadTime()

                mCurPlayRecord = PlayStatisticsRecord(curModel, online, nextType, playUrl, mRecordModel, bitrate, contentLen)
                Logger.d(TAG, "onPlayStart 6-1 起播")
                mCurPlayRecord?.lastStatus = STATUS_STARTED
                EventManager.getInstance().onPlayStart(mCurPlayRecord, position)
            } else {
                // 暂停 -> 起播
                Logger.d(TAG, "onPlayStart 7-1 暂停 -> 起播")
                mCurPlayRecord?.lastStatus = STATUS_STARTED
                EventManager.getInstance().pauseToStart(mCurPlayRecord, position)
            }
        }
    }

    fun pause(reason: Int) {
        mCurPlayRecord?.addPauseReason(reason)
    }

    fun onPlayPause(curModel: PlayableModel?, currPosition: Int) {
        if (OPEN_STACKTRACE) {
            Logger.d(TAG_TRACE, "onPlayPause ${Log.getStackTraceString(Throwable())}")
        }
//        if (isWillNotPlay()) {
//            return
//        }
        if (curModel !is Track) {
            Logger.d(TAG, "onPlayPause curModel is not Track")
            return
        }
        if (mCurPlayRecord == null) {
            Logger.d(TAG, "onPlayPause mCurPlayRecord is null")
            return
        }
        if (mCurPlayRecord?.trackId == curModel.dataId && mCurPlayRecord?.lastStatus == STATUS_PAUSED) {
            Logger.d(TAG, "onPlayPause status is paused")
            return
        }
        mLastStatus = STATUS_PAUSED
        if (mCurPlayRecord?.trackId == curModel.dataId) {
            Logger.d(TAG, "onPlayPause ${mCurPlayRecord?.trackTitle}")
            Logger.d(TAG, "onPlayPause 起播 -> 暂停")
            mCurPlayRecord?.lastStatus = STATUS_PAUSED
            EventManager.getInstance().startToPause(mCurPlayRecord, currPosition)
        } else {
            Logger.d(TAG, "onPlayPause 不应该走到这里 ${curModel.trackTitle}")
        }
    }

    fun onPlayStop(
        curModel: PlayableModel?,
        position: Int,
        soundSwitch: Boolean,
        willPlayModel: PlayableModel? = null,
    ) {
//        if (isWillNotPlay()) {
//            Logger.d(TAG, "onPlayStop 1 willNotPlay")
//            return
//        }
        if (curModel !is Track) {
            Logger.d(TAG, "onPlayStop curModel is not Track")
            return
        }
        if (mCurPlayRecord == null) {
            Logger.d(TAG, "onPlayStop mCurPlayRecord is null")
            return
        }
        val lastStatus = mLastStatus
        mLastStatus = STATUS_STOPPED
        Logger.d(
            TAG,
            "onPlayStop ${curModel.trackTitle}, position=$position, soundSwitch=$soundSwitch, mLastStatus=$lastStatus"
        )
        if (lastStatus == STATUS_COMPLETED) {
            Logger.d(TAG, "onPlayStop status form complete")
            return
        }
        if (mCurPlayRecord?.trackId == curModel.dataId) {
            // 只有当前播放的声音和stop的声音id匹配才能处理
            // 如果是第一次启动，又触发了切歌播放，不能上报，逻辑统一在onPlayStart中
            if (soundSwitch && !mRecordFromFile && lastStatus != STATUS_STOPPED) {
                if (willPlayModel is Track && isTrack(willPlayModel)) {
                    mLastStatus = STATUS_STOPPED
                    mCurPlayRecord?.lastStatus = STATUS_STOPPED
                    // 如果上一次的播放状态是暂停状态，那么不上报切歌埋点
                    val uploadTrace = lastStatus != STATUS_PAUSED
                    val switchOpen = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_PLAY_TRACE_SOUND_SWITCH_UPLOAD, false)
                    val upload = if (switchOpen) true else uploadTrace
                    EventManager.getInstance().soundSwitch(mCurPlayRecord, position, upload)
                } else {
                    // 将要播放的声音不是track
                    mLastStatus = STATUS_PAUSED
                    mCurPlayRecord?.lastStatus = STATUS_PAUSED
                    val uploadTrace = lastStatus != STATUS_PAUSED
                    val switchOpen = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_PLAY_TRACE_SOUND_SWITCH_UPLOAD, false)
                    val upload = if (switchOpen) true else uploadTrace
                    // 如果是暂停的声音，则不触发暂停埋点
                    EventManager.getInstance().startToPause(mCurPlayRecord, position, upload)
                    mCurPlayRecord?.let {
                        it.lastPlayedDuration += it.playedDuration + it.totalPlayedDuration
                        it.lastListenedDuration += it.listenedDuration + it.totalListenedDuration
                        it.playedDuration = 0
                        it.totalPlayedDuration = 0
                        it.totalListenedDuration = 0F
                        it.listenedDuration = 0F
                    }
                    Logger.d(TAG, "onPlayStop 播放转为暂停状态，需要清空时间")
                }
            }
            Logger.d(
                TAG,
                "onPlayStop dataId valid soundSwitch=$soundSwitch, mRecordFromFile=$mRecordFromFile, lastStatus=$lastStatus"
            )
        } else if (!isTrack(curModel) && willPlayModel is Track && isTrack(willPlayModel)) {
            // 由非普通声音切换到普通声音，这里上报下切歌埋点
            Logger.d(TAG, "onPlayStop 非普通声音切换到普通声音")
            if (willPlayModel.dataId != mCurPlayRecord?.trackId) {
                val uploadTrace = lastStatus != STATUS_PAUSED
                val switchOpen = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_PLAY_TRACE_SOUND_SWITCH_UPLOAD, false)
                val upload = if (switchOpen) true else uploadTrace
                // 如果是暂停，则不上报切歌埋点
                EventManager.getInstance().soundSwitch(mCurPlayRecord, null, upload)
                Logger.d(TAG, "触发切歌播放")
            } else {
                mLastStatus = lastStatus
                Logger.d(TAG, "触发继续播放")
            }
        } else {
            Logger.d(TAG, "onPlayStop else")
        }
    }

    fun onSoundPlayComplete(curModel: PlayableModel?, isEnd: Boolean, curPos: Int) {
        if (curModel !is Track) {
            Logger.d(TAG, "onSoundPlayComplete curModel is not Track")
            return
        }
        if (mCurPlayRecord == null) {
            Logger.d(TAG, "onSoundPlayComplete mCurPlayRecord is null")
            return
        }
        Logger.d(TAG, "onSoundPlayComplete ${curModel.trackTitle}, mLastStatus=$mLastStatus")
        val lastStatus = mLastStatus
        mLastStatus = STATUS_COMPLETED
        if (mCurPlayRecord?.trackId == curModel.dataId) {
            Logger.d(TAG, "onSoundPlayComplete isAudition=${curModel.isAudition}, isAuthorized=${curModel.isAuthorized}, ${mCurPlayRecord?.endTime}, ${curModel.sampleDuration}, $lastStatus")
            // 特殊逻辑，如果是付费声音的播放结束，不触发播放结束的回调
            val record = mCurPlayRecord
            if (record != null && curModel.isAudition && !curModel.isAuthorized && Math.abs(record.endTime - curModel.sampleDuration * 1000) <= 4000) {
                Logger.d(TAG, "onSoundPlayComplete 试听声音")
                if (lastStatus == STATUS_PAUSED) {
                    // 如果已经出发了pause，这里就不触发了，重置下时间即可
                } else if (lastStatus == STATUS_STARTED) {
                    EventManager.getInstance().startToPause(mCurPlayRecord, record.endTime)
                }
                // 为避免触发onPlayStop，这里主动把状态置为stop状态
                mLastStatus = STATUS_STOPPED
                mCurPlayRecord?.lastStatus = STATUS_STOPPED
                mCurPlayRecord?.listenedDuration = 0F
                mCurPlayRecord?.playedDuration = 0
                mCurPlayRecord?.totalPlayedDuration = 0
                mCurPlayRecord?.totalListenedDuration = 0F
                mCurPlayRecord?.lastPlayedDuration = 0
                mCurPlayRecord?.lastListenedDuration = 0F
                return
            }
        }

        if (mCurPlayRecord?.trackId == curModel.dataId) {
            Logger.d(TAG, "onSoundPlayComplete ${mCurPlayRecord?.trackTitle}")
            Logger.d(TAG, "onSoundPlayComplete 播放完")
            val tail = getSkipTail(curModel.album?.albumId)
            val tailSkip = SkipHeadTailManager.getInstance().isTailSkipped
            mCurPlayRecord?.lastStatus = STATUS_COMPLETED
            mCurPlayRecord?.tail = tail
            mCurPlayRecord?.tailSkip = tailSkip
            if (isEnd) {
                EventManager.getInstance().playComplete(mCurPlayRecord, tailSkip, tail)
            } else {
                EventManager.getInstance().soundSwitch(mCurPlayRecord, curPos)
            }
        } else {
            Logger.d(TAG, "onSoundPlayComplete 不应该走到这里 ${curModel.trackTitle}")
        }
    }

    fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
        if (lastModel !is Track || curModel !is Track) {
            Logger.d(TAG, "onSoundSwitch model is not Track")
            return
        }
        Logger.d(TAG, "onSoundSwitch ${lastModel.trackTitle}, ${curModel.trackTitle}")
    }

    fun onPlayProgress(curModel: PlayableModel?, currPos: Int, duration: Int) {
        if (mCurPlayRecord == null || curModel?.dataId != mCurPlayRecord?.trackId) {
            Logger.d(TAG, "onPlayProgress mCurPlayRecord is null")
            return
        }
        if (curModel is Track) {
            mCurPlayRecord?.let {
                if (curModel.permissionSource != null && curModel.permissionSource != it.permissionSource) {
                    mCurPlayRecord?.permissionSource =
                        if (curModel.permissionSource == null) "" else curModel.permissionSource
                    Logger.d(TAG, "onPlayProgress permissionSource1=${mCurPlayRecord?.permissionSource}")
                } else if (curModel.permissionSource == null && it.permissionSource != null) {
                    mCurPlayRecord?.permissionSource = ""
                    Logger.d(TAG, "onPlayProgress permissionSource2=${mCurPlayRecord?.permissionSource}")
                }
            }
        }
        Logger.d(TAG, "onPlayProgress currPos=$currPos, duration=$duration")
    }

    private var mLastUploadId = -1L

    fun updateProgress(
        curPos: Int, listenedNatureDuration: Float, vagueDuration: Long, playedDuration: Long,
        playerDuration: Int, deltaListenDuration: Int, deltaListenNatureDuration: Float,
    ) {
//        if (isWillNotPlay()) {
//            return
//        }
        Logger.d(
            TAG, "updateProgress listenedNatureDuration=$listenedNatureDuration, vagueDuration=$vagueDuration"
        )
        val record = mCurPlayRecord ?: return
        val curPlaySound = XmPlayerService.getPlayerSrvice()?.currPlayModel ?: return
        if (curPlaySound.dataId != record.trackId) {
            Logger.d(TAG, "updateProgress 声音id不一致")
            return
        }
        if (record.duration <= 0) {
            if (PlayStatisticsUtil.getStatisticsCallback()?.isBetaVersion == true) {
                Logger.d(TAG, "updateProgress duration <= 0 and isBetaVersion 1")
                if (curPlaySound.dataId != mLastUploadId) {
                    Logger.d(TAG, "updateProgress duration <= 0 and isBetaVersion 2")
                    mLastUploadId = curPlaySound.dataId
                    val xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost::class.java)
                    val track = curPlaySound as? Track?
                    val trackMsg =
                        "id=${curPlaySound.dataId},trackTitle=${curPlaySound.kind},title=${track?.trackTitle}"
                    xdcsPost?.statErrorToXDCS("OnProgressException", "$trackMsg")
                }
            }
            Logger.d(TAG, "updateProgress duration <= 0")
            return
        }

        // 计算广告逻辑
        if (record.isMiddleAdUrl && record.middleAdDuration > 0 && record.gasPointInfoList != null) {
            record.gasPointInfoList?.forEach { item ->
                if (item != null && curPos > item.startMills && curPos <= item.stopMills) {
                    record.adPlayedDuration += Math.min(deltaListenDuration.toLong(), (curPos - item.startMills).toLong())
                    record.adListenedDuration += Math.min(deltaListenNatureDuration, (curPos - item.startMills).toFloat())
                }
            }
            Logger.d(TAG, "adUpdateProgress adPlayedDuration=${record.adPlayedDuration / 1000.0}," +
                    " adListenedDuration=${record.adListenedDuration / 1000.0}, ${record.trackTitle}")
        }

        record.updateEndTime(curPos)
        record.playedDuration = Math.max(0, vagueDuration - record.totalPlayedDuration)
        record.listenedDuration = Math.max(0F, listenedNatureDuration - record.totalListenedDuration)
        EventManager.getInstance().updateProgress(record, vagueDuration, listenedNatureDuration, record.duration)
        EventManager.getInstance().mXdcsPlayedPosition = playedDuration
    }

    private fun getSkipHead(albumId: Long?) =
        if (albumId != null) SkipHeadTailManager.getInstance().getHead(albumId) else -1

    private fun getSkipTail(albumId: Long?) =
        if (albumId != null) SkipHeadTailManager.getInstance().getTail(albumId) else -1

    private fun inPlayMode(playMode: XmPlayListControl.PlayMode) = XmPlayerService.getPlayerSrvice() != null &&
            XmPlayerService.getPlayerSrvice()!!.xmPlayMode == playMode
}