package com.ximalaya.ting.android.opensdk.player.manager;


import android.content.Context;
import android.os.Process;

import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.receive.ScreenStatusReceiver;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.ProcessUtil;
import com.ximalaya.ting.android.xmutil.app.XmAppHelper;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public class BgPlayOptManager {
    private static final String sLastInitTimeKey = "last_init_time_key";
    private static final String sKillTotalTimeKey = "kill_total_time_key";
    private static final String sMainProcessExitTimeKey = "main_process_delay_exit_time";
    public final static String sMaxKillTimeKey = "max_kill_time_in_on_day_key";
    public final static String sForceStopMainProcessInBgKey = "force_stop_main_process_in_bg_key";
    private static int mMaxKillTimeInOnDay = 1;
    private static final String sNeedKillMainProcessInBgKey = "need_kill_main_process_in_bg_key";
    private static final String KEY_LOCK_SCREEN_OPEN = "KEY_LOCK_SCREEN_OPEN"; // 是否开启喜马锁屏页面
    private static final String KEY_LOCK_SCREEN_CHECKBOX_CHECKED = "KEY_LOCK_SCREEN_CHECKBOX_CHECKED"; //锁屏显示CheckBox状态
    private boolean mIsNeedKillMainProcessInBg;
    private Context mContext;
    private int mDelayExitTime;
    private boolean mIsAutoOptOpen;
    private boolean mIsForceStopMainProcessInBg;
    private BgPlayOptManager() {
    }
    private static class BgPlayOptManagerHolder {
        private static BgPlayOptManager sAppExitInfoManager = new BgPlayOptManager();
    }

    public static BgPlayOptManager getSingleInstance() {
        return BgPlayOptManager.BgPlayOptManagerHolder.sAppExitInfoManager;
    }
    //注意！！！ui进程调用
    public void init(Context context, boolean isAutoOptOpen) {
        if (!ProcessUtil.isMainProcess(context)) {
            return;
        }
        mIsAutoOptOpen = isAutoOptOpen;
        mContext = context;
        mIsNeedKillMainProcessInBg = MmkvCommonUtil
                .getInstance(context).getBoolean(sNeedKillMainProcessInBgKey,false);
        Logger.e("cf_test","mIsNeedKillMainProcessInBg:_" + mIsNeedKillMainProcessInBg);
        if (!mIsAutoOptOpen) {
            ScreenStatusReceiver.init(context);
        }
        ScreenStatusReceiver.addScreenStatusListener(mIScreenStatusListener);
    }

    public boolean isNeedKillMainProcessInBg(Context context){
        return mIsNeedKillMainProcessInBg;
    }
    //注意！！！ 播放进程调用
    public void onKillBySystemWhenPlayingAndroid11(Context context){
        if (!mIsAutoOptOpen) {
            // 自动优化开关没有打开就返回。
            return;
        }
        boolean isOpenLockScreen = MmkvCommonUtil.getInstance(context).getBooleanCompat(KEY_LOCK_SCREEN_OPEN, true);
        boolean isLockScreenCheckBoxChecked = MmkvCommonUtil.getInstance(context).getBooleanCompat(KEY_LOCK_SCREEN_CHECKBOX_CHECKED, true);
        if (isLockScreenCheckBoxChecked && isOpenLockScreen) {
            return;
        }
        if (mIsNeedKillMainProcessInBg) {
            return;
        }
        if (ProcessUtil.isMainProcess(context)) {
            return;
        }
        mIsNeedKillMainProcessInBg = MmkvCommonUtil
                        .getInstance(context).getBoolean(sNeedKillMainProcessInBgKey,false);
        if (mIsNeedKillMainProcessInBg) {
            return;
        }
        long lastInitTime = MmkvCommonUtil.getInstance(context).getLong(sLastInitTimeKey, 0);
        if (lastInitTime == 0 && (System.currentTimeMillis() - lastInitTime > 24 * 60 * 60 * 1000)) {
            MmkvCommonUtil.getInstance(context).saveLong(sLastInitTimeKey, System.currentTimeMillis());
            Logger.e("cf_test","onKillBySystemWhenPlayingAndroid11:_" + 1);
            MmkvCommonUtil.getInstance(context).saveInt(sKillTotalTimeKey, 1);
            return;
        }
        int hadKillTime = MmkvCommonUtil.getInstance(context).getInt(sKillTotalTimeKey, 0) + 1;
        Logger.e("cf_test","onKillBySystemWhenPlayingAndroid11:_" + hadKillTime);
        mMaxKillTimeInOnDay = MmkvCommonUtil.getInstance(context).getInt(BgPlayOptManager.sMaxKillTimeKey, 1);
        if (hadKillTime > mMaxKillTimeInOnDay) {
            mIsNeedKillMainProcessInBg = true;
            Logger.e("cf_test","mIsNeedKillMainProcessInBg:_true");
            MmkvCommonUtil.getInstance(context).saveBoolean(sNeedKillMainProcessInBgKey, true);
        }
        MmkvCommonUtil.getInstance(context).saveInt(sKillTotalTimeKey, hadKillTime);
    }

    private Runnable mKillMainProcess = new Runnable() {
        @Override
        public void run() {
            Logger.e("cf_test","mKillMainProcess.run__isMainProcess_" + ProcessUtil.isMainProcess(mContext));
            if (!ProcessUtil.isMainProcess(mContext)){
                return;
            }
            if (ProcessUtil.isAppForeground(mContext)) {
                return;
            }
            if (isPlanTerminate()) {
                return;
            }
            Process.killProcess(Process.myPid());
        }
    };

    private boolean isPlanTerminate() {
        boolean isPlanTermin = false;
        try {
            Class planTerminateManagerClass = Class.forName("com.ximalaya.ting.android.host.manager.PlanTerminateManager");
            Method getInstanceMethod = planTerminateManagerClass.getMethod("getInstance");
            Object planTerminateManager = getInstanceMethod.invoke(null);
            Method isTimingMethod = planTerminateManagerClass.getMethod("isTiming");
            isPlanTermin = (boolean)(isTimingMethod.invoke(planTerminateManager));
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        Logger.e("cf_test","isPlanTerminate_" + isPlanTermin);
        return isPlanTermin;
    }

    private ScreenStatusReceiver.IScreenStatusListener mIScreenStatusListener = new ScreenStatusReceiver.IScreenStatusListener() {
        @Override
        public void onScreenStatusChange(int screenStatus) {
            isPlanTerminate();
            mIsForceStopMainProcessInBg = MmkvCommonUtil.getInstance(mContext).getBoolean(sForceStopMainProcessInBgKey, false);
            if ((mIsNeedKillMainProcessInBg && mIsAutoOptOpen) || mIsForceStopMainProcessInBg) {
                if (screenStatus == ScreenStatusReceiver.IScreenStatusListener.SCREEN_OFF) {
                    if (mContext == null || !ProcessUtil.isMainProcess(mContext) || ProcessUtil.isAppForeground(mContext)) {
                        return;
                    }
                    if (!XmPlayerManager.getInstance(mContext).isPlaying()) {
                        return;
                    }
                    if (mDelayExitTime == 0) {
                        mDelayExitTime = MmkvCommonUtil.getInstance(mContext)
                                .getInt(sMainProcessExitTimeKey, 0);
                    }
                    XmAppHelper.removeTaskInOnWorkThread(mKillMainProcess);
                    XmAppHelper.runOnOnWorkThreadDelayed(mKillMainProcess, mDelayExitTime == 0 ? 30 * 1000 : mDelayExitTime * 1000);
                } else {
                    XmAppHelper.removeTaskInOnWorkThread(mKillMainProcess);
                }
            }


        }
    };

}
