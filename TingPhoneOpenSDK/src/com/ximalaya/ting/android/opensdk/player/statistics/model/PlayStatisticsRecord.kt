package com.ximalaya.ting.android.opensdk.player.statistics.model

import android.net.Uri
import android.text.TextUtils
import androidx.annotation.Keep
import com.ximalaya.ting.android.encryptservice.DeviceTokenUtil
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.model.advertis.GasPointInfoList
import com.ximalaya.ting.android.opensdk.model.history.XmPlayRecord
import com.ximalaya.ting.android.opensdk.model.statistic.RecordModel
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.manager.TrackUrlChooseManager
import com.ximalaya.ting.android.opensdk.player.statistic.PlayIdManager
import com.ximalaya.ting.android.opensdk.player.statistics.event.STATUS_NONE
import com.ximalaya.ting.android.opensdk.player.statistics.event.STATUS_STARTED
import com.ximalaya.ting.android.opensdk.player.statistics.manager.XmPlayStatisticsManager
import com.ximalaya.ting.android.opensdk.player.ubt.TrackUbtSourceManager
import com.ximalaya.ting.android.opensdk.util.DeviceUtil
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONArray
import org.json.JSONObject

/**
 * <AUTHOR>
 * @date 2023/3/7 21:52
 */
private const val TAG = "PlayStatisticsRecord"
private const val TAG_UBT = "PlayStatisticsUbt"

@Keep
class PlayStatisticsRecord {
    var trackId = 0L
    var albumId = 0L
    var trackTitle = ""
    var duration = 0
    var nextType = 0
    var rec_src = ""
    var rec_track = ""
    var playSource = ""
    var playUrl = ""
    var quality = 0
    var connectType = 0
    var connectDevice = 0
    var connectDeviceName = ""
    var permissionSource = ""
    var sceneId = 0L
    var channelId = 0L
    var uploadId = 0L
    var playType = 0
    var ubtSource = ""
    var ubtTraceId = ""
    var ubtPrevTraceId = ""
    var searchId = ""
    var bitrate = 0
    var fileSize = -1L
    var fileType = ""
    var host = ""
    var pauseReason = ""
    var pauseReasonList = ArrayList<Int>()

    var isMiddleAdUrl = false
    var middleAdDuration = 0F // 秒
    var gasPointInfoList: MutableList<GasPointInfoList>? = null

    // 中插广告播放时间
    var adPlayedDuration = 0L // ms
    var adListenedDuration = 0F // ms

    // 自定义字段
    var validPlay = false
    var completePlay = false

    // 当前开始播放的时长
    var playedDuration = 0L
    var listenedDuration = 0F

    // 在整个进程生命周期的播放时长
    var totalPlayedDuration = 0L
    var totalListenedDuration = 0F

    // 前面进程的播放时长
    var lastPlayedDuration = 0L
    var lastListenedDuration = 0F
    var lastStatus = STATUS_NONE
    var curRecordUploaded = false

    var head = 0
    var headSkip = false
    var tail = 0
    var tailSkip = false

    var startTime = 0
        private set
    var endTime = 0
        private set
    var startSysTime = System.currentTimeMillis()
    var endSysTime = 0L

    var uuid: String = ""

    var downloadType = 0

    var addSource = -1  // 默认为 -1，当添加到待播列表非推荐产生的播放为 1，待播列表推荐产生的为 2

    var isPaidTrack = false // 是否付费声音
    var isPaidAlbum = false // 是否付费专辑
    var offlineVisibleType = -1 // 可见类型（下架之后才有的）visiableType = 0 完全下架
    var permissionExpireTime: Long = 0 // 权益过期时间戳
    var albumStatus = 0 // 专辑审核状态
    var albumOfflineType = -1 //专辑下架类型
    var trackStatus = 0 // 审核状态
    var isFree = false // 是否免费听
    var ximiFirstStatus: Int = -1
    var vipFirstStatus: Int = 0
    var aiSectionId: Long = 0
    var ubtV2: Map<String, String>? = null

    constructor()
    constructor(curModel: Track, online: Boolean, nextType: Int, playUrl: String?, recordModel: RecordModel?, bitrate: Int, contentLen: Long) {
        lastStatus = STATUS_STARTED

        trackId = curModel.dataId
        albumId = curModel.album?.albumId ?: 0
        trackTitle = curModel.trackTitle ?: ""
        duration = curModel.duration
        this.bitrate = bitrate
        var xuid = DeviceUtil.deviceCallback?.getXuid() ?: ""
        if (xuid == null || xuid.length <= 0) {
            xuid = DeviceTokenUtil.getDeviceToken(XmPlayStatisticsManager.getInstance().mContext)
        }
        if (xuid == null || xuid.length <= 0) {
            xuid = DeviceTokenUtil.getOldDeviceToken(XmPlayStatisticsManager.getInstance().mContext)
        }
        uuid = "$xuid-${PlayIdManager.generatePlayId()}"
        rec_src = curModel.recSrc ?: ""
        rec_track = curModel.recTrack ?: ""

        // 一键听上传播放统计时 playSource变为5001 废弃掉之前的31
        var playSource = curModel.playSource
        if (playSource == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY || playSource == ConstantsOpenSdk.PLAY_FROM_DAILYNEWS4) {
            playSource = ConstantsOpenSdk.PLAY_FROM_ONE_KEY_STATISTICS
        }
        this.playSource = "$playSource"
        this.nextType = nextType
        this.playUrl = playUrl ?: ""
        this.downloadType =
            if (playUrl == null) 0 else if (playUrl.startsWith("http://") || playUrl.startsWith("https://")) 0 else 1

        if (playUrl == null) {
            this.fileType = "null"
            this.host = "null"
        } else {
            try {
                val uri = Uri.parse(playUrl)
                this.host = uri.host ?: ""
                uri.path?.let { path ->
                    val index = path.lastIndexOf(".")
                    this.fileType = path.substring(index + 1)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        this.fileSize = contentLen

        this.quality = if (curModel.trackCurPlayQualityLevel >= 0)
            curModel.trackCurPlayQualityLevel
        else TrackUrlChooseManager.getInstance().getRealTrackQuality(curModel)

        connectType = recordModel?.type ?: 0
        connectDevice = recordModel?.device ?: 0
        connectDeviceName = recordModel?.deviceName ?: ""
        this.permissionSource = curModel.permissionSource ?: ""

        sceneId = curModel.channelGroupId
        channelId = curModel.channelId

        // 找到当前播放音色所对应的uploadId
        if (!TextUtils.isEmpty(curModel.curTtsTrackTimbreType) && curModel.playUrlInfoList != null && curModel.playUrlInfoList.size > 0) {
            for (playUrlInfo in curModel.playUrlInfoList) {
                if (TextUtils.equals(playUrlInfo.type, curModel.curTtsTrackTimbreType)) {
                    uploadId = playUrlInfo.uploadId
                }
            }
        }

        // url匹配 是否是中插广告
        curModel.middleInsertAdInfo?.let {
            val item = it.isUrlInMiddleAdUrls(playUrl)
            if (item != null) {
                this.isMiddleAdUrl = true
                this.duration = Math.max(item.duration, this.duration)
            } else {
                this.isMiddleAdUrl = false
            }
            this.middleAdDuration = 0F

            if (this.isMiddleAdUrl) {
                val list = ArrayList<GasPointInfoList>()
                this.gasPointInfoList = list

                it.gasPointInfoList?.forEach { item ->
                    item?.let { point ->
                        if (point.startMills >= 0 && point.stopMills > point.startMills) {
                            list.add(point)
                            this.middleAdDuration += Math.abs(point.stopMills - point.startMills) / 1000.0f
                        }
                    }
                }
            }
        }

        playType = if (online) XmPlayRecord.PLAY_TYPE_ONLINE else XmPlayRecord.PLAY_TYPE_LOCAL

        if (curModel.isFromToListenTrack == 1 && !curModel.isToListenRecommend) {
            addSource = 1
        } else if (curModel.isToListenRecommend) {
            addSource = 2
        }

        isPaidTrack = curModel.isPaid
        offlineVisibleType = curModel.offlineVisibleType
        permissionExpireTime = curModel.permissionExpireTime
        trackStatus = curModel.trackStatus
        isFree = curModel.isFree
        curModel.album?.let {
            isPaidAlbum = it.isPaidAlbum
            albumStatus = it.status
            albumOfflineType = it.offlineType
        }
        vipFirstStatus = curModel.vipPriorListenStatus
        ximiFirstStatus = curModel.ximiFirstStatus
        if (curModel.isKindOfChatXmly) {
            aiSectionId = curModel.ttsId
        }

        updateUbtSource(1)
        if (curModel.ubtV2 != null && curModel.ubtV2.size > 0) {
            ubtV2 = curModel.ubtV2
        }
    }

    fun updateUbtSource(from: Int) {
        try {
            val ubtSourceStr = TrackUbtSourceManager.getUbtSourceString("$trackId") ?: return
            val ubtTraceMap = TrackUbtSourceManager.parseUbtSource(ubtSourceStr) as? Map<String, String?>
            ubtSource = ubtTraceMap?.get("ubtSource") ?: ""
            ubtTraceId = ubtTraceMap?.get("ubtTraceId") ?: ""
            ubtPrevTraceId = ubtTraceMap?.get("ubtPrevTraceId") ?: ""
            searchId = getSearchId(ubtSourceStr, trackId)
            Logger.d(TAG_UBT, "from=${from}, trackId=${trackId}, ubtTraceId=$ubtTraceId, ubtPrevTraceId=$ubtPrevTraceId, ubtSourceStr=$ubtSource, searchId=$searchId")
        } catch (e: Exception) {
            e.printStackTrace()
            Logger.e(TAG_UBT, "exception", e)
        }
    }

    fun clearPauseReason() {
        pauseReasonList.clear()
        pauseReason = ""
    }

    fun addPauseReason(reason: Int) {
        pauseReasonList.add(reason)
        pauseReason = if (pauseReasonList.size > 0) {
            try {
                val size = pauseReasonList.size
                with(StringBuilder()) {
                    pauseReasonList.forEachIndexed { index, item ->
                        if (index >= size - 3) {
                            append(item)
                            if (index < size - 1) {
                                append("_")
                            }
                        }
                    }
                    toString()
                }
            } catch (e: Exception) {
                e.printStackTrace()
                "error"
            }
        } else {
            ""
        }
    }

    constructor(xmPlayRecord: XmPlayRecord?) {
        lastStatus = STATUS_STARTED

        trackId = xmPlayRecord?.id ?: 0L
        albumId = xmPlayRecord?.albumId ?: 0L
        trackTitle = ""
//        duration = xmPlayRecord?.duration ?: 0
        var xuid = DeviceUtil.deviceCallback?.getXuid() ?: ""
        if (xuid == null || xuid.length <= 0) {
            xuid = DeviceTokenUtil.getDeviceToken(XmPlayStatisticsManager.getInstance().mContext)
        }
        if (xuid == null || xuid.length <= 0) {
            xuid = DeviceTokenUtil.getOldDeviceToken(XmPlayStatisticsManager.getInstance().mContext)
        }
        uuid = "$xuid-${PlayIdManager.generatePlayId()}"
        this.nextType = xmPlayRecord?.nextType ?: 0
        rec_src = xmPlayRecord?.recSrc ?: ""
        rec_track = xmPlayRecord?.recTrack ?: ""

        // 一键听上传播放统计时 playSource变为5001 废弃掉之前的31
        var playSource = xmPlayRecord?.playSource
        if (playSource == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY || playSource == ConstantsOpenSdk.PLAY_FROM_DAILYNEWS4) {
            playSource = ConstantsOpenSdk.PLAY_FROM_ONE_KEY_STATISTICS
        }
        this.playSource = "$playSource"

        this.playUrl = xmPlayRecord?.playUrl ?: ""
        this.quality = xmPlayRecord?.toneQuality ?: 0

        connectType = 0
        connectDevice = 0
        connectDeviceName = ""
        this.permissionSource = xmPlayRecord?.permissionSource ?: ""

        sceneId = xmPlayRecord?.channelGroupId ?: 0
        channelId = xmPlayRecord?.oneKeyChannelId ?: 0

        playType = XmPlayRecord.PLAY_TYPE_ONLINE
    }

    constructor(map: Map<String, Long>) {
        lastStatus = STATUS_STARTED

        var xuid = DeviceUtil.deviceCallback?.getXuid() ?: ""
        if (xuid == null || xuid.length <= 0) {
            xuid = DeviceTokenUtil.getDeviceToken(XmPlayStatisticsManager.getInstance().mContext)
        }
        if (xuid == null || xuid.length <= 0) {
            xuid = DeviceTokenUtil.getOldDeviceToken(XmPlayStatisticsManager.getInstance().mContext)
        }
        uuid = "$xuid-${PlayIdManager.generatePlayId()}"

        trackId = map["trackId"] ?: 0L
        albumId = map["albumId"] ?: 0L
        playSource = "3002"
        downloadType = 0

        try {
            val ubtSourceStr = TrackUbtSourceManager.getUbtSource() ?: return
            val ubtTraceMap = TrackUbtSourceManager.parseUbtSource(ubtSourceStr) as? Map<String, String?>
            ubtSource = ubtTraceMap?.get("ubtSource") ?: ""
            ubtTraceId = ubtTraceMap?.get("ubtTraceId") ?: ""
            ubtPrevTraceId = ubtTraceMap?.get("ubtPrevTraceId") ?: ""
            Logger.d(TAG_UBT, "mix ubtSource=$ubtSource")
        } catch (e: Exception) {
            e.printStackTrace()
            Logger.e(TAG_UBT, "mix exception", e)
        }
    }

    fun updateStartTime(startTime: Int) {
        this.startTime = startTime
        this.startSysTime = System.currentTimeMillis()
    }

    fun updateEndTime(endTime: Int) {
        this.endTime = endTime
        this.endSysTime = System.currentTimeMillis()
    }

    fun resetDuration() {
        this.lastPlayedDuration += this.playedDuration + this.totalPlayedDuration
        this.lastListenedDuration += this.listenedDuration + this.totalListenedDuration
        this.playedDuration = 0
        this.listenedDuration = 0F
        this.totalPlayedDuration = 0
        this.totalListenedDuration = 0F
    }

    private fun getSearchId(ubtSourceStr: String, trackId: Long): String {
        try {
            val ubtJsonObject = JSONObject(ubtSourceStr)
            if (ubtJsonObject.has("ubtSource")) {
                val jsonArray = JSONArray(ubtJsonObject.optString("ubtSource"))
                if (jsonArray.length() > 0) {
                    for (i in 0 until jsonArray.length()) {
                        val jsonObject: JSONObject = jsonArray.getJSONObject(i)
                        if (jsonObject.has("props")) {
                            val props = jsonObject.optJSONObject("props")
                            if (props != null && props.has("strategy")) {
                                val strategy = JSONObject(props.optString("strategy"))
                                if (strategy.has("searchId")) {
                                    return strategy.optString("searchId")
                                }
                            }
                        }
                    }
                }
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return ""
    }

    fun updateUuid() {
        var xuid = DeviceUtil.deviceCallback?.getXuid() ?: ""
        if (xuid == null || xuid.length <= 0) {
            xuid = DeviceTokenUtil.getDeviceToken(XmPlayStatisticsManager.getInstance().mContext)
        }
        if (xuid == null || xuid.length <= 0) {
            xuid = DeviceTokenUtil.getOldDeviceToken(XmPlayStatisticsManager.getInstance().mContext)
        }
        uuid = "$xuid-${PlayIdManager.generatePlayId()}"
    }

    fun clearDuration() {
        this.lastPlayedDuration = 0
        this.lastListenedDuration = 0F
        this.playedDuration = 0
        this.listenedDuration = 0F
        this.totalPlayedDuration = 0
        this.totalListenedDuration = 0F
    }

    override fun toString(): String {
        return "PlayStatisticsRecord(trackId=$trackId, albumId=$albumId, trackTitle='$trackTitle', duration=$duration, nextType=$nextType, rec_src='$rec_src', rec_track='$rec_track', playSource='$playSource', playUrl='$playUrl', quality=$quality, connectType=$connectType, connectDevice=$connectDevice, connectDeviceName='$connectDeviceName', permissionSource='$permissionSource', sceneId=$sceneId, channelId=$channelId, uploadId=$uploadId, playType=$playType, ubtSource='$ubtSource', ubtTraceId='$ubtTraceId', ubtPrevTraceId='$ubtPrevTraceId', searchId='$searchId', validPlay=$validPlay, completePlay=$completePlay, playedDuration=$playedDuration, listenedDuration=$listenedDuration, totalPlayedDuration=$totalPlayedDuration, totalListenedDuration=$totalListenedDuration, lastPlayedDuration=$lastPlayedDuration, lastListenedDuration=$lastListenedDuration, lastStatus=$lastStatus, curRecordUploaded=$curRecordUploaded, head=$head, headSkip=$headSkip, tail=$tail, tailSkip=$tailSkip, startTime=$startTime, endTime=$endTime, startSysTime=$startSysTime, endSysTime=$endSysTime, uuid='$uuid', downloadType=$downloadType, isPaidTrack=$isPaidTrack, isPaidAlbum=$isPaidAlbum, offlineVisibleType=$offlineVisibleType, permissionExpireTime=$permissionExpireTime, albumStatus=$albumStatus, albumOfflineType=$albumOfflineType, trackStatus=$trackStatus, isFree=$isFree, aiSectionId=$aiSectionId)"
    }
}