package com.ximalaya.ting.android.opensdk.player.ubt;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON> on 2021/12/31 6:11 下午.
 *
 * <AUTHOR>
 */

public class TraceModel implements Parcelable {
    public int metaId;
    public String serviceId;
    public Map<String, String> props;

    public TraceModel() {

    }

    public TraceModel(Parcel in) {
        this.metaId = in.readInt();
        this.serviceId = in.readString();
        this.props = in.readHashMap(HashMap.class.getClassLoader());
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(metaId);
        dest.writeString(serviceId);
        dest.writeMap(props);
    }

    public static final Creator<TraceModel> CREATOR = new Creator<TraceModel>() {
        public TraceModel createFromParcel(Parcel in) {
            return new TraceModel(in);
        }

        public TraceModel[] newArray(int size) {
            return new TraceModel[size];
        }
    };
}
