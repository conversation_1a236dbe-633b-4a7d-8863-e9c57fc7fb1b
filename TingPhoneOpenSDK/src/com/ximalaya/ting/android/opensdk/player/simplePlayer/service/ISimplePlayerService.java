package com.ximalaya.ting.android.opensdk.player.simplePlayer.service;

import android.os.Bundle;

import com.ximalaya.ting.android.opensdk.player.simplePlayer.base.IMediaPlayer;

/**
 * Created by hebin on 2019/3/19.
 */
public interface ISimplePlayerService extends ISimplePlayerStatusListener{

    public static final int STATE_INIT = 0;
    public static final int STATE_PREPARING = 1;
    public static final int STATE_PREPARED = 2;
    public static final int STATE_PLAYING = 3;
    public static final int STATE_BUFFERING = 4;
    public static final int STATE_BUFFERED = 5;
    public static final int STATE_PAUSING = 6;
    public static final int STATE_STOP = 7;
    public static final int STATE_PLAY_COMPLETE = 8;
    public static final int STATE_SEEKING = 9;
    public static final int STATE_SEEKED_COMPLETE = 10;
    public static final int STATE_ERROR = 11;


    IMediaPlayer getMediaPlayer();

    void onCreateService();

    int getBufferedPercent();

    long getCurrentPosition();

    String getDataSource();

    long getDuration();

    int getMediaPlayerState();

    int getStartPosition();

    float getVolume();

    boolean isPlaying();

    void pause();

    void reset();

    void setProgressFrequency(int frequency);

    void seekTo(int i);

    void setDataSource(String str);

    void setDataSource(String str, Bundle bundle);

    void setLooping(boolean z);

    boolean isLooping();

    void setSpeed(float speed);

    void setStartPosition(int i);

    void setVolume(float f);

    void setVolume(float l, float r);

    void start();

    void stop();

    void addPlayerStatusListener(ISimplePlayerStatusListener listener);

    void removePlayerStatusListener(ISimplePlayerStatusListener listener);

    boolean onInfo(IMediaPlayer iMediaPlayer, int i, int i2);


}
