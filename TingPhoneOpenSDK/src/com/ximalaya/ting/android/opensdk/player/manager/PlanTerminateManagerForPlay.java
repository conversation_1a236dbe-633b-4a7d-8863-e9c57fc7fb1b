package com.ximalaya.ting.android.opensdk.player.manager;

import android.content.Intent;
import android.os.RemoteException;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlanTerminateListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.Arrays;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * 定时关闭管理器
 *
 * <AUTHOR>
 */
public class PlanTerminateManagerForPlay implements IXmPlayerStatusListener {
    public static final int MODE_NORMAL = 0;
    public static final int MODE_SLEEPY = 1;

    private static final String TAG = "PlanTerminateManager";
    public static final int TIMER_CONTINUE_PLAYING = -3;
    public static final int TIMER_CUSTOM = -2;
    public static final int TIMER_NONE = -1;
    public static final int TIMER_CURRENT = 1;
    public static final int TIMER_NEXT = 2;
    public static final int TIMER_NEXT_2 = 3;
    public static final int TIMER_NEXT_3 = 4; // 哄睡模式新增类型
    public static final int TIMER_NEXT_4 = 6; // 哄睡模式新增类型
    public static final int TIMER_NEXT_10 = 8;
    public static final int TIMER_10_MIN = 10;
    public static final int TIMER_15_MIN = 15;
    public static final int TIMER_20_MIN = 20;
    public static final int TIMER_30_MIN = 30;
    public static final int TIMER_45_MIN = 45;
    public static final int TIMER_60_MIN = 60;
    public static final int TIMER_90_MIN = 90;
    public static final int TIMER_STOP = 0;

    private ScheduledThreadPoolExecutor mService;
    private ScheduledFuture<?> mTask;
    // 最多9集后关闭
    // 下集时间 下下集时间
    private final long[] mSeriesTime = new long[9];
    private long mLeftTime;
    private int mLeftMin;
    private long mPlanStopTimeStamp;
    private int mMode = MODE_NORMAL;
    // 表示的要延时的时间+当前时间的时间戳
    private long mDstTime;
    private int mLeftSeries;
    private int mType = TIMER_NONE;

    // 上一次设置的type
    private int mLastType = TIMER_NONE;
    // 只在type为TIMER_CUSTOM有效，记录自定义时间，单位min
    private int mCustomTime = -1;
    private int mPlayTimes = 0; // 定时开启的次数
    private boolean mShouldContinuePlayAfterTimeout;
    private boolean mPlayCompleteWithTimeMode;
    private IXmPlanTerminateListener mPlanTerminateListener;

    public void setPlanTerminateListener(IXmPlanTerminateListener planTerminateListener) {
        mPlanTerminateListener = planTerminateListener;
    }

    // 获取当前类型继续播放 提示内容
    public String getContinueStrForPlay() {
        String tip = "";
        if (mLastType > TIMER_NEXT_10) {
            tip = mLastType + "分钟";
        } else if (mLastType == TIMER_CUSTOM) {
            tip = mCustomTime + "分钟";
        } else if (mLastType == TIMER_CURRENT
                || mLastType == TIMER_NEXT
                || mLastType == TIMER_NEXT_2
                || mLastType == TIMER_NEXT_3
                || mLastType == TIMER_NEXT_4
                || mLastType == TIMER_NEXT_10) {
            tip = mLastType + "首";
        }
        return tip;
    }

    // 获取上次设置的定时参数
    public int[] getLastPlanTerminateTypeForPlay() {
        // 校验是不是有效的type
        if (mLastType >= TIMER_CURRENT || (mLastType == TIMER_CUSTOM && mCustomTime > 0)) {
            return new int[]{mLastType, mCustomTime};
        }
        return null;
    }

    @Override
    public void onPlayStart() {
        if (isTimerStoppedForPlay()) {
            resetForPlay();
        }
    }

    @Override
    public void onPlayPause() {
    }

    @Override
    public void onPlayStop() {

    }

    @Override
    public void onSoundPlayComplete() {
        if (isTimingForPlay()) {
            countDownForPlay();
        }
    }

    public void beforeSoundPlayComplete() {
        if (isTimingForPlay() && mShouldContinuePlayAfterTimeout && XmPlayerService.getPlayerSrvice() != null) {
            XmPlayerService.getPlayerSrvice().pausePlayInMillis(XmPlayerService.PLAN_NORMAL);
            Logger.i(TAG, "beforeSoundPlayComplete");
        }
    }

    @Override
    public void onSoundPrepared() {

    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
        computeLeftTime(curModel);
    }

    public void updateTime() {
        PlayableModel playableModel = XmPlayerService.getPlayerSrvice().getCurrPlayModel();
        computeLeftTime(playableModel);
    }

    private void computeLeftTime(PlayableModel curModel) {
        setPauseOnCompleteForSleepyModeIfNeeded(curModel, mLeftTime);
        if (mType >= TIMER_CURRENT && mType <= TIMER_NEXT_10 && XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().getTimeHander() != null) {
            XmPlayerService.getPlayerSrvice().getTimeHander().postDelayed(() -> {
                setSeriesTimeout(mLeftSeries);
            }, 1000);
        }
    }

    private boolean setPauseOnCompleteForSleepyModeIfNeeded(PlayableModel curModel, long time) {
        mShouldContinuePlayAfterTimeout = false;
        if (mMode == MODE_SLEEPY && curModel instanceof Track && XmPlayerService.getPlayerSrvice() != null) {
            if (mType > TIMER_NEXT_10 || mType == TIMER_CUSTOM) {
                // 哄睡模式 定时关闭 如果播放到某一首歌剩余倒计时不足 则设置为本首歌结束时停止播放
                long duration = ((Track) curModel).getDuration() * 1000;

                long left = duration - XmPlayerService.getPlayerSrvice().getPlayCurrPosition();
                if (time < left) {
                    XmPlayerService.getPlayerSrvice().getTimeHander().postDelayed(() -> {
                        Logger.i(TAG, "哄睡模式：剩余时长不足本集时长，设置为本集后停止播放");
                        XmPlayerService.getPlayerSrvice().pausePlayInMillis(XmPlayerService.PLAN_PAUSE_ON_COMPLETE);
                        mShouldContinuePlayAfterTimeout = true;
                    }, 1000);
                    return true;
                }
            }
        } else if (mPlayCompleteWithTimeMode && (mType > TIMER_NEXT_10 || mType == TIMER_CUSTOM) &&
                curModel instanceof Track && XmPlayerService.getPlayerSrvice() != null) {
            // 特定开关打开后 如果播放到某一首歌剩余倒计时不足 则设置为本首歌结束时停止播放
            long duration = ((Track) curModel).getDuration() * 1000;
            long left = duration - XmPlayerService.getPlayerSrvice().getPlayCurrPosition();
            if (time < left) {
                XmPlayerService.getPlayerSrvice().getTimeHander().postDelayed(() -> {
                    Logger.i(TAG, "mPlayCompleteWithTimeMode：剩余时长不足本集时长，设置为本集后停止播放");
                    XmPlayerService.getPlayerSrvice().pausePlayInMillis(XmPlayerService.PLAN_PAUSE_ON_COMPLETE);
                    mShouldContinuePlayAfterTimeout = true;
                }, 1000);
                return true;
            }
        }
        return false;
    }

    @Override
    public void onBufferingStart() {

    }

    @Override
    public void onBufferingStop() {

    }

    @Override
    public void onBufferProgress(int percent) {

    }

    @Override
    public void onPlayProgress(int currPos, int duration) {
        if (!isTimingForPlay()) {
            // 没有在定时的时候不用回调，节省资源
            return;
        }
        if (mType == TIMER_CONTINUE_PLAYING && (duration - currPos) < 1000) {
            mType = TIMER_STOP;
        }
        if (mLeftSeries == 0) {
            return;
        }
        mLeftTime = generateSeriesTime(currPos, duration);
        notifyOnLeftTimeChanged((int) (mLeftTime / 1000));
    }

    private int generateSeriesTime(int currPos, int duration) {
        int time = duration - currPos;
        if (mLeftSeries == 10) {
            time += mSeriesTime[8] + mSeriesTime[7] + mSeriesTime[6] + mSeriesTime[5] + mSeriesTime[4] + mSeriesTime[3] + mSeriesTime[2] + mSeriesTime[1] + mSeriesTime[0];
        } else if (mLeftSeries == 9) {
            time += mSeriesTime[7] + mSeriesTime[6] + mSeriesTime[5] + mSeriesTime[4] + mSeriesTime[3] + mSeriesTime[2] + mSeriesTime[1] + mSeriesTime[0];
        } else if (mLeftSeries == 8) {
            time += mSeriesTime[6] + mSeriesTime[5] + mSeriesTime[4] + mSeriesTime[3] + mSeriesTime[2] + mSeriesTime[1] + mSeriesTime[0];
        } else if (mLeftSeries == 7) {
            time += mSeriesTime[5] + mSeriesTime[4] + mSeriesTime[3] + mSeriesTime[2] + mSeriesTime[1] + mSeriesTime[0];
        } else if (mLeftSeries == 6) {
            time += mSeriesTime[4] + mSeriesTime[3] + mSeriesTime[2] + mSeriesTime[1] + mSeriesTime[0];
        } else if (mLeftSeries == 5) {
            time += mSeriesTime[3] + mSeriesTime[2] + mSeriesTime[1] + mSeriesTime[0];
        } else if (mLeftSeries == 4) {
            time += mSeriesTime[2] + mSeriesTime[1] + mSeriesTime[0];
        } else if (mLeftSeries == 3) {
            time += mSeriesTime[1] + mSeriesTime[0];
        } else if (mLeftSeries == 2) {
            time += mSeriesTime[0];
        }
        return time;
    }

    @Override
    public boolean onError(XmPlayerException exception) {
        return false;
    }

    public void setModeForPlay(int mode) {
        mMode = mode;
    }

    private static class Holder {
        static PlanTerminateManagerForPlay sManager = new PlanTerminateManagerForPlay();
    }

    public static PlanTerminateManagerForPlay getInstance() {
        return PlanTerminateManagerForPlay.Holder.sManager;
    }

    private PlanTerminateManagerForPlay() {
        XmPlayerService.addPlayerStatusListenerOnPlayProcees(this);
    }

    private void initService() {
        if (mService == null || mService.isShutdown()) {
            mService = new ScheduledThreadPoolExecutor(1, new ThreadFactory() {
                @Override
                public Thread newThread(@NonNull Runnable r) {
                    return new Thread(r, "PlayFragmentManageTimerThread");
                }
            });
        }
    }

    /**
     * 开始定时
     *
     * @param type 定时类型
     */
    public void startTimerForPlay(int type) {
        cancelPlan(false, false);
        mLastType = type;
        setType(type);
        mPlayTimes = 1;
        switch (type) {
            case TIMER_CURRENT:
                setSeriesTimeout(1);
                break;
            case TIMER_NEXT:
                setSeriesTimeout(2);
                break;
            case TIMER_NEXT_2:
                setSeriesTimeout(3);
                break;
            case TIMER_NEXT_3:
                setSeriesTimeout(4);
                break;
            case TIMER_NEXT_4:
                setSeriesTimeout(5);
                break;
            case TIMER_NEXT_10:
                setSeriesTimeout(10);
                break;
            case TIMER_10_MIN:
                setTimeout(10 * 60 * 1000L);
                break;
            case TIMER_15_MIN:
                setTimeout(15 * 60 * 1000L);
                break;
            case TIMER_20_MIN:
                setTimeout(20 * 60 * 1000L);
                break;
            case TIMER_30_MIN:
                setTimeout(30 * 60 * 1000L);
                break;
            case TIMER_45_MIN:
                setTimeout(45 * 60 * 1000L);
                break;
            case TIMER_60_MIN:
                setTimeout(60 * 60 * 1000L);
                break;
            case TIMER_90_MIN:
                setTimeout(90 * 60 * 1000L);
                break;
            case TIMER_NONE:
            default:
                notifyOnTimeout();
        }
    }

    public void startCustomTimerForPlay(long time) {
        cancelPlan(false, false);
        setType(TIMER_CUSTOM);
        mLastType = TIMER_CUSTOM;
        mCustomTime = (int) (time / 60 / 1000);

        setTimeout(time);
        mPlayTimes = 1;
    }

    public int getPlayTimesForPlay() {
        return mPlayTimes;
    }

    /**
     * 已播放完一集
     * onSoundSwitch中调用
     */
    public void countDownForPlay() {
        if (mLeftSeries == 0) {
            return;
        }
        mLeftSeries--;
        if (mLeftSeries == 0) {
            setType(TIMER_STOP);
            notifyOnTimeout();
        } else {
            mSeriesTime[0] = mSeriesTime[1];
            mSeriesTime[1] = mSeriesTime[2];
            mSeriesTime[2] = mSeriesTime[3];
            mSeriesTime[3] = mSeriesTime[4];
            mSeriesTime[4] = mSeriesTime[5];
            mSeriesTime[5] = mSeriesTime[6];
            mSeriesTime[6] = mSeriesTime[7];
            mSeriesTime[7] = mSeriesTime[8];
            notifyOnLeftSeriesChanged(mLeftSeries);
        }
    }

    /**
     * 是否正在定时
     */
    public boolean isTimingForPlay() {
        return mType != TIMER_NONE && mType != TIMER_STOP;
    }

    public boolean isTimerStoppedForPlay() {
        return mType == TIMER_STOP;
    }

    public boolean isTimerContinuePlayingForPlay() {
        return mType == TIMER_CONTINUE_PLAYING;
    }

    /**
     * 获得当前定时类型
     */
    public int getTimerTypeForPlay() {
        return mType;
    }

    public void resetForPlay() {
        setType(TIMER_NONE);
    }

    public void setPlayCompleteWithTimeMode(boolean playCompleteWithTimeMode) {
        if (isTimingForPlay() && mPlayCompleteWithTimeMode != playCompleteWithTimeMode && !playCompleteWithTimeMode) {
            mShouldContinuePlayAfterTimeout = false;
        }
        if (isTimingForPlay() && mPlayCompleteWithTimeMode != playCompleteWithTimeMode && playCompleteWithTimeMode && XmPlayerService.getPlayerSrvice() != null) {
            mPlayCompleteWithTimeMode = true;
            setPauseOnCompleteForSleepyModeIfNeeded(XmPlayerService.getPlayerSrvice().getCurrPlayModel(), mLeftTime);
        }
        mPlayCompleteWithTimeMode = playCompleteWithTimeMode;
    }

    /**
     * 获得定时剩余时间
     */
    public long getLeftTimeForPlay() {
        return mLeftTime;
    }

    public int getLeftSeriesForPlay() {
        return mLeftSeries;
    }

    public void releaseForPlay() {
        if (mService != null && !mService.isShutdown()) {
            mService.shutdown();
            try {
                mService.awaitTermination(0, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            mService.shutdownNow();
            mService = null;
        }
        XmPlayerService.removePlayerStatusListenerOnPlayProcess(this);
    }

    private void setTimeout(long timeout) {
        Logger.i(TAG, "设置时间定时关闭：" + timeout);
        mPlanStopTimeStamp = 0;
        mDstTime = timeout + System.currentTimeMillis();
        initService();
        mTask = mService.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                if (mDstTime - System.currentTimeMillis() <= 0) {
                    if (mMode == MODE_NORMAL) {
                        if (mShouldContinuePlayAfterTimeout) {
                            setType(TIMER_CONTINUE_PLAYING);
                            mLeftSeries = 0;
                            Arrays.fill(mSeriesTime, 0);
                            if (mTask != null) {
                                mTask.cancel(true);
                                mTask = null;
                            }
                        } else {
                            if (XmPlayerService.getPlayerSrvice() != null) {
                                XmPlayerService.getPlayerSrvice().pausePlay(true, PauseReason.Common.PLAN_TERMINATE_PAUSE);
                            }
                            cancelPlan(true, true);
                        }
                        mShouldContinuePlayAfterTimeout = false;
                        notifyOnTimeout();
                    } else if (mMode == MODE_SLEEPY) {
                        Logger.i(TAG, "睡眠模式取消定时关闭");
                        if (mShouldContinuePlayAfterTimeout) {
                            setType(TIMER_CONTINUE_PLAYING);
                        } else {
                            setType(TIMER_STOP);
                        }
                        mShouldContinuePlayAfterTimeout = false;
                        mLeftSeries = 0;
                        Arrays.fill(mSeriesTime, 0);
                        notifyOnTimeout();
                        if (mTask != null) {
                            mTask.cancel(true);
                            mTask = null;
                        }
                    }
                } else {
                    mLeftTime = mDstTime - System.currentTimeMillis();
                    notifyOnLeftTimeChanged((int) (mLeftTime / 1000));
                }
            }

        }, 0, 1000, TimeUnit.MILLISECONDS);

        if (XmPlayerService.getPlayerSrvice() == null) {
            return;
        }
        boolean hasSetPauseOnComplete = setPauseOnCompleteForSleepyModeIfNeeded(
                XmPlayerService.getPlayerSrvice().getCurrPlayModel(), timeout);
        if (!hasSetPauseOnComplete) {
            XmPlayerService.getPlayerSrvice().pausePlayInMillis(mDstTime);
        }
    }

    /**
     * 哄睡模式新增类型
     */
    private void setSeriesTimeout(int series) {
        if (XmPlayerService.getPlayerSrvice() == null) {
            return;
        }
        XmPlayListControl.PlayMode mode = XmPlayerService.getPlayerSrvice().getPlayMode();
        if (mode == XmPlayListControl.PlayMode.PLAY_MODEL_RANDOM && series != TIMER_CURRENT) {
            return;
        }
        mLeftSeries = series;
        // 可能会修改mLeftSeries
        switch (series) {
            case 1:
                break;
            case 2:
                handleDouble();
                break;
            case 3:
                handleTriple();
                break;
            case 4:
                handleForth();
                break;
            case 5:
                handleFive();
                break;
            case 10:
                handleTen();
                break;
            default:
                return;
        }
        if (mLeftSeries == 0) {
            setType(TIMER_STOP);
        }
        Logger.i(TAG, "设置集数定时关闭：" + mLeftSeries);
        PlayableModel sound = XmPlayerService.getPlayerSrvice().getCurrPlayModel();
        if (sound instanceof Track) {
            int cur = XmPlayerService.getPlayerSrvice().getPlayCurrPosition();
            int duration = XmPlayerService.getPlayerSrvice().getDuration();
            mLeftTime = generateSeriesTime(cur, duration);
            notifyOnLeftTimeChanged((int) (mLeftTime / 1000));
        }
        mPlanStopTimeStamp = 0;
        notifyOnLeftSeriesChanged(mLeftSeries);
    }

    /**
     * 哄睡模式新增类型
     */
    private void handleTen() {
        XmPlayerService xps = XmPlayerService.getPlayerSrvice();
        if (xps == null) {
            return;
        }
        XmPlayListControl.PlayMode mode = xps.getPlayMode();
        int duration = xps.getDuration();
        int index = xps.getCurrIndex();
        int size = xps.getPlayListSize();
        // 10集后自动关闭只出现在哄睡模式 只有列表循环和单曲循环
        switch (mode) {
            case PLAY_MODEL_LIST_LOOP:
                int nextIndex = (index + 1) % size;
                Track nextTrack = xps.getTrack(nextIndex);
                if (nextTrack != null) {
                    mSeriesTime[0] = nextTrack.getDuration() * 1000;
                }
                int next2Index = (index + 2) % size;
                Track next2Track = xps.getTrack(next2Index);
                if (next2Track != null) {
                    mSeriesTime[1] = next2Track.getDuration() * 1000;
                }
                int next3Index = (index + 3) % size;
                Track next3Track = xps.getTrack(next3Index);
                if (next3Track != null) {
                    mSeriesTime[2] = next3Track.getDuration() * 1000;
                }
                int next4Index = (index + 4) % size;
                Track next4Track = xps.getTrack(next4Index);
                if (next4Track != null) {
                    mSeriesTime[3] = next4Track.getDuration() * 1000;
                }
                int next5Index = (index + 5) % size;
                Track next5Track = xps.getTrack(next5Index);
                if (next5Track != null) {
                    mSeriesTime[4] = next5Track.getDuration() * 1000;
                }
                int next6Index = (index + 6) % size;
                Track next6Track = xps.getTrack(next6Index);
                if (next6Track != null) {
                    mSeriesTime[5] = next6Track.getDuration() * 1000;
                }
                int next7Index = (index + 7) % size;
                Track next7Track = xps.getTrack(next7Index);
                if (next7Track != null) {
                    mSeriesTime[6] = next7Track.getDuration() * 1000;
                }
                int next8Index = (index + 8) % size;
                Track next8Track = xps.getTrack(next8Index);
                if (next8Track != null) {
                    mSeriesTime[7] = next8Track.getDuration() * 1000;
                }
                int next9Index = (index + 9) % size;
                Track next9Track = xps.getTrack(next9Index);
                if (next9Track != null) {
                    mSeriesTime[8] = next9Track.getDuration() * 1000;
                }
                break;
            case PLAY_MODEL_SINGLE_LOOP:
                Track currentTrack = xps.getTrack(index);
                if (currentTrack != null) {
                    mSeriesTime[0] = currentTrack.getDuration() * 1000;
                    mSeriesTime[1] = currentTrack.getDuration() * 1000;
                    mSeriesTime[2] = currentTrack.getDuration() * 1000;
                    mSeriesTime[3] = currentTrack.getDuration() * 1000;
                    mSeriesTime[4] = currentTrack.getDuration() * 1000;
                    mSeriesTime[5] = currentTrack.getDuration() * 1000;
                    mSeriesTime[6] = currentTrack.getDuration() * 1000;
                    mSeriesTime[7] = currentTrack.getDuration() * 1000;
                    mSeriesTime[8] = currentTrack.getDuration() * 1000;
                }
                break;
            default:
        }
    }

    private void handleFive() {
        XmPlayerService xps = XmPlayerService.getPlayerSrvice();
        if (xps == null) {
            return;
        }
        XmPlayListControl.PlayMode mode = xps.getPlayMode();
        int duration = xps.getDuration();
        int index = xps.getCurrIndex();
        int size = xps.getPlayListSize();
        // 5集后自动关闭只出现在哄睡模式 只有列表循环和单曲循环
        switch (mode) {
            case PLAY_MODEL_LIST_LOOP:
                int nextIndex = (index + 1) % size;
                Track nextTrack = xps.getTrack(nextIndex);
                if (nextTrack != null) {
                    mSeriesTime[0] = nextTrack.getDuration() * 1000;
                }
                int next2Index = (index + 2) % size;
                Track next2Track = xps.getTrack(next2Index);
                if (next2Track != null) {
                    mSeriesTime[1] = next2Track.getDuration() * 1000;
                }
                int next3Index = (index + 3) % size;
                Track next3Track = xps.getTrack(next3Index);
                if (next3Track != null) {
                    mSeriesTime[2] = next3Track.getDuration() * 1000;
                }
                int next4Index = (index + 4) % size;
                Track next4Track = xps.getTrack(next4Index);
                if (next4Track != null) {
                    mSeriesTime[3] = next4Track.getDuration() * 1000;
                }
                break;
            case PLAY_MODEL_LIST:
                if (size == 0) {
                    mLeftSeries = 0;
                    return;
                }
                if (size == 1) {
                    mLeftSeries = 1;
                    return;
                }
                if (index == size - 1) {
                    // 最后一集
                    mLeftSeries = 1;
                } else if (index == size - 2) {
                    // 倒数第二集
                    mLeftSeries = 2;
                    int nextIndexListMode = index + 1;
                    Track nextTrackListMode = xps.getTrack(nextIndexListMode);
                    if (nextTrackListMode != null) {
                        mSeriesTime[0] = nextTrackListMode.getDuration() * 1000;
                    }
                } else if (index == size - 3) {
                    // 倒数第三集
                    mLeftSeries = 3;
                    int nextIndexListMode = index + 1;
                    Track nextTrackListMode = xps.getTrack(nextIndexListMode);
                    if (nextTrackListMode != null) {
                        mSeriesTime[0] = nextTrackListMode.getDuration() * 1000;
                    }
                    int nextIndexListMode2 = index + 2;
                    Track nextTrackListMode2 = xps.getTrack(nextIndexListMode2);
                    if (nextTrackListMode2 != null) {
                        mSeriesTime[1] = nextTrackListMode2.getDuration() * 1000;
                    }
                } else if (index == size - 4) {
                    // 倒数第四集及以前
                    int nextIndexListMode = index + 1;
                    Track nextTrackListMode = xps.getTrack(nextIndexListMode);
                    if (nextTrackListMode != null) {
                        mSeriesTime[0] = nextTrackListMode.getDuration() * 1000;
                    }
                    int next2IndexListMode = index + 2;
                    Track next2TrackListMode = xps.getTrack(next2IndexListMode);
                    if (next2TrackListMode != null) {
                        mSeriesTime[1] = next2TrackListMode.getDuration() * 1000;
                    }
                    int next3IndexListMode = index + 3;
                    Track next3TrackListMode = xps.getTrack(next3IndexListMode);
                    if (next3TrackListMode != null) {
                        mSeriesTime[2] = next3TrackListMode.getDuration() * 1000;
                    }
                } else {
                    // 倒数第四集及以前
                    int nextIndexListMode = index + 1;
                    Track nextTrackListMode = xps.getTrack(nextIndexListMode);
                    if (nextTrackListMode != null) {
                        mSeriesTime[0] = nextTrackListMode.getDuration() * 1000;
                    }
                    int next2IndexListMode = index + 2;
                    Track next2TrackListMode = xps.getTrack(next2IndexListMode);
                    if (next2TrackListMode != null) {
                        mSeriesTime[1] = next2TrackListMode.getDuration() * 1000;
                    }
                    int next3IndexListMode = index + 3;
                    Track next3TrackListMode = xps.getTrack(next3IndexListMode);
                    if (next3TrackListMode != null) {
                        mSeriesTime[2] = next3TrackListMode.getDuration() * 1000;
                    }
                    int next4IndexListMode = index + 4;
                    Track next4TrackListMode = xps.getTrack(next4IndexListMode);
                    if (next4TrackListMode != null) {
                        mSeriesTime[3] = next4TrackListMode.getDuration() * 1000;
                    }
                }
                break;
            case PLAY_MODEL_SINGLE_LOOP:
                Track currentTrack = xps.getTrack(index);
                if (currentTrack != null) {
                    mSeriesTime[0] = currentTrack.getDuration() * 1000;
                    mSeriesTime[1] = currentTrack.getDuration() * 1000;
                    mSeriesTime[2] = currentTrack.getDuration() * 1000;
                    mSeriesTime[3] = currentTrack.getDuration() * 1000;
                }
                break;
            default:
        }
    }

    private void handleForth() {
        XmPlayerService xps = XmPlayerService.getPlayerSrvice();
        if (xps == null) {
            return;
        }
        XmPlayListControl.PlayMode mode = xps.getPlayMode();
        int duration = xps.getDuration();
        int index = xps.getCurrIndex();
        int size = xps.getPlayListSize();
        switch (mode) {
            case PLAY_MODEL_LIST_LOOP:
                int nextIndex = (index + 1) % size;
                Track nextTrack = xps.getTrack(nextIndex);
                if (nextTrack != null) {
                    mSeriesTime[0] = nextTrack.getDuration() * 1000;
                }
                int next2Index = (index + 2) % size;
                Track next2Track = xps.getTrack(next2Index);
                if (next2Track != null) {
                    mSeriesTime[1] = next2Track.getDuration() * 1000;
                }
                int next3Index = (index + 3) % size;
                Track next3Track = xps.getTrack(next3Index);
                if (next3Track != null) {
                    mSeriesTime[2] = next3Track.getDuration() * 1000;
                }
                break;
            case PLAY_MODEL_LIST:
                if (size == 0) {
                    mLeftSeries = 0;
                    return;
                }
                if (size == 1) {
                    mLeftSeries = 1;
                    return;
                }
                if (index == size - 1) {
                    // 最后一集
                    mLeftSeries = 1;
                } else if (index == size - 2) {
                    // 倒数第二集
                    mLeftSeries = 2;
                    int nextIndexListMode = index + 1;
                    Track nextTrackListMode = xps.getTrack(nextIndexListMode);
                    if (nextTrackListMode != null) {
                        mSeriesTime[0] = nextTrackListMode.getDuration() * 1000;
                    }
                } else if (index == size - 3) {
                    // 倒数第三集
                    mLeftSeries = 3;
                    int nextIndexListMode = index + 1;
                    Track nextTrackListMode = xps.getTrack(nextIndexListMode);
                    if (nextTrackListMode != null) {
                        mSeriesTime[0] = nextTrackListMode.getDuration() * 1000;
                    }
                    int nextIndexListMode2 = index + 2;
                    Track nextTrackListMode2 = xps.getTrack(nextIndexListMode2);
                    if (nextTrackListMode2 != null) {
                        mSeriesTime[1] = nextTrackListMode2.getDuration() * 1000;
                    }
                } else {
                    // 倒数第四集及以前
                    int nextIndexListMode = index + 1;
                    Track nextTrackListMode = xps.getTrack(nextIndexListMode);
                    if (nextTrackListMode != null) {
                        mSeriesTime[0] = nextTrackListMode.getDuration() * 1000;
                    }
                    int next2IndexListMode = index + 2;
                    Track next2TrackListMode = xps.getTrack(next2IndexListMode);
                    if (next2TrackListMode != null) {
                        mSeriesTime[1] = next2TrackListMode.getDuration() * 1000;
                    }
                    int next3IndexListMode = index + 3;
                    Track next3TrackListMode = xps.getTrack(next3IndexListMode);
                    if (next3TrackListMode != null) {
                        mSeriesTime[2] = next3TrackListMode.getDuration() * 1000;
                    }
                }
                break;
            case PLAY_MODEL_SINGLE_LOOP:
                Track currentTrack = xps.getTrack(index);
                if (currentTrack != null) {
                    mSeriesTime[0] = currentTrack.getDuration() * 1000;
                    mSeriesTime[1] = currentTrack.getDuration() * 1000;
                    mSeriesTime[2] = currentTrack.getDuration() * 1000;
                }
                break;
            default:
        }
    }

    private void handleTriple() {
        XmPlayerService xps = XmPlayerService.getPlayerSrvice();
        if (xps == null) {
            return;
        }
        XmPlayListControl.PlayMode mode = xps.getPlayMode();
        int duration = xps.getDuration();
        int index = xps.getCurrIndex();
        int size = xps.getPlayListSize();
        switch (mode) {
            case PLAY_MODEL_LIST:
                if (size == 0) {
                    mLeftSeries = 0;
                    return;
                }
                if (size == 1) {
                    mLeftSeries = 1;
                    return;
                }
                if (index == size - 1) {
                    // 最后一集
                    mLeftSeries = 1;
                } else if (index == size - 2) {
                    // 倒数第二集
                    mLeftSeries = 2;
                    int nextIndex = index + 1;
                    Track nextTrack = xps.getTrack(nextIndex);
                    if (nextTrack != null) {
                        mSeriesTime[0] = nextTrack.getDuration() * 1000;
                    }
                } else {
                    // 倒数第三集及以前
                    int nextIndex = index + 1;
                    Track nextTrack = xps.getTrack(nextIndex);
                    if (nextTrack != null) {
                        mSeriesTime[0] = nextTrack.getDuration() * 1000;
                    }
                    int next2Index = index + 2;
                    Track next2Track = xps.getTrack(next2Index);
                    if (next2Track != null) {
                        mSeriesTime[1] = next2Track.getDuration() * 1000;
                    }
                }
                break;
            case PLAY_MODEL_LIST_LOOP:
                if(size == 0) {
                    return;
                }

                int nextIndex = (index + 1) % size;
                Track nextTrack = xps.getTrack(nextIndex);
                if (nextTrack != null) {
                    mSeriesTime[0] = nextTrack.getDuration() * 1000;
                }
                int next2Index = (index + 2) % size;
                Track next2Track = xps.getTrack(next2Index);
                if (next2Track != null) {
                    mSeriesTime[1] = next2Track.getDuration() * 1000;
                }
                break;
            case PLAY_MODEL_SINGLE_LOOP:
                Track currentTrack = xps.getTrack(index);
                if (currentTrack != null) {
                    mSeriesTime[0] = currentTrack.getDuration() * 1000;
                    mSeriesTime[1] = currentTrack.getDuration() * 1000;
                }
                break;
            default:
        }
    }

    private void handleDouble() {
        XmPlayerService xps = XmPlayerService.getPlayerSrvice();
        if (xps == null) {
            return;
        }
        XmPlayListControl.PlayMode mode = xps.getPlayMode();
        int duration = xps.getDuration();
        int index = xps.getCurrIndex();
        int size = xps.getPlayListSize();
        switch (mode) {
            case PLAY_MODEL_LIST:
                if (index == size - 1) {
                    mLeftSeries = 1;
                } else {
                    int nextIndex = index + 1;
                    Track nextTrack = xps.getTrack(nextIndex);
                    if (nextTrack != null) {
                        mSeriesTime[0] = nextTrack.getDuration() * 1000;
                    }
                }
                break;
            case PLAY_MODEL_LIST_LOOP:
                int nextIndex = index + 1 < size ? index + 1 : 0;
                Track nextTrack = xps.getTrack(nextIndex);
                if (nextTrack != null) {
                    mSeriesTime[0] = nextTrack.getDuration() * 1000;
                }
                break;
            case PLAY_MODEL_SINGLE_LOOP:
                Track currentTrack = xps.getTrack(index);
                if (currentTrack != null) {
                    mSeriesTime[0] = currentTrack.getDuration() * 1000;
                }
                break;
            default:
        }
    }

    private void notifyOnLeftSeriesChanged(int series) {
        Logger.i(TAG, "剩余集数变化：" + series);
        if (series == 0) {
            return;
        }
        if (series == 1 && XmPlayerService.getPlayerSrvice() != null) {
            // 剩余一集时设置本集后关闭
            XmPlayerService.getPlayerSrvice().pausePlayInMillis(XmPlayerService.PLAN_PAUSE_ON_COMPLETE);
            Logger.i(TAG, "本集播完后关闭");
        }
        if (mPlanTerminateListener != null) {
            try {
                mPlanTerminateListener.onLeftSeriesChanged(series, mType);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
//        if (XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().getAutoStopWidgetProvider() != null) {
//            Intent intent = new Intent(ConstantsOpenSdk.WIDGET_ACTION_AUTO_STOP);
//            intent.putExtra(ConstantsOpenSdk.WIDGET_EXTRA_AUTO_STOP_TYPE, "onLeftSeriesChanged");
//            intent.putExtra("AutoStopParams1", mType);
//            intent.putExtra("AutoStopParams2", series);
//            XmPlayerService.getPlayerSrvice().getAutoStopWidgetProvider().onReceive(XmPlayerService.getPlayerSrvice(), intent);
//        }
    }

    private void notifyOnLeftTimeChanged(int leftTime) {
        if (mPlanTerminateListener != null) {
            try {
                mPlanTerminateListener.onLeftTimeChanged(leftTime, mType);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        if (XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().getAutoStopWidgetProvider() != null) {
            int leftMin = leftTime == 0 ? 0 : leftTime / 60;
            if (mLeftMin == leftMin) {
                return;
            }
            mLeftMin = leftMin;
            Intent intent = new Intent(ConstantsOpenSdk.WIDGET_ACTION_AUTO_STOP);
            intent.putExtra(ConstantsOpenSdk.WIDGET_EXTRA_AUTO_STOP_TYPE, "onLeftTimeChanged");
            intent.putExtra("AutoStopParams1", mType);
            intent.putExtra("AutoStopParams2", leftTime);
            XmPlayerService.getPlayerSrvice().getAutoStopWidgetProvider().onReceive(XmPlayerService.getPlayerSrvice(), intent);
        }
    }

    private void notifyOnTimeout() {
        mLeftMin = 0;
        mLeftTime = 0;
        Logger.i(TAG, "定时关闭触发");
        if (mPlanTerminateListener != null) {
            try {
                mPlanTerminateListener.onTimeout(mType);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        mPlanStopTimeStamp = System.currentTimeMillis();
        if (XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().getAutoStopWidgetProvider() != null) {
            Intent intent = new Intent(ConstantsOpenSdk.WIDGET_ACTION_AUTO_STOP);
            intent.putExtra(ConstantsOpenSdk.WIDGET_EXTRA_AUTO_STOP_TYPE, "onTimeout");
            intent.putExtra("AutoStopParams1", mType);
            XmPlayerService.getPlayerSrvice().getAutoStopWidgetProvider().onReceive(XmPlayerService.getPlayerSrvice(), intent);
        }
    }

    private void notifyOnCancel() {
        mLeftMin = 0;
        mLeftTime = 0;
        Logger.i(TAG, "定时关闭取消");
        if (mPlanTerminateListener != null) {
            try {
                mPlanTerminateListener.onCancel();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        if (XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().getAutoStopWidgetProvider() != null) {
            Intent intent = new Intent(ConstantsOpenSdk.WIDGET_ACTION_AUTO_STOP);
            intent.putExtra(ConstantsOpenSdk.WIDGET_EXTRA_AUTO_STOP_TYPE, "onCancel");
            intent.putExtra("AutoStopParams1", mType);
            XmPlayerService.getPlayerSrvice().getAutoStopWidgetProvider().onReceive(XmPlayerService.getPlayerSrvice(), intent);
        }
    }

    public void forceCancelForPlay() {
        cancelPlan(true, false);
    }

    public void cancelPlan(boolean notify, boolean isStop) {
        Logger.i(TAG, "取消定时关闭");
        setType(isStop ? TIMER_STOP : TIMER_NONE);
        if (mTask != null) {
            mTask.cancel(true);
            mTask = null;
        }
        if (XmPlayerService.getPlayerSrvice() != null) {
            XmPlayerService.getPlayerSrvice().pausePlayInMillis(XmPlayerService.PLAN_NORMAL);
        }
        mLeftSeries = 0;
        Arrays.fill(mSeriesTime, 0);
        if (notify) {
            notifyOnCancel();
        }
    }

    private void setType(int type) {
        Logger.i("PlanTerminateManager", "状态转换：" + type);
        mType = type;
    }

    public boolean isSeriesType() {
        return mType >= TIMER_CURRENT && mType <= TIMER_NEXT_10;
    }

    public boolean isLastSeriesType() {
        return mLastType >= TIMER_CURRENT && mLastType <= TIMER_NEXT_10;
    }
}
