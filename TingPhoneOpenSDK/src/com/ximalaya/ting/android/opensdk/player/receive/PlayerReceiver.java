/**
 * PlayerReceiver.java
 * com.ximalaya.ting.android.framework.player.receive
 *
 * Function： TODO 
 *
 *   ver     date      		author
 * ──────────────────────────────────
 *   		 2015-7-30 		jack.qin
 *
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.player.receive;

import static com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationCreater.NOTIFICATION_EXTRY_KEY;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManagerForPlayer;
import com.ximalaya.ting.android.opensdk.player.appnotification.NotificationStyleUtils;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationCreater;
import com.ximalaya.ting.android.opensdk.player.appwidget.AutoStopWidgetProvider4x2;
import com.ximalaya.ting.android.opensdk.player.appwidget.PlayBoxWidgetProvider2x2;
import com.ximalaya.ting.android.opensdk.player.appwidget.PlayBoxWidgetProvider4x2;
import com.ximalaya.ting.android.opensdk.player.appwidget.WidgetProvider;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.manager.PlanTerminateManagerForPlay;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerControl;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.mixplay.MixPlayerService;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.mixplay.MixTrack;
import com.ximalaya.ting.android.opensdk.player.statistics.manager.UserInteractivePlayStatistics;
import com.ximalaya.ting.android.opensdk.player.ubt.UbtSourceProvider;
import com.ximalaya.ting.android.opensdk.push.PushGuardPlayerManager;
import com.ximalaya.ting.android.opensdk.util.Constants;
import com.ximalaya.ting.android.opensdk.util.MyClubUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForPlay;
import com.ximalaya.ting.android.routeservice.service.history.IPlayListLoadCallBack;
import com.ximalaya.ting.android.routeservice.service.push.IWidgetService;
import com.ximalaya.ting.android.routeservice.service.stat.IPushGuardUbtTrace;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 */
@Keep
public class PlayerReceiver extends BroadcastReceiver {
	private static final String BYD_PACKAGE_NAME = "com.ximalaya.ting.android.car";
	private int index = -1;
	private static final String TAG = "PLAYERRECEIVER";
	@Override
	public void onReceive(final Context context, final Intent intent) {
		if (intent == null) {
			return;
		}
		Logger.logToFile("PlayerReceiver intent.action=" + intent.getAction());

		final XmPlayerService service = XmPlayerService.getPlayerSrvice();
        if (service == null
                && XmNotificationCreater.ACTION_CONTROL_CLOSE_MAIN.equals(intent.getAction())) {
            return;
        }
		if (service == null) {
			IHistoryManagerForPlay historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForPlay.class);
			if(historyManager != null) {
				historyManager.addPlayListLoadListener(new IPlayListLoadCallBack() {
					@Override
					public void onLoadFinish(int playIndex) {
						index = playIndex;
						Logger.logToFile("setHistoryPlayListToPlayer onLoadFinish  onService IS Null");
						handleAction(context, intent, XmPlayerService.getPlayerSrvice(), true, false);
					}
				});
			}
            XmPlayerManagerForPlayer.getInstance(context).init(new XmPlayerManagerForPlayer.IConnectListener() {
				@Override
				public void onConnected() {
					Logger.logToFile("XmPlayerManagerForPlayer onConnected");
				}
			});
		} else {
			XmPlayListControl listControl = service.getPlayListControl();
			if(listControl != null && listControl.getPlayList() != null && listControl.getPlayList().size() == 0) {
				IHistoryManagerForPlay historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForPlay.class);
				if(historyManager != null){
					historyManager.addPlayListLoadListener(new IPlayListLoadCallBack() {
						@Override
						public void onLoadFinish(int playIndex) {
							index = playIndex;
							Logger.logToFile("setHistoryPlayListToPlayer onLoadFinish");
							handleAction(context, intent, service, false, false);
						}
					});

					historyManager.setHistoryPlayListToPlayer(MixPlayerService.getMixService() == null
						|| MixPlayerService.getMixService().getPlaySource() == null);
				}
			}else {
				Logger.logToFile("setHistoryPlayListToPlayer toHandlerAction ");
				handleAction(context, intent, service, false, false);
			}
		}
	}

	private void handleAction(Context context, Intent intent, XmPlayerService service, boolean autoPlay, boolean fromPushSetList) {
		if (service == null)
			return;

		Logger.logToFile("handleAction " + intent.getAction() + ", fromPushSetList=" + fromPushSetList + ", autoPlay=" + autoPlay);
		if (isLegalAction(intent) && !fromPushSetList) {
			boolean played = PushGuardPlayerManager.getInstance().playListWithRecommend(false, 0);
			Logger.logToFile("handleAction played=" + played);
			if (played) {
				PushGuardPlayerManager.getInstance().getHandler().postDelayed(() ->
						handleAction(context, intent, service, autoPlay, true), 1000);
				return;
			}
		}
		boolean fromPushGuard = PushGuardPlayerManager.getInstance().mIsFromPushGuard;
		Logger.logToFile("handleAction fromPushGuard=" + fromPushGuard);
		if(intent.getBooleanExtra(XmNotificationCreater.EXTRA_FROM_NOTIFICATION, false)){

			Intent notificationEventIntent = new Intent();
			notificationEventIntent.setAction(XmNotificationCreater.ACTION_NOTIFICATION_EVENT);
			notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_CUR_POSITION, service.getPlayCurrPosition());
			notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_DURATION, service.getDuration());

			PlayableModel playableModel = service.getPlayableModel();
			if(playableModel != null){
				UserInteractivePlayStatistics.clickPlay(playableModel.getDataId(), "notification");

				if(PlayableModel.KIND_TRACK.equals(playableModel.getKind())){
					notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_PLAYER_TYPE, "track");
				}else if(PlayableModel.KIND_LIVE_FLV.equals(playableModel.getKind())){
					notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_PLAYER_TYPE, "live");
				}else if(PlayableModel.KIND_RADIO.equals(playableModel.getKind())){
					notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_PLAYER_TYPE, "radio");
				}else if(PlayableModel.KIND_SCHEDULE.equals(playableModel.getKind())){
					notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_PLAYER_TYPE, "radio");
				}

				notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_PLAYER_ID, String.valueOf(playableModel.getDataId()));
			}

			if(XmNotificationCreater.ACTION_CONTROL_CLOSE.equals(intent.getAction()) ||
					XmNotificationCreater.ACTION_CONTROL_CLOSE_MAIN.equals(intent.getAction())){

				notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_ITEM_ID, "close");

			}else if(XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT.equals(intent.getAction()) ||
					XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT_MAIN.equals(intent.getAction())){

				notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_ITEM_ID, "next");

			}else if(XmNotificationCreater.ACTION_CONTROL_PLAY_PRE.equals(intent.getAction()) ||
					XmNotificationCreater.ACTION_CONTROL_PLAY_PRE_MAIN.equals(intent.getAction())){

				notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_ITEM_ID, "previous");

			}else if(XmNotificationCreater.ACTION_CONTROL_PLUS_TIME.equals(intent.getAction()) ||
					XmNotificationCreater.ACTION_CONTROL_PLUS_TIME_MAIN.equals(intent.getAction())){
				notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_ITEM_ID, "fastForward");

			}else if(XmNotificationCreater.ACTION_CONTROL_LESS_TIME.equals(intent.getAction()) ||
					XmNotificationCreater.ACTION_CONTROL_LESS_TIME_MAIN.equals(intent.getAction())){
				notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_ITEM_ID, "fastReverse");

			}else if(XmNotificationCreater.ACTION_CONTROL_START_PAUSE.equals(intent.getAction()) ||
					XmNotificationCreater.ACTION_CONTROL_START_PAUSE_MAIN.equals(intent.getAction())){

				boolean isPlaying = false;
				MixTrack mixTrack = MixPlayerService.getMixService() == null ? null : MixPlayerService.getMixService().getPlaySource();

				if (mixTrack != null) {
					isPlaying = MixPlayerService.getMixService().isMixPlaying();
				} else {
					isPlaying = service.isPlaying();
				}
				if(isPlaying){
					notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_ITEM_ID, "pause");
				}else{
					notificationEventIntent.putExtra(XmNotificationCreater.EXTRA_ITEM_ID, "play");
				}

				PlayableModel curPlayable = service.getPlayableModel();
				if(!isPlaying){
					if (curPlayable instanceof Track) {
						Track track = (Track)curPlayable;
						if(track.getType() == Track.TYPE_DUBSHOW){
							startMainActivity(context, service);
						}
					} else if (service.getPlayListSize() == 0 && mixTrack == null) {
						collapseStatusBar(context);
						startMainActivity(context, service);
					}

				}

			}
            LocalBroadcastManager.getInstance(context).sendBroadcast(notificationEventIntent);
		} else if (!TextUtils.isEmpty(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME))) {
			// 小组件
			PlayableModel playableModel = service.getPlayableModel();
			if (playableModel != null) {
				UserInteractivePlayStatistics.clickPlay(playableModel.getDataId(), "widgetApp");
			}
		}

		handleIntentForUbtSource(intent, service);
		handleIntentForPushAndWidget(intent);
		handleIntentForWidgetNew(intent);
		if (XmNotificationCreater.ACTION_CONTROL_CLOSE_MAIN.equals(intent.getAction())
				|| XmNotificationCreater.ACTION_CONTROL_CLOSE.equals(intent.getAction())) {
			Logger.logToFile("process Main is running");

			if(intent.getBooleanExtra(XmNotificationCreater.EXTRE_IS_REAL_CLOSE_APP ,false)) {
				if (service != null) {
					service.closeNotification();
					service.closeApp();
				}
			} else {
				if (service != null) {
					if (MixPlayerService.getMixService().isMixPlaying()) {
						MixPlayerService.getMixService().pause();
						return;
					}
					service.pausePlay(true, PauseReason.Common.NOTIFICATION_CLOSE);
					service.closeNotification();
					service.removeScreenChangeBroadCast();
				}
			}
		} else if (intent.getAction().equals(
				XmNotificationCreater.ACTION_CONTROL_PLAY_PRE_MAIN)
				&& service != null) {
			if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null &&
					!TextUtils.isEmpty(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME))) {
				// 小组件不控制mix播放器
				return;
			}
			if ((PlayBoxWidgetProvider2x2.TRACE_TAG.equals(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME)) ||
					AutoStopWidgetProvider4x2.TRACE_TAG.equals(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME)) ||
					PlayBoxWidgetProvider4x2.TRACE_TAG.equals(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME))) &&
					!service.hasPreSound()) {
				// 打开播放页
				startPlayFragmentForWidget(context, service, intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME),
						service.getPlayableModel() == null);
			}
			if (AutoStopWidgetProvider4x2.TRACE_TAG.equals(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME))) {
				if (!PlanTerminateManagerForPlay.getInstance().isTimingForPlay() && service.getPlayableModel() != null &&
						PlayableModel.KIND_TRACK.equals(service.getPlayableModel().getKind())) {
					PlanTerminateManagerForPlay.getInstance().startTimerForPlay(PlanTerminateManagerForPlay.TIMER_CURRENT);
				}
			}
			if (fromPushGuard && !service.hasPreSound()) {
				// 拉活且没有上一首  直接开播这首
				service.startPlay(true);
			} else {
				service.playPre();
			}
		} else if (intent.getAction().equals(
				XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT_MAIN)
				&& service != null) {
			if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null &&
					!TextUtils.isEmpty(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME))) {
				// 小组件不控制mix播放器
				return;
			}
			if ((PlayBoxWidgetProvider2x2.TRACE_TAG.equals(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME)) ||
					AutoStopWidgetProvider4x2.TRACE_TAG.equals(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME)) ||
					PlayBoxWidgetProvider4x2.TRACE_TAG.equals(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME))) &&
					!service.hasNextSound()) {
				// 打开播放页
				startPlayFragmentForWidget(context, service, intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME),
						service.getPlayableModel() == null);
			}
			if (AutoStopWidgetProvider4x2.TRACE_TAG.equals(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME))) {
				if (!PlanTerminateManagerForPlay.getInstance().isTimingForPlay() && service.getPlayableModel() != null &&
						PlayableModel.KIND_TRACK.equals(service.getPlayableModel().getKind())) {
					PlanTerminateManagerForPlay.getInstance().startTimerForPlay(PlanTerminateManagerForPlay.TIMER_CURRENT);
				}
			}
			if (fromPushGuard && !service.hasPreSound()) {
				// 拉活且没有下一首  直接开播这首
				service.startPlay(true);
			} else {
				service.playNext();
			}
		} else if (intent.getAction().equals(
				XmNotificationCreater.ACTION_CONTROL_PLUS_TIME_MAIN)
				&& service != null) {
			service.seekTo(service.getPlayCurrPosition()+15000);
			boolean hasPermission = NotificationStyleUtils.isSystemNotificationEnable(context);
			if (fromPushGuard || !hasPermission) {
				service.startPlay(true);
			}
		} else if (intent.getAction().equals(
				XmNotificationCreater.ACTION_CONTROL_LESS_TIME_MAIN)
				&& service != null) {
            int seekToPos = service.getPlayCurrPosition() - 15000;
            seekToPos = seekToPos <= 0 ? 0 : seekToPos;
            service.seekTo(seekToPos);
			boolean hasPermission = NotificationStyleUtils.isSystemNotificationEnable(context);
			if (fromPushGuard || !hasPermission) {
				service.startPlay(true);
			}
		} else if (intent.getAction().equals(
				XmNotificationCreater.ACTION_CONTROL_START_PAUSE_MAIN)
				&& service != null) {

			if (service.isVideoMode()) {
				Intent intentVideo = new Intent(PlayerConstants.ACTION_BROADCAST_VIDEO_PLAY_PAUSE);
				if (service.isVideoPlaying()) {
					intentVideo.putExtra(PlayerConstants.EXTRA_VIDEO_PLAY_EVENT, "video_pause");
					context.sendBroadcast(intentVideo);
					return;
				} else {
					intentVideo.putExtra(PlayerConstants.EXTRA_VIDEO_PLAY_EVENT, "video_play");
					context.sendBroadcast(intentVideo);
					return;
				}
			}
			if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null) {
				if (!TextUtils.isEmpty(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME))) {
					// 小组件不控制mix播放器
					return;
				}
				if (MixPlayerService.getMixService().isMixPlaying()) {
					MixPlayerService.getMixService().pause();
				} else {
					MixPlayerService.getMixService().start();
				}
				return;
			}
			if ((PlayBoxWidgetProvider2x2.TRACE_TAG.equals(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME)) ||
					PlayBoxWidgetProvider4x2.TRACE_TAG.equals(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME)) ||
					AutoStopWidgetProvider4x2.TRACE_TAG.equals(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME))) &&
					service.getPlayableModel() == null) {
				// 打开播放页
				startPlayFragmentForWidget(context, service, intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME),
						true);
			}
			Logger.logToFile("PlayerReceiver : ACTION_CONTROL_START_PAUSE_MAIN");
			if (service.isPlaying()) {
				Logger.logToFile("PlayerReceiver : ACTION_CONTROL_START_PAUSE_MAIN  playing");
				service.pausePlay(true, PauseReason.Common.NOTIFICATION_PAUSE_MAIN);
				if (AutoStopWidgetProvider4x2.TRACE_TAG.equals(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME))) {
					if (PlanTerminateManagerForPlay.getInstance().isTimingForPlay() && service.getPlayableModel() != null &&
							PlayableModel.KIND_TRACK.equals(service.getPlayableModel().getKind())) {
						PlanTerminateManagerForPlay.getInstance().forceCancelForPlay();
					}
				}
			} else {
				if (AutoStopWidgetProvider4x2.TRACE_TAG.equals(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME))) {
					if (!PlanTerminateManagerForPlay.getInstance().isTimingForPlay() && service.getPlayableModel() != null &&
							PlayableModel.KIND_TRACK.equals(service.getPlayableModel().getKind())) {
						PlanTerminateManagerForPlay.getInstance().startTimerForPlay(PlanTerminateManagerForPlay.TIMER_CURRENT);
					}
				}
				XmPlayerControl playerControl = service.getPlayControl();
				XmPlayListControl playerListControl = service.getPlayListControl();
				if(playerControl != null && playerListControl != null) {
					Logger.logToFile("PlayerReceiver : ACTION_CONTROL_START_PAUSE_MAIN  playerState = " + playerControl.getPlayerState());
					if(playerControl.getPlayerState() == PlayerConstants.STATE_STOPPED) {
						int currIndex = playerListControl.getCurrIndex();
						if(currIndex == -1)
							currIndex = index;
						if(currIndex == -1) {
							return;
						}
						Logger.logToFile("PlayerReceiver : ACTION_CONTROL_START_PAUSE_MAIN  currIndex = " + currIndex);
						service.play(currIndex);
						return;
					}
				}
				Logger.logToFile("PlayerReceiver : ACTION_CONTROL_START_PAUSE_MAIN  autoPlay = " + autoPlay);
				if (autoPlay && playerControl != null) {
					playerControl.setShouldForcePlay(true);
				}
				PlayableModel curPlayable = service.getPlayableModel();
				if (curPlayable != null && PlayableModel.KIND_MYCLUB_FLV.equals(curPlayable.getKind())) {
					// 付费且没权限的，不播放，先进房间
					if (curPlayable instanceof Track && ((Track) curPlayable).isPaid() && !((Track) curPlayable).isAuthorized()) {
						MyClubUtil.startRoom(context, (Track) curPlayable);
					} else {
						service.startPlay(autoPlay);
					}
				} else if (curPlayable != null && PlayableModel.KIND_LIVE_FLV.equals(curPlayable.getKind())) {
					//直播不播放
				} else {
					service.startPlay(autoPlay);
				}
			}
		} else if(intent.getAction().equals(XmNotificationCreater.ACTION_CONTROL_LIKE)) {
			// 喜欢
			NotificationLikeManager.INSTANCE.changeLikeState();
		} else {

			if (service == null
					&& context.getApplicationInfo().packageName
					.equalsIgnoreCase(BYD_PACKAGE_NAME)) {
				XmPlayerManager.release();//解绑，防内存泄漏
				Intent actionIntent = new Intent(Intent.ACTION_VIEW);
				actionIntent.setData(Uri.parse("tingcar://open"));
				actionIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
				if(actionIntent.resolveActivity(context.getPackageManager()) != null) {
					context.startActivity(actionIntent);
				}
				return;
			}
			if (service != null) {
				if (intent.getAction().equals(
						XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT)) {
					service.playNext();
				} else if (intent.getAction().equals(
						XmNotificationCreater.ACTION_CONTROL_PLAY_PRE)) {
					service.playPre();
				} else if (intent.getAction().equals(
						XmNotificationCreater.ACTION_CONTROL_START_PAUSE)) {
					if (service.getPlayListSize() == 0
							&& context.getApplicationInfo().packageName
							.equalsIgnoreCase(BYD_PACKAGE_NAME)) {
						Intent actionIntent = new Intent(Intent.ACTION_VIEW);
						actionIntent.setData(Uri.parse("tingcar://open"));
						actionIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
						if(actionIntent.resolveActivity(context.getPackageManager()) != null) {
							context.startActivity(actionIntent);
						}
						return;
					}
					if (service.isPlaying()) {
						service.pausePlay(true, PauseReason.Common.NOTIFICATION_PAUSE);
					} else {
						service.startPlay(autoPlay);
					}
				}
			}
		}
	}

	private void startMainActivity(Context context, XmPlayerService service) {
		try {
			Class clazz = Class.forName("com.ximalaya.ting.android.host.activity.MainActivity");

			Intent activityIntent = new Intent(context, clazz);
			activityIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
			activityIntent.putExtra(NOTIFICATION_EXTRY_KEY,
					XmNotificationCreater.NOTIFICATION_EXTYR_DATA);
			service.startActivity(activityIntent);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void startPlayFragmentForWidget(Context context, XmPlayerService service, String providerName, boolean goHome) {
		try {
			Class clazz = Class.forName("com.ximalaya.ting.android.host.activity.MainActivity");
			Intent activityIntent = new Intent(context, clazz);
			activityIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
			String itingUrl = goHome ? "iting://open?msg_type=198&guiyin=no_ggzs" : "iting://open?msg_type=11&guiyin=no_ggzs&only_show=true";
			activityIntent.setData(Uri.parse(itingUrl));
			activityIntent.putExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER, true);
			activityIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME, providerName);
			service.startActivity(activityIntent);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void collapseStatusBar(Context context) {
		int currentApiVersion = android.os.Build.VERSION.SDK_INT;
		try {
			Object service = context.getSystemService("statusbar");
			Class<?> statusbarManager = Class
					.forName("android.app.StatusBarManager");
			Method collapse = null;
			if (service != null) {
				if (currentApiVersion <= 16) {
					collapse = statusbarManager.getMethod("collapse");
				} else {
					collapse = statusbarManager.getMethod("collapsePanels");
				}
				collapse.setAccessible(true);
				collapse.invoke(service);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private boolean isLegalAction(Intent intent) {
		if (intent == null) {
			return false;
		}
		String action = intent.getAction();
		if (!XmNotificationCreater.ACTION_CONTROL_START_PAUSE_MAIN.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_START_PAUSE.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT_MAIN.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_PLAY_PRE.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_LIKE.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_PLAY_PRE_MAIN.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_PLUS_TIME.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_PLUS_TIME_MAIN.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_LESS_TIME.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_LESS_TIME_MAIN.equals(action)) {
			return false;
		}
		return true;
	}

	private void handleIntentForPushAndWidget(Intent intent) {
		if (intent == null) {
			return;
		}
		if (!isLegalAction(intent)) {
			return;
		}
		String item = "other";
		String notificationItem = "";
		if (XmNotificationCreater.ACTION_CONTROL_START_PAUSE_MAIN.equals(intent.getAction()) ||
				XmNotificationCreater.ACTION_CONTROL_START_PAUSE.equals(intent.getAction())) {
			item = "play";
			notificationItem = "播控";
		} else if (XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT.equals(intent.getAction()) ||
				XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT_MAIN.equals(intent.getAction())) {
			item = "next";
			notificationItem = "下一首";
		} else if (XmNotificationCreater.ACTION_CONTROL_PLAY_PRE.equals(intent.getAction()) ||
				XmNotificationCreater.ACTION_CONTROL_PLAY_PRE_MAIN.equals(intent.getAction())) {
			item = "before";
			notificationItem = "上一首";
		} else if (XmNotificationCreater.ACTION_CONTROL_LESS_TIME.equals(intent.getAction()) ||
				XmNotificationCreater.ACTION_CONTROL_LESS_TIME_MAIN.equals(intent.getAction())) {
			notificationItem = "快退";
			item = "less";
		} else if (XmNotificationCreater.ACTION_CONTROL_PLUS_TIME.equals(intent.getAction()) ||
				XmNotificationCreater.ACTION_CONTROL_PLUS_TIME_MAIN.equals(intent.getAction())) {
			notificationItem = "快进";
			item = "plus";
		} else if (XmNotificationCreater.ACTION_CONTROL_LIKE.equals(intent.getAction())) {
			notificationItem = "点赞";
			item = "like";
		}
		PushGuardPlayerManager.getInstance().traceGuardRealClick(true, true, item);
		IPushGuardUbtTrace pushGuardUbtTrace = RouterServiceManager.getInstance().getService(IPushGuardUbtTrace.class);
		if (pushGuardUbtTrace != null) {
			if (intent.hasExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER)) {
				String providerName = intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME);
				if (providerName != null && providerName.equals(WidgetProvider.TRACE_TAG)) {
					if (notificationItem.equals("播控") && XmPlayerService.getPlayerSrvice() != null) {
						notificationItem = XmPlayerService.getPlayerSrvice().isPlaying() ? "暂停" : "播放";
					}
					pushGuardUbtTrace.traceNotificationClick(false, false, notificationItem);
				}
			} else {
				pushGuardUbtTrace.traceNotificationClick(false, true, notificationItem);
			}
		}
	}

	private void handleIntentForWidgetNew(Intent intent) {
		if (intent == null || !intent.hasExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER) ||
				!intent.hasExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME) ||
				intent.hasExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_FROM_OTHER_AREA)) {
			return;
		}
		String item = "其他";
		if (XmNotificationCreater.ACTION_CONTROL_START_PAUSE_MAIN.equals(intent.getAction()) ||
				XmNotificationCreater.ACTION_CONTROL_START_PAUSE.equals(intent.getAction())) {
			if (XmPlayerService.getPlayerSrvice() != null) {
				item = XmPlayerService.getPlayerSrvice().isPlaying() ? "暂停" : "播放";
			}
		} else if (XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT.equals(intent.getAction()) ||
				XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT_MAIN.equals(intent.getAction())) {
			item = "下一首";
		} else if (XmNotificationCreater.ACTION_CONTROL_PLAY_PRE.equals(intent.getAction()) ||
				XmNotificationCreater.ACTION_CONTROL_PLAY_PRE_MAIN.equals(intent.getAction())) {
			item = "上一首";
		}
		IWidgetService iWidgetService = RouterServiceManager.getInstance().getService(IWidgetService.class);
		if (iWidgetService != null) {
			iWidgetService.traceWidgetNewClick(intent.getStringExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME), item);
		}
	}

	private void handleIntentForUbtSource(Intent intent, XmPlayerService service) {
		if (intent == null || service == null) {
			return;
		}
		String action = intent.getAction();
		if (!XmNotificationCreater.ACTION_CONTROL_START_PAUSE_MAIN.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_START_PAUSE.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT_MAIN.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_PLAY_PRE.equals(action) &&
				!XmNotificationCreater.ACTION_CONTROL_PLAY_PRE_MAIN.equals(action)) {
			return;
		}
		if (service.isPlaying()) {
			return;
		}
		if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null) {
			if (MixPlayerService.getMixService().isMixPlaying()) {
				return;
			}
			if (action.equals(XmNotificationCreater.ACTION_CONTROL_START_PAUSE_MAIN)) {
				UbtSourceProvider.getInstance().setUbtSource(Constants.KEY_SLEEP, null);
			}
			return;
		}
		try {
		    if (service.mListControl != null && service.mListControl.getOriginParams() != null
					&& "1".equals(service.mListControl.getOriginParams().get(ConstantsOpenSdk.KEY_PLAY_PAGE_TYPE))) {
		    	UbtSourceProvider.getInstance().setUbtSource(Constants.KEY_FEED_PLAY, service.getPlayableModel());
			} else {
				UbtSourceProvider.getInstance().setUbtSource(service.getPlayableModel());
			}
		} catch (Exception e) {
			Logger.logToFile("WireControlReceiver : ubtSource error");
		}
	}
}
