package com.ximalaya.ting.android.opensdk.push;

import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationPendingIntentCreateUtils;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.push.IPushLogicService;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.util.Random;

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2023/2/14
 * Description：
 */
public class PushNotificationReceiver extends BroadcastReceiver {
    public static final int TYPE_NOTIFY_CLICK = 0;
    public static final int TYPE_PLAY_CLICK = 1;
    public static final String ACTION_PUSH_CLICK = "com.ximalaya.ting.android.action.push_xm_pipe_click";
    public static final String ACTION_PLAY_CLICK = "com.ximalaya.ting.android.action.action_push_card_play";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null) {
            return;
        }
        String action = intent.getAction();
        IPushLogicService iPushClickHandle = RouterServiceManager.getInstance().getService(IPushLogicService.class);
        String message = intent.getStringExtra("extra_push_xm_pipe_click_msg");
        if (ACTION_PUSH_CLICK.equals(action)) {
            if (iPushClickHandle != null) {
                iPushClickHandle.handleMessageClicked(context, message);
            }
        } else if (ACTION_PLAY_CLICK.equals(action)) {
            // 请求热点 直接开播
            if (iPushClickHandle != null) {
                iPushClickHandle.handleDailyNewsPlay(context, message);
            }
            int notifyId = intent.getIntExtra("extra_push_xm_pipe_click_notify_id", -1);
            if (notifyId > 0) {
                NotificationManager notificationManager = SystemServiceManager.getNotificationManager(context);
                notificationManager.cancel(notifyId);
            }
        }
        if (iPushClickHandle != null) {
            iPushClickHandle.traceClick(message);
        }

        // 每点击一个通知就会自动删一个  删完了 需要remove channelGroup
        String groupName = intent.getStringExtra("extra_push_xm_pipe_click_group_name");
        int groupNotifyId = intent.getIntExtra("extra_push_xm_pipe_click_group_notify_id", -1);
        Logger.e("sjc", "receive action = " + action + "  , groupName = " + groupName  + "  , groupNotifyId = " + groupNotifyId);
        PushNotificationFilterManager.decreaseChannelGroupCount(context, groupName, groupNotifyId);
    }

    public static PendingIntent getClickIntent(Context context, int clickType, String msg, int notifyId, String groupName, int groupNotifyId) {
        Intent playIntent = new Intent();
        playIntent.setClass(context, PushNotificationReceiver.class);
        if (clickType == TYPE_PLAY_CLICK) {
            playIntent.setAction(ACTION_PLAY_CLICK);
        } else {
            playIntent.setAction(ACTION_PUSH_CLICK);
        }
        playIntent.putExtra("extra_push_xm_pipe_click_group_name", groupName);
        playIntent.putExtra("extra_push_xm_pipe_click_msg", msg);
        playIntent.putExtra("extra_push_xm_pipe_click_notify_id", notifyId);
        playIntent.putExtra("extra_push_xm_pipe_click_group_notify_id", groupNotifyId);
        return PendingIntent.getBroadcast(context, new Random().nextInt(1000), playIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
    }
}
