package com.ximalaya.ting.android.opensdk.push;

import android.content.ComponentName;
import android.content.Context;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.IBinder;
import android.os.RemoteException;

import com.ximalaya.ting.android.opensdk.player.service.IXmPlayer;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;

/**
 * Author: <PERSON>
 * Email: <EMAIL>
 * Date: 2022/11/30
 * Description：推送拉活进程后，拉活player进程，通知栏展示notification
 */

public class XmPlayerManagerForPush {
    private volatile static XmPlayerManagerForPush sInstance;
    private Context mAppCtx;
    private IXmPlayer mPlayerStub;
    private boolean mServiceConnected;

    public static XmPlayerManagerForPush getInstance(Context context) {
        if (sInstance == null) {
            synchronized (XmPlayerManagerForPush.class) {
                if (sInstance == null) {
                    sInstance = new XmPlayerManagerForPush(context);
                }
            }
        }
        return sInstance;

    }

    private XmPlayerManagerForPush(Context context) {
        this.mAppCtx = context.getApplicationContext();
    }

    private ServiceConnection mConn = new ServiceConnection() {

        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            mServiceConnected = true;
            mPlayerStub = IXmPlayer.Stub.asInterface(service);
            try {
                mPlayerStub.initXiMaPipePush();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            mServiceConnected = false;
        }
    };

    public synchronized boolean isConnectedStatus() {
        return sInstance != null && mServiceConnected && mPlayerStub != null;
    }

    public void init(boolean showNotification, boolean fromGuard) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                mAppCtx.startForegroundService(XmPlayerService.getIntentFromPush(mAppCtx, showNotification, fromGuard));
            } else {
                mAppCtx.startService(XmPlayerService.getIntentFromPush(mAppCtx, showNotification, fromGuard));
            }
            mAppCtx.bindService(XmPlayerService.getIntentFromPush(mAppCtx, showNotification, fromGuard), mConn, Context.BIND_AUTO_CREATE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
