package com.ximalaya.ting.android.opensdk.push;

import static android.content.Context.ALARM_SERVICE;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationManagerCompat;

import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.CommonRequestForMain;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationCreater;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationPendingIntentCreateUtils;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.stat.IPushGuardUbtTrace;
import com.ximalaya.ting.android.util.OsUtil;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2023/1/30
 * Description：后台线程、JobScheduler均不靠谱，这里暂时用alarm实现定时轮询功能
 */

public class PushGuardPlayerManager {
    public static final String ITEM_BAOHUO_PLAYER_UPDATE_1 = "baohuo_player_update_1";   // 保活播放器_更新频率_未转化时长
    public static final String ITEM_BAOHUO_PLAYER_UPDATE_2 = "baohuo_player_update_2";   // 保活播放器_更新频率_推荐内容
    public static final String ITEM_BAOHUO_PLAYER_RECOMMEND_CONTENT = "baohuo_player_recommend_content";   // 保活播放器_推荐内容开关
    public static final String ITEM_BAOHUO_PLAYER_RECOMMEND_CONTENT_HARMONYOS = "baohuo_player_recommend_content_HarmonyOS";   // 保活播放器_推荐内容开关_鸿蒙系统

    public static final String CLOSE_PUSH_GUARD_RECOMMEND_LOCAL = "close_push_guard_recommend_local";   // 关闭播放进程的声音推荐
    public static final String ACTION_PUSH_GUARD_ALARM_TYPE = "action_push_guard_alarm_type";
    public static final String ACTION_PUSH_GUARD_ALARM_INDEX = "action_push_guard_alarm_index";
    public static final String KEY_PUSH_GUARD_RECOMMEND_TYPE = "push_guard_recommend_type";
    public static final String KEY_PUSH_GUARD_HW_NOTIFICATION = "push_guard_hw_notification";
    public static final String KEY_PUSH_GUARD_HW_PLAYER_TIME = "key_push_guard_hw_player_time";
    public boolean mIsFromPushGuard; // 通过push保活拉活，无关播放进程原来的状态
    public int mIsFirstFromPushGuard = -1; // 通过push保活拉活，播放进程原来不存在
    public PlayableModel mPlayableModel;
    private CommonTrackList mCommonTrackList;
    private int mCurrentRecommendType = 1;
    private int mCurrentIndex = 0;
    private Handler mHandler;
    private int mReTryCount = 0;
    private long mIndexDuration;// 推荐类型变化时长 分钟
    private long mTypeDuration; // 推荐序号变化时长 分钟
    private boolean mFirstSwitch = true;

    private AlarmReceiverForPushGuard mAlarmReceiver;
    private Context mContext;
    public boolean shouldStartPlayTraceFlag; // 开播埋点是否应该上报，在点击动作的时候就设置
    public boolean mFromHarmonyAutoNotification;
    public boolean mFromHarmonyAutoNotificationFirstTime;
    public boolean mHasPlayedForHarmony;

    public void init(Context context) {
        mContext = context;
        mCurrentIndex = 0;
        mTypeDuration = (long) MmkvCommonUtil.getInstance(context).getInt(ITEM_BAOHUO_PLAYER_UPDATE_1, 120) * 60 * 1000;
        mIndexDuration = (long) MmkvCommonUtil.getInstance(context).getInt(ITEM_BAOHUO_PLAYER_UPDATE_2, 30) * 60 * 1000;
        if (ConstantsOpenSdk.isDebug) {
            mTypeDuration = 60 * 1000;
        }
        boolean serverStatus = MmkvCommonUtil.getInstance(context).getBoolean(ITEM_BAOHUO_PLAYER_RECOMMEND_CONTENT, true);
        boolean serverHarmonyOSStatus = MmkvCommonUtil.getInstance(context).getBoolean(ITEM_BAOHUO_PLAYER_RECOMMEND_CONTENT_HARMONYOS, true);
        if (!serverStatus) {
            // 不展示人工+系统推荐内容，拉起播放器后仅展示用户最近收听
            return;
        }
        if (!serverHarmonyOSStatus && OsUtil.isHarmonyOs()) {
            // 鸿蒙 不展示人工+系统推荐内容，拉起播放器后仅展示用户最近收听
            return;
        }
        boolean localClose = MMKVUtil.getInstance().getBoolean(CLOSE_PUSH_GUARD_RECOMMEND_LOCAL, false);
        if (localClose) {
            return;
        }
        MMKVUtil.getInstance().saveLong(KEY_PUSH_GUARD_HW_PLAYER_TIME, System.currentTimeMillis());
        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        if (service != null && service.getPlayListControl() != null && service.getPlayListControl().getCurrentPlayableModel() != null) {
            Logger.e("sjc", "model not null");
            registerAlarmReceiver();
        } else {
            getHandler().postDelayed(this::registerAlarmReceiver, 4000);
        }
    }

    private void registerAlarmReceiver() {
        if (!mIsFromPushGuard) {
            Logger.e("sjc", "registerAlarmReceiver cancel");
            return;
        }
        if (mAlarmReceiver != null || mContext == null) {
            return;
        }
        if (mAlarmReceiver == null) {
            mAlarmReceiver = new AlarmReceiverForPushGuard();
        }
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ACTION_PUSH_GUARD_ALARM_TYPE);
        intentFilter.addAction(ACTION_PUSH_GUARD_ALARM_INDEX);

        try {
            mContext.registerReceiver(mAlarmReceiver, intentFilter);
        } catch (Exception e) {
            e.printStackTrace();
        }

        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        boolean hwOpenNotification = MmkvCommonUtil.getInstance(mContext).getBoolean(PushGuardPlayerManager.KEY_PUSH_GUARD_HW_NOTIFICATION, true);
        if (service != null && OsUtil.isHarmonyOs() && hwOpenNotification) {
            service.openHarmonyOsNotification();
        }
        if (service != null && service.getPlayListControl() != null && service.getPlayListControl().getCurrentPlayableModel() instanceof Track) {
            ((Track) service.getPlayListControl().getCurrentPlayableModel()).setIsFromPushRecommend(1);
            traceRecommendSwitch();
        }
        if (service != null && service.getPlayListControl() != null && service.getPlayListControl().getCurrentPlayableModel() == null) {
            // 原来没有播过声音，直接推荐
            Logger.e("sjc", "原来没有播过声音，直接推荐");
            dealIndexAndTypeChangeLogic(ACTION_PUSH_GUARD_ALARM_TYPE);
        } else {
            makeTypeAlarm(true);
        }
    }

    private void makeTypeAlarm(boolean isTypeAlarm) {
        if (mContext == null) {
            return;
        }
        cancelAlarm(isTypeAlarm ? ACTION_PUSH_GUARD_ALARM_TYPE : ACTION_PUSH_GUARD_ALARM_INDEX);
//        if (isTypeAlarm && mCurrentRecommendType >= 2) {
//            return;
//        } else if (!isTypeAlarm && mCurrentRecommendType <= 1) {
//            return;
//        }

        //得到日历实例，主要是为了下面的获取时间
        Calendar mCalendar = Calendar.getInstance();
        long systemTime = System.currentTimeMillis();
        mCalendar.setTimeInMillis(systemTime + (isTypeAlarm ? mTypeDuration : mIndexDuration + 1000));
        // 这里时区需要设置一下，不然可能个别手机会有8个小时的时间差
        mCalendar.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        AlarmManager alarmManager = (AlarmManager) mContext.getSystemService(ALARM_SERVICE);
        Intent intent = new Intent(isTypeAlarm ? ACTION_PUSH_GUARD_ALARM_TYPE : ACTION_PUSH_GUARD_ALARM_INDEX);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(mContext, 21, intent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, mCalendar.getTimeInMillis(), pendingIntent);
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                alarmManager.setExact(AlarmManager.RTC_WAKEUP, mCalendar.getTimeInMillis(), pendingIntent);
            } else {
                alarmManager.set(AlarmManager.RTC_WAKEUP, mCalendar.getTimeInMillis(), pendingIntent);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onDestroy() {
        try {
            if (mAlarmReceiver != null) {
                cancelAlarm(ACTION_PUSH_GUARD_ALARM_INDEX);
                cancelAlarm(ACTION_PUSH_GUARD_ALARM_TYPE);
                mContext.unregisterReceiver(mAlarmReceiver);
            }
            XmPlayerService service = XmPlayerService.getPlayerSrvice();
            if (service != null) {
                service.updateNotification();
                service.sendPushRecommendDataChange();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        mCommonTrackList = null;
        mPlayableModel = null;
        mAlarmReceiver = null;
        mContext = null;
        mHandler = null;
    }

    public void dealIndexAndTypeChangeLogic(String action) {
        if (!mIsFromPushGuard) {
            Logger.e("sjc", "cancel");
            return;
        }
        if (ACTION_PUSH_GUARD_ALARM_TYPE.equals(action)) {
            // 更换推荐类型
            cancelAlarm(ACTION_PUSH_GUARD_ALARM_INDEX);
            Logger.e("sjc", "更换推荐类型, mCurrentRecommendType = " + mCurrentRecommendType);
//            getTingList(mCurrentRecommendType + 1);
            getTingList();
        } else if (ACTION_PUSH_GUARD_ALARM_INDEX.equals(action) && mCurrentRecommendType > 1) {
            // 更换同类型声音的index
            Logger.e("sjc", "更换同类型声音的index, mCurrentIndex = " + mCurrentIndex);
            if (mCommonTrackList != null && mCommonTrackList.getTracks() != null && mCommonTrackList.getTracks().size() > mCurrentIndex + 1) {
                mCurrentIndex++;
                mPlayableModel = (PlayableModel) mCommonTrackList.getTracks().get(mCurrentIndex);
                if (mPlayableModel instanceof Track) {
                    Logger.e("sjc", "index Change  = " + ((Track) mPlayableModel).getTrackTitle());
                }
                XmPlayerService service = XmPlayerService.getPlayerSrvice();
                if (service != null) {
                    service.play(mCurrentIndex, false);
                    try {
                        service.updateNotification();
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
            }
            cancelAlarm(ACTION_PUSH_GUARD_ALARM_INDEX);
            makeTypeAlarm(false);
        }
    }

    private void cancelAlarm(String action) {
        if (mContext == null) {
            return;
        }
        Intent intent = new Intent(action);
        PendingIntent pi = PendingIntent.getBroadcast(mContext, 21, intent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
        AlarmManager am = (AlarmManager) mContext.getSystemService(ALARM_SERVICE);
        //取消警报
        am.cancel(pi);
    }

    private static class PushGuardPlayerManagerHolder {
        private static final PushGuardPlayerManager INSTANCE = new PushGuardPlayerManager();
    }

    public static PushGuardPlayerManager getInstance() {
        return PushGuardPlayerManagerHolder.INSTANCE;
    }

    public XmNotificationCreater.RemoteViewDetailModel handleRemoteLogic(XmNotificationCreater.RemoteViewDetailModel remoteViewDetailModel) {
        if (remoteViewDetailModel == null) {
            return null;
        }
        if (!mIsFromPushGuard || mCurrentRecommendType == 1) {
            return remoteViewDetailModel;
        }
        if (mCommonTrackList != null && mPlayableModel != null && mCommonTrackList.getTracks() != null) {
            int totalSize = mCommonTrackList.getTracks().size();
            if (mCurrentIndex == 0) {
                remoteViewDetailModel.isDisablePreBtn = true;
                remoteViewDetailModel.isDisableNextBtn = totalSize == 1;
            } else if (mCurrentIndex == totalSize - 1) {
                remoteViewDetailModel.isDisableNextBtn = true;
                remoteViewDetailModel.isDisablePreBtn = totalSize < 2;
            } else {
                remoteViewDetailModel.isDisablePreBtn = false;
                remoteViewDetailModel.isDisableNextBtn = false;
            }
        }
        return remoteViewDetailModel;
    }

    public PlayableModel getCurrentPlayableModel() {
        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        if (service != null && service.getPlayListControl() != null) {
            PlayableModel playableModel = service.getPlayListControl().getCurrentPlayableModel();
            if (mIsFromPushGuard && mPlayableModel != null) {
                return mPlayableModel;
            }
            return playableModel;
        }
        return null;
    }

    public void getTingList() {
        Logger.e("sjc", "getTingListList");
        Map<String, String> params = new HashMap<>();
        params.put(DTransferConstants.TRACK_BASE_URL, getUrl("keep-alive-player-mobile/playNotifyContent/query/ts-"));
        CommonRequestForMain.getTrackList(params, new IDataCallBack<CommonTrackList>() {
            @Override
            public void onSuccess(@Nullable CommonTrackList data) {
                if (data == null) {
                    doLogicWhileFail();
                    return;
                }
                if (data.getTracks() != null && data.getTracks().size() > 0) {
                    mCurrentRecommendType = 4;
                    cancelAlarm(ACTION_PUSH_GUARD_ALARM_TYPE);
                    mCurrentIndex = 0;
                    mCommonTrackList = data;
                    mPlayableModel = (PlayableModel) mCommonTrackList.getTracks().get(mCurrentIndex);
                    if (mPlayableModel instanceof Track) {
                        Logger.e("sjc", "type Change  = " + ((Track) mPlayableModel).getTrackTitle());
                    }
                    XmPlayerService service = XmPlayerService.getPlayerSrvice();
                    if (service != null) {
                        try {
                            service.updateNotification();
                            service.sendPushRecommendDataChange();
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                    traceRecommendSwitch();
                    makeTypeAlarm(true);
                } else {
                    doLogicWhileFail();
                }
            }

            @Override
            public void onError(int code, String message) {
                Logger.e("sjc", "getTingListList fail = " + message);
                doLogicWhileFail();
            }
        });
    }

    private void doLogicWhileFail() {
        if (!mIsFromPushGuard) {
            onDestroy();
            return;
        }
//        if (type == 2) {
//            getTingList(3);
//        } else {
//            makeTypeAlarm(false);
//        }
        makeTypeAlarm(true);
    }

    public boolean playListWithRecommend(boolean playForce, int increase) {
        if (!mIsFromPushGuard) {
            return false;
        }
        IPushGuardUbtTrace pushGuardUbtTrace = RouterServiceManager.getInstance().getService(IPushGuardUbtTrace.class);
        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        shouldStartPlayTraceFlag = true;
        if (mCurrentRecommendType == 1 || mCommonTrackList == null) {
            PlayableModel playableModel = getCurrentPlayableModel();
            if (pushGuardUbtTrace != null) {
                pushGuardUbtTrace.traceRecommendStartPlay(false, playableModel != null ? playableModel.getDataId() : 0L);
            }
            return false;
        }
        boolean shouldReturnFalse = false;
        if (increase == -1 && mCurrentIndex - 1 >= 0) {
            mCurrentIndex--;
        } else if (increase == 1 && mCommonTrackList != null && mCommonTrackList.getTracks() != null && mCommonTrackList.getTracks().size() > mCurrentIndex + 1) {
            mCurrentIndex++;
        } else if (increase == 1 || increase == -1) {
            shouldReturnFalse = true;
        }
        try {
            service.setPlayList(mCommonTrackList.getParams(), mCommonTrackList.getTracks());
            service.play(mCurrentIndex, playForce);
            if (pushGuardUbtTrace != null) {
                Track track = (Track) mCommonTrackList.getTracks().get(mCurrentIndex);
                pushGuardUbtTrace.traceRecommendStartPlay(false, track.getDataId());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return !shouldReturnFalse;
    }

    private String getUrl(String url) {
        return BaseUtil.chooseEnvironmentUrl("http://mobile.ximalaya.com/") + url;
    }

    public void setFromPushGuard(boolean fromPushGuard) {
        mIsFromPushGuard = fromPushGuard;
        if (!mIsFromPushGuard) {
            onDestroy();
        }
    }

    /**
     * 推荐过程中 如果用户使用线控开播，播的是原来的声音，这时候要重置一下标志位
     * */
    public void resetPushGuardFlagAfterPlay() {
        if (mIsFromPushGuard) {
            getHandler().postDelayed(() -> {
                if (mIsFromPushGuard) {
                    mIsFromPushGuard = false;
                    onDestroy();
                }
            }, 5000);
        }
    }

    public Handler getHandler() {
        if (mHandler == null) {
            mHandler = new Handler(Looper.getMainLooper());
        }
        return mHandler;
    }

    public void traceGuardWhileUbtInit(int guardSource, boolean openPlayer, boolean isPlayerHasLived, Context context, boolean judgeByServer, int jPushGuardType) {
        IPushGuardUbtTrace pushGuardUbtTrace = RouterServiceManager.getInstance().getService(IPushGuardUbtTrace.class);
        if (pushGuardUbtTrace != null) {
            traceGuard(guardSource, openPlayer, isPlayerHasLived, context, judgeByServer, jPushGuardType);
        } else {
            getHandler().postDelayed(() -> {
                mReTryCount++;
                if (mReTryCount >= 5) {
                    return;
                }
                traceGuardWhileUbtInit(guardSource, openPlayer, isPlayerHasLived, context, judgeByServer, jPushGuardType);
            }, 1000);
        }
    }

    /**
     * 保活拉起埋点
     * */
    private void traceGuard(int guardSource, boolean openPlayer, boolean isPlayerHasLived, Context context, boolean judgeByServer, int jPushGuardType) {
        String reason = "0";
        if (judgeByServer && !openPlayer) {
            reason = "3";
        } else if (!judgeByServer && !openPlayer) {
            reason = "2";
        } else if (isPlayerHasLived) {
            reason = "1";
        }
        IPushGuardUbtTrace pushGuardUbtTrace = RouterServiceManager.getInstance().getService(IPushGuardUbtTrace.class);
        if (pushGuardUbtTrace != null) {
            pushGuardUbtTrace.traceGuard(guardSource, reason, isSystemNotificationEnable(context) ? "notification" : "none", jPushGuardType);
        }
        try {
//            Logger.e("sjc", "traceGuard,  openPlayer = " + openPlayer + "  , isPlayerHasLived = " + isPlayerHasLived);
            JSONObject reportData = new JSONObject();
            reportData.put("openPlayer", openPlayer);
            reportData.put("isPlayerHasLived", isPlayerHasLived);
            XmLogger.syncLog("apm", "pushGuard", reportData.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 保活点击埋点
     * */
    public void traceGuardRealClick(boolean report, boolean fromPlay, String item) {
        if (!report) {
            Logger.e("sjc", "traceGuardRealClick no report = " + mIsFromPushGuard);
            if (mIsFromPushGuard) {
                setFromPushGuard(false);
            }
            return;
        }
        if (mIsFromPushGuard) {
            String type = "1";
            String trackId = "";
            String recTrack = "";
            String recSrc = "";
            String ubtTraceId = "";
            PlayableModel model = getCurrentPlayableModel();
            if (model instanceof Track) {
                Track track = (Track) model;
                type = track.getIsFromPushRecommend() + "";
                if ("0".equals(type)) {
                    type = "1";
                }
                trackId = track.getDataId() + "";
                recTrack = track.getRecTrack();
                recSrc = track.getRecSrc();
                ubtTraceId = track.getUbtTraceId();
            }
            setFromPushGuard(false);
            IPushGuardUbtTrace pushGuardUbtTrace = RouterServiceManager.getInstance().getService(IPushGuardUbtTrace.class);
            if (pushGuardUbtTrace != null) {
                pushGuardUbtTrace.traceGuardRealClick(item, type, trackId, recTrack, recSrc, ubtTraceId);
            }
            try {
//                Logger.e("sjc", "traceGuardReal,  fromPlay = " + fromPlay);
                JSONObject reportData = new JSONObject();
                reportData.put("fromPlay", fromPlay);
                XmLogger.syncLog("apm", OsUtil.isHarmonyOs() ? "pushGuardRealForHarmony" : "pushGuardReal", reportData.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    private void traceRecommendSwitch() {
        boolean firstSwitch = mFirstSwitch;
        IPushGuardUbtTrace pushGuardUbtTrace = RouterServiceManager.getInstance().getService(IPushGuardUbtTrace.class);
        if (pushGuardUbtTrace != null) {
            String trackId = "";
            String recTrack = "";
            String recSrc = "";
            String ubtTraceId = "";
            PlayableModel model = getCurrentPlayableModel();
            if (model instanceof Track) {
                Track track = (Track) model;
                trackId = track.getDataId() + "";
                recTrack = track.getRecTrack();
                recSrc = track.getRecSrc();
                ubtTraceId = track.getUbtTraceId();
            }
            pushGuardUbtTrace.traceRecommendSwitch(firstSwitch, mCurrentRecommendType + "", trackId, recTrack, recSrc, ubtTraceId);
        }
        mFirstSwitch = false;
    }

    public boolean isSystemNotificationEnable(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) { //19以上可检测
            return NotificationManagerCompat.from(context)
                    .areNotificationsEnabled();
        }
        return true;
    }
}
