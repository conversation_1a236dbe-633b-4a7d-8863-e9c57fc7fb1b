package com.ximalaya.ting.android.opensdk.push;

import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationPendingIntentCreateUtils;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.util.Random;

/**
 * Author: <PERSON> Shi
 * Email: <EMAIL>
 * Date: 2023/2/14
 * Description：运行在播放进程
 */
public class PushNotificationDeleteReceiver extends BroadcastReceiver {
    public static final String EXTRA_PUSH_GROUP_NOTIFY_ID = "extra_push_group_notify_id";
    public static final String EXTRA_PUSH_GROUP_NAME = "extra_push_group_name";
    public static final String EXTRA_PUSH_BTN_NAME = "extra_push_btn_name";
    public static final String EXTRA_PUSH_BTN_NAME_NOTIFY = "extra_push_btn_name_notify";
    public static final String EXTRA_PUSH_BTN_NAME_GROUP = "extra_push_btn_name_group";
    public static final String ACTION_PUSH_DELETE = "com.ximalaya.ting.android.action.action_push_delete";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null) {
            return;
        }
        String btnName = intent.getStringExtra(EXTRA_PUSH_BTN_NAME);
        String deleteGroupName = intent.getStringExtra(EXTRA_PUSH_GROUP_NAME);
        int notifyId = intent.getIntExtra(EXTRA_PUSH_GROUP_NOTIFY_ID, -1);

        if (EXTRA_PUSH_BTN_NAME_GROUP.equals(btnName)) {
            if (deleteGroupName != null && PushNotificationFilterManager.getInstance().mGroupEnable) {
                Logger.e("sjc", "group remove = " + deleteGroupName);
                PushNotificationFilterManager.getInstance().getNotificationMap().remove(deleteGroupName);
                PushNotificationFilterManager.getNotifyGroupMMKVUtil().removeByKey(deleteGroupName);
                NotificationManager notificationManager = SystemServiceManager.getNotificationManager(context);
                notificationManager.cancel(notifyId);
            }
        } else if (EXTRA_PUSH_BTN_NAME_NOTIFY.equals(btnName)) {
            Logger.e("sjc", "delete = " + deleteGroupName +  "   notifyId = " + notifyId);
            PushNotificationFilterManager.decreaseChannelGroupCount(context, deleteGroupName, notifyId);
        }
    }

    public static PendingIntent getDeleteIntent(Context context, String groupName, String btnName, int groupNotifyId) {
        Intent playIntent = new Intent();
        playIntent.setClass(context, PushNotificationDeleteReceiver.class);
        playIntent.setAction(ACTION_PUSH_DELETE);
        playIntent.putExtra(EXTRA_PUSH_GROUP_NAME, groupName);
        playIntent.putExtra(EXTRA_PUSH_BTN_NAME, btnName);
        playIntent.putExtra(EXTRA_PUSH_GROUP_NOTIFY_ID, groupNotifyId);
        return PendingIntent.getBroadcast(context, new Random().nextInt(1000), playIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
    }
}
