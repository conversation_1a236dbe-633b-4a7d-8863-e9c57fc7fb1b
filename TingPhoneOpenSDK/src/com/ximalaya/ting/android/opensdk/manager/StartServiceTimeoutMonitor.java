package com.ximalaya.ting.android.opensdk.manager;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.ximalaya.ting.android.opensdk.model.PlayExceptionModel;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayExceptionCollector;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;
import com.ximalaya.ting.android.player.Logger;
import com.ximalaya.ting.android.xmutil.ProcessUtil;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.util.List;

/**
 * Created by jack.qin on 2022/1/17.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class StartServiceTimeoutMonitor {
    private static volatile boolean hasInit;
    private static long sTimeRecord = 0;
    private static long sTimeRecordBegin = 0;
    private static volatile boolean hasCheck;
    private static final String TAG = "StartServiceTimeoutMonitor";
    private static int PLAYER_PROCESS_PID;

    //探测service启动timeout 主进程
    public synchronized static void checkStart(Context context) {
        if (hasCheck) {
            return;
        }
        hasCheck = true;
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                try {
                    ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
                    List<ActivityManager.RunningAppProcessInfo> processes = am.getRunningAppProcesses();
                    if (processes == null) {
                        return;
                    }

                    for (ActivityManager.RunningAppProcessInfo item : processes) {
                        if (item.processName.contains("player")) {
                            Logger.i(TAG, "player process start, pid = " + item.pid);
                            PLAYER_PROCESS_PID = item.pid;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }, 4000);

        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                try {
                    ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
                    List<ActivityManager.RunningAppProcessInfo> processes = am.getRunningAppProcesses();
                    if (processes == null) {
                        return;
                    }
                    int pid = 0;
                    for (ActivityManager.RunningAppProcessInfo item : processes) {
                        if (item.processName.contains("player")) {
                            Logger.i(TAG, "player process start, pid = " + item.pid);
                            pid = item.pid;
                        }
                    }
                    if (PLAYER_PROCESS_PID != 0 && pid != PLAYER_PROCESS_PID) {
                        String str = StartServiceTimeoutMonitor.readStageRecord(context);
                        Logger.i(TAG, "player process start fail" + str);
                        PlayExceptionModel playExceptionModel = new PlayExceptionModel();
                        playExceptionModel.startForegroundServiceTimeout = true;
                        playExceptionModel.errorMsg = str;
                        PlayExceptionCollector.postDataMainProcess(playExceptionModel, context);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }, 15000);
    }

    //初始化文件记录 播放进程
    public synchronized static void init(Context context) {
        if (!BaseUtil.isPlayerProcess(context)) {
            return;
        }
        if (hasInit) {
            return;
        }
        hasInit = true;
        if (context == null) {
            return;
        }
        try {
            sTimeRecord = System.currentTimeMillis();
            sTimeRecordBegin = System.currentTimeMillis();
            File file = new File(context.getFilesDir() + File.separator + "start_service_record.txt");
            if (file.exists()) {
                file.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //读取文件内容 主进程
    private synchronized static String readStageRecord(Context context) {
        try {
            if (context == null) {
                return "";
            }
            File file = new File(context.getFilesDir() + File.separator + "start_service_record.txt");
            if (!file.exists()) {
                return "";
            }
            String data;
            StringBuilder stringBuilder = new StringBuilder();
            FileReader fileReader = new FileReader(file);
            BufferedReader br = new BufferedReader(fileReader);
            while ((data = br.readLine()) != null) {
                stringBuilder.append(data).append(" ");
            }
            fileReader.close();
            return stringBuilder.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    //写入文件内容 播放进程
    public synchronized static void startStageRecord(String stageName, Context context) {
        if (!BaseUtil.isPlayerProcess(context)) {
            return;
        }
        if (hasInit) {
            try {
                if (context == null) {
                    return;
                }
                if (sTimeRecordBegin != 0 && (System.currentTimeMillis() - sTimeRecordBegin) > 30000) {
                    return;
                }
                Logger.i(TAG, "write stage record " + stageName);
                File file = new File(context.getFilesDir() + File.separator + "start_service_record.txt");
                if (!file.exists()) {
                    file.createNewFile();
                }
                long stageTime;
                if (sTimeRecord == 0) {
                    stageTime = 0;
                } else {
                    stageTime = System.currentTimeMillis() - sTimeRecord;
                }
                sTimeRecord = System.currentTimeMillis();
                FileWriter fileWriter = new FileWriter(file, true);
                fileWriter.write(stageName + " " + stageTime);
                fileWriter.flush();
                fileWriter.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

}
