package com.ximalaya.ting.android.opensdk.manager;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;

import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayExceptionModel;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.receive.ScreenStatusReceiver;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayExceptionCollector;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.ProcessUtil;

import java.lang.reflect.Method;

/**
 * Created by jack.qin on 2022/1/7.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class WifiSleepMonitor {
    private final static String TAG = "WifiSleepMonitor";
    private int wifiStateWhenScreenOff = -1; //-1 screenon,0 close ,1 open
    private boolean hasInit;
    private Context context;
    private boolean hasPostData;
    private boolean screenState = true;

    protected WifiSleepMonitor() {
    }

    public static WifiSleepMonitor getInstance() {
        return WifiSleepMonitor.HolderClass.instance;
    }

    private static class HolderClass {
        private final static WifiSleepMonitor instance = new WifiSleepMonitor();
    }

    private final ScreenStatusReceiver.IScreenStatusListener mIScreenStatusListener = new ScreenStatusReceiver.IScreenStatusListener() {
        @Override
        public void onScreenStatusChange(int screenStatus) {
            screenStateChange(screenStatus);
        }
    };

    public void init(Context context) {
        if (hasInit) {
            return;
        }
        hasInit = true;
        ScreenStatusReceiver.addScreenStatusListener(mIScreenStatusListener);
        this.context = context;
    }

    private void screenStateChange(int screenStatus) {
        if (screenStatus == ScreenStatusReceiver.IScreenStatusListener.SCREEN_OFF) {
            screenState = false;
            wifiStateWhenScreenOff = isWifiOn() ? 1 : 0;
            Logger.i(TAG, "screen off, wifiStateWhenScreenOff:" + wifiStateWhenScreenOff);
        } else if (screenStatus == ScreenStatusReceiver.IScreenStatusListener.SCREEN_ON) {
            screenState = true;
            wifiStateWhenScreenOff = -1;
            Logger.i(TAG, "screnn on, clear wifi state");
        }
    }

    public int getWifiStateWhenScreenOff() {
        return wifiStateWhenScreenOff;
    }

//    private boolean wifiSleepHappen = false;

    //call by playError
    public void wifiCloseInSleep() {
        boolean wifiSleep = wifiStateWhenScreenOff == 1 && !isWifiOn();
        if (wifiSleep && (!hasPostData || ConstantsOpenSdk.isDebug)) {
            hasPostData = true;
            Logger.i(TAG, "wifi sleep get");
            PlayExceptionModel playExceptionModel = new PlayExceptionModel();
            playExceptionModel.closeWifi = true;
            PlayExceptionCollector.postData(playExceptionModel);
            MmkvCommonUtil.getInstance(context).saveBoolean(PreferenceConstantsInOpenSdk.KEY_WIFI_SLEEP_HAPPEN, true);
        }
    }

    public boolean wifiCloseInSleepHappen(Context context) {
        if (context == null) {
            return false;
        }
        boolean wifiSleepHappen = MmkvCommonUtil.getInstance(context).getBoolean(PreferenceConstantsInOpenSdk.KEY_WIFI_SLEEP_HAPPEN, false);
        Logger.i(TAG, "wifiCloseInSleepHappen " + wifiSleepHappen);
        return wifiSleepHappen;
    }

    private boolean isWifiOn() {
        boolean isWifiOpen = false;
        try {
            boolean isWifiOn = Settings.Global.getInt(context.getContentResolver(), Settings.Global.WIFI_ON) != 0;
            @SuppressLint("WifiManagerPotentialLeak") WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
            boolean isWifiEnabled = wifiManager.isWifiEnabled();
            isWifiOpen = isWifiOn && isWifiEnabled;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return isWifiOpen;
    }

    public static void statToXDCSError(String module, String errorStr) {
        try {
            Class xdcsCollectUtil = Class.forName("com.ximalaya.ting.android.framework.manager.XDCSCollectUtil");
            if (xdcsCollectUtil != null) {
                Method method = xdcsCollectUtil.getDeclaredMethod("statErrorToXDCS", String.class, String.class);
                if (method != null) {
                    method.invoke(null, module, errorStr);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isScreenOn() {
        return screenState;
    }
}
