package com.ximalaya.ting.android.opensdk.util;

import android.content.Context;

import com.ximalaya.ting.android.opensdk.constants.MmkvConstantsInOpenSdk;

/**
 * Created by zhifu.zhang on 5/14/2020 AD.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18362080562
 * @desc mmkv通用工具类
 * 兼容迁移SharedPreferenceUtil(ting_data)数据功能
 * 如需处理迁移其他sp文件数据，可继承该类，重新创建SharedPreferencesUtil
 */
public class TingReader<PERSON>KVUtil extends BaseMmkvUtil {

    private static volatile TingReaderMMKVUtil INSTANCE;

    protected TingReaderMMKVUtil(Context context, String name) {
        super(context, name);
    }

    protected SharedPreferencesUtil createSharedPreferenceUtil(Context context) {
        return SharedPreferencesUtil.getInstance(mAppContext);
    }

    public static TingReaderMMKVUtil getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (TingReaderMMKVUtil.class) {
                if (INSTANCE == null) {
                    INSTANCE = new TingReaderMMKVUtil(context, MmkvConstantsInOpenSdk.MMKV_FILE_TING_READER_FILE);
                }
            }
        }
        return INSTANCE;
    }
}
