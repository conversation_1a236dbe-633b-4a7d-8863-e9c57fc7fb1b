package com.ximalaya.ting.android.opensdk.util;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.encryptservice.EncryptUtil;
import com.ximalaya.ting.android.opensdk.constants.MmkvConstantsInOpenSdk;

import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 播放器书籍mmkv存储器(因为有书籍关联的专辑有几万本,所以单独建一个)
 */
public class BookMmkvUtil extends BaseMmkvUtil {

    private static volatile BookMmkvUtil INSTANCE;

    protected BookMmkvUtil(Context context, String name) {
        super(context, name);
    }

    public static BookMmkvUtil getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (BookMmkvUtil.class) {
                if (INSTANCE == null) {
                    INSTANCE = new BookMmkvUtil(context, "play_book_mmkv_file_name");
                }
            }
        }
        return INSTANCE;
    }

}
