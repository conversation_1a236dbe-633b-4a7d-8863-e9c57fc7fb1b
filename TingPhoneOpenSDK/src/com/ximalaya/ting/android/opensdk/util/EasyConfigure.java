package com.ximalaya.ting.android.opensdk.util;

import android.text.TextUtils;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.configurecenter.exception.NonException;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 配置中心辅助类
 * 1.简化配置，获取过，就会自动从配置中心获取
 * 2.方便调试，支持 adb shell setprop 测试修改
 * <p>
 * 注意：如果太早获取，首次一般会走默认值，下次启动时，会从配置中心获取
 */
public class EasyConfigure {
    public static final String TAG = "EasyConfigure";
    public static final String BOOLEAN_KEY_NEED_GET_FROM_CONFIGURE = "BOOLEAN_KEY_NEED_GET_FROM_CONFIGURE";
    private final static Object sLock = new Object();
    private final static Map<String, Boolean> sKeySwitchMap = new ConcurrentHashMap<>();

    /**
     * 从配置中心或者 MMKV 获取，如果不存在，则记录，下次启动时从配置中心里获取，保存到 MMKV
     * group 是 android
     *
     * @param key
     * @param defaultValue
     * @return
     */
    public static boolean getBoolean(String key, boolean defaultValue) {
        if (TextUtils.isEmpty(key)) {
            return defaultValue;
        }

        if (ConstantsOpenSdk.isDebug) {
            String prop = debugHandled(key);
            if (!"-1".equals(prop)) {
                return "1".equals(prop);
            }
        }

        Boolean isOpen = sKeySwitchMap.get(key);
        if (isOpen != null) {
            Logger.d(TAG, "getBoolean from sKeySwitchMap: " + isOpen);
            return isOpen;
        }

        try {
            boolean result = ConfigureCenter.getInstance().getBool("android", key);
            Logger.d(TAG, "getBoolean from ConfigureCenter: " + result);
            sKeySwitchMap.put(key, result);
            return result;
        } catch (NonException e) {
            e.printStackTrace();
            Logger.e(TAG, "not found from ConfigureCenter: " + key);
            //没有，需要下次去获取
            saveNeedGetFromConfigureCenter(key, 1);
        }

        boolean result = MMKVUtil.getInstance().getBoolean(key, defaultValue);
        Logger.d(TAG, "getBoolean from MMKV: " + result);
        sKeySwitchMap.put(key, result);
        return result;
    }

    /**
     * 保存需要从配置中心获取值的 key
     * 一般走到这，是一启动就调用配置的场景
     *
     * @param key
     * @param type 1: boolean, 2: string, 3: long
     */
    private static void saveNeedGetFromConfigureCenter(String key, int type) {
        synchronized (sLock) {
            String typeKey = getTypeKey(type);
            ArrayList<String> arrayList = MMKVUtil.getInstance().getArrayList(typeKey);
            if (arrayList == null) {
                arrayList = new ArrayList<>();
            }
            if (arrayList.contains(key)) {
                return;
            }
            arrayList.add(key);
            MMKVUtil.getInstance().saveArrayList(typeKey, arrayList);

            Logger.d(TAG, "saveNeedGetFromConfigureCenter: " + key + ", list size: " + arrayList.size());
        }
    }

    /**
     * 获取不同类型值的 key
     *
     * @param type
     * @return
     */
    private static String getTypeKey(int type) {
        // TODO: 2024/6/26 支持更多类型
        return BOOLEAN_KEY_NEED_GET_FROM_CONFIGURE;
    }

    /**
     * adb shell setprop debug.xima.xxx 1 即可强制打开
     *
     * @param key
     * @return
     */
    private static String debugHandled(String key) {
        int propKeyMaxLength = Math.min(10, key.length() - 1);
        String shortKey = key.substring(0, propKeyMaxLength);
        String propKey = "debug.xima." + shortKey;

        String propValue = getSystemProperty(propKey, "-1");

        Logger.d(TAG, String.format("debugHandled, getBoolean key: %s, value: %s", propKey, propValue));
        return propValue;
    }


    /**
     * 获取系统属性
     *
     * @param key          ro.build.display.id
     * @param defaultValue 默认值
     * @return 系统操作版本标识
     */
    public static String getSystemProperty(String key, String defaultValue) {
        try {
            Class<?> SystemProperties = Class.forName("android.os.SystemProperties");
            Method m = SystemProperties.getMethod("get", String.class, String.class);
            String result = (String) m.invoke(null, key, defaultValue);
            return result;
        } catch (Exception e) {
            Logger.e(e);
        }
        return defaultValue;
    }

    /**
     * 配置中心更新了，查询需要的 key
     */
    public static void onConfigureCenterUpdate() {
        Logger.d(TAG, "onConfigureCenterUpdate >>>");

        ArrayList<String> booleanKeyList = null;
        synchronized (sLock) {
            booleanKeyList = MMKVUtil.getInstance().getArrayList(BOOLEAN_KEY_NEED_GET_FROM_CONFIGURE);
        }
        if (booleanKeyList == null || booleanKeyList.isEmpty()) {
            return;
        }
        for (String key : booleanKeyList) {
            try {
                boolean result = ConfigureCenter.getInstance().getBool("android", key);
                Logger.d(TAG, "getBoolean from configure center, key:" + key + ", value:" + result);
                MMKVUtil.getInstance().saveBoolean(key, result);
            } catch (NonException e) {
                e.printStackTrace();
            }
        }
    }
}
