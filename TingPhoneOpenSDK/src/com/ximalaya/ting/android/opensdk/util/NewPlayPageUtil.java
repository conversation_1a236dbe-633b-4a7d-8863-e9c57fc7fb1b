package com.ximalaya.ting.android.opensdk.util;

import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;

public class NewPlayPageUtil {

    public static void saveYPageStyle(boolean isYPage) {
        MMKVUtil.getInstance().saveBoolean("yPageStyle", isYPage);
    }

    public static boolean isYPageStyle(){
        return MMKVUtil.getInstance().getBoolean("yPageStyle", false);
    }

    public static void saveRnPageStyle(boolean isRnPage) {
        MMKVUtil.getInstance().saveBoolean("rnPageStyle", isRnPage);
    }

    public static boolean isRnPageStyle(){
        return MMKVUtil.getInstance().getBoolean("rnPageStyle", false);
    }

    public static String getNewPageParams(){
        if(isYPageStyle()){
            return "2";
        }

        if (isRnPageStyle()){
            return "1";
        }
        return "0";
    }
}
