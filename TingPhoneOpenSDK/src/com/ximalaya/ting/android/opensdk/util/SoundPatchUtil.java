package com.ximalaya.ting.android.opensdk.util;

import com.ximalaya.ting.android.opensdk.player.service.IXmCommonBusinessDispatcher;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.mixplay.MixPlayerService;

/**
 * Created by 5Greatest on 2021.04.29
 *
 * <AUTHOR>
 * On 2021/4/29
 */
public class SoundPatchUtil {
    public static boolean isPlayingOtherThing() {
        XmPlayerService playerService = XmPlayerService.getPlayerSrvice();
        boolean isPlayingConsiderMixPlayer = (null != playerService && playerService.isPlaying()) || MixPlayerService.getMixService().isMixPlaying();
        IXmCommonBusinessDispatcher dispatcher = null == playerService ? null : playerService.getIXmCommonBusinessDispatcher();
        boolean isPlayingVideoAd = false;
        boolean isPlayingFreeListenAd = false;
        try {
            isPlayingVideoAd = null != dispatcher && dispatcher.isPlayingVideoAd();
            isPlayingFreeListenAd = null != dispatcher && dispatcher.isPlayingFreeListenAd();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return isPlayingConsiderMixPlayer || isPlayingVideoAd || isPlayingFreeListenAd;
    }
}
