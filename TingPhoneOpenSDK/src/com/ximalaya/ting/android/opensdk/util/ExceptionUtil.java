package com.ximalaya.ting.android.opensdk.util;

import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * <AUTHOR>
 * @date 2024/8/15 17:55
 */
public class ExceptionUtil {
    private static Boolean sIsOpen = null;

    private static boolean isOpen() {
        if (sIsOpen == null) {
            sIsOpen = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEM_TRACE_EXCEPTION, true);
        }
        return sIsOpen;
    }

    public static Throwable getRootCause(Throwable error) {
        if (!isOpen()) {
            return null;
        }
        if (error == null) {
            return null;
        }
        Throwable pre = error;
        Throwable exception = error;
        int count = 0;

        while (exception != null) {
            pre = exception;
            exception = exception.getCause();
            ++count;
            if (count > 10) {
                break;
            }
        }
        return pre;
    }

    public static String getCauseTrace(Throwable error) {
        if (!isOpen()) {
            return null;
        }
        if (error == null) {
            return null;
        }
        try {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            error.printStackTrace(pw);
            pw.flush();
            return sw.toString();
        } catch (Throwable e) {
            e.printStackTrace();
            return "exception error";
        }
    }
}
