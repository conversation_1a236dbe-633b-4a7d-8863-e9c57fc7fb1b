package com.ximalaya.ting.android.opensdk.util;

import android.content.Context;

import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;

/**
 * Created by Wolf<PERSON>u on 2023/7/28.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class PrivacyUtil {

    public static boolean hasAgreePrivacyPolicy(Context context) {
        return MmkvCommonUtil.getInstance(context).getBooleanCompat(
                PreferenceConstantsInOpenSdk.TINGMAIN_KEY_PRIVACY_POLICY_AGREED_NEW);
    }
}
