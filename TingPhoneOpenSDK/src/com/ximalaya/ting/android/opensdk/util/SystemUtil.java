package com.ximalaya.ting.android.opensdk.util;

import android.app.ActivityManager;
import android.app.AppOpsManager;
import android.content.ContentResolver;
import android.content.Context;
import android.os.Build;
import android.os.PowerManager;
import android.provider.Settings;
import android.text.TextUtils;

import com.ximalaya.ting.android.xmutil.BuildProperties;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.ProcessUtil;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import androidx.annotation.NonNull;

/**
 * 类的大体描述放在这里。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18621868330
 * @wiki Wiki网址放在这里
 * @server 服务端开发人员放在这里
 * @since 2020/2/28
 */
public class SystemUtil {
    private static final String KEY_MIUI_VERSION_CODE = "ro.miui.ui.version.code";
    private static final String KEY_MIUI_VERSION_NAME = "ro.miui.ui.version.name";
    private static final String KEY_MIUI_INTERNAL_STORAGE = "ro.miui.internal.storage";

    private static final String TAG = "SystemUtil";
    private static final int NEW_VERSION_API_LEVEL = 27;
    private static Boolean isHuaweiDevice;
    private static Boolean isHonorDevice;
    private static Boolean isVivoDevice;
    private static Boolean isOppoDevice;
    private static String emuiVersion;
    private static Boolean isEmui111OrOver;
    private static Boolean isHuaweiNewSystem;
    private static Boolean isSystemOat;

    private static boolean hasCheckXiaoMi = false;
    private static boolean isXiaoMi = false;

    public static final int WIFI_SLEEP_POLICY_DEFAULT = 0;
    public static final int WIFI_SLEEP_POLICY_NEVER = 2;
    public static final int WIFI_SLEEP_POLICY_NEVER_WHILE_PLUGGED = 1;

    public static boolean isScreenOn(Context context) {
        return SystemServiceManager.isScreenOn(context);
    }

    public static boolean isIgnoringBatteryOptimizations(Context context) {
        if (context == null) {
            return false;
        }
        boolean isIgnoring = false;
        PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        if (powerManager != null ) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                isIgnoring = powerManager.isIgnoringBatteryOptimizations(context.getPackageName());
            } else {
                return true;
            }
        }
        return isIgnoring;
    }

    public static boolean isKeepNetConnectInSleepMode(Context context) {
        if (context == null) {
            return false;
        }
        int wifiSleepPolicy = -1;
        ContentResolver cv = context.getContentResolver();
        try {
            wifiSleepPolicy = Settings.System.getInt(cv,
                    Settings.Global.WIFI_SLEEP_POLICY);
        } catch (Settings.SettingNotFoundException e) {
            wifiSleepPolicy = WIFI_SLEEP_POLICY_NEVER;
        }
        Logger.d("SystemUtil", "wifiSleepPolicy...." + wifiSleepPolicy + "\n");
        return wifiSleepPolicy == WIFI_SLEEP_POLICY_NEVER;
    }

    public static boolean isSystemOat(Context context){
        if (isSystemOat == null){
            String fingerPrint = MmkvCommonUtil.getInstance(context).getString("finger_print","");
            if (!TextUtils.isEmpty(fingerPrint) && !fingerPrint.equals(Build.FINGERPRINT)) {
                isSystemOat = true;
                MmkvCommonUtil.getInstance(context).saveString("finger_print", Build.FINGERPRINT);
            } else {
                isSystemOat = false;
            }
            if (TextUtils.isEmpty(fingerPrint)) {
                MmkvCommonUtil.getInstance(context).saveString("finger_print", Build.FINGERPRINT);
            }
        }
        return isSystemOat;
    }

    private static final boolean VM_IS_ART = isVmArt(System.getProperty("java.vm.version"));

    private static boolean isVmArt(String versionString) {
        boolean isArt = false;
        if (versionString != null) {
            Matcher matcher = Pattern.compile("(\\d+)\\.(\\d+)(\\.\\d+)?").matcher(versionString);
            if (matcher.matches()) {
                try {
                    int major = Integer.parseInt(matcher.group(1));
                    int minor = Integer.parseInt(matcher.group(2));
                    isArt = (major > 2)
                            || ((major == 2)
                            && (minor >= 1));
                } catch (NumberFormatException e) {
                    // let isMultidexCapable be false
                }
            }
        }
        return isArt;
    }

    public static boolean isVmArt() {
        return VM_IS_ART || Build.VERSION.SDK_INT >= 21;
    }

    public static boolean isNeedCheckSystemSetting(Context context){
        return !isIgnoringBatteryOptimizations(context) || !isKeepNetConnectInSleepMode(context) || isPowerSaveMode(context);
    }

    public static String getProOomAdjByFile(){
        try {
            File file = new File("/proc/" + android.os.Process.myPid() + "/" + "oom_score_adj");
            BufferedReader mBufferedReader = new BufferedReader(new FileReader(file));
            String processName = mBufferedReader.readLine().trim();
            mBufferedReader.close();
            return processName;
        } catch (Throwable e) {
            e.printStackTrace();
            return "";
        }
    }

    public static int  getProImportance(Context context){
        if (context == null) {
            return 0;
        }
        String processName = ProcessUtil.getProcessName(context);
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (am == null) {
            return 0;
        }
        List<ActivityManager.RunningAppProcessInfo> processes = null;
        try {
            processes = am.getRunningAppProcesses();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (processes == null || processes.size() == 0) {
            return 0;
        }

        for (ActivityManager.RunningAppProcessInfo item : processes) {
            if (item.processName.startsWith(processName)) {
                return item.importance;
            }
        }
        return 0;
    }



    @Deprecated
    public static String getProOomAdj(){
        int pid = android.os.Process.myPid();
        try {
            String result = runCmd("cat proc/"+pid+"/oom_adj");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

//    public static String getFocusStackPeek(){
//        return getProOomAdj();
//    }

    public static String runCmd(String cmd){
        Runtime runtime = Runtime.getRuntime();
        Process proc = null;

        String result = "";
        BufferedReader reader = null;
        StringBuilder resultBuilder = null;
        try {
            final String command = cmd.trim();
            proc = runtime.exec(command);
            proc.waitFor();
            InputStream inputStream = proc.getInputStream();
            result = "";

            reader = new BufferedReader(new InputStreamReader(inputStream));
            resultBuilder = new StringBuilder();
            String line = "";
            while (null != (line = reader.readLine())) {
                resultBuilder.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            if (reader != null){
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        if (resultBuilder != null){
            String responseStr = resultBuilder.toString();
            result = responseStr.toLowerCase().trim();
        }
        return result;
    }

    public static String getSystemSettings(Context context) {
        try {
            StringBuilder builder = new StringBuilder();
            return builder.append("runAny:").append(getRunAnyInBackground(context)).append(" ")
                    .append("startFg:").append(getStartForeground(context)).append(" ")
                    .append("runBg:").append(getRunInBackground(context)).toString();
        } catch (Exception e) {}
        return "";
    }

    public static int getRunAnyInBackground(Context context) {
        int code = -1;
        AppOpsManager appOpsManager = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
            appOpsManager = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
            code = appOpsManager.unsafeCheckOpNoThrow("android:run_any_in_background", context.getApplicationInfo().uid, context.getApplicationInfo().packageName);
        }
        return code;
    }

    public static int getStartForeground(Context context) {
        int code = -1;
        AppOpsManager appOpsManager = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
            appOpsManager = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
            code = appOpsManager.unsafeCheckOpNoThrow("android:start_foreground", context.getApplicationInfo().uid, context.getApplicationInfo().packageName);
        }
        return code;
    }

    public static int getRunInBackground(Context context) {
        int code = -1;
        AppOpsManager appOpsManager = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
            appOpsManager = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
            code = appOpsManager.unsafeCheckOpNoThrow("android:run_in_background", context.getApplicationInfo().uid, context.getApplicationInfo().packageName);
        }
        return code;
    }
    public static boolean isOppoDevice() {
        if (isOppoDevice != null) {
            return isOppoDevice;
        }
        String manufacturer = Build.MANUFACTURER;
        isOppoDevice = !TextUtils.isEmpty(manufacturer)
                && ("oppo".equalsIgnoreCase(manufacturer) || manufacturer.toLowerCase(Locale.CHINA).contains("oppo"));
        return isOppoDevice;
    }

    public static boolean isVivoDevice() {
        if (isVivoDevice != null) {
            return isVivoDevice;
        }
        String manufacturer = Build.MANUFACTURER;
        isVivoDevice = !TextUtils.isEmpty(manufacturer)
                && ("vivo".equalsIgnoreCase(manufacturer) || manufacturer.toLowerCase(Locale.CHINA).contains("vivo"));
        return isVivoDevice;
    }

    /**
     * 是否华为设备
     */
    public static boolean isHuaweiDevice() {
        if (isHuaweiDevice != null) {
            return isHuaweiDevice;
        }
        String manufacturer = Build.MANUFACTURER;
        isHuaweiDevice = !TextUtils.isEmpty(manufacturer)
                && ("huawei".equalsIgnoreCase(manufacturer) || manufacturer.toLowerCase(Locale.CHINA).contains("huawei"));
        return isHuaweiDevice;
    }

    /**
     * 是否华为设备
     */
    public static boolean isHonorDevice() {
        if (isHonorDevice != null) {
            return isHonorDevice;
        }
        String manufacturer = Build.MANUFACTURER;
        isHonorDevice = !TextUtils.isEmpty(manufacturer)
                && ("honor".equalsIgnoreCase(manufacturer) || manufacturer.toLowerCase(Locale.CHINA).contains("honor"));
        return isHonorDevice;
    }

    public static boolean isNeedUseMediaStyleForHuawei() {
        return isHuaweiDevice() && (isEmui111OrOver() || isHuaweiNewSystem() || Build.VERSION.SDK_INT >= 28);
    }

    /**
     * 获取EMUI版本名称，格式为 EmotionUI_10.1.0
     */
    public static String getEmuiVersion() {
        if (emuiVersion != null) {
            return emuiVersion;
        }
        emuiVersion = BuildProperties.getSystemProperty("ro.build.version.emui");
        return emuiVersion;
    }

    public static boolean isPowerSaveMode(Context context) {
        int powerSaverMode = getPowerSaveMode(context);
        Logger.e("powerSaverMode", "_____" + powerSaverMode);
        return powerSaverMode == 1;
    }

    public static int getPowerSaveMode(Context context) {
        if (Build.MANUFACTURER.equalsIgnoreCase("Xiaomi")) {
            try {
                int value = android.provider.Settings.System.getInt(context.getContentResolver(), "POWER_SAVE_MODE_OPEN");
                return value == 1 ? 1 :-1;
            } catch (Settings.SettingNotFoundException e) {
                e.printStackTrace();
            }
        } else if (Build.MANUFACTURER.equalsIgnoreCase("Huawei")) {
            try {
                int value = android.provider.Settings.System.getInt(context.getContentResolver(), "SmartModeStatus");
                return value == 4 ? 1 :-1;
            } catch (Settings.SettingNotFoundException e) {
                e.printStackTrace();
            }
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                PowerManager pm = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
                if (pm != null) {
                    return pm.isPowerSaveMode() ? 1 : -1;
                }
            }
        }
        return -1;
    }

    /**
     * 是否EMUI11.1及以上版本
     */
    @NonNull
    public static Boolean isEmui111OrOver() {
        if (isEmui111OrOver != null) {
            return isEmui111OrOver;
        }
        String buildVersion = getEmuiVersion();
        if (buildVersion == null || "".equals(buildVersion)) {
            isEmui111OrOver = false;
            return false;
        }

        String[] strArrays = buildVersion.split("_");
        if (strArrays == null || strArrays.length < 2) {
            Logger.i(TAG, "strArrays.length : " + strArrays.length);
            isEmui111OrOver = false;
            return false;
        }

        String[] versions = strArrays[1].split("\\.");
        if (versions == null || versions.length < 1) {
            Logger.i(TAG, "versions.length : " + versions.length);
            isEmui111OrOver = false;
            return false;
        }

        try {
            if (Integer.parseInt(versions[0]) > 11) {
                isEmui111OrOver = true;
                return true;
            }

            if (Integer.parseInt(versions[0]) == 11 && Integer.parseInt(versions[1]) > 0) {
                isEmui111OrOver = true;
                return true;
            }
        } catch (NumberFormatException e) {
            Logger.i(TAG, "NumberFormatException");
            isEmui111OrOver = false;
            return false;
        }
        isEmui111OrOver = false;
        return false;
    }

    /**
     * 是否华为新系统
     *
     * @return true/false
     */
    public static Boolean isHuaweiNewSystem() {
        if ( isHuaweiNewSystem != null) {
            return isHuaweiNewSystem;
        }
        int currentApiLevel;
        try {
            Class cls = Class.forName("android.os.SystemProperties");
            Method method = cls.getDeclaredMethod("get", new Class[] {String.class});
            currentApiLevel = Integer.parseInt((String) method.invoke(cls,
                    new Object[] {"ro.build.hw_emui_api_level"}));
        } catch (ClassNotFoundException e) {
            Logger.i(TAG, "ClassNotFoundException");
            isHuaweiNewSystem = false;
            return false;
        } catch (NoSuchMethodException e) {
            Logger.e(TAG, "NoSuchMethodException");
            isHuaweiNewSystem = false;
            return false;
        } catch (IllegalAccessException e) {
            Logger.i(TAG, "NoSuchFieldException");
            isHuaweiNewSystem = false;
            return false;
        } catch (InvocationTargetException e) {
            Logger.e(TAG, "InvocationTargetException");
            isHuaweiNewSystem = false;
            return false;
        } catch (Exception e) {
            Logger.e(TAG, "Exception");
            isHuaweiNewSystem = false;
            return false;
        }
        isHuaweiNewSystem = currentApiLevel >= NEW_VERSION_API_LEVEL;
        Logger.i(TAG, "currentApiLevel = " + currentApiLevel);
        return isHuaweiNewSystem;
    }

    public static boolean isMIUI() {
        if (hasCheckXiaoMi) {
            return isXiaoMi;
        }
        try {
            isXiaoMi = !TextUtils.isEmpty(getSystemProperty(KEY_MIUI_VERSION_CODE, ""))
                    || !TextUtils.isEmpty(getSystemProperty(KEY_MIUI_VERSION_NAME, ""))
                    || !TextUtils.isEmpty(getSystemProperty(KEY_MIUI_INTERNAL_STORAGE, ""));
        } catch (Exception e) {
            isXiaoMi = false;
        }
        return isXiaoMi;
    }

    /**
     * 获取系统属性
     *
     * @param key          ro.build.display.id
     * @param defaultValue 默认值
     * @return 系统操作版本标识
     */
    public static String getSystemProperty(String key, String defaultValue) {
        try {
            Class<?> SystemProperties = Class.forName("android.os.SystemProperties");
            Method m = SystemProperties.getMethod("get", String.class, String.class);
            String result = (String) m.invoke(null, key, defaultValue);
            return result;
        } catch (Exception e) {
            Logger.e(e);
        }
        return defaultValue;
    }

    // 音量是否可以单独媒体进行设置 -1没取到 1 打开 0关闭
    public static int isXiaomiSoundAssistKey(Context context) {
        try {
            if(isMIUI()) {
                return Settings.Global.getInt(context.getContentResolver(), "sound_assist_key", -1);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return -1;
    }

    // 小米 允许多声音 -1没取到 1 打开 0 关闭
    public static int isXiaomiIgnoreMusicFocusReq(Context context) {
        try {
            if(isMIUI()) {
                return Settings.Global.getInt(context.getContentResolver(), "key_ignore_music_focus_req", -1);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return -1;
    }
}
