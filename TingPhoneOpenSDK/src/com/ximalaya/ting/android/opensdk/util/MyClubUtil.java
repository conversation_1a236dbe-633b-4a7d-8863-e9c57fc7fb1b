package com.ximalaya.ting.android.opensdk.util;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import com.ximalaya.ting.android.opensdk.model.track.Track;

/**
 * Created by WolfXu on 2022/11/10.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class MyClubUtil {
   public static void startRoom(Context context, Track track) {
      if (track == null) {
         return;
      }
      Intent intent = new Intent(Intent.ACTION_VIEW);
      intent.setData(Uri.parse("iting://open?msg_type=343&room_id=" + track.getLiveRoomId()));
      intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
      context.startActivity(intent);
   }
}
