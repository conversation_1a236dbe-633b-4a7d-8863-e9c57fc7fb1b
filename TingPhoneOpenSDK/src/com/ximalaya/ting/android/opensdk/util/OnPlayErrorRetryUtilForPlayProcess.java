package com.ximalaya.ting.android.opensdk.util;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.Uri;
import android.text.TextUtils;

import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.manager.CrossProcessTransferValueManager;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;

import java.util.ArrayList;
import java.util.List;

import static android.net.ConnectivityManager.CONNECTIVITY_ACTION;

/**
 * Created by le.xin on 2019/3/19.
 * 当播放失败后的重试逻辑(在播放进程中使用)
 * <AUTHOR>
 * @email <EMAIL>
 */
public class OnPlayErrorRetryUtilForPlayProcess {

    private static final RetryControl mRetryControl = new RetryControl();
    private static final MediaDecodeError mMediaDecodeError = new MediaDecodeError();

    // 是否显示了播放错误的弹窗
    public static boolean isShowPlayError = false;
    public static long lastErrorTime;

    private static NetworkBroadcastReceiver mNetworkBroadcastReceiver;
    public static void register(Context context) {
        IntentFilter filter = new IntentFilter();
        filter.addAction(CONNECTIVITY_ACTION);
        try {
            if(mNetworkBroadcastReceiver == null) {
                mNetworkBroadcastReceiver = new NetworkBroadcastReceiver();
            }
            context.registerReceiver(mNetworkBroadcastReceiver, filter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    };

    public static void unregister(Context context) {
        try {
            context.unregisterReceiver(mNetworkBroadcastReceiver);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static NetworkType.NetWorkType lastNetType;
    public static class NetworkBroadcastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            NetworkType.NetWorkType type = NetworkType.getNetWorkType(context);

            if (lastNetType == type) {
                return;
            }

            lastNetType = type;

            if (TextUtils.equals(action, ConnectivityManager.CONNECTIVITY_ACTION)) {
                if (type != NetworkType.NetWorkType.NETWORKTYPE_INVALID) {
                    // 是否正在显示播放错误 是否错误时间小于 1分钟
                    if (isShowPlayError && (System.currentTimeMillis() - lastErrorTime < 90 * 1000)) {
                        if(XmPlayerService.getPlayerSrvice() != null) {
                            XmPlayerService.getPlayerSrvice().startPlay();
                        }
                    }
                }
            }
        }
    }

    public static boolean onPlayError(XmPlayerException exception, long trackId, IOnErrorCallback errorCallback) {
        XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();
        if(playerSrvice == null) {
            return false;
        }

        Logger.logToFile("OnPlayErrorRetryUtilForPlayProcess :  " + CrossProcessTransferValueManager.playErrRetry + ",exception=" + exception);
        if(!CrossProcessTransferValueManager.playErrRetry) {
            return false;
        }

        if(exception != null) {
            // 播放器解码错误
            String messageLowerCase = null;
            if(exception.getMessage() != null) {
                messageLowerCase = exception.getMessage().toLowerCase();
            }
            Logger.logToFile("OnPlayErrorRetryUtilForPlayProcess messageLowerCase=" + messageLowerCase + ",what=" + exception.getWhat());
            if (NetworkType.isConnectTONetWork(playerSrvice)
                    && (messageLowerCase != null && !messageLowerCase.contains("unable to connect"))
                    && (exception.getWhat() != XmPlayerException.ERROR_NO_NET)) {
                // 播放错误问题修复
                if(mMediaDecodeError.errorCount >= 3) {
                    return false;
                }

                mMediaDecodeError.errorCount ++;

                String playUrl = null;
                PlayableModel currPlayModel = playerSrvice.getCurrPlayModel();
                Logger.logToFile("OnPlayErrorRetryUtilForPlayProcess track=" + TrackUtil.trackToStr(currPlayModel));
                if(currPlayModel instanceof Track && PlayableModel.KIND_TRACK.equals(currPlayModel.getKind())) {
                    Track track = (Track) currPlayModel;
                    List<String> trackList = new ArrayList<>();
                    trackList.add(PlayUrlUtil.getTrackUrlByType(track, Track.TRACK_PLAY_URL_TYPE_M4A_64));
                    trackList.add(PlayUrlUtil.getTrackUrlByType(track, Track.TRACK_PLAY_URL_TYPE_MP3_64));
                    trackList.add(PlayUrlUtil.getTrackUrlByType(track, Track.TRACK_PLAY_URL_TYPE_M4A_24));
                    trackList.add(PlayUrlUtil.getTrackUrlByType(track, Track.TRACK_PLAY_URL_TYPE_MP3_32));

                    String curPlayUrl = playerSrvice.getCurPlayUrl();
                    if(!TextUtils.isEmpty(curPlayUrl)) {
                        String path = null;
                        try {
                            path = Uri.parse(curPlayUrl).getPath();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        if(path == null) {
                            playUrl = PlayUrlUtil.getTrackUrlByType(track, Track.TRACK_PLAY_URL_TYPE_MP3_64);
                        } else {
                            int startIndex = 0;
                            if (PlayUrlUtil.getTrackUrlByType(track, Track.TRACK_PLAY_URL_TYPE_M4A_64) != null && PlayUrlUtil.getTrackUrlByType(track, Track.TRACK_PLAY_URL_TYPE_M4A_64).contains(path)) {
                                startIndex = 1;
                            } else if (PlayUrlUtil.getTrackUrlByType(track, Track.TRACK_PLAY_URL_TYPE_MP3_64) != null && PlayUrlUtil.getTrackUrlByType(track, Track.TRACK_PLAY_URL_TYPE_MP3_64).contains(path)) {
                                startIndex = 2;
                            } else if (PlayUrlUtil.getTrackUrlByType(track, Track.TRACK_PLAY_URL_TYPE_M4A_24) != null && PlayUrlUtil.getTrackUrlByType(track, Track.TRACK_PLAY_URL_TYPE_M4A_24).contains(path)) {
                                startIndex = 3;
                            }

                            for (int i = startIndex; i < trackList.size(); i++) {
                                String url = trackList.get(i);
                                if (TextUtils.isEmpty(url)) {
                                    continue;
                                }

                                if(i > 0 && TextUtils.equals(trackList.get(i - 1), url)) {
                                    continue;
                                }

                                playUrl = url;
                                break;
                            }
                        }
                    } else {
                        playUrl = PlayUrlUtil.getTrackUrlByType(track, Track.TRACK_PLAY_URL_TYPE_MP3_64);
                    }

                    Logger.logToFile("OnPlayErrorRetryUtilForPlayProcess : playUrl " + playUrl);
                    if(!TextUtils.isEmpty(playUrl)) {
                        if (errorCallback != null) {
                            errorCallback.retryPlay();
                        }

                        float volumeGain = playerSrvice.getVolumeGain(track, playUrl);
                        playerSrvice.playTrackInner(playUrl, track, true, volumeGain, "errorRetry");
                        return true;
                    } else {
                        return false;
                    }
                }
            }

            // 如果网络错误导致播放异常短时间内重试会对服务端造成压力
            mRetryControl.onPlayError(exception, trackId);

            // 如果是不能授权播放的不进行重试
            if(exception.getWhat() == DTransferConstants.NO_AUTHORIZED_CODE
                    || exception.getWhat() == XmPlayerException.ERROR_SAVE_PATH_NO_EXIT) {
                return false;
            }

            if(exception.getWhat() == XmPlayerException.ERROR_NO_PLAY_URL) {
                if(!mRetryControl.errorForNoPlayUrl()) {
                    return false;
                }
            }
        }

        PlayableModel playableModel = playerSrvice.getCurrPlayModel();
        if(playableModel == null) {
            return false;
        }

        boolean isConnectToNetwork = NetworkType.isConnectTONetWork(playerSrvice);
        Logger.logToFile("OnPlayErrorRetryUtilForPlayProcess isConnectToNetwork=" + isConnectToNetwork);
        if (isConnectToNetwork) {
            if(mRetryControl.canRetryPlay()) {
                Logger.logToSd("OnPlayErrorRetryUtilForPlayProcess = retryPlay");

                if (errorCallback != null) {
                    errorCallback.retryPlay();
                }

                playerSrvice.startPlay();
                return true;
            } else {
                return false;
            }
        } else {
            isShowPlayError = true;
            lastErrorTime = System.currentTimeMillis();
            return false;
        }
    }

    public static void resertPlayErrorSign() {
        isShowPlayError = false;
        lastErrorTime = 0;

        mRetryControl.resert();
    }

    public static void resetMediaDecodeErrorOnSoundSwitch() {
        mMediaDecodeError.reset();
    }

    static class MediaDecodeError {
        long trackId;
        int errorCount;

        void reset() {
            trackId = 0;
            errorCount = 0;
        }
    }

    static class RetryControl {
        private static final int MAX_RETRY_COUNT = 1;

        int errorCode;  // 错误类型
        long lastTrackId = 0;
        int retryCountForErrorCode; // 重试次数
        long firstRetryTime; // 第一次重试时间

        boolean canRetryPlay() {
            Logger.logToFile("OnPlayErrorRetryUtilForPlayProcess canRetryPlay retryCountForErrorCode=" + retryCountForErrorCode);
            return retryCountForErrorCode < MAX_RETRY_COUNT;
        }

        boolean errorForNoPlayUrl() {
            Logger.logToFile("OnPlayErrorRetryUtilForPlayProcess errorForNoPlayUrl retryCountForErrorCod=" + retryCountForErrorCode);
            if(retryCountForErrorCode > MAX_RETRY_COUNT) {
                if(System.currentTimeMillis() - firstRetryTime < 5 * 60 * 1000) {
                    Logger.logToFile("OnPlayErrorRetryUtilForPlayProcess errorForNoPlayUrl-1");
                    return false;
                } else {
                    Logger.logToFile("OnPlayErrorRetryUtilForPlayProcess errorForNoPlayUrl-2");
                    retryCountForErrorCode = 0;
                    firstRetryTime = 0;
                }
            }
            return true;
        }

        private boolean checkErrorWithTrackId() {
            return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEM_CHECK_ERROR_WITH_TRACKID, false);
        }

        void onPlayError(XmPlayerException exception, long trackId) {
            Logger.logToFile("OnPlayErrorRetryUtilForPlayProcess onPlayError-1 e=" + exception
                    + ",trackId=" + trackId + ",lastTrackId=" + lastTrackId + ",errorCode=" + errorCode
                    + ",retryCountForErrorCode=" + retryCountForErrorCode + ",firstRetryTime=" + firstRetryTime);
            if (trackId > 0 && checkErrorWithTrackId()) {
                Logger.logToFile("OnPlayErrorRetryUtilForPlayProcess onPlayError-2");
                if (lastTrackId <= 0) {
                    lastTrackId = trackId;
                    errorCode = exception.getWhat();
                    retryCountForErrorCode = 0;
                    firstRetryTime = 0;
                } else if (lastTrackId == trackId) {
                    retryCountForErrorCode++;
                    if (firstRetryTime == 0) {
                        firstRetryTime = System.currentTimeMillis();
                    }
                } else {
                    lastTrackId = trackId;
                    errorCode = exception.getWhat();
                    retryCountForErrorCode = 0;
                    firstRetryTime = 0;
                }
            } else {
                Logger.logToFile("OnPlayErrorRetryUtilForPlayProcess onPlayError-3");
                if (errorCode != exception.getWhat()) {
                    errorCode = exception.getWhat();
                    retryCountForErrorCode = 0;
                    firstRetryTime = 0;
                } else {
                    retryCountForErrorCode++;
                    if (firstRetryTime == 0) {
                        firstRetryTime = System.currentTimeMillis();
                    }
                }
            }
        }

        void resert() {
            Logger.logToFile("OnPlayErrorRetryUtilForPlayProcess resert");
            errorCode = 0;
            retryCountForErrorCode = 0;
            firstRetryTime = 0;
            lastTrackId = 0;
        }
    }

    public interface IOnErrorCallback {
        void retryPlay();
    }
}
