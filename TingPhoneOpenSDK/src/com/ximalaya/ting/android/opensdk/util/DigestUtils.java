package com.ximalaya.ting.android.opensdk.util;

/*
 * Copyright 2011 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
/**
 * <AUTHOR>
 */
public class DigestUtils {
	private static final char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
	
	static MessageDigest getDigest(String algorithm) {
        try {
            return MessageDigest.getInstance(algorithm);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e.getMessage());
        }
    }
	
    protected static String encodeHex(byte[] data) {
        int l = data.length;
        char[] out = new char[l << 1];
        for (int i = 0, j = 0; i < l; i++) {
            out[j++] = HEX_DIGITS[(0xF0 & data[i]) >>> 4];
            out[j++] = HEX_DIGITS[0x0F & data[i]];
        }
        return new String(out);
    }
    
    public static byte[] md5(byte[] data) {
        return getDigest("MD5").digest(data);
    }
    
    public static String md5Hex(String data) {
        return encodeHex(md5(utf8Bytes(data)));
    }
    
    public static String md5Hex(byte[] data) {
        return encodeHex(md5(data));
    }

	private static byte[] utf8Bytes(String data) {
		try {
		    if(data == null) {
		        return null;
            }
			return data.getBytes("UTF-8");
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		}
	}
	
	public static String sha1Hex(final String data) {
        return new String(encodeHex(sha1(data)));
    }
	
	public static byte[] sha1(final String data) {
        return sha1(utf8Bytes(data));
    }
	public static byte[] sha1(final byte[] data) {
        return getDigest("SHA-1").digest(data);
    }
}