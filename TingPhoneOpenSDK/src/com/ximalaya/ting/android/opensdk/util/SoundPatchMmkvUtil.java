package com.ximalaya.ting.android.opensdk.util;

import android.content.Context;
import android.util.Log;

import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;

import java.util.Calendar;
import java.util.HashSet;
import java.util.Set;

public class SoundPatchMmkvUtil {
    public static final int INVALID_TIMES = -100;


    private static final String TAG = SoundPatchMmkvUtil.class.getSimpleName();

    /**
     * 各个使用方，为了防止MMKV被随意创建保存文件，每个使用的文件需要被注册在
     * 注册方式：在静态代码块中添加到MMKV_FILE_REGISTER
     * */
    private static final Set<String> MMKV_FILE_REGISTER = new HashSet<>();

    private static final String SOUND_PATCH_MMKV_N = "SOUND_PATCH_MMKV_N";

    private static final String KEY_AD_TIMES_RECORD_DATE = "KEY_AD_TIMES_RECORD_DATE";
    private static final String KEY_FOLLOW_AD_SOUND_PATCH_TIMES_RECORD_DATE = "KEY_FOLLOW_AD_SOUND_PATCH_TIMES_RECORD_DATE";
    private static final String KEY_SWITCH_OF_FOLLOW_AD_FUNCTION_ENABLE = "KEY_SWITCH_OF_FOLLOW_AD_FUNCTION_ENABLE";
    private static final String KEY_FOLLOW_AD_FUNCTION_PARAM = "KEY_FOLLOW_AD_FUNCTION_PARAM";

    private static final String PREFIX_AD_TIMES_RECORD_DATE = "PREFIX_AD_TIMES_RECORD_DATE_";
    private static final String PREFIX_FOLLOW_AD_SOUND_PATCH_TIMES_RECORD_DATE = "PREFIX_FOLLOW_AD_SOUND_PATCH_TIMES_RECORD_DATE_";

    static {
        MMKV_FILE_REGISTER.add(SOUND_PATCH_MMKV_N);
        // MMKV_FILE_REGISTER.add(SOUND_PATCH_MMKV_E);
    }

    public static int increaseAdPlayTimes(Context context) {
        if (null == context) {
            return INVALID_TIMES;
        }
        MMKVUtil mmkvUtil = getInstance(context);
        if (null == mmkvUtil) {
            return INVALID_TIMES;
        }
        String nowKey = PREFIX_AD_TIMES_RECORD_DATE + Util.getCurrentFormatDateTime();
        String oldKey = mmkvUtil.getString(KEY_AD_TIMES_RECORD_DATE, null);
        if (nowKey.equals(oldKey)) {
            // 还在同一天，且已有数据
            int recordTimes = mmkvUtil.getInt(nowKey, 0);
            recordTimes++;
            mmkvUtil.saveInt(nowKey, recordTimes);
            return recordTimes;
        } else {
            mmkvUtil.removeByKey(oldKey);
            mmkvUtil.saveString(KEY_AD_TIMES_RECORD_DATE, nowKey);
            mmkvUtil.saveInt(nowKey, 1);
            return 1;
        }
    }

    public static int getAdPlayTimes(Context context) {
        if (null == context) {
            return INVALID_TIMES;
        }
        MMKVUtil mmkvUtil = getInstance(context);
        if (null == mmkvUtil) {
            return INVALID_TIMES;
        }
        String nowKey = PREFIX_AD_TIMES_RECORD_DATE + Util.getCurrentFormatDateTime();
        String oldKey = mmkvUtil.getString(KEY_AD_TIMES_RECORD_DATE, null);
        if (nowKey.equals(oldKey)) {
            // 默认值：只是还没有记录，所以可以返回有效的数据 0
            return mmkvUtil.getInt(nowKey, 0);
        }
        mmkvUtil.removeByKey(oldKey);
        return 0;
    }

    public static void setSwitchOfFollowAdFunctionEnable(Context context, boolean enable) {
        if (null == context) {
            return;
        }
        MMKVUtil mmkvUtil = getInstance(context);
        if (null == mmkvUtil) {
            return;
        }
        mmkvUtil.saveBoolean(KEY_SWITCH_OF_FOLLOW_AD_FUNCTION_ENABLE, enable);
    }

    public static boolean isSwitchOfFollowAdFunctionEnable(Context context) {
        if (null == context) {
            return false;
        }
        MMKVUtil mmkvUtil = getInstance(context);
        if (null == mmkvUtil) {
            return false;
        }
        return mmkvUtil.getBoolean(KEY_SWITCH_OF_FOLLOW_AD_FUNCTION_ENABLE, false);
    }

    public static void increaseFollowAdSoundPatchTimes(Context context) {
        if (null == context) {
            return;
        }
        MMKVUtil mmkvUtil = getInstance(context);
        if (null == mmkvUtil) {
            return;
        }
        String nowKey = PREFIX_FOLLOW_AD_SOUND_PATCH_TIMES_RECORD_DATE + Util.getCurrentFormatDateTime();
        String oldKey = mmkvUtil.getString(KEY_FOLLOW_AD_SOUND_PATCH_TIMES_RECORD_DATE, null);
        if (nowKey.equals(oldKey)) {
            // 还在同一天，且已有数据
            int recordTimes = mmkvUtil.getInt(nowKey, 0);
            recordTimes++;
            mmkvUtil.saveInt(nowKey, recordTimes);
        } else {
            mmkvUtil.removeByKey(oldKey);
            mmkvUtil.saveString(KEY_FOLLOW_AD_SOUND_PATCH_TIMES_RECORD_DATE, nowKey);
            mmkvUtil.saveInt(nowKey, 1);
        }
    }

    public static int getFollowAdSoundPatchTimes(Context context) {
        if (null == context) {
            return Integer.MAX_VALUE;
        }
        MMKVUtil mmkvUtil = getInstance(context);
        if (null == mmkvUtil) {
            return Integer.MAX_VALUE;
        }
        String nowKey = PREFIX_FOLLOW_AD_SOUND_PATCH_TIMES_RECORD_DATE + Util.getCurrentFormatDateTime();
        String oldKey = mmkvUtil.getString(KEY_FOLLOW_AD_SOUND_PATCH_TIMES_RECORD_DATE, null);
        if (nowKey.equals(oldKey)) {
            // 默认值：只是还没有记录，所以可以返回有效的数据 0
            return mmkvUtil.getInt(nowKey, 0);
        }
        mmkvUtil.removeByKey(oldKey);
        return 0;
    }

    public static void saveFollowAdFunctionParam(Context context, String param) {
        if (null == context) {
            return;
        }
        MMKVUtil mmkvUtil = getInstance(context);
        if (null == mmkvUtil) {
            return;
        }
        if (null == param) {
            mmkvUtil.removeByKey(KEY_FOLLOW_AD_FUNCTION_PARAM);
        } else {
            mmkvUtil.saveString(KEY_FOLLOW_AD_FUNCTION_PARAM, param);
        }
    }

    public static String getFollowAdFunctionParam(Context context) {
        if (null == context) {
            return null;
        }
        MMKVUtil mmkvUtil = getInstance(context);
        if (null == mmkvUtil) {
            return null;
        }
        return mmkvUtil.getString(KEY_FOLLOW_AD_FUNCTION_PARAM, null);
    }

    /**
     * @return 可能为null，使用时注意判断
     */
    private static MMKVUtil getInstance(Context context) {
        return initeMMKV(context, SOUND_PATCH_MMKV_N, false);
    }

    /**
     * @return 可能为null，使用时注意判断
     */
    /*private static MMKVUtil getEncryptInstance(Context context) {
        return initeMMKV(context, SOUND_PATCH_MMKV_E, true);
    }*/

    private static MMKVUtil initeMMKV(Context context, String mmkvFile, boolean isEncrypt) {
        if (null == mmkvFile || !MMKV_FILE_REGISTER.contains(mmkvFile)) {
            return null;
        }
        if (null != context) {
            MMKVUtil.initialize(context);
            MMKVUtil mmkvUtil;
            if (isEncrypt) {
                mmkvUtil = MMKVUtil.getEncryptedInstance(mmkvFile);
            } else {
                mmkvUtil = MMKVUtil.getInstance(mmkvFile);
            }
            return mmkvUtil;
        } else {
            Log.e(TAG, "Method: initeMMKV, Parameter is null");
        }
        return null;
    }

    private static class Util {
        public static int getCurrentFormatDateTime() {
            long now = System.currentTimeMillis();
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(now);
            int result = 0;
            result += 10000 * calendar.get(Calendar.YEAR);
            result += 100 * calendar.get(Calendar.MONTH);
            result += calendar.get(Calendar.DAY_OF_MONTH);
            return result;
        }
    }
}
