package com.ximalaya.ting.android.opensdk.util

import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl.PlayMode

/**
 * <AUTHOR>
 * @date 2025/5/15 16:18
 */
class PlayModeUtil {
}

fun playModeToStr(mode: PlayMode): String =
    with(mode) {
        if (PlayMode.PLAY_MODEL_SINGLE == this) { // 单曲
            "single"
        } else if (PlayMode.PLAY_MODEL_SINGLE_LOOP == this) { // 单曲循环
            "single_loop"
        } else if (PlayMode.PLAY_MODEL_LIST == this) { // 顺序
            "order"
        } else if (PlayMode.PLAY_MODEL_LIST_LOOP == this) { // 列表循环
            "list"
        } else if (PlayMode.PLAY_MODEL_RANDOM == this) { // 随机播放
            "random"
        } else {
            "list"
        }
    }