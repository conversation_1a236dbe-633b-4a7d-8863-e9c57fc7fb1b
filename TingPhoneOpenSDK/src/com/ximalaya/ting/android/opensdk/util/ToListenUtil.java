package com.ximalaya.ting.android.opensdk.util;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.SimpleTrackForToListen;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.manager.PlayerToListenManager;
import com.ximalaya.ting.android.opensdk.player.manager.SkipHeadTailManager;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForPlay;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2022/6/23
 * Description：
 */
public class ToListenUtil {
    public static String PLAY_ORDER_BY_LISTEN_LIST = "play_order_by_listen_list";
    public static String TO_LISTEN_ANDROID_NEW_CLOSE = "to_listen_android_new_close";
    public static MMKVUtil getMMKVUtil() {
        return MMKVUtil.getInstance("ToListenManager");
    }

    public static MMKVUtil getRecommendMMKVUtil() {
        return MMKVUtil.getInstance("RecommendToListenManager");
    }

    public static void deleteTrackDataById(long trackId) {
        getMMKVUtil().removeByKey(String.valueOf(trackId));
    }

    public static MMKVUtil getCommonMMKVUtil() {
        return MMKVUtil.getInstance();
    }

    public static boolean closeRecommend() {
        return true;
    }

    /**
     * 播放进程直接调用获取，其他地方请使用ToListenManager，不要用这个方法
     * 回调在子线程
     * **/
    public static void getToListenTracksForPlayService(IDataCallBack<List<Track>> dataCallBack) {
        if (dataCallBack == null) {
            return;
        }
        MyAsyncTask.execute(() -> {
            String[] allKeys = getMMKVUtil().getAllKeys();
            if (allKeys == null) {
                dataCallBack.onError(1, "no data");
                return;
            }
            List<SimpleTrackForToListen> tempList = new ArrayList<>(allKeys.length);
            for (String key : allKeys) {
                if (!TextUtils.isEmpty(key)) {
                    try {
                        SimpleTrackForToListen tempTrack = new Gson().fromJson(getMMKVUtil().getString(key), SimpleTrackForToListen.class);
                        if (tempTrack == null) {
                            continue;
                        }
                        tempList.add(tempTrack);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            Collections.sort(tempList, (o1, o2) -> {
                if (o1 == null && o2 == null) {
                    return 0;
                }
                if (o1 == null) {
                    return -1;
                }
                if (o2 == null) {
                    return 1;
                }
                if (o1.getUpdateOrderTime() < o2.getUpdateOrderTime()) {
                    return 1;
                } else if (o1.getUpdateOrderTime() == o2.getUpdateOrderTime()) {
                    return 0;
                } else {
                    return -1;
                }
            });
            List<Track> resultList = new ArrayList<>(tempList.size());
            for (int i = 0; i < tempList.size(); i ++) {
                resultList.add(tempList.get(i).toTrack());
            }
            dataCallBack.onSuccess(resultList);
        });
    }

    /**
     * 播放进程直接调用获取，其他地方请使用ToListenManager，不要用这个方法
     * 回调在子线程
     * **/
//    public static void changeOrderForPlayService(long curTrackId, long lastTrackId, boolean toListen) {
//        if (curTrackId <= 0 || lastTrackId <= 0) {
//            return;
//        }
//        MyAsyncTask.execute(() -> {
//            MMKVUtil mmkvUtil = toListen ? getMMKVUtil() : getRecommendMMKVUtil();
//            String curTrackStr = mmkvUtil.getString(String.valueOf(curTrackId));
//            String lastTrackStr = mmkvUtil.getString(String.valueOf(lastTrackId));
//            SimpleTrackForToListen curTrack = null;
//            SimpleTrackForToListen lastTrack = null;
//            try {
//                Gson gson = new Gson();
//                if (!TextUtils.isEmpty(curTrackStr)) {
//                    curTrack = gson.fromJson(curTrackStr, SimpleTrackForToListen.class);
//                    lastTrack = gson.fromJson(lastTrackStr, SimpleTrackForToListen.class);
//                    lastTrack.setUpdateOrderTime(curTrack.getUpdateOrderTime() - 1);
//                    mmkvUtil.saveString(String.valueOf(curTrackId), gson.toJson(curTrack));
//                    mmkvUtil.saveString(String.valueOf(lastTrackId), gson.toJson(lastTrack));
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        });
//    }

    public static boolean isListenComplete(boolean isFromPlayService, Context context, Track track) {
        // 播放进程和主进程调用getPlayProgress方法内部需区别处理
        if (track == null) {
            return false;
        }
        int pro = getPlayProgress(isFromPlayService, track, context);
        return pro >= 95;
    }

    /**
     * 当天第一次从播放页返回热点
     * */
    public static boolean isLastDailyNewsDateToday() {
        String lastDate = MMKVUtil.getInstance().getString("key_dailynews4_should_open_daily_today");
        String today = getDailyNewsDate();
        return TextUtils.isEmpty(lastDate) || !lastDate.equals(today);
    }

    public static void saveLastDailyNewsDate() {
         MMKVUtil.getInstance().saveString("key_dailynews4_should_open_daily_today", getDailyNewsDate());
    }

    public static String getDailyNewsDate() {
        Calendar calender = Calendar.getInstance();
        calender.setTimeInMillis(System.currentTimeMillis());
        return calender.get(Calendar.YEAR) + "-" + (calender.get(Calendar.MONTH) + 1) + "-" + calender.get(Calendar.DAY_OF_MONTH);
    }

    private static int getPlayProgress(boolean isFromPlayService, Track track, Context context) {
        if (track == null) {
            return 0;
        }
        int lastPlayedMills = 0;
        if (isFromPlayService) {
            IHistoryManagerForPlay iHistoryManagerForPlay = RouterServiceManager.getInstance().getService(IHistoryManagerForPlay.class);
            if (iHistoryManagerForPlay != null) {
                lastPlayedMills = iHistoryManagerForPlay.getSoundHistoryPos(context, track.getDataId());
            }
        } else {
            lastPlayedMills = XmPlayerManager.getInstance(context).getHistoryPos(track.getDataId());
        }
        int skipTail = 0;
        long albumId = track.getAlbum() != null ? track.getAlbum().getAlbumId() : 0L;
        if (isFromPlayService && albumId > 0) {
            skipTail = SkipHeadTailManager.getInstance().getTail(albumId);
        } else if (albumId > 0) {
            skipTail = XmPlayerManager.getInstance(context).getTrackSkipTailFromCache(albumId);
        }
        if (track.getDuration() - skipTail <= 0) {
            skipTail = 0;
        }

        if (lastPlayedMills == PlayerConstants.PLAY_COMPLETE) {
            return 100;
        } else if (lastPlayedMills == PlayerConstants.PLAY_NO_HISTORY) {
            return 0;
        } else {
            if (track.getDuration() - skipTail <= 0 || lastPlayedMills < 0) {
                return 0;
            }
            double time = lastPlayedMills / ((track.getDuration() - skipTail) * 1000d) * 100;
            return (int) time;
        }
    }

    // 获取播放列表参数map
    public static Map<String, String> getToListenOriginalParams() {
        HashMap<String, String> tempMap = null;
        try {
            String tempParamString = ToListenUtil.getCommonMMKVUtil().getString("ToListenOriginalParams");
            tempMap = new Gson().fromJson(tempParamString, new TypeToken<HashMap<String, String>>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return tempMap;
    }


    public static void getRealPlayDataFromMmkv(IDataCallBack<List<Track>> callBack) {
        new MyAsyncTask<Void, Void, List<Track>>() {
            @Override
            protected List<Track> doInBackground(Void... params) {
                List<Track> tracks = null;
                try {
                    String tempListString = ToListenUtil.getCommonMMKVUtil().getString("ToListenOriginalTrackList");
                    tracks = new Gson().fromJson(tempListString, new TypeToken<List<Track>>() {}.getType());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return tracks;
            }
            @Override
            protected void onPostExecute(List<Track> trackList) {
                if (callBack != null) {
                    callBack.onSuccess(trackList);
                }
            }
        }.myexec();
    }

    public static String getToListenOriginalPlayListMode() {
        return ToListenUtil.getCommonMMKVUtil().getString("ToListenOriginalPlayListMode");
    }


    /**
     * 	判断播放页是否是播客播放列表模式 主进程使用
     * */
    public static boolean isPlayPagePodCastPlayListMode(Context context) {
        try {
            String str = null;
            if (XmPlayerManager.getInstance(context).isPlayingToListenTracks()) {
                str = ToListenUtil.getToListenOriginalPlayListMode();
            } else {
                Map<String, String> params = XmPlayerManager.getInstance(context).getPlayListParams();
                if (params != null) {
                    str = params.get(DTransferConstants.PARAM_SUB_PLAY_LIST_ITEM);
                }
            }
            if (!TextUtils.isEmpty(str) && TextUtils.equals(str, DTransferConstants.PARAM_SUB_PLAY_LIST_TO_LISTEN)) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 播放进程调用时，传XmPlayerService，不要传XmPlayerManager
     * 主进程调用时反之
     * */
    public static void playOriginalList(XmPlayerService xmPlayerService, XmPlayerManager xmPlayerManager, long setTopId, final boolean autoPlay) {
        new MyAsyncTask<Void, Void, CommonTrackList<Track>>() {
            @Override
            protected CommonTrackList<Track> doInBackground(Void... params) {
                boolean badCase = false;
                Logger.logToFile("PlayerToListenManager playOriginalList setTopId = " + setTopId + "  , autoPlay = " + autoPlay);
                String tempListString = ToListenUtil.getCommonMMKVUtil().getString("ToListenOriginalTrackList");
                List<Track> tracks = new Gson().fromJson(tempListString, new TypeToken<List<Track>>() {}.getType());
                if (tracks == null) {
                    tracks = new ArrayList<>();
                }
                if (tracks.size() == 0) {
                    if (xmPlayerService != null && xmPlayerService.getCurrPlayModel() instanceof Track) {
                        Track track = (Track) xmPlayerService.getCurrPlayModel();
                        if (PlayableModel.KIND_TRACK.equals(track.getKind())) {
                            track.setIsFromToListenTrack(-1);
                            badCase = true;
                            Logger.logToFile("PlayerToListenManager playOriginalList 没声音了 边界情况处理");
                            tracks.add(track);
                        }
                    } else if (xmPlayerManager != null && xmPlayerManager.getCurrSound() instanceof Track) {
                        Track track = (Track) xmPlayerManager.getCurrSound();
                        if (PlayableModel.KIND_TRACK.equals(track.getKind())) {
                            track.setIsFromToListenTrack(-1);
                            badCase = true;
                            Logger.logToFile("PlayerToListenManager playOriginalList 没声音了 边界情况处理");
                            tracks.add(track);
                        }
                    }
                }
                Map<String, String> playParams = ToListenUtil.getToListenOriginalParams();
//                if (setTopId > 0) {
//                    int removePosition = -1;
//                    for (int i = 0; i < tracks.size(); i++) {
//                        if (tracks.get(i).getDataId() == setTopId) {
//                            removePosition = i;
//                            break;
//                        }
//                    }
//                    if (removePosition > -1) {
//                        Track removedTrack = tracks.remove(removePosition);
//                        tracks.add(0, removedTrack);
//                    }
//                }
                CommonTrackList<Track> commonTrackList = new CommonTrackList();
                if (playParams == null) {
                    playParams = new HashMap<>();
                }
                playParams.put("tempDataBadCase", badCase + "");
                commonTrackList.setParams(playParams);
                commonTrackList.setTracks(tracks);
                return commonTrackList;
            }

            @Override
            protected void onPostExecute(CommonTrackList<Track> commonTrackList) {
                try {
                    boolean badCase = "true".equals(commonTrackList.getParams().get("tempDataBadCase"));
                    commonTrackList.getParams().remove("tempDataBadCase");
                    List<Track> tracks = commonTrackList.getTracks();
                    int playIndex = ToListenUtil.getCommonMMKVUtil().getInt("ToListenOriginalIndex", 0);
                    if (setTopId > 0 && tracks != null && tracks.size() > 0) {
                        for (int i = 0; i < tracks.size(); i++) {
                            if (tracks.get(i).getDataId() == setTopId) {
                                playIndex = i;
                                break;
                            }
                        }
                    }
                    if (xmPlayerService != null) {
                        Logger.logToFile("PlayerToListenManager playOriginalList xmPlayerService");
                        if (!badCase) {
                            PlayerToListenManager.getInstance().ignorePlayerToListenLogic = true;
                        }
                        xmPlayerService.setPlayList(commonTrackList.getParams(), commonTrackList.getTracks());
                        if (autoPlay && !badCase) {
                            xmPlayerService.play(playIndex);
                        } else {
                            xmPlayerService.setPlayIndex(playIndex);
                        }
                    } else if (xmPlayerManager != null) {
                        Logger.logToFile("PlayerToListenManager playOriginalList xmPlayerManager");
                        if (!badCase) {
                            xmPlayerManager.ignorePlayerToListenLogic(true);
                        }
                        if (autoPlay && !badCase) {
                            xmPlayerManager.playList(commonTrackList, playIndex);
                        } else {
                            xmPlayerManager.setPlayList(commonTrackList, playIndex);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }.myexec();
    }
}
