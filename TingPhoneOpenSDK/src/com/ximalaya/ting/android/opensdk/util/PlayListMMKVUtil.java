package com.ximalaya.ting.android.opensdk.util;

import android.content.Context;

import com.ximalaya.ting.android.opensdk.constants.MmkvConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;

/**
 * Created by <PERSON><PERSON><PERSON> on 2021/10/13.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class PlayListMMKVUtil extends MmkvCommonUtil {
    private static Context sContext;
    private SharedPreferencesUtil mTingDataSharedPreferencesUtil;

    protected PlayListMMKVUtil(Context context, String name) {
        super(context, name);
    }

    private static class Holder {
        static final PlayListMMKVUtil sInstance = new PlayListMMKVUtil(sContext, MmkvConstantsInOpenSdk.OPENSDK_FILENAME_PLAY_LIST);
    }

    public static PlayListMMKVUtil getInstance(Context context) {
        if (sContext == null && context != null) {
            sContext = context.getApplicationContext();
        }
        return Holder.sInstance;
    }

    @Override
    protected SharedPreferencesUtil createSharedPreferenceUtil(Context context) {
        return new SharedPreferencesUtil(context, PreferenceConstantsInOpenSdk.XFramework_FILENAME_PRIVATE_DATA, Context.MODE_PRIVATE);
    }

    public int getIntCompatFromTingDataSp(String key, int defaultValue) {
        if (containsKey(key)) {
            return getInt(key, defaultValue);
        }
        if (mTingDataSharedPreferencesUtil == null) {
            mTingDataSharedPreferencesUtil = SharedPreferencesUtil.getInstance(sContext);
        }
        if (mTingDataSharedPreferencesUtil != null && mTingDataSharedPreferencesUtil.contains(key)) {
            int value = mTingDataSharedPreferencesUtil.getInt(key, defaultValue);
            saveInt(key, value);
            return value;
        } else {
            return defaultValue;
        }
    }
}
