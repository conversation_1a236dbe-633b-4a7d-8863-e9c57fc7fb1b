package com.ximalaya.ting.android.opensdk.util;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.reflect.Type;

/**
 * Created by le.xin on 2019/3/14.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class HandlerPlayerProcessDiedUtil {
    public static boolean mLastIsPlaying = false;
    private static long mLastDisconnectedTime;
    private static long mLastRestartPlayTime;
    private static int mShortRestartCount;

    public static boolean restartService = false;
    public static long restartTime = 0L;

    public static void onServiceDisconnected() {
        mLastDisconnectedTime = System.currentTimeMillis();
    }

    public static void onServiceConnectedAndLastIsKilled(Context context) {
        if (ConstantsOpenSdk.isDebug){
            return;
        }
        if(System.currentTimeMillis() - mLastDisconnectedTime <= 5000) {
            if(mLastIsPlaying) {
                // 被杀重启后5s内 ,如果上次在播放状态 ,则重新启动播放
                if(System.currentTimeMillis() - mLastRestartPlayTime <= 5000) {
                    mShortRestartCount ++;
                } else {
                    mShortRestartCount = 0;
                }

                mLastRestartPlayTime = System.currentTimeMillis();
                mLastIsPlaying = false;
                // 如果在短时间内重启3次不再尝试播放
                if(mShortRestartCount <= 3) {
                    loadHistoryPlayListToPlayerOnMainProcess(context, new IDataCallBack<CommonTrackList<Track>>() {
                        @Override
                        public void onSuccess(@Nullable CommonTrackList<Track> object) {
                            if(object != null) {
                                XmPlayerManager.getInstance(context).playList(object ,object.getPlayIndex());
                            }
                        }

                        @Override
                        public void onError(int code, String message) {

                        }
                    });
                }
            }
        }
    }

    // 主进程调用
    public static void loadHistoryPlayListToPlayerOnMainProcess(Context context , IDataCallBack<CommonTrackList<Track>> callBack) {
        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                String playListStr = PlayListMMKVUtil.getInstance(context).getStringCompat(PreferenceConstantsInOpenSdk.XFramework_KEY_HISTORY_PLAY_LIST);
                if (!TextUtils.isEmpty(playListStr)) {
                    try {

                        Type objectType = new TypeToken<CommonTrackList<Track>>() {
                        }.getType();
                        CommonTrackList<Track> commonTrackList = new Gson().fromJson(
                                playListStr, objectType);
                        int playIndex = PlayListMMKVUtil.getInstance(context).getIntCompat(PreferenceConstantsInOpenSdk.XFramework_KEY_HISTORY_PLAY_INDEX, 0);
                        commonTrackList.setPlayIndex(playIndex);
                        new Handler(Looper.getMainLooper()).post(new Runnable() {
                            @Override
                            public void run() {
                                if(callBack != null) {
                                    callBack.onSuccess(commonTrackList);
                                }
                            }
                        });

                        return;
                    } catch (Exception e) {
                        e.printStackTrace();
                        IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
                        if (xdcsPost != null) {
                            if (e instanceof NumberFormatException || e instanceof JsonSyntaxException) {
                                xdcsPost.statErrorToXDCS("HistoryManager", "loadHistoryPlayListToPlayerOnMainProcess————setHistoryPlayListToPlayer" + e + "————————" + playListStr);
                            } else {
                                xdcsPost.statErrorToXDCS("HistoryManager", "loadHistoryPlayListToPlayerOnMainProcess——————setHistoryPlayListToPlayer" + e + "____" + Log.getStackTraceString(e));
                            }
                        }
                    }
                }

                new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        if(callBack != null) {
                            callBack.onSuccess(null);
                        }
                    }
                });
            }
        });

    }

    public static IXmPlayerStatusListener mPlayerStatusListener = new IXmPlayerStatusListener() {
        @Override
        public void onPlayStart() {
            HandlerPlayerProcessDiedUtil.mLastIsPlaying = true;

            if (HandlerPlayerProcessDiedUtil.restartService) {
                if (System.currentTimeMillis() - HandlerPlayerProcessDiedUtil.restartTime <= 5000) {
                    Logger.d("zimotag", "HandlerPlayerProcessDiedUtil onPlayStart: ");
                    IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
                    if (xdcsPost != null) {
                        xdcsPost.statErrorToXDCS("exoRestartPlay", "killServiceAndStartIn5s");
                    }
                }
                HandlerPlayerProcessDiedUtil.restartTime = 0L;
                HandlerPlayerProcessDiedUtil.restartService = false;
            }
        }

        @Override
        public void onPlayPause() {
            HandlerPlayerProcessDiedUtil.mLastIsPlaying = false;
        }

        @Override
        public void onPlayStop() {
            HandlerPlayerProcessDiedUtil.mLastIsPlaying = false;
        }

        @Override
        public void onSoundPlayComplete() {
            HandlerPlayerProcessDiedUtil.mLastIsPlaying = false;
        }

        @Override
        public void onSoundPrepared() {
        }

        @Override
        public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
        }

        @Override
        public void onBufferingStart() {
        }

        @Override
        public void onBufferingStop() {
        }

        @Override
        public void onBufferProgress(int percent) {

        }

        @Override
        public void onPlayProgress(int currPos, int duration) {

        }

        @Override
        public boolean onError(XmPlayerException exception) {
            HandlerPlayerProcessDiedUtil.mLastIsPlaying = false;
            return false;
        }
    };


}
