package com.ximalaya.ting.android.opensdk.util;

import android.content.Context;
import android.text.TextUtils;

import com.ximalaya.ting.android.player.PlayerUtil;
import com.ximalaya.ting.android.xmnetmonitor.core.NetworkMonitorInterceptor;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2019-06-09 15:11
 */
public class NetMonitorUtil {

    static SimpleDateFormat sDateFormatYYMMDDHHMMSS =
            new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
    private static PrintWriter printWriter;
    private static File sLogFile;
    private static boolean sRunning = false;

    private static NetworkMonitorInterceptor.Logger sLogger = new NetworkMonitorInterceptor.Logger() {
        @Override
        public void log(String netMessage) {
            if (TextUtils.isEmpty(netMessage))
                return;
            if (printWriter == null)
                return;
            try {
                // 临时加一个 过滤url的方法
                // String str = netMessage.substring(0, netMessage.length() > 100 ? 100 : netMessage.length());
                // if (str.contains("ip138.com") || str.contains("ip.cn"))
                //    return;
                printWriter.write(netMessage);
            } catch (Exception e) {
                Logger.e(e);
            }
        }
    };

    public static String getNetMonitorStoreFolder(Context context) {
        if (context == null)
            return null;
        return context.getExternalFilesDir("") + File.separator + "netLog" + File.separator;
    }

    public static void clearNetMonitorCacheFile(Context context) {
        String folderPath = getNetMonitorStoreFolder(context);
        if (folderPath == null)
            return;
        FileUtilBase.deleFileNoCheckDownloadFile(new File(folderPath));
    }

    /**
     * 在start 之前不会清空netLog文件夹下的文件
     */
    public static void startNetMonitor(Context context, boolean inMainProcess) {
        String fileSavePath = getNetMonitorStoreFolder(context);
        if (fileSavePath == null)
            return;
        String fileName = "netLog-" + (inMainProcess ? "main" : "player") + ".txt";
        PlayerUtil.fileIsExistCreate(fileSavePath + fileName);
        sLogFile = new File(fileSavePath + fileName);
        if (!sLogFile.exists())
            return;
        try {
//            sink = Okio.appendingSink(sLogFile);
//            sBuffer = Okio.buffer(sink);
            printWriter = new PrintWriter(new FileWriter(sLogFile, true));
            NetworkMonitorInterceptor.setLevel(NetworkMonitorInterceptor.Level.BODY);
            NetworkMonitorInterceptor.setNetWorkProxy(sLogger);
            sRunning = true;
            Logger.logToSd("startNetMonitor success in inMainProcess: " + inMainProcess);
        } catch (Exception e) {
            Logger.e(e);
        }
    }

    public static void closeNetMonitor() {
        sRunning = false;
        sLogFile = null;
        NetworkMonitorInterceptor.setLevel(NetworkMonitorInterceptor.Level.NONE);
        NetworkMonitorInterceptor.setNetWorkProxy(null);
        try {
            if (printWriter != null) {
                printWriter.close();
            }
        } catch (Exception e) {
            Logger.e(e);
        }
    }

    public static void flushIo() {
        if (printWriter != null)
            printWriter.flush();
    }

    public static File getLogFile() {
        return sLogFile;
    }

    public static void addMsg(Context context, String msg) {
        if (TextUtils.isEmpty(msg))
            return;
        try {
            if (printWriter != null) {
                printWriter.write(msg);
            } else {
                String fileSavePath = getNetMonitorStoreFolder(context);
                if (fileSavePath == null)
                    return;
                if (BaseUtil.isMainProcess(context)) {
                    File mainProcessFile = new File(fileSavePath, "netLog-main.txt");
                    if (mainProcessFile.exists()) {
                        printWriter = new PrintWriter(new FileWriter(mainProcessFile, true));
                        printWriter.write(msg);
                        printWriter.close();
                    }
                } else {
                    File playerProcessFile = new File(fileSavePath, "netLog-player.txt");
                    if (playerProcessFile.exists()) {
                        printWriter = new PrintWriter(new FileWriter(playerProcessFile, true));
                        printWriter.write(msg);
                        printWriter.close();
                    }
                }
            }
        } catch (Exception e) {
            Logger.e(e);
        }
    }

    public static boolean isNetMonitorRunning() {
        return sRunning && sLogFile != null;
    }
}
