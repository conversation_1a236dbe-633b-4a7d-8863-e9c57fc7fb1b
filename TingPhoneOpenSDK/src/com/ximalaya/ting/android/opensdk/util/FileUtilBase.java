package com.ximalaya.ting.android.opensdk.util;

import android.content.Context;
import android.content.res.AssetManager;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.media.ExifInterface;
import android.media.ThumbnailUtils;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.StatFs;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;

import com.ximalaya.ting.android.opensdk.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.CommonRequestForMain;
import com.ximalaya.ting.android.opensdk.httputil.BaseBuilder;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.httputil.Config;
import com.ximalaya.ting.android.opensdk.httputil.XimalayaException;
import com.ximalaya.ting.android.opensdk.httputil.util.MarkableInputStream;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationCreater;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.player.MD5;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.push.IWidgetService;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;

import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Cache;
import okhttp3.CacheControl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.internal.Util;

/**
 * <AUTHOR>
 */
public class FileUtilBase {
	public static String getAdsCacheDir(Context context, String uniqueName) {
		if(context == null) {
			return "";
		}
		String cachePath = getPrivateRootDir(context) + "/soundtiepian";
		File file = new File(cachePath);
		if (!file.exists()) {
			file.mkdirs();
		}
		return cachePath + File.separator + uniqueName;
	}

	public static String getPrivateRootDir(Context context){
		String cachePath;
		try {
			if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())) {
				cachePath = context.getExternalFilesDir("").getPath();
			} else {
				cachePath = context.getFilesDir().getPath();
			}
		} catch (Exception e) {
			e.printStackTrace();
			if(context == null) {
				return "";
			}
			cachePath = context.getFilesDir().getPath() ;
		}

		return cachePath;
	}

	public static String getAssetsCacheDir(Context context, String uniqueName) {
		String cachePath = getPrivateRootDir(context) + "/assets";
		File file = new File(cachePath);
		if (!file.exists()) {
			file.mkdirs();
		}
		return cachePath + File.separator + uniqueName;
	}

	public interface IBitmapDownOkCallBack {
		void onSuccess(Bitmap bitmap);
	}

	private static String lastImageUrl;
	private static String PICASSO_CACHE_PATH ;
	private static String PERPETUAL_CACHE_PATH;
	private static ExecutorService executorService = Executors.newCachedThreadPool();
	private static Bitmap tempBitmap;
	private static Map<String ,Set<RequestCallBackModel>> callBackMaps = new ConcurrentHashMap<String, Set<RequestCallBackModel>>();


	static class RequestCallBackModel {
		public IBitmapDownOkCallBack bitmapDownOkCallBack;
		public int width;
		public int height;

		public RequestCallBackModel(IBitmapDownOkCallBack bitmapCallBackSet, int width, int height) {
			this.bitmapDownOkCallBack = bitmapCallBackSet;
			this.width = width;
			this.height = height;
		}
	}

	public static String getLargeImgUrl(Track track) {
		String imgUrl = "";
		if(track != null) {
			if(!TextUtils.isEmpty(track.getCoverUrlLarge())) {
				imgUrl = track.getCoverUrlLarge();
			} else if(!TextUtils.isEmpty(track.getCoverUrlMiddle())) {
				imgUrl = track.getCoverUrlMiddle();
			} else if(!TextUtils.isEmpty(track.getCoverUrlSmall())) {
				imgUrl = track.getCoverUrlSmall();
			}
			if(TextUtils.isEmpty(imgUrl)) {
				imgUrl = track.getDataId() + "";
			}
		}
		return imgUrl;
	}

	private static String getNotifiAndRemoteImgUrl(Track track) {
		String imgUrl = "";
		if(track != null) {
			if(!TextUtils.isEmpty(track.getCoverUrlLarge())) {
				imgUrl = track.getCoverUrlLarge();
			} else if(!TextUtils.isEmpty(track.getCoverUrlMiddle())) {
				imgUrl = track.getCoverUrlMiddle();
			} else if(!TextUtils.isEmpty(track.getCoverUrlSmall())) {
				imgUrl = track.getCoverUrlSmall();
			}
		}
		return imgUrl;
	}

	/**
	 * 获得bitmap通过url 不检查并发 一般情况下使用此方法
	 * 如果可以使用ImageManager就不要使用此方法
	 */
	public static void getBitmapByUrlAndNotConcurrence(final Context context , final Track track ,
													   final int requestWidth , final int requestHeight , final IBitmapDownOkCallBack bitmapDownOkCallBack) {
		getBitmapByUrl(false, context,track, getLargeImgUrl(track), requestWidth, requestHeight, bitmapDownOkCallBack ,true);
	}

	/**
	 * 如果可以使用ImageManager就不要使用此方法,基本上就是播放器内部使用
	 */
	public static void getBitmapByUrl(final Context context , final Track track , final int requestWidth ,
									  final int requestHeight , final IBitmapDownOkCallBack bitmapDownOkCallBack) {


		getBitmapByUrl(false, context , track, getLargeImgUrl(track), requestWidth, requestHeight, bitmapDownOkCallBack ,false);
	}

	public static void getBitmapByUrl(boolean useSmallBitmapForRemote, final Context context , final Track track, final String imgUrl, final int requestWidth ,
									  final int requestHeight , final IBitmapDownOkCallBack bitmapDownOkCallBack) {
		getBitmapByUrl(useSmallBitmapForRemote, context , track, imgUrl, requestWidth, requestHeight, bitmapDownOkCallBack ,false);
	}
	/**
	 * 如果可以使用ImageManager就不要使用此方法,基本上就是播放器内部使用
	 */
	public static void getBitmapByUrl(boolean useSmallBitmapForRemote, final Context context , final Track track , final int requestWidth ,
									  final int requestHeight , final IBitmapDownOkCallBack bitmapDownOkCallBack) {


		getBitmapByUrl(useSmallBitmapForRemote, context , track, getLargeImgUrl(track), requestWidth, requestHeight, bitmapDownOkCallBack ,false);
	}

	public static void getBitmapByUrlByUrl(boolean useSmallBitmapForRemote, final Context context, String url, final int requestWidth ,
										   final int requestHeight , final IBitmapDownOkCallBack bitmapDownOkCallBack) {


		getBitmapByUrl(useSmallBitmapForRemote, context , null, url, requestWidth, requestHeight, bitmapDownOkCallBack ,true);
	}

	/**
	 * 如果可以使用ImageManager就不要使用此方法
	 */
	private static void getBitmapByUrl(boolean useSmallBitmapForRemote, final Context context ,Track track , final String imgPath , final int requestWidth ,
									   final int requestHeight , final IBitmapDownOkCallBack bitmapDownOkCallBack ,boolean isConcurrence) {
		if(TextUtils.isEmpty(imgPath) || "null".equals(imgPath)) {
			if(bitmapDownOkCallBack != null) {
				bitmapDownOkCallBack.onSuccess(null);
			}
			return;
		}

		if(context == null) {
			if(bitmapDownOkCallBack != null) {
				bitmapDownOkCallBack.onSuccess(null);
			}
			return;
		}

		synchronized (FileUtilBase.class) {
			if(isConcurrence) {
				if(executorService == null || executorService.isShutdown()) {
					executorService = Executors.newCachedThreadPool();
				}

				try {
					executorService.execute(new GetBitmapRunnable(context, imgPath, requestWidth, requestHeight ,bitmapDownOkCallBack ,track, useSmallBitmapForRemote));
				} catch (Exception e) {
					e.printStackTrace();
				}
				return;
			}

			boolean isFirst = false;

			if(!imgPath.equals(lastImageUrl)) {
				lastImageUrl = imgPath;
				callBackMaps.clear();
				tempBitmap = null;
			} else {
				if(tempBitmap != null && !tempBitmap.isRecycled() && bitmapDownOkCallBack != null) {
					bitmapDownOkCallBack.onSuccess(tempBitmap);
					return;
				}
			}

			Set<RequestCallBackModel> requestCallBackModels = callBackMaps.get(imgPath);
			if(requestCallBackModels == null) {
				requestCallBackModels = new CopyOnWriteArraySet<>();
				isFirst = true;
			}

			requestCallBackModels.add(new RequestCallBackModel(bitmapDownOkCallBack ,requestWidth ,requestHeight));
			callBackMaps.put(imgPath ,requestCallBackModels);

			if(!isFirst) {
				return;
			}

			if(executorService == null || executorService.isShutdown()) {
				executorService = Executors.newCachedThreadPool();
			}
			try {
				executorService.execute(new GetBitmapRunnable(context, imgPath, track, requestWidth, requestHeight, useSmallBitmapForRemote));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	private static void callBackResult(Bitmap bitmap , String imgPath, int requestWidth, int requestHeight, boolean useSmallBitmapForRemote) {
		if(TextUtils.isEmpty(imgPath)) {
			return;
		}

		final Set<RequestCallBackModel> requestCallBackModelSet = callBackMaps.get(imgPath);
		if(requestCallBackModelSet != null) {
			for (RequestCallBackModel requestCallBackModel : requestCallBackModelSet) {
				if(requestCallBackModel != null && requestCallBackModel.bitmapDownOkCallBack != null && (bitmap == null || (bitmap != null && !bitmap.isRecycled()))) {
					if(requestCallBackModel.width > 0 && requestCallBackModel.height > 0) {
						Bitmap bitmap1 = ThumbnailUtils.extractThumbnail(bitmap, requestCallBackModel.width, requestCallBackModel.height);
						handleBitmapCallBack(requestCallBackModel.bitmapDownOkCallBack ,bitmap1, imgPath, requestWidth, requestHeight, useSmallBitmapForRemote);
					} else {
						handleBitmapCallBack(requestCallBackModel.bitmapDownOkCallBack ,bitmap, imgPath, requestWidth, requestHeight, useSmallBitmapForRemote);
					}
				} else {
					if (requestCallBackModel != null)
						handleBitmapCallBack(requestCallBackModel.bitmapDownOkCallBack ,null, imgPath, requestWidth, requestHeight, useSmallBitmapForRemote);
				}
			}
			callBackMaps.remove(imgPath);
		}
	}

	static void handleBitmapCallBack(final IBitmapDownOkCallBack callBack , final Bitmap bitmap, String imgPath, int requestWidth, int requestHeight, boolean useSmallBitmapForRemote) {
		Handler handler = XmPlayerService.getPlayerSrvice() != null ? XmPlayerService.getPlayerSrvice().getTimeHander() :  new Handler(Looper.getMainLooper());
		handler.post(() -> {
			if(callBack != null) {
				if(bitmap != null && !bitmap.isRecycled()) {
					try {
						callBack.onSuccess(bitmap);
					} catch (Throwable e) {
						e.printStackTrace();
					}
				} else {
					IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
					IWidgetService iWidgetService = RouterServiceManager.getInstance().getService(IWidgetService.class);
					boolean configOpen = MMKVUtil.getInstance().getBoolean(XmNotificationCreater.ITEM_USE_IMAGE_MANAGER_FIX_BLACK_REMOTE, true);
					if (iWidgetService != null && configOpen) {
						iWidgetService.getBitmapWithGlide(bitmap1 -> {
							if (bitmap1 != null) {
								Bitmap remoteBitmap = null;
								if (useSmallBitmapForRemote && requestHeight > 0 && requestWidth > 0) {
									remoteBitmap = Bitmap.createBitmap(requestWidth, requestHeight, Bitmap.Config.RGB_565);
									Canvas tempCanvas = new Canvas(remoteBitmap);
									Paint paint = new Paint();
									paint.setAntiAlias(true);
									tempCanvas.drawBitmap(bitmap1, null, new Rect(0, 0, requestWidth, requestHeight), paint);
								}
								tempBitmap = remoteBitmap != null ? remoteBitmap : bitmap1;
								callBack.onSuccess(remoteBitmap != null ? remoteBitmap : bitmap1);
							} else {
								if (XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().getContext() != null) {
									Bitmap defaultMap = BitmapFactory.decodeResource(XmPlayerService.getPlayerSrvice().getContext().getResources(), R.drawable.notify_default);
									tempBitmap = null;
									if (xdcsPost != null) {
										xdcsPost.statErrorToXDCS("remote_callback_bitmap_null_new", "remoteBitmap1 = " + imgPath);
									}
									callBack.onSuccess(defaultMap);
								} else {
									if (xdcsPost != null) {
										xdcsPost.statErrorToXDCS("remote_callback_bitmap_null_new", "remoteBitmap2 = " + imgPath);
									}
									tempBitmap = null;
									callBack.onSuccess(null);
								}
							}
						}, imgPath);
					} else {
						if (XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().getContext() != null) {
							Bitmap defaultMap = BitmapFactory.decodeResource(XmPlayerService.getPlayerSrvice().getContext().getResources(), R.drawable.notify_default);
							tempBitmap = null;
							if (xdcsPost != null) {
								xdcsPost.statErrorToXDCS("remote_callback_bitmap_null_new", "remoteBitmap3 = " + imgPath);
							}
							callBack.onSuccess(defaultMap);
						} else {
							if (xdcsPost != null) {
								xdcsPost.statErrorToXDCS("remote_callback_bitmap_null_new", "remoteBitmap4 = " + imgPath);
							}
							tempBitmap = null;
							callBack.onSuccess(null);
						}
					}
				}
			}
		});
	}

	static class GetBitmapRunnable implements Runnable {
		private Context context;
		private String imgPath;
		int requestWidth ,requestHeight;
		IBitmapDownOkCallBack bitmapDownOkCallBack;
		private Track track;
		private long trackId;
		private boolean useSmallBitmapForRemote;

		// 不考虑并发
		public GetBitmapRunnable(Context context, String imgPath, int requestWidth, int requestHeight, final IBitmapDownOkCallBack bitmapDownOkCallBack,
								 Track track, boolean useSmallBitmapForRemote) {
			this(context ,imgPath ,track);
			this.requestWidth = requestWidth;
			this.requestHeight = requestHeight;
			this.bitmapDownOkCallBack = bitmapDownOkCallBack;
			this.useSmallBitmapForRemote = useSmallBitmapForRemote;
		}

		public GetBitmapRunnable(Context context, String imgPath, int requestWidth, int requestHeight ,Track track, boolean useSmallBitmapForRemote) {
			this(context, imgPath, requestWidth, requestHeight, null ,track, useSmallBitmapForRemote);
		}

		// 不进行缩放
		public GetBitmapRunnable(Context context ,String imgPath ,Track track) {
			this.context = context;
			this.imgPath = imgPath;
			this.track = track;
			if(track != null) {
				trackId = track.getDataId();
			}
		}


		// 下载原图不进行缩放，返回的时候返回小图
		public GetBitmapRunnable(Context context ,String imgPath ,Track track, int requestWidth, int requestHeight, boolean useSmallBitmapForRemote) {
			this.context = context;
			this.imgPath = imgPath;
			this.track = track;
			this.useSmallBitmapForRemote = useSmallBitmapForRemote;
			if(track != null) {
				trackId = track.getDataId();
			}
			if (useSmallBitmapForRemote) {
				this.requestWidth = requestWidth;
				this.requestHeight = requestHeight;
			}
		}

		@Override
		public void run() {
			boolean isCoverImgPath = false;
			if(imgPath != null && imgPath.equals(trackId + "")) {
				imgPath = getNotifiAndRemoteImgUrl(track);
				if(TextUtils.isEmpty(imgPath) && track != null && PlayableModel.KIND_TRACK.equals(track.getKind())) {
					try {
						Track track = CommonRequestForMain.getTrackInfo(this.track);
						if(track != null) {
							this.track.setCoverUrlLarge(track.getCoverUrlLarge());
							this.track.setCoverUrlMiddle(track.getCoverUrlMiddle());
							this.track.setCoverUrlSmall(track.getCoverUrlSmall());
						}
						imgPath = getNotifiAndRemoteImgUrl(track);
					} catch (Exception e) {
						e.printStackTrace();
					}
				}

				isCoverImgPath = true;
			}

			Bitmap bitmap = null;
			Bitmap remoteBitmap = null;
			try {
				if(!TextUtils.isEmpty(imgPath)) {
					bitmap = getBitmapThread(context, imgPath, requestWidth, requestHeight);
				}

				if (useSmallBitmapForRemote && bitmap != null) {
					remoteBitmap = Bitmap.createBitmap(requestWidth, requestHeight, Bitmap.Config.RGB_565);
					Canvas tempCanvas = new Canvas(remoteBitmap);
					Paint paint = new Paint();
					paint.setAntiAlias(true);
					tempCanvas.drawBitmap(bitmap, null, new Rect(0, 0, requestWidth, requestHeight), paint);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}

			if(bitmapDownOkCallBack != null) {
				handleBitmapCallBack(bitmapDownOkCallBack, useSmallBitmapForRemote && remoteBitmap != null ? remoteBitmap : bitmap, imgPath, requestWidth, requestHeight, useSmallBitmapForRemote);
				return;
			}

			if(imgPath.equals(lastImageUrl) || (isCoverImgPath && (trackId + "").equals(lastImageUrl))) {
				tempBitmap = useSmallBitmapForRemote && remoteBitmap != null ? remoteBitmap : bitmap;
				callBackResult(tempBitmap, imgPath, requestWidth, requestHeight, useSmallBitmapForRemote);
			} else {
				callBackMaps.remove(imgPath);
			}
		}
	}

	@WorkerThread
	static Bitmap getBitmapThread(Context context, String imgPath, int requestWidth, int requestHeight) {
		if (context == null){
			logToFile("remote getBitmapThread context null");
			return null;
		}
		if (requestHeight <= 0){
			requestHeight = getScreenHeight(context);
		}
		if (requestWidth <= 0){
			requestWidth = getScreenWidth(context);
		}
		PICASSO_CACHE_PATH = context.getCacheDir().getAbsolutePath() + File.separator + "picasso-cache";
		File exterFile = context.getExternalFilesDir("images");
		if(exterFile!=null&&exterFile.exists()) {
			PERPETUAL_CACHE_PATH = exterFile.getAbsolutePath() + File.separator + "images";
		}else{
			PERPETUAL_CACHE_PATH = PICASSO_CACHE_PATH;
		}

		String filePath = PICASSO_CACHE_PATH + File.separator + MD5.md5(imgPath) + ".1";

		boolean isExist = new File(filePath).exists();

		if(!isExist) {
			String tempPath = PERPETUAL_CACHE_PATH + File.separator + urlToOldFileName(imgPath);
			isExist = new File(tempPath).exists();
			if(isExist) {
				filePath = tempPath;
			}
		}
		logToFile("remote getBitmapThread isExist = " + isExist);

		if (isExist) {
			Bitmap decodeBitmap = decode(filePath ,requestWidth ,requestHeight);
			if (decodeBitmap != null) {
				return decodeBitmap;
			}
		}
		CacheControl cacheControl = null;

		if(!NetworkType.isConnectTONetWork(context)) {
			cacheControl = CacheControl.FORCE_CACHE;
		} else {
			CacheControl.Builder builder = new CacheControl.Builder();
			cacheControl = builder.build();
		}

		Request request = null;

		try {
			Request.Builder builder = BaseBuilder.urlGet(reduceHttpsToHttp(context ,imgPath)).tag(imgPath);
			if (cacheControl != null) {
				builder.cacheControl(cacheControl);
			}
			request = builder.build();
		} catch (XimalayaException e1) {
			logToFile("remote getBitmapThread e1 = " + e1);
			return null;
		} catch (Exception e) {
			logToFile("remote getBitmapThread e = " + e);
			return null;
		}

		try {
			Response response = getOkHttpClient(context).newCall(request).execute();
			if(response == null) {
				logToFile("remote getBitmapThread response = null");
				return null;
			}

			if(response.code() == 200) {
				if(new File(filePath).exists()) {
					response.body().close();
					logToFile("remote getBitmapThread exists decode");
					Bitmap decodeBitmap = decode(filePath, requestWidth, requestHeight);
					if (decodeBitmap != null) {
						return decodeBitmap;
					}
				}
				if (response.body().contentLength() > 30 * 1024 * 1024){
					logToFile("remote getBitmapThread contentLength to big");
					return null;
				}
				InputStream inputStream = response.body().byteStream();
				Bitmap bitmap = null;
				try {
					bitmap = decodeStream(inputStream,requestWidth,requestHeight);
				} catch (Exception e) {
					e.printStackTrace();
					logToFile("remote getBitmapThread decodeStream e = " + e);
					bitmap = BitmapFactory.decodeStream(inputStream);
				}

				Util.closeQuietly(inputStream);
				return bitmap;
			}
		} catch (Exception e) {
			e.printStackTrace();
			logToFile("remote getBitmapThread decodeStream out e = " + e);
		}
		return null;
	}

	/**
	 * get the width of the device screen
	 *
	 * @param context
	 * @return
	 */
	public static int getScreenWidth(Context context) {
		if (context == null)
			return 1;

		Resources resources = context.getResources();
		if(resources == null) {
			return 1;
		}
		int screenWidth = resources.getDisplayMetrics().widthPixels;
		int screenHeight = resources.getDisplayMetrics().heightPixels;

		return screenWidth < screenHeight ? screenWidth : screenHeight;
	}

	/**
	 * get the height of the device screen
	 *
	 * @param context
	 * @return
	 */
	public static int getScreenHeight(Context context) {
		if (context == null)
			return 1;

		Resources resources = context.getResources();
		if(resources == null) {
			return 1;
		}
		int screenWidth = resources.getDisplayMetrics().widthPixels;
		int screenHeight = resources.getDisplayMetrics().heightPixels;

		return screenWidth > screenHeight ? screenWidth : screenHeight;
	}

	public static void release() {
		lastImageUrl = null;
		PICASSO_CACHE_PATH = null;
		tempBitmap = null;
		callBackMaps.clear();
		mOkHttpClient = null;
		mCache = null;
	}

	private static OkHttpClient mOkHttpClient;
	private static OkHttpClient getOkHttpClient(Context context) {
		if (mOkHttpClient == null) {
			synchronized (FileUtilBase.class) {
				if (mOkHttpClient == null) {
					mOkHttpClient = BaseCall.getInstanse().getOkHttpClient(null);

					OkHttpClient.Builder builder = mOkHttpClient.newBuilder();
					builder.cache(getCache(context));
					mOkHttpClient = builder.build();
				}
			}
		}
		return mOkHttpClient;
	}

	public static void setProxy(Context context ,Config config) {
		if(mOkHttpClient == null) {
			return;
		}
		OkHttpClient.Builder builder = mOkHttpClient.newBuilder();
		FreeFlowServiceUtil.updateProxyToBuilder(context ,config ,builder ,false);
		mOkHttpClient = builder.build();
	}

	/***
	 * TODO 下面的代码是在picasso 项目中的Utils.java 拷贝过来的如果那边有改变要做相应的改变 ,不做改变也没有关系只是另外新建了一个图片缓存目录
	 */
	private static Cache mCache;
	public static Cache getCache(Context context) {
		if(mCache == null) {
			synchronized (FileUtilBase.class) {
				if(mCache == null) {
					File cacheDir = createDefaultCacheDir(context);
					mCache = new Cache(cacheDir, calculateDiskCacheSize(cacheDir)) ;
				}
			}
		}
		return mCache;
	}

	private static final String PICASSO_CACHE = "picasso-cache";

	public static File createDefaultCacheDir(Context context) {
		File cache = new File(context.getApplicationContext().getCacheDir(),
				PICASSO_CACHE);
		if (!cache.exists()) {
			// noinspection ResultOfMethodCallIgnored
			cache.mkdirs();
		}
		return cache;
	}

	private static final int MIN_DISK_CACHE_SIZE = 5 * 1024 * 1024; // 5MB
	private static final int MAX_DISK_CACHE_SIZE = 50 * 1024 * 1024; // 50MB

	public static long calculateDiskCacheSize(File dir) {
		long size = MIN_DISK_CACHE_SIZE;

		try {
			StatFs statFs = new StatFs(dir.getAbsolutePath());
			long available = ((long) statFs.getBlockCount())
					* statFs.getBlockSize();
			// Target 2% of the total space.
			size = available / 50;
		} catch (IllegalArgumentException ignored) {
		}

		// Bound inside min/max size for disk cache.
		return Math.max(Math.min(size, MAX_DISK_CACHE_SIZE),
				MIN_DISK_CACHE_SIZE);
	}

	public static String urlToOldFileName(String url) {
		// 扩展名位置
		int index = url.lastIndexOf('.');
		if (index == -1) {
			return null;
		}

		StringBuilder filePath = new StringBuilder();

		// 图片文件名
		filePath.append(MD5.md5(url)).append(url.substring(index + 1));

		return filePath.toString();
	}

	@Nullable
	@WorkerThread
	public static Bitmap decode(@Nullable final String filePath,
								int requiredWidth, int requiredHeight) {
		if (TextUtils.isEmpty(filePath)) {
			logToFile("remote decode filePath null");
			return null;
		}
		try {
			Bitmap decodeSampledBitmap = null;
			if(requiredWidth <= 0 && requiredHeight <= 0) {
				decodeSampledBitmap =  BitmapFactory.decodeFile(filePath);
				if (decodeSampledBitmap == null) {
					logToFile("remote decode decodeFile1 null");
				}
			} else {
				final BitmapFactory.Options options = new BitmapFactory.Options();
				options.inJustDecodeBounds = true;
				BitmapFactory.decodeFile(filePath ,options);
				options.inSampleSize = calculateInSampleSize(options, requiredWidth, requiredHeight);
				options.inJustDecodeBounds = false;
				decodeSampledBitmap = BitmapFactory.decodeFile(filePath ,options);
				if (decodeSampledBitmap == null) {
					logToFile("remote decode decodeFile2 null");
				}
			}

			ExifInterface exif = getExif(filePath);
			if (exif != null) {
				logToFile("remote decode exif != null");
				final int exifOrientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
				if (decodeSampledBitmap == null) {
					logToFile("remote decode exif != null: decodeSampledBitmap = null");
				}
				return rotateBitmap(decodeSampledBitmap, exifToDegrees(exifOrientation));
			} else {
				logToFile("remote decode exif null");
				return decodeSampledBitmap;
			}
		} catch (Throwable e) {
			logToFile("remote decode Throwable " + e);
			e.printStackTrace();
		}
		return null;
	}

	public static Bitmap rotateBitmap(@Nullable Bitmap bitmap, int degrees) {
		if (bitmap != null && degrees != 0) {
			Matrix rotateMatrix = new Matrix();
			rotateMatrix.setRotate(degrees, bitmap.getWidth() / (float) 2, bitmap.getHeight() / (float) 2);

			Bitmap converted = Bitmap.createBitmap(bitmap, 0, 0,
					bitmap.getWidth(), bitmap.getHeight(), rotateMatrix, true);
			if (bitmap != converted) {
				if (converted == null) {
					logToFile("remote rotateBitmap converted = null");
				}
				bitmap = converted;
			}
		}
		if (bitmap == null) {
			logToFile("remote rotateBitmap bitmap = null");
		}
		return bitmap;
	}

	public static int calculateInSampleSize(@NonNull BitmapFactory.Options options, int reqWidth, int reqHeight) {
		// Raw height and width of image
		final int height = options.outHeight;
		final int width = options.outWidth;
		int inSampleSize = 1;

		if (height > reqHeight || width > reqWidth) {
			// Calculate the largest inSampleSize value that is a power of 2 and keeps both
			// height and width lower or equal to the requested height and width.
			while ((height / inSampleSize) > reqHeight || (width / inSampleSize) > reqWidth) {
				inSampleSize *= 2;
			}
		}
		return inSampleSize;
	}

	@Nullable
	private static ExifInterface getExif(@NonNull String imagePath) {
		try {
			return new ExifInterface(imagePath);
		} catch (IOException e) {
		}
		return null;
	}

	private static int exifToDegrees(int exifOrientation) {
		if (exifOrientation == ExifInterface.ORIENTATION_ROTATE_90) {
			return 90;
		} else if (exifOrientation == ExifInterface.ORIENTATION_ROTATE_180) {
			return 180;
		} else if (exifOrientation == ExifInterface.ORIENTATION_ROTATE_270) {
			return 270;
		}
		return 0;
	}

	/**
	 * 一般使用FileUtil 中的deleFile
	 * @param dir
	 */
	public static void deleFileNoCheckDownloadFile(File dir) {
		if(dir == null) {
			return;
		}
		if (dir.isDirectory()) {
			File[] files = dir.listFiles();
			if(files!=null) {
				for (File f : files) {
					deleFileNoCheckDownloadFile(f);
				}
			}
			dir.delete();
		} else {
			dir.delete();
		}
	}

	// 因为目前免流运营商不支持https设置代理 ,所以如果发现资源被替换为https 就将其降级为http
	public static String reduceHttpsToHttp(Context context ,String url) {
		// 这里现在都支持https的代理了
//		if(TextUtils.isEmpty(url) || url.startsWith("http://")) {
//			return url;
//		}
//
//		IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
//		boolean usingFreeFlow = false;
//		if (freeFlowService != null) {
//			usingFreeFlow = freeFlowService.isUsingFreeFlow();
//		}
//		if(usingFreeFlow) {
//			String host = Uri.parse(url).getHost();
//			if(Advertis.thirdHostList.contains(host)) {
//				return url;
//			}
//			return url.replaceFirst("https" ,"http");
//		}
		return url;
	}

	public static int copyAssetsToFile(AssetManager manager, String asset,
									   String file) {
		InputStream fis = null;
		File dest = null;
		FileOutputStream fos = null;
		try {
			fis = manager.open(asset);
			dest = new File(file);
			if (dest.exists() && dest.length() > 0) {
				return 0;
			}
			fos = new FileOutputStream(dest);
			byte[] buff = new byte[8192];
			int read = 0;
			while ((read = fis.read(buff)) > 0) {
				if (read > 0) {
					fos.write(buff, 0, read);
				}
			}
			fos.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (fos != null) {
				try {
					fos.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (fis != null) {
				try {
					fis.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return 0;
	}

	public static void logToFile(String log) {
		if (!ConstantsOpenSdk.isDebug) {
			return;
		}
		Logger.logToFile(log);
	}

	public static Bitmap decodeStream(InputStream stream, int requiredWidth, int requiredHeight) throws IOException {
		MarkableInputStream markStream = new MarkableInputStream(stream);
		markStream.allowMarksToExpire(false);
		long mark = markStream.savePosition(1024);
		final BitmapFactory.Options options = new BitmapFactory.Options();
		options.inJustDecodeBounds = true;
		boolean isWebPFile = isWebPFile(markStream);
		markStream.reset(mark);
		if(!isWebPFile) {
			BitmapFactory.decodeStream(markStream, (Rect)null, options);
			options.inSampleSize = calculateInSampleSize(options, requiredWidth, requiredHeight);
			options.inJustDecodeBounds = false;
			markStream.reset(mark);
			markStream.allowMarksToExpire(true);
			Bitmap bitmap = BitmapFactory.decodeStream(markStream, (Rect)null, options);
			if(bitmap == null) {
				throw new IOException("Failed to decode stream.");
			} else {
				return bitmap;
			}
		} else {
			byte[] bytes = toByteArray(markStream);
			BitmapFactory.decodeByteArray(bytes, 0, bytes.length, options);
			options.inSampleSize = calculateInSampleSize(options, requiredWidth, requiredHeight);
			options.inJustDecodeBounds = false;
			return BitmapFactory.decodeByteArray(bytes, 0, bytes.length, options);
		}
	}

	public  static boolean isWebPFile(InputStream stream) throws IOException {
		byte[] fileHeaderBytes = new byte[12];
		boolean isWebPFile = false;
		if(stream.read(fileHeaderBytes, 0, 12) == 12) {
			isWebPFile = "RIFF".equals(new String(fileHeaderBytes,
					0, 4, "US-ASCII"))
					&& "WEBP".equals(new String(fileHeaderBytes, 8, 4, "US-ASCII"));
		}

		return isWebPFile;
	}

	public  static byte[] toByteArray(InputStream input) throws IOException {
		ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
		byte[] buffer = new byte[4096];

		int n;
		while(-1 != (n = input.read(buffer))) {
			byteArrayOutputStream.write(buffer, 0, n);
		}

		return byteArrayOutputStream.toByteArray();
	}

	public static void close(final Closeable... closeables) {
		try {
			for (Closeable closeable : closeables) {
				if (closeable == null) {
					continue;
				}
				closeable.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
