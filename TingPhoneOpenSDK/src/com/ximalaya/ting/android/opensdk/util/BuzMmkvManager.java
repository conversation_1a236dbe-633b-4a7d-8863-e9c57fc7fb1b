package com.ximalaya.ting.android.opensdk.util;


import android.content.Context;
import android.text.TextUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class BuzMmkvManager {
    public static final String sStartCommonBuzKey = "start_common_buz";
    public static final String sPluginBuzKey = "plugin_buz";

    private Map<String, BaseMmkvUtil> mBaseMmkvUtilMap = new ConcurrentHashMap<>();
    private static BuzMmkvManager mInstance;
    private Context mContext;
    private BuzMmkvManager(Context context) {
        mContext = context;
    }

    public static BuzMmkvManager getInstance(Context context) {
        if (mInstance == null) {
            synchronized (BuzMmkvManager.class) {
                if (mInstance == null) {
                    mInstance = new BuzMmkvManager(context);
                }
            }
        }
        return mInstance;
    }
    public BaseMmkvUtil getStartMmkv(){
        return getMmkvForBz("");
    }

    public synchronized BaseMmkvUtil getMmkvForBz(String buzKey) {
        if (TextUtils.isEmpty(buzKey)){
            buzKey = sStartCommonBuzKey;
        }
        BaseMmkvUtil baseMmkvUtil = mBaseMmkvUtilMap.get(buzKey);
        if (baseMmkvUtil == null){
            baseMmkvUtil = new BaseMmkvUtil(mContext, buzKey);
            mBaseMmkvUtilMap.put(buzKey,baseMmkvUtil);
        }
        return baseMmkvUtil;
    }
}
