package com.ximalaya.ting.android.opensdk.util;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.encryptservice.EncryptUtil;
import com.ximalaya.ting.android.opensdk.constants.MmkvConstantsInOpenSdk;

import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Created by zhifu.zhang on 5/14/2020 AD.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18362080562
 * @desc mmkv通用工具类
 * 兼容迁移SharedPreferenceUtil(ting_data)数据功能
 * 如需处理迁移其他sp文件数据，可继承该类，重新创建SharedPreferencesUtil
 */
public class MmkvCommonUtil extends BaseMmkvUtil {

    private static volatile MmkvCommonUtil INSTANCE;
    @Nullable
    private SharedPreferencesUtil mSharedPreferencesUtil;

    protected MmkvCommonUtil(Context context, String name) {
        super(context, name);
        mSharedPreferencesUtil = createSharedPreferenceUtil(mAppContext);
    }

    protected SharedPreferencesUtil createSharedPreferenceUtil(Context context) {
        return SharedPreferencesUtil.getInstance(mAppContext);
    }

    public static MmkvCommonUtil getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (MmkvCommonUtil.class) {
                if (INSTANCE == null) {
                    INSTANCE = new MmkvCommonUtil(context, MmkvConstantsInOpenSdk.OPENSDK_FILENAME_TING_DATA);
                }
            }
        }
        return INSTANCE;
    }

    /******************以下方法是为了兼容已经存在于SharedPreferencesUtil(ting_data文件)中的数据**********/
    /******************新增加的数据禁止调用下面方法****************************************************/

    public boolean containsKeyCompat(String key) {
        return containsKey(key) || (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key));
    }

    public String getStringCompat(String key) {
        String val = getString(key, null);
        if (val != null) {
            return val;
        }
        if (mSharedPreferencesUtil == null || !mSharedPreferencesUtil.contains(key)) {
            return val;
        }
        val = mSharedPreferencesUtil.getString(key);
        if (!TextUtils.isEmpty(val)) {
            saveString(key, val);
        }
        return val;
    }

    public boolean getBooleanCompat(String key) {
        return getBooleanCompat(key, false);
    }

    public boolean getBooleanCompat(String key, boolean defaultVal) {
        if (containsKey(key)) {
            return getBoolean(key);
        } else if (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key)) {
            boolean value = mSharedPreferencesUtil.getBoolean(key);
            saveBoolean(key, value);
            return value;
        } else {
            return defaultVal;
        }
    }

    public float getFloatCompat(String key) {
        if (containsKey(key)) {
            return getFloat(key);
        } else if (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key)) {
            float value = mSharedPreferencesUtil.getFloat(key);
            saveFloat(key, value);
            return value;
        } else {
            return -1f;
        }
    }

    public int getIntCompat(String key) {
        if (containsKey(key)) {
            return getInt(key);
        } else if (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key)) {
            int value = mSharedPreferencesUtil.getInt(key, -1);
            saveInt(key, value);
            return value;
        } else {
            return -1;
        }
    }

    public int getIntCompat(String key, int defaultValue) {
        if (containsKey(key)) {
            return getInt(key, defaultValue);
        } else if (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key)) {
            int value = mSharedPreferencesUtil.getInt(key, defaultValue);
            saveInt(key, value);
            return value;
        } else {
            return defaultValue;
        }
    }

    public double getDoubleCompat(String key) {
        if (containsKey(key)) {
            return getDouble(key);
        } else if (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key)) {
            double value = mSharedPreferencesUtil.getDouble(key);
            saveDouble(key, value);
            return value;
        } else {
            return -1d;
        }
    }

    public long getLongCompat(String key) {
        if (containsKey(key)) {
            return getLong(key);
        } else if (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key)) {
            long value = mSharedPreferencesUtil.getLong(key);
            saveLong(key, value);
            return value;
        } else {
            return -1L;
        }
    }

    public long getLongCompat(String key, long defaultValue) {
        if (containsKey(key)) {
            return getLong(key, defaultValue);
        } else if (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key)) {
            long value = mSharedPreferencesUtil.getLong(key, defaultValue);
            saveLong(key, value);
            return value;
        } else {
            return defaultValue;
        }
    }

    public Map getMapCompat(String key) {
        if (containsKey(key)) {
            return getHashMap(key);
        } else if (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key)) {
            Map<String, String> value = mSharedPreferencesUtil.getHashMapByKey(key);
            if (value != null && value.size() > 0) {
                saveHashMap(key, value);
            }
            return value;
        } else {
            return null;
        }
    }

    public ConcurrentHashMap getConcurrentHashMapCompat(String key) {
        if (containsKey(key)) {
            return getConcurrentHashMap(key);
        } else if (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key)) {
            ConcurrentHashMap value = mSharedPreferencesUtil.getConcurrentHashMapByKey(key);
            if (value != null && value.size() > 0) {
                saveConcurrentHashMap(key, value);
            }
            return value;
        } else {
            return null;
        }
    }

    public ArrayList<String> getArrayListCompat(String key) {
        if (containsKey(key)) {
            return getArrayList(key);
        } else if (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key)) {
            ArrayList<String> value = mSharedPreferencesUtil.getArrayList(key);
            if (value != null && value.size() > 0) {
                saveArrayList(key, value);
            }
            return value;
        } else {
            return null;
        }
    }

    public CopyOnWriteArrayList<String> getCopyOnWriteArrayListCompat(String key) {
        if (containsKey(key)) {
            return getCopyOnWriteList(key);
        } else if (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key)) {
            CopyOnWriteArrayList<String> value = mSharedPreferencesUtil.getCopyOnWriteList(key);
            if (value != null && value.size() > 0) {
                saveCopyOnWriteList(key, value);
            }
            return value;
        } else {
            return null;
        }
    }

    public void removeCompat(String key) {
        if (containsKey(key)) {
            removeKey(key);
        } else if (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key)) {
            mSharedPreferencesUtil.removeByKey(key);
        }
    }

    public void removeCompatBoth(String key) {
        if (containsKey(key)) {
            removeKey(key);
        }
        if (mSharedPreferencesUtil != null && mSharedPreferencesUtil.contains(key)) {
            mSharedPreferencesUtil.removeByKey(key);
        }
    }

    @Nullable
    @Deprecated
    // 不要使用这个了,可以使用MMKVUtil.getEncryptedInstance()
    public String getStringFromEncryptStr(String key) {
        String string = getString(key);
        if(!TextUtils.isEmpty(string)) {
            try {
                return EncryptUtil.getInstance(mAppContext).decryptByPrivateKeyNative(string);
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

        return string;
    }

    @Deprecated
    // 不要使用这个了,可以使用MMKVUtil.getEncryptedInstance()
    public void saveStringUseEncrypt(String key,@Nullable String value) {
        if(TextUtils.isEmpty(value)) {
            saveString(key, value);
        } else {
            try {
                saveString(key, EncryptUtil.getInstance(mAppContext).encryptByPublicKeyNative(value));
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }

}
