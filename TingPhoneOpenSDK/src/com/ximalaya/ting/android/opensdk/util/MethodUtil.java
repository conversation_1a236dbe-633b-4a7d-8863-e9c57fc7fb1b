package com.ximalaya.ting.android.opensdk.util;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * Created by roc on 2016/6/28.
* <AUTHOR>
 */
public class MethodUtil {

    public static Object invokeMethod(final Object object, final String methodName,
                                      Object[] args, Class<?>[] parameterTypes)
            throws NoSuchMethodException, IllegalAccessException,
            InvocationTargetException {
        parameterTypes = BaseUtil.nullToEmpty(parameterTypes);
        args = BaseUtil.nullToEmpty(args);
        final Method method = object.getClass().getMethod(methodName,parameterTypes);
        if (method == null) {
            throw new NoSuchMethodException("No such accessible method: "
                    + methodName + "() on object: "
                    + object.getClass().getName());
        }
        return method.invoke(object, args);
    }

    public static Object invokeDeclaredMethod(final Object object, final String methodName,
                                      Object[] args, Class<?>[] parameterTypes)
            throws NoSuchMethodException, IllegalAccessException,
            InvocationTargetException {
        parameterTypes = BaseUtil.nullToEmpty(parameterTypes);
        args = BaseUtil.nullToEmpty(args);
        final Method method = object.getClass().getDeclaredMethod(methodName,parameterTypes);
        method.setAccessible(true);
        if (method == null) {
            throw new NoSuchMethodException("No such accessible method: "
                    + methodName + "() on object: "
                    + object.getClass().getName());
        }
        return method.invoke(object, args);
    }

    public static Object invokeStaticMethod(final Class clazz, final String methodName,
                                            Object[] args, Class<?>[] parameterTypes)
            throws NoSuchMethodException, IllegalAccessException,
            InvocationTargetException {
        if(clazz == null) {
            return null;
        }
        parameterTypes = BaseUtil.nullToEmpty(parameterTypes);
        args = BaseUtil.nullToEmpty(args);
        final Method method = clazz.getMethod(methodName,parameterTypes);
        if (method == null) {
            throw new NoSuchMethodException("No such accessible method: "
                    + methodName + "() on object: "
                    + clazz.getName());
        }
        return method.invoke(null, args);
    }

}
