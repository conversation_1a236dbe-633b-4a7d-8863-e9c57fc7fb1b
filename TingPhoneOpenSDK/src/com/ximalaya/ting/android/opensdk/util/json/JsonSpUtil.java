package com.ximalaya.ting.android.opensdk.util.json;

import android.content.Context;

import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmutil.app.XmAppHelper;

import java.lang.reflect.Type;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;

/**
 * Created by zhangkaikai on 1/21/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15721173906
 * @Description json 存取统一入口工具
 */
public class JsonSpUtil {

    public static <T> void getData(Context context, String key, Type type, @NonNull OnDataLoadedListener<T> listener) {
        MyAsyncTask.execute(() -> {
            T data = getDataBlock(context, key, type, false);
            XmAppHelper.runOnUiThread(() -> listener.onLoaded(data));
        });
    }

    public static <T> void getDataCompat(Context context, String key, Type type, @NonNull OnDataLoadedListener<T> listener) {
        MyAsyncTask.execute(() -> {
            T data = getDataBlock(context, key, type, true);
            XmAppHelper.runOnUiThread(() -> listener.onLoaded(data));
        });
    }

    public static <T> void getData(Context context, String key, Class<T> cls, @NonNull OnDataLoadedListener<T> listener) {
        MyAsyncTask.execute(() -> {
            T data = getDataBlock(context, key, cls, false);
            XmAppHelper.runOnUiThread(() -> listener.onLoaded(data));
        });
    }

    public static <T> void getDataCompat(Context context, String key, Class<T> cls, @NonNull OnDataLoadedListener<T> listener) {
        MyAsyncTask.execute(() -> {
            T data = getDataBlock(context, key, cls, true);
            XmAppHelper.runOnUiThread(() -> listener.onLoaded(data));
        });
    }

    public static <T> void setDataCompat(Context context, String key, T data) {
        MyAsyncTask.execute(() -> { setDataBlockCompat(context, key, data); });
    }

    public static <T> void setData(Context context, String key, T data) {
        MyAsyncTask.execute(() -> { setDataBlock(context, key, data); });
    }

    public static void remove(Context context, String key) {
        MyAsyncTask.execute(() -> { JsonSharedPreference.getInstance().remove(context, key); });
    }

    @WorkerThread
    public static <T> void setDataBlock(Context context, String key, T data) {
        JsonSharedPreference.getInstance().setData(context, key, data);
    }

    @WorkerThread
    public static <T> void setDataBlockThrow(Context context, String key, T data) throws Exception {
        JsonSharedPreference.getInstance().setDataThrow(context, key, data);
    }

    @WorkerThread
    public static <T> void setDataBlockCompat(Context context, String key, T data) {
        JsonSharedPreference.getInstance().setDataCompat(context, key, data);
    }

    @Nullable
    @WorkerThread
    public static <T> T getDataBlock(Context context, String key, Type type, boolean compat) {
        return JsonSharedPreference.getInstance().getData(context, key, type, compat);
    }

    @Nullable
    @WorkerThread
    public static <T> T getDataBlock(Context context, String key, Class<T> cls, boolean compat) {
        return JsonSharedPreference.getInstance().getData(context, key, cls, compat);
    }
    @Nullable
    @WorkerThread
    public static <T> T getDataBlockThrow(Context context, String key, Class<T> cls, boolean compat) throws Exception {
        return JsonSharedPreference.getInstance().getDataThrow(context, key, cls);
    }

    @Nullable
    @WorkerThread
    public static String getJsonBlockCompat(Context context, String key) {
        return JsonSharedPreference.getInstance().getJsonCompat(context, key);
    }

    public interface OnDataLoadedListener<T> {

        void onLoaded(@Nullable T data);
    }

    public static boolean jsonFileExist(Context context, String key) {
        return JsonSharedPreference.jsonFileExist(context, key);
    }

}
