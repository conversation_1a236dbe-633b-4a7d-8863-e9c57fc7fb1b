package com.ximalaya.ting.android.opensdk.util;

import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.PlayedTrackModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;

/**
 * <AUTHOR>
 * @date 2023/11/13 17:41
 */
public class TrackUtil {
    public static String trackToStr(Track track) {
        if (track == null) {
            return "track=null";
        }
        return "id=" + track.getDataId() + ",hash=" + System.identityHashCode(track) + ",kind=" + track.getKind() + ",title=" + track.getTrackTitle();
    }

    public static String trackToStr(PlayableModel track) {
        if (track == null) {
            return "track=null";
        }
        return "id=" + track.getDataId() + ",kind=" + track.getKind() + ",title=" + ((track instanceof Track) ? ((Track) track).getTrackTitle() : "");
    }

    public static String trackToStr(PlayedTrackModel model) {
        if (model == null) {
            return "track=null";
        }
        return "track[id=" + model.getTrackId() + ",title=" + model.getTrackTitle() + ",totalBufferDuration=" + model.getTotalBufferTime() + "]";
    }
}
