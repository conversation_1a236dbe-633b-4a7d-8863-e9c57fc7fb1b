package com.ximalaya.ting.android.opensdk.util;

import android.content.Context;

import com.ximalaya.ting.android.opensdk.constants.MmkvConstantsInOpenSdk;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;

import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Created by zhifu.zhang on 5/14/2020 AD.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18362080562
 * @desc mmkv 基础工具类
 * 因MMKVUtil不允许继承，故有了该类
 */
public class BaseMmkvUtil {

    protected Context mAppContext;
    private MMKVUtil mmkvUtil;

    public BaseMmkvUtil(Context context, String name) {
        if (context == null) {
            throw new IllegalArgumentException("context can not be null!!");
        }
        if (name == null || name.length() == 0) {
            name = MmkvConstantsInOpenSdk.OPENSDK_FILENAME_TING_DATA;
        }
        mAppContext = context.getApplicationContext();
        if (mAppContext == null) {
            mAppContext = context;
        }
        MMKVUtil.initialize(mAppContext);
        mmkvUtil = MMKVUtil.getInstance(name);
    }

    public void saveInt(String key, int value) {
        mmkvUtil.saveInt(key, value);
    }

    public int getInt(String key, int defValue) {
        return mmkvUtil.getInt(key, defValue);
    }

    public int getInt(String key) {
        return mmkvUtil.getInt(key);
    }

    public void saveBoolean(String key, boolean value) {
        mmkvUtil.saveBoolean(key, value);
    }

    public boolean getBoolean(String key, boolean defValue) {
        return mmkvUtil.getBoolean(key, defValue);
    }

    public boolean getBoolean(String key) {
        return mmkvUtil.getBoolean(key);
    }

    public void saveString(String key, String value) {
        mmkvUtil.saveString(key, value);
    }

    public String getString(String key, String defValue) {
        return mmkvUtil.getString(key, defValue);
    }

    public String getString(String key) {
        return mmkvUtil.getString(key);
    }

    public void saveFloat(String key, float value) {
        mmkvUtil.saveFloat(key, value);
    }

    public float getFloat(String key, float defValue) {
        return mmkvUtil.getFloat(key, defValue);
    }

    public float getFloat(String key) {
        return mmkvUtil.getFloat(key);
    }

    public void saveDouble(String key, double value) {
        mmkvUtil.saveDouble(key, value);
    }

    public double getDouble(String key, double defValue) {
        return mmkvUtil.getDouble(key, defValue);
    }

    public double getDouble(String key) {
        return mmkvUtil.getDouble(key);
    }

    public void saveLong(String key, long value) {
        mmkvUtil.saveLong(key, value);
    }

    public long getLong(String key, long defValue) {
        return mmkvUtil.getLong(key, defValue);
    }

    public long getLong(String key) {
        return mmkvUtil.getLong(key);
    }

    public void saveArrayList(String key, ArrayList<String> value) {
        mmkvUtil.saveArrayList(key, value);
    }

    public ArrayList<String> getArrayList(String key) {
        return mmkvUtil.getArrayList(key);
    }

    public void saveCopyOnWriteList(String key, CopyOnWriteArrayList<String> value) {
        mmkvUtil.saveCopyOnWriteList(key, value);
    }

    public CopyOnWriteArrayList<String> getCopyOnWriteList(String key) {
        return mmkvUtil.getCopyOnWriteList(key);
    }

    public void saveHashMap(String key, Map<String, String> map) {
        mmkvUtil.saveHashMap(key, map);
    }

    public Map getHashMap(String key) {
        return mmkvUtil.getHashMap(key);
    }

    public void saveConcurrentHashMap(String key, ConcurrentHashMap value) {
        mmkvUtil.saveConcurrentHashMap(key, value);
    }

    public ConcurrentHashMap getConcurrentHashMap(String key) {
        return mmkvUtil.getConcurrentHashMap(key);
    }

    public boolean containsKey(String key) {
        return mmkvUtil.containsKey(key);
    }

    public void removeKey(String key) {
        mmkvUtil.removeByKey(key);
    }

    public void removeKeys(String... keys) {
        if (keys == null || keys.length == 0) {
            return;
        }

        for (String key : keys) {
            removeKey(key);
        }
    }

    public void clear(){
        mmkvUtil.clear();
    }
}
