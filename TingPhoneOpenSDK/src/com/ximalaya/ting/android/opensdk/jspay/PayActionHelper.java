package com.ximalaya.ting.android.opensdk.jspay;

import android.app.Activity;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.pay.IPayAction;
import com.ximalaya.ting.android.routeservice.service.pay.IThirdPayManager;
import com.ximalaya.ting.android.routeservice.service.pay.PayResult;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 支付操作
 *
 * <AUTHOR>
 */

/**
 * TODO
 * 原因：子项目化开发，未排期
 * 涉及类：PayActionHelper
 * 涉及分支：live2
 * 时间：2017年2月20日
 * 注意：bundle分支代码同学merge时候需要注意
 * 注意：一定要在detroy 时候调用releaseAllCallBack
 */
public class PayActionHelper implements LifecycleCallback {

    private static final String TAG = "PayActionHelper";

    public final static int PAY_WAY_ALI = 1;// 支付宝
    public final static int PAY_WAY_WECHAT = 2;// 微信

    private Activity mActivity;

    private IPayAction.PayCallBack mCallback;

    IThirdPayManager mThirdPayManager;

    public PayActionHelper(IWebFragment callback) {
        this.mActivity = callback.getActivity();
        callback.setLifecycleCallback(this);
        mThirdPayManager = RouterServiceManager.getInstance().getService(IThirdPayManager.class);
    }

    private void unRegisterPayCallback() {
        if (mCallback != null && mThirdPayManager != null) {
            mThirdPayManager.unRegisterPayCallBack(mCallback);
        }
    }

    /**
     * @param result
     */
    public void appPay(String result, final IPayAction.PayCallBack payCallBack) {
        if (TextUtils.isEmpty(result))
            return;

        if (mThirdPayManager == null) {
            return;
        }

        mCallback = payCallBack;
        try {
            JSONObject object = new JSONObject(result);

            /**
             *  payType 2  微信支付
             *  payType 1 支付宝
             */
            int payWay = object.getInt("payType");
            String aliPayInfo = null;
            String wxPayInfo;

            if (object.has("params")) {//如果订单信息不是在params里面，就是在最外层的数据中
                JSONObject payInfoObject = object.optJSONObject("params");
                if (payInfoObject != null) {
                    if (payInfoObject.has("payInfo")) {
                        aliPayInfo = payInfoObject.optString("payInfo");
                    }
                }
                wxPayInfo = object.optString("params");
            } else {
                aliPayInfo = object.optString("payInfo");//用在阿里支付
                wxPayInfo = object.toString();
            }

            if (payWay == PAY_WAY_WECHAT) {
                IPayAction<WxPayRequest> payAction = mThirdPayManager.getPayActionForType(mActivity, PayType.WX_PAY);
                if (payAction != null) {
                    WxPayRequest wxPayRequest = getWxPayRequestFromString(wxPayInfo);
                    payAction.pay(wxPayRequest, mCallback);
                    return;
                }
            } else if (payWay == PAY_WAY_ALI) {
                IPayAction<AliPayRequest> payAction = mThirdPayManager.getPayActionForType(mActivity, PayType.ALI_PAY);
                if (payAction != null) {
                    AliPayRequest payRequest = new AliPayRequest();
                    payRequest.setPayInfo(aliPayInfo);
                    payAction.pay(payRequest, mCallback);
                    return;
                }
            }

            //fail
            if (mCallback != null) {
                PayResult payResult = new PayResult();
                payResult.retCode = -1;
                payResult.errorMsg = "支付失败";
                mCallback.onPayResult(payResult);
            }

        } catch (Exception e) {
            Logger.log("appPay exception=" + e.getMessage());
            if (mCallback != null) {
                PayResult payResult = new PayResult();
                payResult.retCode = -1;
                payResult.errorMsg = "支付失败";
                mCallback.onPayResult(payResult);
            }
        }
    }

    private WxPayRequest getWxPayRequestFromString(String payInfo) throws JSONException {
        JSONObject object = new JSONObject(payInfo);
        WxpayModel model = null;

        try {
            model = new Gson().fromJson(payInfo, WxpayModel.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (object.has("package")) {
            if (model != null) {
                model.setPackage(object.optString("package"));
            }
        }

        WxPayRequest wxPayRequest = new WxPayRequest();
        wxPayRequest.setAppid(model.getAppid());
        wxPayRequest.setNoncestr(model.getNoncestr());
        wxPayRequest.setPackageValue(model.getPackageValue());
        wxPayRequest.setPartnerid(model.getPartnerid());
        wxPayRequest.setPrepayid(model.getPrepayid());
        wxPayRequest.setSign(model.getSign());
        wxPayRequest.setTimestamp(model.getTimestamp());
        return wxPayRequest;
    }


    @Override
    public void onDestroy() {
        unRegisterPayCallback();
    }


    public static class WxpayModel {

        private String appid;

        private String partnerid;

        private String prepayid;

        private String noncestr;

        private String timestamp;

        private String packageValue;

        private String sign;

        public String getAppid() {
            return appid;
        }

        public void setAppid(String appid) {
            this.appid = appid;
        }

        public String getPartnerid() {
            return partnerid;
        }

        public void setPartnerid(String partnerid) {
            this.partnerid = partnerid;
        }

        public String getPrepayid() {
            return prepayid;
        }

        public void setPrepayid(String prepayid) {
            this.prepayid = prepayid;
        }

        public String getNoncestr() {
            return noncestr;
        }

        public void setNoncestr(String noncestr) {
            this.noncestr = noncestr;
        }

        public String getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(String timestamp) {
            this.timestamp = timestamp;
        }

        public String getPackageValue() {
            return packageValue;
        }

        public void setPackageValue(String packageValue) {
            this.packageValue = packageValue;
        }

        // 打赏中该字段名为 package
        public void setPackage(String packageValue) {
            this.packageValue = packageValue;
        }

        public String getSign() {
            return sign;
        }

        public void setSign(String sign) {
            this.sign = sign;
        }

    }

    class WxPayRequest {

        private String appid;//应用ID

        private String partnerid;//商户号

        private String prepayid;//预支付交易会话ID

        private String noncestr;//随机字符串

        private String timestamp;//时间戳

        private String packageValue;//扩展字段

        private String sign;//签名

        public String getAppid() {
            return appid;
        }

        public void setAppid(String appid) {
            this.appid = appid;
        }

        public String getPartnerid() {
            return partnerid;
        }

        public void setPartnerid(String partnerid) {
            this.partnerid = partnerid;
        }

        public String getPrepayid() {
            return prepayid;
        }

        public void setPrepayid(String prepayid) {
            this.prepayid = prepayid;
        }

        public String getNoncestr() {
            return noncestr;
        }

        public void setNoncestr(String noncestr) {
            this.noncestr = noncestr;
        }

        public String getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(String timestamp) {
            this.timestamp = timestamp;
        }

        public String getPackageValue() {
            return packageValue;
        }

        public void setPackageValue(String packageValue) {
            this.packageValue = packageValue;
        }

        public String getSign() {
            return sign;
        }

        public void setSign(String sign) {
            this.sign = sign;
        }
    }

    class AliPayRequest {

        private String payInfo;//app支付请求参数字符串，主要包含商户的订单信息，key=value形式，以&连接。

        public String getPayInfo() {
            return payInfo;
        }

        public void setPayInfo(String payInfo) {
            this.payInfo = payInfo;
        }
    }
}
