package com.ximalaya.ting.android.opensdk.jspay;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;

/**
 * Created by nali on 2018/7/24.
 * <AUTHOR>
 */

public class Util {

    public static String getVersion(Context context) {
        String version = getVersionName(context);
        if (!TextUtils.isEmpty(version)) {
            String[] str = version.split("\\.");
            if (str != null && str.length > 3) {
                StringBuilder sb = null;
                for (int i = 0; i < 3; i++) {
                    if (sb == null) {
                        sb = new StringBuilder();
                        sb.append(str[i]);
                    } else {
                        sb.append(".");
                        sb.append(str[i]);
                    }
                }
                if (sb != null)
                    version = sb.toString();
            }
        }

        return version;
    }

    private static String sVersion = "";

    public static String getVersionName(Context context) {

        if (!TextUtils.isEmpty(sVersion)) {
            return sVersion;
        }

        if (context == null) {
            return sVersion;
        }
        try {
            // 获取packagemanager的实例
            PackageManager packageManager = context.getPackageManager();
            // getPackageName()是你当前类的包名，0代表是获取版本信息
            if (packageManager != null) {
                PackageInfo packInfo;
                packInfo = packageManager.getPackageInfo(context.getPackageName(),
                        0);
                if (packInfo != null) {
                    sVersion = packInfo.versionName;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            sVersion = "";
        }
        return sVersion;
    }

    public static String getSimpleSystemVersion() {
        return "Android" + android.os.Build.VERSION.SDK_INT;
    }
}
