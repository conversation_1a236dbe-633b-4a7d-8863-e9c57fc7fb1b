package com.ximalaya.ting.android.opensdk.jspay;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import com.ximalaya.ting.android.routeservice.service.pay.IPayAction;
import com.ximalaya.ting.android.routeservice.service.pay.PayResult;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;

/**
 * Create by felix.chen on 2018/6/28.
 *
 * <AUTHOR>
 */

public abstract class AbstractJsPay {
    public void autoRenew(String callback, String params, IWebFragment webFragment) {
        try {
            String aliPayPackageName = "com.eg.android.AlipayGphone";
            boolean isAliPayInstall = isAppInstalled(webFragment.getActivity().getApplicationContext(), aliPayPackageName);
            if (isAliPayInstall) {
                String decodeParams = URLDecoder.decode(params,"utf-8");
                JSONObject jsonObject = new JSONObject(decodeParams);
                if (jsonObject != null && jsonObject.has("params")) {
                    JSONObject paramsJsonObject = jsonObject.getJSONObject("params");
                    if (paramsJsonObject != null && paramsJsonObject.has("payInfo")) {
                        String host = "https://openapi.alipay.com/gateway.do?";
                        String payInfoString = paramsJsonObject.optString("payInfo");
                        String encodeUrl = URLEncoder.encode(host+payInfoString, "UTF-8");
                        String url = "alipays://platformapi/startapp?appId=20000067&url="
                                + encodeUrl;
                        Uri uri = Uri.parse(url);
                        Activity activity = webFragment.getActivity();
                        if (activity != null) {
                            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                            activity.startActivity(intent);
                        }
                    }
                }
            } else {
                JSONObject object = new JSONObject();
                try {
                    object.put("ret", -1);//0成功；非0失败
                    object.put("msg", "请先安装支付宝app");
                    doJsCallback(object.toString(), callback,webFragment);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String appReady(IWebFragment iWebFragment) {
        String token = "";
        if (iWebFragment.hasLogined()) {
            token = iWebFragment.getFakeToken();
        }
        try {
            JSONObject json = new JSONObject();
            json.put("platform", "android");
            json.put("version", Util.getVersion(iWebFragment.getActivity()));
            json.put("platformVersion", Util.getSimpleSystemVersion());
            json.put("deviceId", iWebFragment.getDeviceToken());
            if (iWebFragment.hasLogined()) {
                json.put("uid", iWebFragment.getUid());
                json.put("token", token);
            }
            json.put("idfa", "");
            return json.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return "";
    }

    public void appPay(final String callback, String params, IWebFragment iWebFragment) {
        try {
            new PayActionHelper(iWebFragment).appPay(URLDecoder.decode(params, "utf-8"), new IPayAction.PayCallBack() {
                @Override
                public void onPayResult(PayResult result) {
                    int retCode = -1;
                    String msg = "";
                    if (result != null) {
                        if (result.retCode == 0) {
                            retCode = 0;
                            msg = "支付成功";
                        } else {
                            retCode = -1;
                            if (TextUtils.isEmpty(result.errorMsg)) {
                                msg = "支付异常";
                            } else {
                                msg = result.errorMsg;
                            }
                        }
                    } else {
                        retCode = -1;
                        msg = "支付异常";
                    }

                    final JSONObject object = new JSONObject();
                    try {
                        object.put("ret", retCode);//0成功；非0失败
                        object.put("msg", msg);

                        doJsCallback(object.toString(), callback, iWebFragment);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            });
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    private boolean isAppInstalled(Context context, String appPackageName) {
        PackageInfo packageInfo;

        try {
            packageInfo = context.getPackageManager().getPackageInfo(appPackageName, 0);

        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }

        if (packageInfo == null) {
            return false;
        } else {
            return true;
        }
    }

    private void doJsCallback(String param, String callbackName, IWebFragment webFragment){
        if (TextUtils.isEmpty(callbackName) || "undefined".equals(callbackName)) {
            return;
        }
        if (webFragment != null && webFragment.getWebView() != null){
            webFragment.getWebView().post(new Runnable() {
                @Override
                public void run() {
                    String js = "javascript:window.nativeCallBack." + callbackName + "('"
                            + param + "')";
                    if (webFragment != null && webFragment.canUpdateUi() && webFragment.getWebView() != null) {
                        webFragment.getWebView().loadUrl(js);
                    }
                }
            });
        }
    }
}
