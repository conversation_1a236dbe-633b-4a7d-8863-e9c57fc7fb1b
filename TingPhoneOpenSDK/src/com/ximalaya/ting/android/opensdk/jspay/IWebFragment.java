package com.ximalaya.ting.android.opensdk.jspay;

import android.app.Activity;
import android.webkit.WebView;

/**
 * Create by felix.chen on 2018/6/28.
 *
 * <AUTHOR>
 */

public interface IWebFragment {
    WebView getWebView();
    boolean canUpdateUi();
    Activity getActivity();
    String getFakeToken();

    boolean hasLogined();

    boolean getUid();

    boolean getDeviceToken();

    void setLifecycleCallback(LifecycleCallback lifecycleCallback);
}
