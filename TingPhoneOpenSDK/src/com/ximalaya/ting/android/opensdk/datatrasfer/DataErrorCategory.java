/**
 * DataErrorCategory.java
 * com.ximalaya.ting.android.bydcarlib.datatransfer
 *
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.datatrasfer;

import com.google.gson.annotations.SerializedName;

/**
 * ClassName:DataErrorCategory Function: TODO ADD FUNCTION Reason: TODO ADD
 * REASON
 * 
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015-7-13 下午5:58:23
 * 
 * @see
 */
public class DataErrorCategory
{
	@SerializedName("error_no")
	private int mErrorNo;
	@SerializedName("error_code")
	private String mErrorCode;
	@SerializedName("error_desc")
	private String mErrorDesc;

	public int getErrorNo()
	{
		return mErrorNo;
	}

	public void setErrorNo(int mErrorNo)
	{
		this.mErrorNo = mErrorNo;
	}

	public String getErrorCode()
	{
		return mErrorCode;
	}

	public void setErrorCode(String mErrorCode)
	{
		this.mErrorCode = mErrorCode;
	}

	public String getErrorDesc()
	{
		return mErrorDesc;
	}

	public void setErrorDesc(String mErrorDesc)
	{
		this.mErrorDesc = mErrorDesc;
	}

}
