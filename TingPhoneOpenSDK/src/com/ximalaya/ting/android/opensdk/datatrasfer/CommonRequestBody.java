package com.ximalaya.ting.android.opensdk.datatrasfer;

import java.io.IOException;

import okhttp3.MediaType;
import okhttp3.RequestBody;
import okio.Buffer;
import okio.BufferedSink;
import okio.ForwardingSink;
import okio.Okio;
import okio.Sink;


/**
 * 自定义相应类
 * <AUTHOR>
 *
 */
public class CommonRequestBody extends RequestBody {

	private final RequestBody requestBody;
	private BufferedSink bufferedSink;
	private IUploadCallBack callBack;
	
	/**
	 * 
	 * @param body 请求体
	 */
	private CommonRequestBody(RequestBody body) {
		this.requestBody = body;
	}
	
	public CommonRequestBody(RequestBody requestBody, IUploadCallBack callBack) {
		this(requestBody);
		this.callBack = callBack;
	}

	@Override
	public MediaType contentType() {
		return requestBody.contentType();
	}
	
	@Override
	public long contentLength() throws IOException {
		return requestBody.contentLength();
	}
	
	/**
	 * 重写写入请求数据
	 */
	@Override
	public void writeTo(BufferedSink sink) throws IOException {
		if (bufferedSink == null) {
			bufferedSink = Okio.buffer(sink(sink));
		}
		requestBody.writeTo(bufferedSink);
		bufferedSink.flush();//写入数据
		
		if (callBack != null) {
			callBack.onSuccess();//成功之后才会flush
		}
	}

	private Sink sink(BufferedSink buffer) {
		return new ForwardingSink(buffer) {
			long writed = 0;
			long total = 0;
			
			@Override
			public void write(Buffer source, long byteCount) throws IOException {
				super.write(source, byteCount);
				if (total == 0) {
					total = contentLength();
				}
				writed += byteCount;
				
				if (callBack != null) {
					callBack.onProgress(writed, total);
				}
			}
		};
	}

}
