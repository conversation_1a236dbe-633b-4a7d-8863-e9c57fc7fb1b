/**
 * IDataCallbackM.java
 * com.ximalaya.ting.android.data.request
 *
 * Function： TODO 
 *
 *   ver     date      		author
 * ──────────────────────────────────
 *   		 2015-11-12 		jack.qin
 *
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.datatrasfer;

/**
 * 上传文件回调
 * <AUTHOR>
 *
 * @param <T>
 */
public interface IUploadCallBack {
	public void onSuccess();
	public void onProgress(long writed, long total);
	public void onError(int errCode , String errContent);
}
