package com.ximalaya.ting.android.opensdk.model.performance

import androidx.annotation.Keep
import org.json.JSONObject

/**
 * 直播秒开数据。
 *
 * <AUTHOR>
 * @since 2022/10/20
 */
@Keep
class XmLivePlayPerformanceModel (var businessId: Int = 0, var livePageEnterTime: Long = 0, var isSuccess: Boolean = false, var totalTime: Long = 0,
var bufferReadyTime: Long = 0, var firstFrameTime: Long = 0, var playUrl: String? = "", var startTime: Long = 0, var roomId: Long = 0,
var isLiveScroll: Boolean = false, var livePlaySource: Int = 0, var aidlCost: Long = 0, var prepareMediaSourceCost: Long = 0, var cdnRequestCost: Long = 0, var flvHeaderCost: Long = 0,
var flvScrpitTagCost: Long = 0, var audioHeaderTagCost: Long = 0, var firstVideoTagCost: Long = 0, var secondVideoTagCost: Long = 0, var firstFrameDecodeCost: Long = 0,
var startCdnRequestTime: Long = 0, var startReadFlvHeaderTime: Long = 0, var startReadScriptTagTime: Long = 0,
var startReadAudioHeaderTime: Long = 0, var startReadFirstVideoTagTime: Long = 0, var startReadSecondVideoTagTime: Long = 0, var endReadSecondVideoTagTime: Long = 0,
var cdnIp: String? = "", var connectCost: Long = 0, var dnsCost: Long = 0, var requestCost: Long = 0, var firstPackageCost: Long = 0,
var audioDecoderCreateCost: Long = 0, var videoDecoderCreateCost: Long = 0,var subBusinessId: Int = 0,
var extra: MutableMap<String, String> = mutableMapOf()) {

    fun toJsonString(): String {
        val jsonObject = JSONObject()
        jsonObject.put("businessId", businessId)
        jsonObject.put("subBusinessId", subBusinessId)
        jsonObject.put("isSuccess", isSuccess)
        jsonObject.put("totalTime", totalTime)
        jsonObject.put("bufferReadyTime", bufferReadyTime)
        jsonObject.put("firstFrameTime", firstFrameTime)
        jsonObject.put("playUrl", playUrl)
        jsonObject.put("isLiveScroll", isLiveScroll)
        jsonObject.put("livePlaySource", livePlaySource)
        jsonObject.put("aidlCost", aidlCost)
        jsonObject.put("prepareMediaSourceCost", prepareMediaSourceCost)
        jsonObject.put("cdnRequestCost", cdnRequestCost)
        jsonObject.put("flvHeaderCost", flvHeaderCost)
        jsonObject.put("flvScrpitTagCost", flvScrpitTagCost)
        jsonObject.put("audioHeaderTagCost", audioHeaderTagCost)
        jsonObject.put("firstVideoTagCost", firstVideoTagCost)
        jsonObject.put("secondVideoTagCost", secondVideoTagCost)
        jsonObject.put("firstFrameDecodeCost", firstFrameDecodeCost)
        jsonObject.put("cdnIp", cdnIp)
        jsonObject.put("connectCost", connectCost)
        jsonObject.put("dnsCost", dnsCost)
        jsonObject.put("requestCost", requestCost)
        jsonObject.put("firstPackageCost", firstPackageCost)
        jsonObject.put("audioDecoderCreateCost", audioDecoderCreateCost)
        jsonObject.put("videoDecoderCreateCost", videoDecoderCreateCost)
        val extraJson = JSONObject()
        extra.forEach { (key, value) ->
            extraJson.put(key, value)
        }
        jsonObject.put("extra", extraJson)
        
        return jsonObject.toString()
    }

    fun getTotalTimeCost(): Long {
        return (aidlCost + prepareMediaSourceCost + cdnRequestCost
                + flvHeaderCost + flvScrpitTagCost + audioHeaderTagCost + firstVideoTagCost + secondVideoTagCost + firstFrameDecodeCost)
    }

}