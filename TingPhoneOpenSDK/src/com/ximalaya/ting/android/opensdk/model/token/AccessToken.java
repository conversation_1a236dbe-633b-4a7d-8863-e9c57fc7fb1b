package com.ximalaya.ting.android.opensdk.model.token;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 */
public class AccessToken implements Parcelable {
    private String mAccessToken;
    private long mExpire;
    private String mUid;
    private long mLastGetTime;
    private String mRefreshToken;
    private String mThirdUid;
    private String mThirdToken;

    public String getThirdUid() {
        return mThirdUid;
    }

    public void setThirdUid(String thirdUid) {
        this.mThirdUid = thirdUid;
    }

    public String getThirdToken() {
        return mThirdToken;
    }

    public void setThirdToken(String thirdToken) {
        this.mThirdToken = thirdToken;
    }

    public String getAccessToken() {
        return mAccessToken;
    }

    public void setAccessToken(String accessToken) {
        mAccessToken = accessToken;
    }

    public long getExpire() {
        return mExpire;
    }

    public void setExpire(long expire) {
        mExpire = expire;
    }

    public String getUid() {
        return mUid;
    }

    public void setUid(String uid) {
        mUid = uid;
    }

    public long getLastGetTime() {
        return mLastGetTime;
    }

    public void setLastGetTime(long lastGetTime) {
        mLastGetTime = lastGetTime;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.mAccessToken);
        dest.writeLong(this.mExpire);
        dest.writeString(this.mUid);
        dest.writeLong(this.mLastGetTime);
        dest.writeString(this.mRefreshToken);
        dest.writeString(this.mThirdUid);
        dest.writeString(this.mThirdToken);
    }

    public AccessToken() {
    }

    public void readFromParcel(Parcel in) {
        this.mAccessToken = in.readString();
        this.mExpire = in.readLong();
        this.mUid = in.readString();
        this.mLastGetTime = in.readLong();
        this.mRefreshToken = in.readString();
        this.mThirdUid = in.readString();
        this.mThirdToken = in.readString();
    }

    public static final Creator<AccessToken> CREATOR = new Creator<AccessToken>() {
        @Override
        public AccessToken createFromParcel(Parcel source) {
            AccessToken info = new AccessToken();
            info.readFromParcel(source);
            return info;
        }

        @Override
        public AccessToken[] newArray(int size) {
            return new AccessToken[size];
        }
    };

    public String getRefreshToken() {
        return mRefreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        mRefreshToken = refreshToken;
    }
}