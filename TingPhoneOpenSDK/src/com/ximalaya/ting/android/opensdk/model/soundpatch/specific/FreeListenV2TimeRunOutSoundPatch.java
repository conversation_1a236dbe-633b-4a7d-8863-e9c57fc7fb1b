package com.ximalaya.ting.android.opensdk.model.soundpatch.specific;

import android.os.Bundle;

import com.ximalaya.ting.android.opensdk.constants.SoundPatchConstants;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.ad.SoundPatchInfo;
import com.ximalaya.ting.android.opensdk.model.soundpatch.BaseSoundPatch;
import com.ximalaya.ting.android.opensdk.model.soundpatch.NetSoundPatch;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.trace.ITrace;

import org.json.JSONObject;

public class FreeListenV2TimeRunOutSoundPatch  extends NetSoundPatch {

    public static FreeListenV2TimeRunOutSoundPatch emptySample() {
        FreeListenV2TimeRunOutSoundPatch soundPatch = new FreeListenV2TimeRunOutSoundPatch();
        soundPatch.releaseSoundPatch();
        return soundPatch;
    }

    private long mTrackId = -100;
    private String mUrl;
    private double mGain = SoundPatchConstants.DEFAULT_DOUBLE;
    private String mCategoryName = null;

    private void setPatchInfo(long trackId, String url, double gain, String categoryName) {
        this.mTrackId = trackId;
        this.mUrl = url;
        this.mGain = gain;
        this.mCategoryName = categoryName;
    }

    @Override
    public int getPriority() {
        return SoundPatchConstants.FrameWorkSoundPatchController.PRIORITY_FREE_LISTEN_V2_TIME_RUN_OUT;
    }

    @Override
    public boolean isValid() {
        return true;
    }

    @Override
    protected boolean checkPlayCyclePeriod() {
        return true;
    }

    @Override
    protected boolean checkPlayConditionExcludePlayCyclePeriod() {
        return true;
    }

    @Override
    public BaseSoundPatch cloneSoundPatch(String paramsJsonString) {
        long trackId = SoundPatchConstants.DEFAULT_LONG;
        String url = null;
        double gain = SoundPatchConstants.DEFAULT_DOUBLE;
        String categoryName = null;

        try {
            JSONObject jsonObject = new JSONObject(paramsJsonString);
            trackId = SoundPatchHelper.decodeTrackId(jsonObject);
            url = SoundPatchHelper.decodeUrl(jsonObject);
            gain = SoundPatchHelper.decodeGain(jsonObject);
            categoryName = SoundPatchHelper.decodeCategoryName(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }

        FreeListenV2TimeRunOutSoundPatch emptySoundPatch = new FreeListenV2TimeRunOutSoundPatch();
        emptySoundPatch.setPatchInfo(trackId, url, gain, categoryName);
        return emptySoundPatch;
    }

    @Override
    public void resetOnRemoveSoundPatch() {

    }

    @Override
    protected void requestFromNetAndSetSoundPatchInfo() {
        PlayableModel playableModel = null == XmPlayerService.getPlayerSrvice() ? null : XmPlayerService.getPlayerSrvice().getCurrPlayModel();
        if (playableModel instanceof Track) {
            Track track = (Track) playableModel;
            if (mTrackId != track.getDataId()) {
                return;
            }
            SoundPatchInfo soundPatch = new SoundPatchInfo(mTrackId, mUrl, null, SoundPatchInfo.PLAY_NOW, 0, mGain, mCategoryName);
            Bundle bundle = new Bundle();
            bundle.putString("sgId", "0s");
            soundPatch.setExtraInfo(bundle);
            setSoundPatchAndPlay(soundPatch);
        }
    }

    @Override
    public boolean shouldPlayOnlyOncePerTrack() {
        return false;
    }

    @Override
    protected void doOnSoundPatchStartPlay(SoundPatchInfo soundPatchInfo) {
        super.doOnSoundPatchStartPlay(soundPatchInfo);
        ITrace trace = RouterServiceManager.getInstance().getService(ITrace.class);
        if (trace != null && soundPatchInfo != null) {
            trace.traceOnSoundPatchPlayStart(soundPatchInfo.getCategoryName(), soundPatchInfo.getTrackId(), soundPatchInfo.getSgId(), soundPatchInfo.getUrl());
        }
    }
}