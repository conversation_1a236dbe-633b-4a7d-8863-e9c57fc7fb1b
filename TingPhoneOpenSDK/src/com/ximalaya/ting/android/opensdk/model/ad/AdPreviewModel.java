package com.ximalaya.ting.android.opensdk.model.ad;

import android.os.Parcel;
import android.os.Parcelable;

import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;

import java.util.List;

/**
 * Created by xmly on 2019-08-26.
 *
 * <AUTHOR>
 */
public class AdPreviewModel implements Parcelable {
    private int ret;
    private int positionId;
    private boolean isLiveBanner;
    private String content;
    private boolean clickReportFlag;
    private List<Advertis> mAdvertis;


    public static final Creator<AdPreviewModel> CREATOR = new Creator<AdPreviewModel>() {
        @Override
        public AdPreviewModel createFromParcel(Parcel in) {
            AdPreviewModel ad = new AdPreviewModel();
            ad.readFromParcel(in);
            return ad;
        }

        @Override
        public AdPreviewModel[] newArray(int size) {
            return new AdPreviewModel[size];
        }
    };

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public List<Advertis> getAdvertis() {
        return mAdvertis;
    }

    public void setAdvertis(List<Advertis> advertis) {
        mAdvertis = advertis;
    }

    public int getPositionId() {
        return positionId;
    }

    public void setPositionId(int positionId) {
        this.positionId = positionId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setClickReportFlag(boolean clickReportFlag) {
        this.clickReportFlag = clickReportFlag;
    }

    public boolean isClickReportFlag() {
        return clickReportFlag;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public void readFromParcel(Parcel in) {
        ret = in.readInt();
        positionId = in.readInt();
        content = in.readString();
        mAdvertis = in.createTypedArrayList(Advertis.CREATOR);
        isLiveBanner = in.readInt() == 1;
        clickReportFlag = in.readInt() == 1;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(ret);
        dest.writeInt(positionId);
        dest.writeString(content);
        dest.writeTypedList(mAdvertis);
        dest.writeInt(isLiveBanner ? 1 : 0);
        dest.writeInt(clickReportFlag ? 1 : 0);
    }

    public boolean isLiveBanner() {
        return isLiveBanner;
    }

    public void setLiveBanner(boolean liveBanner) {
        isLiveBanner = liveBanner;
    }
}
