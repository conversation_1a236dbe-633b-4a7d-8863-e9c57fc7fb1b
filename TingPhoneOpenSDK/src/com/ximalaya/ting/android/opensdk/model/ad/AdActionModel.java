package com.ximalaya.ting.android.opensdk.model.ad;

import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.util.TrackUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/5/7 16:58
 */
public class AdActionModel {
    private static final String TAG = "AdActionModel";

    private long beginAdTime;
    private long containPlayTime;
    private long trackId;

    private long intervalTime;
    private boolean hasPlayed;
    private int adDuration;
    private int breakPoint;
    private String trace;
    private boolean uploaded = false;
    public long onSoundSwitchDuration = 0;

    public void beginPlay(Track curPlayTrack) {
        Logger.d(TAG, "beginPlay " + TrackUtil.trackToStr(curPlayTrack));
        beginAdTime = System.currentTimeMillis();
        if (curPlayTrack != null) {
            trackId = curPlayTrack.getDataId();
        }
    }

    public void containAdPlay() {
        long startTime = System.currentTimeMillis();
        containPlayTime = startTime - beginAdTime;
        beginAdTime = startTime;
        Logger.d(TAG, "containAdPlay containPlayTime=" + containPlayTime);
    }

    public void adPlayed(int adDuration, int breakPoint) {
        this.hasPlayed = true;
        this.adDuration = adDuration;
        this.breakPoint = breakPoint;
        Logger.d(TAG, "adPlayed,hasPlayed=true,adDuration=" + adDuration + ",breakPoint=" + breakPoint);
    }

    public void onSoundSwitch(long duration) {
        this.onSoundSwitchDuration = duration;
    }

    public void endAdPlay(String trace) {
        if (uploaded) {
            return;
        }
        uploaded = true;
        this.trace = trace;
        intervalTime = System.currentTimeMillis() - beginAdTime;
        if (canUpload()) {
            try {
                String json = toJson();
                Logger.logToFile(TAG + "endAdPlay intervalTime=" + intervalTime + ",containPlayTime=" + containPlayTime + ",json=" + json);
                if (intervalTime + containPlayTime - breakPoint > 3_000) {
                    IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
                    if (xdcsPost != null) {
                        xdcsPost.statErrorToXDCS("adCostTimeOut", json);
                    }
                }
                XmLogger.log("apm", "adActionModel", json);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private boolean canUpload() {
        return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEM_CAN_UPLOAD_AD_COST_TIME, true);
    }

    private String toJson() {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("trackId", trackId);
            jsonObject.put("containPlayTime", containPlayTime);
            jsonObject.put("intervalTime", intervalTime);
            jsonObject.put("hasPlayed", hasPlayed);
            jsonObject.put("adDuration", adDuration);
            jsonObject.put("breakPoint", breakPoint);
            jsonObject.put("onSoundSwitchDuration", onSoundSwitchDuration);
            jsonObject.put("trace", trace != null && trace.length() > 600 ? trace.substring(0, 600) : trace);
            return jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
