package com.ximalaya.ting.android.opensdk.model.soundpatch.specific;

import android.os.RemoteException;

import com.ximalaya.ting.android.opensdk.constants.SoundPatchConstants;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.ad.SoundPatchInfo;
import com.ximalaya.ting.android.opensdk.model.soundpatch.BaseSoundPatch;
import com.ximalaya.ting.android.opensdk.model.soundpatch.NetSoundPatch;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.service.IXmCommonBusinessDispatcher;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.SoundPatchMmkvUtil;

import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.trace.ITrace;
import org.json.JSONObject;

public class FollowAdSoundPatch extends NetSoundPatch {
    public static FollowAdSoundPatch emptySample() {
        FollowAdSoundPatch sample = new FollowAdSoundPatch();
        sample.releaseSoundPatch();
        return sample;
    }

    private long mTrackId;
    private long mAlbumId;
    private String mUrl;
    private double mGain = SoundPatchConstants.DEFAULT_DOUBLE;
    private String mCategoryName = null;

    private void setPatchInfo(long albumId, long trackId, String url, double gain, String categoryName) {
        this.mAlbumId = albumId;
        this.mTrackId = trackId;
        this.mUrl = url;
        this.mGain = gain;
        this.mCategoryName = categoryName;
    }

    @Override
    public int getPriority() {
        return SoundPatchConstants.FrameWorkSoundPatchController.PRIORITY_FOLLOW_AD;
    }

    @Override
    public boolean isValid() {
        return true;
    }

    @Override
    protected boolean checkPlayCyclePeriod() {
        return true;
    }

    @Override
    protected boolean checkPlayConditionExcludePlayCyclePeriod() {
        return true;
    }

    @Override
    protected void requestFromNetAndSetSoundPatchInfo() {
        PlayableModel playableModel = null == XmPlayerService.getPlayerSrvice() ? null : XmPlayerService.getPlayerSrvice().getCurrPlayModel();
        if (playableModel instanceof Track) {
            Track track = (Track) playableModel;
            if (mTrackId != track.getDataId()) {
                return;
            }
            SoundPatchInfo soundPatch = new SoundPatchInfo(mTrackId, mUrl, null, SoundPatchInfo.PLAY_INDEX_START, 0, mGain, mCategoryName);
            setSoundPatchAndPlay(soundPatch);
        }
    }

    @Override
    public BaseSoundPatch cloneSoundPatch(String paramsJsonString) {
        long albumId = SoundPatchConstants.DEFAULT_LONG;
        long trackId = SoundPatchConstants.DEFAULT_LONG;
        String url = null;
        double gain = SoundPatchConstants.DEFAULT_DOUBLE;
        String categoryName = null;

        try {
            JSONObject jsonObject = new JSONObject(paramsJsonString);
            albumId = SoundPatchHelper.decodeAlbumId(jsonObject);
            trackId = SoundPatchHelper.decodeTrackId(jsonObject);
            url = SoundPatchHelper.decodeUrl(jsonObject);
            gain = SoundPatchHelper.decodeGain(jsonObject);
            categoryName = SoundPatchHelper.decodeCategoryName(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }

        FollowAdSoundPatch emptySoundPatch = new FollowAdSoundPatch();
        emptySoundPatch.setPatchInfo(albumId, trackId, url, gain, categoryName);
        return emptySoundPatch;
    }

    @Override
    public void resetOnRemoveSoundPatch() {

    }

    @Override
    protected void doOnSoundPatchStartPlay(SoundPatchInfo soundPatchInfo) {
        super.doOnSoundPatchStartPlay(soundPatchInfo);
        XmPlayerService service = XmPlayerService.getPlayerSrvice();
        if (null != service) {
            IXmCommonBusinessDispatcher iXmCommonBusinessDispatcher =
                    service.getIXmCommonBusinessDispatcher();
            if (iXmCommonBusinessDispatcher != null) {
                try {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("currTrackId", "" + this.mTrackId);
                    jsonObject.put("currAlbumId", "" + this.mAlbumId);
                    jsonObject.put("contentId", this.mUrl);
                    iXmCommonBusinessDispatcher.callForTraceMarkPoint(48427, jsonObject.toString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            SoundPatchMmkvUtil.increaseFollowAdSoundPatchTimes(service);
        }
        ITrace trace = RouterServiceManager.getInstance().getService(ITrace.class);
        if (trace != null && soundPatchInfo != null) {
            trace.traceOnSoundPatchPlayStart(soundPatchInfo.getCategoryName(), soundPatchInfo.getTrackId(), soundPatchInfo.getSgId(), soundPatchInfo.getUrl());
        }
    }
}
