package com.ximalaya.ting.android.opensdk.model.power;

import androidx.annotation.Keep;

/**
 * Created by jack.qin on 2021/12/23.
 *
 * <AUTHOR>
 * @email <EMAIL>
 *
 * String log = "device_idle_mode=" + powerManager.isDeviceIdleMode()
 *                                                 + ",screen_status=" + powerManager.isInteractive()
 *                                                 + ",process_status=" + SystemUtil.getProImportance(context)
 *                                                 + ",power_save=" + isPowerSaveModeHuaweiXiaomi(context)
 *                                                 + ",net_sleep_policy=" + SystemUtil.isKeepNetConnectInSleepMode(context)
 *                                                 + ",whitelist=" + SystemUtil.isIgnoringBatteryOptimizations(context);
 */
@Keep
public class DozeModel {
    public boolean isDeviceIdle;
    public boolean isInteractive;
    public boolean isPowerSaveMode;
    public boolean isKeepNetSleepMode;
    public boolean isIgnoringBatteryOp;
}
