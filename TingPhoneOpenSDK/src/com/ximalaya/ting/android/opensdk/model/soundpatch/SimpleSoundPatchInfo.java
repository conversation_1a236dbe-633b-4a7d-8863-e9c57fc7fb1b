package com.ximalaya.ting.android.opensdk.model.soundpatch;

import android.os.Parcel;
import android.os.Parcelable;

import com.ximalaya.ting.android.opensdk.constants.SoundPatchConstants;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * Created by 5Greatest on 2021.04.27
 *
 * <AUTHOR>
 * On 2021/4/27
 */
public class SimpleSoundPatchInfo implements Parcelable {
    private String clazzName;

    public SimpleSoundPatchInfo(String clazzName) {
        this.clazzName = clazzName;
    }

    public SimpleSoundPatchInfo(Parcel source) {
        if (null != source) {
            this.clazzName = source.readString();
        }
    }

    public static final Creator<SimpleSoundPatchInfo> CREATOR = new Creator<SimpleSoundPatchInfo>() {
        @Override
        public SimpleSoundPatchInfo createFromParcel(Parcel source) {
            return new SimpleSoundPatchInfo(source);
        }

        @Override
        public SimpleSoundPatchInfo[] newArray(int size) {
            return new SimpleSoundPatchInfo[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.clazzName);
    }

    public BaseSoundPatch createSoundPatch() {
        if (null != this.clazzName) {
            try {
                Logger.d(SoundPatchConstants.TAG, clazzName);
                Class<?> clazz = Class.forName(this.clazzName);
                if (null != clazz) {
                    Object result = clazz.newInstance();
                    if (result instanceof BaseSoundPatch) {
                        return (BaseSoundPatch) result;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
