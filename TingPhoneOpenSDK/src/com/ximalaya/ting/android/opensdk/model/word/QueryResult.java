/**
 * QueryResult.java
 * com.ximalaya.ting.android.framework.model.word
 *
 * Function： TODO 
 *
 *   ver     date      		author
 * ──────────────────────────────────
 *   		 2015-7-17 		jack.qin
 *
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.model.word;

import com.google.gson.annotations.SerializedName;

/**
 * ClassName:QueryResult Function: TODO ADD FUNCTION Reason: TODO ADD REASON
 * 
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015-7-17 下午4:42:58
 * 
 * @see
 */
public class QueryResult
{
	@SerializedName("id")
	private long queryId;
	private String keyword;
	@SerializedName("highlight_keyword")
	private String highlightKeyword;

	private String xmRequestId;

	public long getQueryId()
	{
		return queryId;
	}

	public void setQueryId(long queryId)
	{
		this.queryId = queryId;
	}

	public String getKeyword()
	{
		return keyword;
	}

	public void setKeyword(String keyword)
	{
		this.keyword = keyword;
	}

	public String getHighlightKeyword()
	{
		return highlightKeyword;
	}

	public void setHighlightKeyword(String highlightKeyword)
	{
		this.highlightKeyword = highlightKeyword;
	}

	public String getXmRequestId() {
		return xmRequestId;
	}

	public void setXmRequestId(String xmRequestId) {
		this.xmRequestId = xmRequestId;
	}

	@Override
	public String toString()
	{
		return "QueryResult [queryId=" + queryId + ", keyword=" + keyword
				+ ", highlightKeyword=" + highlightKeyword + "]";
	}

}
