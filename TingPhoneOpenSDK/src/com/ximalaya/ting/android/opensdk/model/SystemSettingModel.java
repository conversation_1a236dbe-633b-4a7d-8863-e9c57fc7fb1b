package com.ximalaya.ting.android.opensdk.model;

public class SystemSettingModel {
    private  int inWhiteLists; //0默认值； -1没在；1已经在了；
    private int keepConnectInSleepMode; //0没取到； -1关闭；1打开；
    private int powerSaveMode; //省电模式
    private int isSystemOta;//0没取到；-1否；1是；
    private int xiaomiSoundAssistKey;   // 小米 音量是否可以单独媒体进行设置 -1没取到 1 打开 0关闭
    private int xiaomiIgnoreMusicFocusReq;   // 小米 允许多声音 -1没取到 1 打开 0 关闭

    public int getInWhiteLists() {
        return inWhiteLists;
    }

    public void setInWhiteLists(int inWhiteLists) {
        this.inWhiteLists = inWhiteLists;
    }

    public int getKeepConnectInSleepMode() {
        return keepConnectInSleepMode;
    }

    public void setKeepConnectInSleepMode(int keepConnectInSleepMode) {
        this.keepConnectInSleepMode = keepConnectInSleepMode;
    }

    public int getPowerSaveMode() {
        return powerSaveMode;
    }

    public void setPowerSaveMode(int powerSaveMode) {
        this.powerSaveMode = powerSaveMode;
    }

    public int getIsSystemOta() {
        return isSystemOta;
    }

    public void setIsSystemOta(int isSystemOta) {
        this.isSystemOta = isSystemOta;
    }

    public int getXiaomiSoundAssistKey() {
        return xiaomiSoundAssistKey;
    }

    public void setXiaomiSoundAssistKey(int xiaomiSoundAssistKey) {
        this.xiaomiSoundAssistKey = xiaomiSoundAssistKey;
    }

    public int getXiaomiIgnoreMusicFocusReq() {
        return xiaomiIgnoreMusicFocusReq;
    }

    public void setXiaomiIgnoreMusicFocusReq(int xiaomiIgnoreMusicFocusReq) {
        this.xiaomiIgnoreMusicFocusReq = xiaomiIgnoreMusicFocusReq;
    }
}
