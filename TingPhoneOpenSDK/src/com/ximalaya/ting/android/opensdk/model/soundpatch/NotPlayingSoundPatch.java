package com.ximalaya.ting.android.opensdk.model.soundpatch;

import com.ximalaya.ting.android.opensdk.constants.SoundPatchConstants;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.ad.SoundPatchInfo;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.player.soundpatch.ICommercialSoundPatchControlStatusCallBack;
import com.ximalaya.ting.android.opensdk.player.soundpatch.SoundPatchArbitrateManager;

import java.util.Map;

/**
 * Created by 5Greatest on 2020.07.22
 * <p>
 * 处在非播放时去播放的声音贴片
 *
 * <AUTHOR>
 * On 2020-07-22
 */
public abstract class NotPlayingSoundPatch extends NetSoundPatch {
    private static final long TO_RESET_TIME_TOLERANCE = 1000L;

    // 起播时间
    private long startTime = 0;

    //// 返回贴片的基本类型
    @Override
    public int getBasicType() {
        return SoundPatchConstants.TYPE_NOT_PLAYING_SOUND_PATCH;
    }

    //// 允许在一次播放前可以允许同时存在的非播放时期贴片的数量
    public abstract int getAllowTolerance();

    @Override
    protected void notifyStart() {
        SoundPatchArbitrateManager.onSoundPatchEvent(ICommercialSoundPatchControlStatusCallBack.ON_NOT_PLAYING_SOUND_PATCH_START);
    }

    @Override
    protected void notifyStop() {
        SoundPatchArbitrateManager.onSoundPatchEvent(ICommercialSoundPatchControlStatusCallBack.ON_NOT_PLAYING_SOUND_PATCH_STOP);
    }

    protected void playUrl(String url) {
        if (null != url) {
            PlayableModel playableModel = null == XmPlayerService.getPlayerSrvice() ? null : XmPlayerService.getPlayerSrvice().getCurrPlayModel();
            SoundPatchInfo soundPatch = new SoundPatchInfo(null == playableModel ? 0 : playableModel.getDataId(), url, null, SoundPatchInfo.PLAY_NOW, 0);
            setSoundPatchAndPlay(soundPatch);
        }
    }

    protected void playUrl(String url, double gain, String categoryName) {
        if (null != url) {
            PlayableModel playableModel = null == XmPlayerService.getPlayerSrvice() ? null : XmPlayerService.getPlayerSrvice().getCurrPlayModel();
            SoundPatchInfo soundPatch = new SoundPatchInfo(null == playableModel ? 0 : playableModel.getDataId(), url, null, SoundPatchInfo.PLAY_NOW, 0, gain, categoryName);
            setSoundPatchAndPlay(soundPatch);
        }
    }

    /*@Override
    public void setSoundPatchAndPlay(SoundPatchInfo soundPatch) {
        if (!flag_Removed) {
            this.soundPatch = soundPatch;

            // 贴片放入播放器中，并记录添加到播放器中的贴片
            SoundPatchManager.getInstance().appendSoundPatchInfo(this.soundPatch);
        }
    }*/

    @Override
    protected void requestFromNetAndSetSoundPatchInfo() {

    }

    public abstract boolean isAbleToBlockLowPriorities(Map<String, Object> requirementParam);

    @Override
    protected void doOnSoundPatchStartPlay(SoundPatchInfo soundPatchInfo) {
        super.doOnSoundPatchStartPlay(soundPatchInfo);
        startTime = System.currentTimeMillis();
    }

    public boolean resetOnVideoAdPlay() {
        if (isPlaying()) {
            stopSoundPatch();
            /*if (TO_RESET_TIME_TOLERANCE > (System.currentTimeMillis() - startTime)) {
                // 可以重新播放
                // 暂时不设为true
                return true;
            } else {
                // 仅仅停，不可以重新播放
                return false;
            }*/
        }
        return false;
    }
}
