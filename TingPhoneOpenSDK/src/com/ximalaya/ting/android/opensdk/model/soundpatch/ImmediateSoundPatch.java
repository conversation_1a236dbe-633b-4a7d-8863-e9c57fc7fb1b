package com.ximalaya.ting.android.opensdk.model.soundpatch;

import android.content.Context;
import android.content.res.AssetManager;
import android.os.Build;
import android.os.PowerManager;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.opensdk.constants.SoundPatchConstants;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.ad.SoundPatchInfo;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.manager.ISoundPatchStatusListener;
import com.ximalaya.ting.android.opensdk.player.manager.SoundPatchManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmCommonBusinessDispatcher;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.mixplay.MixPlayerService;
import com.ximalaya.ting.android.opensdk.util.FileUtilBase;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.player.SMediaPlayer;
import com.ximalaya.ting.android.player.XMediaPlayer;
import com.ximalaya.ting.android.player.XMediaplayerImpl;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by 5Greatest on 2020.03.12
 * <p>
 * 本地文件形式的声音贴片
 *
 * <AUTHOR>
 * On 2020-03-12
 */
public abstract class ImmediateSoundPatch extends BaseSoundPatch {
    private static String sPlayUrl;
    private static SMediaPlayer sUrlPlayer;


    // 因为在播放贴片后会继续播放 又会回调onPlayStart 所以这里要做一下时间限制
    protected static long LastPlayTime = -1l;

    // 不论是onHintComplete()还是onPlayError()，都认为是完成播放了
    private boolean flag_Complete = false;

    // 确认被停止了
    private volatile boolean comfirnStop = false;

    // 是否被暂停了
    private volatile boolean flag_Paused = false;

    private final AtomicBoolean hasRemovedListener;
    private final IXmPlayerStatusListener listener = new IXmPlayerStatusListener() {
        @Override
        public void onPlayStart() {

        }

        @Override
        public void onPlayPause() {
            stopSoundPatch();
        }

        @Override
        public void onPlayStop() {

        }

        @Override
        public void onSoundPlayComplete() {

        }

        @Override
        public void onSoundPrepared() {

        }

        @Override
        public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
            stopSoundPatch();
        }

        @Override
        public void onBufferingStart() {

        }

        @Override
        public void onBufferingStop() {

        }

        @Override
        public void onBufferProgress(int percent) {

        }

        @Override
        public void onPlayProgress(int currPos, int duration) {

        }

        @Override
        public boolean onError(XmPlayerException exception) {
            return false;
        }
    };

    public ImmediateSoundPatch() {
        XmPlayerService.addPlayerStatusListenerOnPlayProcees(listener);
        hasRemovedListener = new AtomicBoolean(false);
    }

    //// 返回贴片的基本类型
    @Override
    public int getBasicType() {
        return SoundPatchConstants.TYPE_LOCAL_SOUND_PATCH;
    }

    /**
     * 判断是否满足当前贴片播放条件（对外接口）
     * 或者说是否满足去屏蔽低级贴片的条件
     * <p>
     * 需要满足 checkPlayCyclePeriod() 和 checkPlayConditionExcludePlayCyclePeriod() 中的条件
     * <p>
     * 目前：
     *
     * @param requirementParam 需要里面有  KEY_CURRENT_PLAYABLEMODEL  对应的PlayableModel对象
     */
    public abstract boolean isAbleToBlockLowPriorities(Map<String, Object> requirementParam);

    protected boolean canPauseTrack() {
        return false;
    }

    protected void playUrl(String url) {
        if (MixPlayerService.getMixService().getPlaySource() != null) {
            return;
        }

        if ((TextUtils.isEmpty(url))
                || (sUrlPlayer != null && sUrlPlayer.isPlaying() && url.equals(sPlayUrl))) {
            return;
        }

        sUrlPlayer = new SMediaPlayer(mContext);
        sUrlPlayer.reset();
        sUrlPlayer.setDataSource(url);
        sUrlPlayer.prepareAsync();
        sUrlPlayer.setOnPreparedListener(new XMediaPlayer.OnPreparedListener() {
            @Override
            public void onPrepared(XMediaplayerImpl xMediaplayer) {
                sPlayUrl = url;
                if (!comfirnStop && sUrlPlayer != null) {
                    XmPlayerService playerService = XmPlayerService.getPlayerSrvice();
                    if (null != playerService && playerService.isPlaying()) {
                        if (canPauseTrack()) {
                            playerService.pausePlay(false, PauseReason.SoundPatch.IMMEDIATE_SOUND_PATCH);
                            sUrlPlayer.start();
                            doOnSoundPatchStartPlay(ImmediateSoundPatch.this);
                        } else {
                            stopSoundPatch();
                        }
                    } else {
                        sUrlPlayer.start();
                        doOnSoundPatchStartPlay(ImmediateSoundPatch.this);
                    }
                }
            }
        });
        sUrlPlayer.setOnCompletionListener(new XMediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(XMediaplayerImpl xMediaplayer) {
                if (hasRemovedListener.compareAndSet(false, true)) {
                    XmPlayerService.removePlayerStatusListenerOnPlayProcess(listener);
                }
                sPlayUrl = null;
                if (sUrlPlayer != null) {
                    sUrlPlayer.stop();
                    sUrlPlayer.release();
                }
                sUrlPlayer = null;

                flag_Complete = true;
                notifyStop();

                doOnSoundPatchCompletePlay(ImmediateSoundPatch.this);
                Logger.i(SoundPatchConstants.TAG, "Url播放完成");
                if (isAutoPlayNextOnComplete() && !isForbiddenPlayNext()) {
                    /*XmPlayerService playerService = XmPlayerService.getPlayerSrvice();
                    if (null != playerService) {
                        playerService.playNext();
                    }*/
                    return;
                } /*else {
                    if (isContinuePlayOnComplete()) {
                        XmPlayerService playerService = XmPlayerService.getPlayerSrvice();
                        if (null != playerService) {
                            IXmCommonBusinessDispatcher dispatcher = playerService.getIXmCommonBusinessDispatcher();
                            boolean isPlayVideoAd = false;
                            try {
                                isPlayVideoAd = null != dispatcher && dispatcher.checkAndPlayPausedVideoAd();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            if (!isPlayVideoAd) {
                                playerService.startPlay();
                            }
                        }
                    }
                }*/
                if (!isContinuePlayOnComplete() && !isForbiddenPausePlay()) {
                    XmPlayerService playerService = XmPlayerService.getPlayerSrvice();
                    if (null != playerService) {
                        playerService.pausePlay(false, PauseReason.SoundPatch.IMMEDIATE_SOUND_PATCH);
                    }
                }
            }
        });
        sUrlPlayer.setOnErrorListener(new XMediaPlayer.OnErrorListener() {
            @Override
            public boolean onError(XMediaplayerImpl xMediaplayer, int i, int i1, String cause) {
                if (hasRemovedListener.compareAndSet(false, true)) {
                    XmPlayerService.removePlayerStatusListenerOnPlayProcess(listener);
                }
                sPlayUrl = null;
                if (sUrlPlayer != null) {
                    sUrlPlayer.stop();
                    sUrlPlayer.release();
                }
                sUrlPlayer = null;

                flag_Complete = true;
                notifyStop();

                Logger.i(SoundPatchConstants.TAG, "Url播放失败");
                doOnSoundPatchError(ImmediateSoundPatch.this, i, i1);
                if (isAutoPlayNextOnComplete() && !isForbiddenPlayNext()) {
                    /*XmPlayerService playerService = XmPlayerService.getPlayerSrvice();
                    if (null != playerService) {
                        playerService.playNext();
                    }*/
                    return true;
                } /*else {
                    if (isContinuePlayOnComplete()) {
                        XmPlayerService playerService = XmPlayerService.getPlayerSrvice();
                        if (null != playerService) {
                            IXmCommonBusinessDispatcher dispatcher = playerService.getIXmCommonBusinessDispatcher();
                            boolean isPlayVideoAd = false;
                            try {
                                isPlayVideoAd = null != dispatcher && dispatcher.checkAndPlayPausedVideoAd();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            if (!isPlayVideoAd) {
                                playerService.startPlay();
                            }
                        }
                    }
                }*/
                if (!isContinuePlayOnComplete() && !isForbiddenPausePlay()) {
                    XmPlayerService playerService = XmPlayerService.getPlayerSrvice();
                    if (null != playerService) {
                        playerService.pausePlay(false, PauseReason.SoundPatch.IMMEDIATE_SOUND_PATCH);
                    }
                }

                return true;
            }
        });
        // XmPlayerManager.getInstance(mContext).pause();
    }

    /**
     * 暂停声音贴片的播放
     */
    public boolean pause() {
        if (isPlaying()) {
            if (null != sUrlPlayer) {
                sUrlPlayer.pause();
                flag_Paused = true;
                return true;
            }
            return false;
        }
        return false;
    }

    /**
     * 恢复声音贴片的播放
     */
    public boolean resumePlay() {
        if (isPaused()) {
            flag_Paused = false;
            if (null != sUrlPlayer) {
                sUrlPlayer.start();
                return true;
            }
            return false;
        }
        return false;
    }

    /**
     * 终止声音贴片的播放
     */
    @Override
    public void stopSoundPatch() {
        comfirnStop = true;
        if (isPlaying()) {
            if (null != sUrlPlayer) {
                sUrlPlayer.stop();
                sUrlPlayer.release();
            }
        }
        releaseSoundPatch();
    }

    /**
     * 判断是否暂停
     */
    public boolean isPaused() {
        return !comfirnStop && !flag_Complete && flag_Paused;
    }

    /**
     * 判断是否正在播放
     */
    @Override
    public boolean isPlaying() {
        return (null != sUrlPlayer && sUrlPlayer.isPlaying());
    }

    /**
     * 本地文件类型的声音贴片时即时播放的，所以不存在移除将要播放的声音贴片的操作,
     */
    @Override
    public void removeSoundPatch() {
        stopSoundPatch();
        releaseSoundPatch();
        resetOnRemoveSoundPatch();
    }

    @Override
    public void releaseSoundPatch() {
        if (null != listener && hasRemovedListener.compareAndSet(false, true)) {
            XmPlayerService.removePlayerStatusListenerOnPlayProcess(listener);
        }
        // do Nothing
    }

    /**
     * 判断是否已经完成播放
     */
    @Override
    public boolean isComplete() {
        return flag_Complete;
    }

    @Override
    public boolean shouldPlayOnlyOncePerTrack() {
        return true;
    }

    //// 播放状态变更时的动作
    protected void doOnSoundPatchStartPlay(ImmediateSoundPatch soundPatch) {
        // do Nothing
    }

    protected void doOnSoundPatchCompletePlay(ImmediateSoundPatch soundPatchInfo) {
        // do Nothing
    }

    protected void doOnSoundPatchError(ImmediateSoundPatch soundPatchInfo, int what, int extra) {
        // do Nothing
    }
}
