package com.ximalaya.ting.android.opensdk.model;


public class XmAppExitInfo {
    private String reason;

    private String timestamp;

    private String processName;

    private int importance;

    private String playStatus;

    private String description;

    private String status;

    private String pss;

    private String rss;

    private String state;

    private String oomAdj;

    private String xabtestIds;

    private int screenStatus;

    private boolean isInWhiteList;

    public void setReason(String reason){
        this.reason = reason;
    }
    public String getReason(){
        return this.reason;
    }
    public void setTimestamp(String timestamp){
        this.timestamp = timestamp;
    }
    public String getTimestamp(){
        return this.timestamp;
    }
    public void setProcessName(String processName){
        this.processName = processName;
    }
    public String getProcessName(){
        return this.processName;
    }
    public void setImportance(int importance){
        this.importance = importance;
    }
    public int getImportance(){
        return this.importance;
    }
    public void setDescription(String description){
        this.description = description;
    }
    public String getDescription(){
        return this.description;
    }
    public void setStatus(String status){
        this.status = status;
    }
    public String getStatus(){
        return this.status;
    }
    public void setPss(String pss){
        this.pss = pss;
    }
    public String getPss(){
        return this.pss;
    }
    public void setRss(String rss){
        this.rss = rss;
    }
    public String getRss(){
        return this.rss;
    }
    public void setState(String state){
        this.state = state;
    }
    public String getState(){
        return this.state;
    }
    public String getOomAdj() {
        return oomAdj;
    }
    public void setOomAdj(String oomAdj) {
        this.oomAdj = oomAdj;
    }

    public String getPlayStatus() {
        return playStatus;
    }

    public void setPlayStatus(String playStatus) {
        this.playStatus = playStatus;
    }

    public String getXabtestIds() {
        return xabtestIds;
    }

    public void setXabtestIds(String xabtestIds) {
        this.xabtestIds = xabtestIds;
    }

    public int getScreenStatus() {
        return screenStatus;
    }

    public void setScreenStatus(int screenStatus) {
        this.screenStatus = screenStatus;
    }

    public boolean isInWhiteList() {
        return isInWhiteList;
    }

    public void setInWhiteList(boolean inWhiteList) {
        isInWhiteList = inWhiteList;
    }
}
