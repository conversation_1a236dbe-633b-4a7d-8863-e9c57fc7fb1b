package com.ximalaya.ting.android.opensdk.model.xdcs;

import com.ximalaya.ting.android.player.xdcs.IStatToServer;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by chengyun.wu on 17/7/12.
 *
 * <AUTHOR>
 */

public class StatToServerForMain implements IStatToServer{


    private static final String TAG = "IStatToServer";

    private List<XdcsEvent> mEventList = new ArrayList<>();


    @Override
    public void addHttpDnsEvent(String requestUrl, String requestResult,
                                String requestSeq, String requestHost,
                                String ua, String requestTs, String errorInfo) {
        XdcsEvent xdcsEvent = new XdcsEvent();
        Map<String, String> props = new HashMap<>();
        props.put("type", "httpdns_request");
        props.put("request_url", requestUrl);
        props.put("request_result", requestResult);//请求结果,success/fail
        props.put("request_seq", requestSeq);//此次请求链中的第几次尝试
        props.put("request_host", requestHost); //请求的host头，可为空，ip请求时必需有
        props.put("ua", ua);//user-agent
        props.put("request_ts", requestTs);//发生错误的时间
        props.put("error_info", errorInfo);//错误信息

        xdcsEvent.props = props;
        xdcsEvent.setTs(System.currentTimeMillis());
        xdcsEvent.setType("NETWORKINFO");
        Logger.i(TAG , "addHttpDnsEvent " + xdcsEvent);
        if(mEventList != null){
            mEventList.add(xdcsEvent);
        }
    }

    @Override
    public void addEndHttpDnsRequest(String requestUrl, String requestResult,
                                     String requestSeq, String requestHost,
                                     String ua, String requestTs, String errorInfo) {
        if(mEventList != null && mEventList.size() > 0) {
            addHttpDnsEvent(requestUrl, requestResult, requestSeq, requestHost, ua, requestTs, errorInfo);
            endAllHttpDnsRequest();
        }
    }


    @Override
    public void statNormalRequest(String requestUrl, String ua,
                                  String requestTs, String errorInfo) {
        XdcsEvent xdcsEvent = new XdcsEvent();
        Map<String, String> props = new HashMap<>();
        props.put("type", "normal_request");
        props.put("request_url", requestUrl);
        props.put("ua", ua);//user-agent
        props.put("request_ts", requestTs); //发生错误的时间
        props.put("error_info", errorInfo);//错误信息

        xdcsEvent.props = props;
        xdcsEvent.setTs(System.currentTimeMillis());
        xdcsEvent.setType("NETWORKINFO");
        List<XdcsEvent> eventList = new ArrayList<>();
        eventList.add(xdcsEvent);
        IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
        if(xdcsPost != null){
            xdcsPost.postError(XdcsRecord.createXdcsRecord(eventList));
        }
    }

    @Override
    public void endAllHttpDnsRequest() {
        if(mEventList != null && mEventList.size() > 1) {
            Logger.i(TAG , "sendHttpDnsEvent : " + mEventList.size());
            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
            if(xdcsPost != null){
                xdcsPost.postError(XdcsRecord.createXdcsRecord(mEventList));
            }
        }
    }
}
