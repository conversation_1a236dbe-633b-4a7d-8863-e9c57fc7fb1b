package com.ximalaya.ting.android.opensdk.model.soundEffect;

import android.os.Parcel;
import android.os.Parcelable;

import com.ximalaya.ting.android.opensdk.player.soundEffect.SoundEffectPlayerConstant;


public class SoundEffectInfo implements Parcelable {

    public int currentValidType;
    public long currentEffectId;
    /*public int lastValidType;
    public long lastEffectId;*/

    public SoundEffectInfo(int currentValidType, long currentEffectId) {
        this.currentValidType = currentValidType;
        this.currentEffectId = currentEffectId;
    }

    public SoundEffectInfo(Parcel in) {
        this.currentValidType = in.readInt();
        this.currentEffectId = in.readLong();
        /*this.lastValidType = in.readInt();
        this.lastEffectId = in.readLong();*/
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.currentValidType);
        dest.writeLong(this.currentEffectId);
        /*dest.writeInt(this.lastValidType);
        dest.writeLong(this.lastEffectId);*/
    }

    public static final Parcelable.Creator<SoundEffectInfo> CREATOR =
            new Parcelable.Creator<SoundEffectInfo>() {
                @Override
                public SoundEffectInfo createFromParcel(Parcel source) {
                    return new SoundEffectInfo(source);
                }

                @Override
                public SoundEffectInfo[] newArray(int size) {
                    return new SoundEffectInfo[size];
                }
            };

    private static final SoundEffectInfo RESET_INFO = new SoundEffectInfo(SoundEffectPlayerConstant.VALID_TYPE_NONE, SoundEffectPlayerConstant.EFFECT_ID_NONE);
    public static SoundEffectInfo getResetInfo() {
        return RESET_INFO;
    }
}
