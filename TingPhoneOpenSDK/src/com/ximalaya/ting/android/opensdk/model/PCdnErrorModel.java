package com.ximalaya.ting.android.opensdk.model;

import android.net.Uri;

import androidx.annotation.Keep;

import com.ximalaya.ting.android.exoplayer.model.PlayErrorSavedModel;

import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/8/1 17:29
 */
@Keep
public class PCdnErrorModel {
    //    public List<String> mUrls;
    public String errorUrl;
    public boolean reachedMaxFailCount;
    public PlayErrorSavedModel errorModel;

    public PCdnErrorModel(String url) {
        errorUrl = url;
    }

    public String toJson() {
        try {
            JSONObject jsonObject = new JSONObject();

            try {
                Uri uri = Uri.parse(errorUrl);
                jsonObject.put("host", uri.getHost());
            } catch (Exception e) {
                e.printStackTrace();
            }
            jsonObject.put("errorUrl", errorUrl);
            jsonObject.put("reachedMaxFailCount", reachedMaxFailCount);

            if (errorModel != null) {
                jsonObject.put("errorModel", errorModel.toJsonObject());
            }
            return jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
