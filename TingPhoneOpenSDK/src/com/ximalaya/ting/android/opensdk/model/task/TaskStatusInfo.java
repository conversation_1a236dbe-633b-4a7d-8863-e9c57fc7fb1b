package com.ximalaya.ting.android.opensdk.model.task;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by 5Greatest on 2020.09.14
 *
 * <AUTHOR>
 * On 2020-09-14
 */
public class TaskStatusInfo implements Parcelable {

    public boolean mSwitch;
    public long mOffsetRangeMin;

    public TaskStatusInfo() {
    }

    public TaskStatusInfo(Parcel source) {
        this.mSwitch = source.readInt() == 1;
        this.mOffsetRangeMin = source.readLong();
    }

    public static final Creator<TaskStatusInfo> CREATOR = new Creator<TaskStatusInfo>() {
        @Override
        public TaskStatusInfo createFromParcel(Parcel source) {
            return new TaskStatusInfo(source);
        }

        @Override
        public TaskStatusInfo[] newArray(int size) {
            return new TaskStatusInfo[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(mSwitch ? 1 : 0);
        dest.writeLong(mOffsetRangeMin);
    }


    private static TaskStatusInfoReceiver sReceiver;

    public static void registerInfoReceiver(TaskStatusInfoReceiver receiver) {
        sReceiver = receiver;
    }

    public static void notifyInfo(TaskStatusInfo info) {
        if (null != sReceiver) {
            sReceiver.onReceive(info);
        }
    }

    public interface TaskStatusInfoReceiver {
        void onReceive(TaskStatusInfo info);
    }
}
