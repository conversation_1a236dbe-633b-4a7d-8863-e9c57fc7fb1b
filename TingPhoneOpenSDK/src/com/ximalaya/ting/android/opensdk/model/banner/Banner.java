/**
 * RankBanners.java
 * com.ximalaya.ting.android.framework.model.banner
 *
 * Function： TODO 
 *
 *   ver     date      		author
 * ──────────────────────────────────
 *   		 2015-8-19 		jack.qin
 *
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.model.banner;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.opensdk.datatrasfer.XimalayaResponse;

/**
 * ClassName:RankBanners Function: TODO ADD FUNCTION Reason: TODO ADD REASON
 * 
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015-8-19 下午1:33:55
 * 
 * @see
 */
public class Banner extends XimalayaResponse
{
	@SerializedName("id")
	private long bannerId;
	@SerializedName("banner_title")
	private String bannerTitle;
	@SerializedName("banner_short_title")
	private String bannerShortTitle;
	@SerializedName("banner_url")
	private String bannerUrl;
	@SerializedName("banner_redirect_url")
	private String bannerRedirectUrl;
	@SerializedName("can_share")
	private boolean canShare;
	@SerializedName("banner_content_type")
	private int bannerContentType;
	@SerializedName("banner_uid")
	private int bannerUid;
	@SerializedName("track_id")
	private long trackId;
	@SerializedName("column_id")
	private int columnId;
	@SerializedName("column_content_type")
	private String columnContentType;
	@SerializedName("album_id")
	private long albumId;
	@SerializedName("third_party_url")
	private String thirdPartyUrl;
	@SerializedName("is_external_url")
	private boolean isExternalUrl;

	public long getBannerId()
	{
		return bannerId;
	}

	public void setBannerId(long bannerId)
	{
		this.bannerId = bannerId;
	}

	public String getBannerTitle()
	{
		return bannerTitle;
	}

	public void setBannerTitle(String bannerTitle)
	{
		this.bannerTitle = bannerTitle;
	}

	public String getBannerShortTitle()
	{
		return bannerShortTitle;
	}

	public void setBannerShortTitle(String bannerShortTitle)
	{
		this.bannerShortTitle = bannerShortTitle;
	}

	public String getBannerUrl()
	{
		return bannerUrl;
	}

	public void setBannerUrl(String bannerUrl)
	{
		this.bannerUrl = bannerUrl;
	}

	public String getBannerRedirectUrl()
	{
		return bannerRedirectUrl;
	}

	public void setBannerRedirectUrl(String bannerRedirectUrl)
	{
		this.bannerRedirectUrl = bannerRedirectUrl;
	}

	public boolean isCanShare()
	{
		return canShare;
	}

	public void setCanShare(boolean canShare)
	{
		this.canShare = canShare;
	}

	public int getBannerContentType()
	{
		return bannerContentType;
	}

	public void setBannerContentType(int bannerContentType)
	{
		this.bannerContentType = bannerContentType;
	}

	public int getBannerUid()
	{
		return bannerUid;
	}

	public void setBannerUid(int bannerUid)
	{
		this.bannerUid = bannerUid;
	}

	public long getTrackId()
	{
		return trackId;
	}

	public void setTrackId(long trackId)
	{
		this.trackId = trackId;
	}

	public int getColumnId()
	{
		return columnId;
	}

	public void setColumnId(int columnId)
	{
		this.columnId = columnId;
	}

	public String getColumnContentType()
	{
		return columnContentType;
	}

	public void setColumnContentType(String columnContentType)
	{
		this.columnContentType = columnContentType;
	}

	public long getAlbumId()
	{
		return albumId;
	}

	public void setAlbumId(long albumId)
	{
		this.albumId = albumId;
	}

	public String getThirdPartyUrl()
	{
		return thirdPartyUrl;
	}

	public void setThirdPartyUrl(String thirdPartyUrl)
	{
		this.thirdPartyUrl = thirdPartyUrl;
	}

	@Override
	public String toString()
	{
		return "RankBanners [bannerId=" + bannerId + ", bannerTitle="
				+ bannerTitle + ", bannerShortTitle=" + bannerShortTitle
				+ ", bannerUrl=" + bannerUrl + ", bannerRedirectUrl="
				+ bannerRedirectUrl + ", canShare=" + canShare
				+ ", bannerContentType=" + bannerContentType + ", bannerUid="
				+ bannerUid + ", trackId=" + trackId + ", columnId=" + columnId
				+ ", columnContentType=" + columnContentType + ", albumId="
				+ albumId + ", thirdPartyUrl=" + thirdPartyUrl
				+ ", isExternalUrl=" + isExternalUrl + "]";
	}

	public void setExternalUrl(boolean externalUrl) {
		isExternalUrl = externalUrl;
	}

	public boolean isExternalUrl() {
		return isExternalUrl;
	}
}
