package com.ximalaya.ting.android.opensdk.model.ad;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

import android.util.Log;
import androidx.annotation.NonNull;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * Created by le.xin on 2020-01-17.
 * 声音贴片的信息
 * <AUTHOR>
 * @email <EMAIL>
 */
public class SoundPatchInfo implements Parcelable {

    public static final int PLAY_INDEX_START = -1;
    public static final int PLAY_INDEX_END = -2;
    public static final int PLAY_NOW = -3;  // 立马播放

    private long trackId;
    private String url;
    private double gain;
    private String categoryName;
    private String filePath;
    // 需要播放的位置 ,-1 表示开头 ,-2 表示结尾, 其他按照秒进行插入 ,如果设置的开头播放,必须提前设置
    private int playIndex;
    // 贴片的播放间隔类型
    private int type;
    /*//贴片的播放间隔类型
    private int type;
    //贴片的类型
    private String soundPatchType;
    //播放间隔
    private long playInternal;*/

    private Bundle extraInfo;

    // 是否是声音贴片
    private boolean isAppendSoundPatch;

    // 紧跟在后面的声音贴片
    private SoundPatchInfo appendSoundPatch;

    public SoundPatchInfo(long trackId, String url, String filePath, int playIndex, int type) {
        this(trackId, url, filePath, playIndex, type, 0.0, null);
    }

    public SoundPatchInfo(long trackId, String url, String filePath, int playIndex, int type, double gain, String categoryName) {
        this.trackId = trackId;
        this.url = url;
        this.filePath = filePath;
        this.playIndex = playIndex;
        this.type = type;
        this.gain = gain;
        this.categoryName = categoryName;
        if (ConstantsOpenSdk.isDebug) {
            Logger.d("SoundPatchInfoTrace", "SoundPatchInfo SoundPatchInfo: " + Log.getStackTraceString(new Throwable()) + "," + this);
        }
    }

    private boolean isDownloaded;   // 是否已超时
    private boolean isTimeout;      // 是否已下载

    public long getTrackId() {
        return trackId;
    }

    public void setTrackId(long trackId) {
        this.trackId = trackId;
    }

    public String getUrl() {
        return url;
    }

    public String getSgId() {
        String sgId = "";
        if (extraInfo != null) {
            sgId = extraInfo.getString("sgId");
        }
        return sgId;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFilePath() {
        return filePath;
    }

    public double getGain() {
        return gain;
    }

    public void setGain(double gain) {
        this.gain = gain;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public int getPlayIndex() {
        return playIndex;
    }

    public void setPlayIndex(int playIndex) {
        this.playIndex = playIndex;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Bundle getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(Bundle extraInfo) {
        this.extraInfo = extraInfo;
    }

    public String getCategoryName() {
        return categoryName;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(this.trackId);
        dest.writeString(this.url);
        dest.writeString(this.filePath);
        dest.writeInt(this.playIndex);
        dest.writeInt(this.type);
        /*dest.writeString(this.soundPatchType);
        dest.writeLong(this.playInternal);*/
        dest.writeBundle(this.extraInfo);
        dest.writeDouble(gain);
    }

    public SoundPatchInfo() {
    }

    protected SoundPatchInfo(Parcel in) {
        this.trackId = in.readLong();
        this.url = in.readString();
        this.filePath = in.readString();
        this.playIndex = in.readInt();
        this.type = in.readInt();
        /*this.soundPatchType = in.readString();
        this.playInternal = in.readLong();*/
        this.extraInfo = in.readBundle();
        this.gain = in.readDouble();
    }

    public static final Parcelable.Creator<SoundPatchInfo> CREATOR =
            new Parcelable.Creator<SoundPatchInfo>() {
        @Override
        public SoundPatchInfo createFromParcel(Parcel source) {
            return new SoundPatchInfo(source);
        }

        @Override
        public SoundPatchInfo[] newArray(int size) {
            return new SoundPatchInfo[size];
        }
    };

    public boolean isDownloaded() {
        return isDownloaded;
    }

    public void setDownloaded(boolean downloaded) {
        isDownloaded = downloaded;
    }

    public boolean isTimeout() {
        return isTimeout;
    }

    public void setTimeout(boolean timeout) {
        isTimeout = timeout;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SoundPatchInfo that = (SoundPatchInfo) o;

        if (trackId != that.trackId) return false;
        if (playIndex != that.playIndex) return false;
        return type == that.type;
    }

    @Override
    public int hashCode() {
        int result = (int) (trackId ^ (trackId >>> 32));
        result = 31 * result + playIndex;
        result = 31 * result + type;
        return result;
    }

    public SoundPatchInfo getAppendSoundPatch() {
        return appendSoundPatch;
    }

    public void setAppendSoundPatch(SoundPatchInfo appendSoundPatch) {
        this.appendSoundPatch = appendSoundPatch;
    }

    public boolean isAppendSoundPatch() {
        return isAppendSoundPatch;
    }

    public void setAppendSoundPatch(boolean appendSoundPatch) {
        isAppendSoundPatch = appendSoundPatch;
    }

    @NonNull
    @Override
    public String toString() {
        return "SoundPatchInfo{" +
                "trackId=" + trackId +
                ", url='" + url + '\'' +
                ", gain='" + gain + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", playIndex=" + playIndex +
                ", type=" + type +
                ", filePath=" + filePath +
                ", extraInfo=" + extraInfo +
                ", isAppendSoundPatch=" + isAppendSoundPatch +
                ", hashCode=" + hashCode() +
                '}';
    }
}
