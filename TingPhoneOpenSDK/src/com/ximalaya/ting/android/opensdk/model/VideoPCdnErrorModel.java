package com.ximalaya.ting.android.opensdk.model;

import android.net.Uri;
import androidx.annotation.Keep;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/8/1 17:29
 */
@Keep
public class VideoPCdnErrorModel {
    public String errorUrl;
    public boolean reachedMaxFailCount;

    public int responseCode;
    public String rootCauseName;
    public String rootMessage;
    public String rootTrace;

    public String causeName;
    public String message;
    public String trace;

    public VideoPCdnErrorModel(String url) {
        errorUrl = url;
    }

    public String toJson() {
        try {
            JSONObject jsonObject = new JSONObject();

            try {
                Uri uri = Uri.parse(errorUrl);
                jsonObject.put("host", uri.getHost());
            } catch (Exception e) {
                e.printStackTrace();
            }
            jsonObject.put("errorUrl", errorUrl);
            jsonObject.put("reachedMaxFailCount", reachedMaxFailCount);
            jsonObject.put("responseCode", responseCode);

            jsonObject.put("rootCauseName", rootCauseName);
            jsonObject.put("rootMessage", rootMessage);
            jsonObject.put("rootTrace", rootTrace);

            jsonObject.put("causeName", causeName);
            jsonObject.put("message", message);
            jsonObject.put("trace", trace);

            return jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
