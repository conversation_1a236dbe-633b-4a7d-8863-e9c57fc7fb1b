package com.ximalaya.ting.android.opensdk.model;

import androidx.annotation.Keep;

import com.ximalaya.ting.android.opensdk.player.receive.FilterCarBluetoothDevice;

/**
 * Created by jack.qin on 2022/1/7.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Keep
public class PlayExceptionModel {
    //    public boolean pausePlay = false; //非用户主动pause 主要记录后台播放情况下，通过media以及receiver调用的pause
    public boolean stopService; //true代表是存在 false代表不存在  华为省电模式可能会导致stopservice
    //后台wifi被关闭
    public boolean closeWifi; //true代表wifi被关闭
    public boolean ffmpegDecodeError = false; //ffmpeg decode error ， true表示将会上报该错误
    public boolean playResourceInvalid;
    public boolean startForegroundServiceTimeout = false; //启动播放service超时
    public boolean problem403;
    public boolean playerProcessLevelLow;
//    public boolean playerException = false; //播放器异常
    public boolean unlimitedLoading = false; // 播放器无限加载

    //附加错误信息
    public String errorMsg = null;

    public String playUrl = null;

    public String contentType = null;

    //相关信息
    public boolean wifiSleepSetting; //休眠下是否保持网络连接
    public boolean powerSaveMode; //省电模式是否开启
    public int processLevel;//进程等级
    public boolean isForegroundService;//播放service是否是前台service
    public boolean isWap;   // 是否是wap类型的网络
    public boolean useExoPlayer = true; // 是否使用exoPlayer
    public boolean useFFmpegDecode = true;  // 是否使用ffmpeg进行解码

    public String bluetoothJson;

    public PlayExceptionModel() {
        BluetoothStateModel model = FilterCarBluetoothDevice.getBluetoothStateModel();
        if (model != null) {
            bluetoothJson = model.toString();
        } else {
            bluetoothJson = "model null";
        }
    }
}
