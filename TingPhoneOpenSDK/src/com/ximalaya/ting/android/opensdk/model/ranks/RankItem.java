/**
 * RankItem.java
 * com.ximalaya.ting.android.framework.model.ranks
 *
 * Function： TODO 
 *
 *   ver     date      		author
 * ──────────────────────────────────
 *   		 2015-8-19 		jack.qin
 *
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.model.ranks;

import com.google.gson.annotations.SerializedName;

/**
 * ClassName:RankItem Function: TODO ADD FUNCTION Reason: TODO ADD REASON
 * 
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015-8-19 下午2:21:10
 * 
 * @see
 */
public class RankItem
{
	@SerializedName("id")
	private long dataId;
	private String title;
	@SerializedName("content_type")
	private String contentType;

	public long getDataId()
	{
		return dataId;
	}

	public void setDataId(long dataId)
	{
		this.dataId = dataId;
	}

	public String getTitle()
	{
		return title;
	}

	public void setTitle(String title)
	{
		this.title = title;
	}

	public String getContentType()
	{
		return contentType;
	}

	public void setContentType(String contentType)
	{
		this.contentType = contentType;
	}

	@Override
	public String toString()
	{
		return "RankItem [dataId=" + dataId + ", title=" + title
				+ ", contentType=" + contentType + "]";
	}

}
