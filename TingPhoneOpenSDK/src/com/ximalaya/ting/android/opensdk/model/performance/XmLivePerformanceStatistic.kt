package com.ximalaya.ting.android.opensdk.model.performance

import com.google.android.exoplayer2.analytics.ExoPlayerProcessStore
import com.ximalaya.ting.android.opensdk.httputil.XmCronetManager
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.xmlog.XmLogger
import com.ximalaya.ting.android.xmutil.Logger
import okhttp3.Protocol

/**
 * 上报直播秒开数据。
 *
 * <AUTHOR>
 * @since 2022/10/20
 */
class XmLivePerformanceStatistic {

    private val TAG = "XmLivePerformanceStatistic"
    private var livePlayPerformanceModel: XmLivePlayPerformanceModel? = null
    private val APM = "apm_live"
    private val PLAY_PERFORMANCE = "liveplayperformance"

    companion object {
        @JvmStatic
        val instance = SingletonHolder.livePerformanceStatistic
    }

    private object SingletonHolder {
        val livePerformanceStatistic = XmLivePerformanceStatistic()
    }

    fun setMediaSource(track: Track, playUrl: String?) {
        if (livePlayPerformanceModel == null || !(livePlayPerformanceModel?.playUrl?:"").equals(playUrl)) {
            Logger.i(TAG, "setMediaSource:${playUrl}")
            livePlayPerformanceModel = XmLivePlayPerformanceModel()
            livePlayPerformanceModel?.startTime = System.currentTimeMillis()
            livePlayPerformanceModel?.businessId = track.liveType
            livePlayPerformanceModel?.subBusinessId = track.roomModelType
            livePlayPerformanceModel?.playUrl = playUrl
            livePlayPerformanceModel?.isLiveScroll = track.liveScroll > 0
            livePlayPerformanceModel?.livePlaySource = track.livePlaySource
            if (track.livePageEnterTime > 0 && livePlayPerformanceModel != null) {
                livePlayPerformanceModel?.livePageEnterTime = track.livePageEnterTime
                livePlayPerformanceModel?.aidlCost =
                    livePlayPerformanceModel!!.startTime - track.livePageEnterTime
            }
        }

    }

    fun bufferReadyPlay(track: Track) {
        livePlayPerformanceModel?.let {
            if (track.playUrl32?.equals(it.playUrl) ?: false && track.livePageEnterTime > 0) {
                it.bufferReadyTime = System.currentTimeMillis() - (livePlayPerformanceModel?.startTime ?: 0)
            }
        }
    }

    fun startPlay(track: Track) {
        livePlayPerformanceModel?.let {
            if (track.playUrl32?.equals(it.playUrl) ?: false && it.livePageEnterTime > 0) {
                it.totalTime = System.currentTimeMillis() - it.livePageEnterTime
                it.firstFrameTime = System.currentTimeMillis() - (livePlayPerformanceModel?.startTime ?: 0)
                if (it.bufferReadyTime == 0L) {
                    it.bufferReadyTime = it.firstFrameTime
                }
                livePlayPerformanceModel = null
                track.livePageEnterTime = 0
                if (it.firstFrameTime > 60 * 1000 || it.totalTime > 60 * 1000) {
                    Logger.i(TAG, "firstFrameTime=${it.firstFrameTime}  totalTime=${it.totalTime}")
                    return
                }
                if (it.endReadSecondVideoTagTime > 0) {
                    it.firstFrameDecodeCost = System.currentTimeMillis() - it.endReadSecondVideoTagTime
                }
                it.isSuccess = true
                Logger.i(TAG, "各环节总计耗时:${it.getTotalTimeCost()}   用户体感耗时:${it.totalTime}")

                if (ExoPlayerProcessStore.urlNetEventInfoCacheMap.containsKey(it.playUrl)) {
                    it.cdnIp = ExoPlayerProcessStore.getCdnIP(it.playUrl?: "")
                    val playerNetEventInfo = ExoPlayerProcessStore.urlNetEventInfoCacheMap.get(it.playUrl)
                    if (playerNetEventInfo != null) {
                        it.dnsCost = playerNetEventInfo.dnsEndTime
                        if (playerNetEventInfo.connectEndTime >= playerNetEventInfo.connectStartTime && playerNetEventInfo.connectStartTime > 0 ) {
                            it.connectCost = playerNetEventInfo.connectEndTime - playerNetEventInfo.connectStartTime
                        }
                        it.requestCost = playerNetEventInfo.requestHeaderEndTime - playerNetEventInfo.requestHeaderStartTime
                        if (playerNetEventInfo.connectEndTime > 0 && playerNetEventInfo.responseBodyStartTime > 0) {
                            it.firstPackageCost = playerNetEventInfo.responseBodyStartTime - playerNetEventInfo.connectEndTime
                        }
                    }
                }
                it.audioDecoderCreateCost = ExoPlayerProcessStore.getAudioDecoderInitializeDuration()
                it.videoDecoderCreateCost = ExoPlayerProcessStore.getVideoDecoderInitializeDuration()
                it.extra["quic"] = if (XmCronetManager.getInstance().getUrlProtocolType(it.playUrl ?: "") == Protocol.QUIC) "1" else "0"
                it.extra["netStatus"] = "${XmCronetManager.getInstance().getNetworkRttType()}"
                it.extra["quicConfig"] = "${XmCronetManager.getInstance().getQuicConfig()}"

                val jsonStr = it.toJsonString()
                Logger.i(TAG, jsonStr)
                XmLogger.log(APM, PLAY_PERFORMANCE, jsonStr)
            } else {
                livePlayPerformanceModel = null
            }
        }
    }

    fun onTransferInitializing() {
        livePlayPerformanceModel?.let {
            it.prepareMediaSourceCost = System.currentTimeMillis() - it.startTime
            it.startCdnRequestTime = System.currentTimeMillis()
        }
    }

    fun onTransferStart() {
        livePlayPerformanceModel?.let {
            if (it.startCdnRequestTime > 0) {
                it.cdnRequestCost = System.currentTimeMillis() - it.startCdnRequestTime
            }
        }
    }

    fun onStartReadFlvHeader() {
        livePlayPerformanceModel?.let {
            it.startReadFlvHeaderTime = System.currentTimeMillis()
        }
    }

    fun onReadFlvHeaderFinished() {
        livePlayPerformanceModel?.let {
            it.flvHeaderCost = System.currentTimeMillis() - it.startReadFlvHeaderTime
        }
    }

    fun onStartReadFlvScriptTag() {
        livePlayPerformanceModel?.let {
            it.startReadScriptTagTime = System.currentTimeMillis()
        }
    }

    fun onReadFlvScriptTagFinished() {
        livePlayPerformanceModel?.let {
            it.flvScrpitTagCost = System.currentTimeMillis() - it.startReadScriptTagTime
        }
    }

    fun onStartReadAudioHeader() {
        livePlayPerformanceModel?.let {
            it.startReadAudioHeaderTime = System.currentTimeMillis()
        }
    }

    fun onReadAudioHeaderFinished() {
        livePlayPerformanceModel?.let {
            it.audioHeaderTagCost = System.currentTimeMillis() - it.startReadAudioHeaderTime
        }
    }

    fun onStartReadFirstVideoTag() {
        livePlayPerformanceModel?.let {
            it.startReadFirstVideoTagTime = System.currentTimeMillis()
        }
    }

    fun onReadFirstVideoTagFinished() {
        livePlayPerformanceModel?.let {
            it.firstVideoTagCost = System.currentTimeMillis() - it.startReadFirstVideoTagTime
        }
    }

    fun onStartReadSecondVideoTag() {
        livePlayPerformanceModel?.let {
            it.startReadSecondVideoTagTime = System.currentTimeMillis()
        }
    }

    fun onReadSecondVideoTagFinished() {
        livePlayPerformanceModel?.let {
            it.endReadSecondVideoTagTime = System.currentTimeMillis()
            if (it.startReadSecondVideoTagTime > 0) {
                it.secondVideoTagCost = it.endReadSecondVideoTagTime - it.startReadSecondVideoTagTime
            }
        }
    }
}