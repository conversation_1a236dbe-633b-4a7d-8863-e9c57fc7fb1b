package com.ximalaya.ting.android.opensdk.model;

public class PlayErrorModel {
    public static final int UNKNOW = 0;
    public static final int APPLICATION_CRASH = 1;
    public static final int HTTP_DNS_REQUEST_ERROR = 2;
    public static final int PLAY_ERROR = 3;
    public static final int CDN_ERROR = 4;
    public static final int FOCUS_LOSS = 5;
    public static final int NETWORK_ERROR = 6;

    /*
    1  应用崩溃
    2  dns错误
    3  播放器错误
    4  cdn错误
    5  焦点丢失
    11 系统错误
    13 声音不存在
    704 声音待审核
    705 私密声音
    927 专辑无版权
    929 声音下架
    */

    //0-未知，1-应用非正常退出，2-httpDNSRequest错误，3-playError播放错误,
    //4-cdn错误，5-焦点被抢占
    private int playErrorType;
    //0-未获取到，1-开屏，2-锁屏
    private int screenState;
    //发生时间
    private String time;
    //不同错误的具体详细信息，如：播放器错误相关信息，cdn错误相关信息等。
    private String errorMsg;
    //进程优先级,0为前台进程
    private String processOomAdj;
    //当前获取音频焦点的应用，及播放服务client端名称.(用于定位是哪个应用抢占了我们的焦点)
    private String focusStackPeek;
    private int appExitType;
    private String systemSettings;
    private boolean isInWhiteList;

    private String bluetoothJson;

    public int getPlayErrorType() {
        return playErrorType;
    }

    public void setPlayErrorType(int playErrorType) {
        this.playErrorType = playErrorType;
    }

    public int getScreenState() {
        return screenState;
    }

    public void setScreenState(int screenState) {
        this.screenState = screenState;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getProcessOomAdj() {
        return processOomAdj;
    }

    public void setProcessOomAdj(String processOomAdj) {
        this.processOomAdj = processOomAdj;
    }

    public String getFocusStackPeek() {
        return focusStackPeek;
    }

    public void setFocusStackPeek(String focusStackPeek) {
        this.focusStackPeek = focusStackPeek;
    }

    public int getAppExitType() {
        return appExitType;
    }

    public void setAppExitType(int appExitType) {
        this.appExitType = appExitType;
    }

    public String getSystemSettings() {
        return systemSettings;
    }

    public void setSystemSettings(String systemSettings) {
        this.systemSettings = systemSettings;
    }

    public boolean isInWhiteList() {
        return isInWhiteList;
    }

    public void setInWhiteList(boolean inWhiteList) {
        isInWhiteList = inWhiteList;
    }

    public String getBluetoothJson() {
        return bluetoothJson;
    }

    public void setBluetoothJson(String bluetoothJson) {
        this.bluetoothJson = bluetoothJson;
    }
}
