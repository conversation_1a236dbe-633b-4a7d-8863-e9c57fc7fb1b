package com.ximalaya.ting.android.host.manager.ad.adrtb;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.Keep;

/**
 * <AUTHOR>
 * @Time 2021/10/28
 * @Description
 */
@Keep
public class AdRtbItemModel implements Parcelable {
    public String dspPositionId;
    public int adType;

    @Override
    public String toString() {
        return "AdRtbItemModel{" +
                "dspPositionId='" + dspPositionId + '\'' +
                ", adType=" + adType +
                '}';
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.dspPositionId);
        dest.writeInt(this.adType);
    }

    public void readFromParcel(Parcel source) {
        this.dspPositionId = source.readString();
        this.adType = source.readInt();
    }

    public AdRtbItemModel() {
    }

    public static final Parcelable.Creator<AdRtbItemModel> CREATOR =
            new Parcelable.Creator<AdRtbItemModel>() {
        @Override
        public AdRtbItemModel createFromParcel(Parcel source) {
            AdRtbItemModel adRtbItemModel = new AdRtbItemModel();
            adRtbItemModel.readFromParcel(source);
            return adRtbItemModel;
        }

        @Override
        public AdRtbItemModel[] newArray(int size) {
            return new AdRtbItemModel[size];
        }
    };
}
