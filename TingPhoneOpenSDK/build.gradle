apply plugin: 'com.android.library'
apply from: project.rootDir.absolutePath + '/util.gradle'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'

dependencies {
    api fileTree(dir: 'libs', include: '*.jar' /*,exclude: 'xmediaplayer.jar'*/)
    api project(path: ':XAndroidFramework:RouteService', configuration: rootProject.configurationType)
    api 'com.xiamlaya.ting.android.dnscache:dnscache-' + DNS_CACHE_REPO_BRANCH + ':' + DNS_CACHE_REPP_VERSION
    if (INCLUDE_PLAYER_SOURCE_COMPILE.toBoolean()) {
        api project(path: ':xmmediaplayerprj:XMediaPlayer', configuration: rootProject.configurationType)
    } else {

        api 'com.ximalaya.ting.android.xmedia:xmedia-' + XMEDIA_REPO_BRANCH + ':' + XMEDIA_REPO_VERSION
    }
    api 'com.ximalaya.ting.android.lockmonitor:lockmonitor-developer:1.0.0'
    api rootProject.ext.xmDependencies.androidxAnnotations
    api rootProject.ext.xmDependencies.androidXMedia
    api rootProject.ext.xmDependencies.androidXLocalBraoadCast
    api rootProject.ext.xmDependencies.okHttp
    api rootProject.ext.xmDependencies.okHttpUrlConnect
    api rootProject.ext.xmDependencies.okio

//    api project(path: ':XAndroidFramework:ExoPlayer', configuration: rootProject.configurationType)

    api 'com.ximalaya.ting.android.xmlogmanager:xmlogmanager-' + XMLOGMANAGER_REPO_BRANCH + ':' + XMLOGMANAGER_REPO_VRSION
    api 'com.ximalaya.ting.android.xmnetmonitor:xmnetmonitor-' + NETWORK_REPO_BRANCH + ':' + NETWORK_REPO_VERSION
//    compile project(':XAndroidFramework:XMediaplayerLibrary:XMediaplayer')

    api 'com.ximalaya.ting.android.firework:firework-' + FIREWORK_REPO_BRANCH + ':' + FIREWORK_VERSION

//    api project(':Firework_local')

//    api project(path: ':NewAndroidFramewrok:CommonBusiness:Firework', configuration: rootProject.configurationType)
    api 'com.ximalaya.ting.android.xmlymmkv:xmlymmkv-' + XmMMKV_REPO_BRANCE + ':' + XmMMKV_REPO_VERSION
    api 'com.ximalaya.ting.android.voicewakecallback:voicewakecallback-' + XM_VOICE_WAKE_CALLBACK_REPO_BRANCH + ':' + XM_VOICE_WAKE_CALLBACK_VERSION
//    api project(path : ':NewAndroidFramewrok:CommonBusiness:Firework', configuration: rootProject.configurationType)
    if (CMOPILE_NEW_ANDROID_FRAMEWORK_SRC.toBoolean()) {
        api project(path: ':NewAndroidFramework:EncryptService', configuration: rootProject.configurationType)
        api project(path: ':NewAndroidFramework:EncryptServiceCheck', configuration: rootProject.configurationType)
    } else {
        api 'com.ximalaya.ting.android.encryptservice:encryptservice-' + ENCRYPT_SERVICE_REPO_BRANCH + ':' + ENCRYPT_SERVICE_REPO_VERSION

        if (isReleaseDebug.toBoolean()) {
            api 'com.ximalaya.ting.android.encryptservice:encryptservice-check-' + ENCRYPT_SERVICE_REPO_BRANCH + ':' + ENCRYPT_SERVICE_REPO_VERSION
        } else {
            api 'com.ximalaya.ting.android.encryptservice:encryptservice-check-noop-' + ENCRYPT_SERVICE_REPO_BRANCH + ':' + ENCRYPT_SERVICE_REPO_VERSION
        }
    }
    api 'com.ximalaya.ting.android.apm:apm-developer:2.1.10'
    api 'com.ximalaya.ting.android.configurecenter:configurecenter-developer:3.2.6'
}

//aidlOpt {
//    aidlNameList = [
//        "IXmPlayer"
//    ]
//}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion Integer.parseInt(rootProject.ext.minSdkVersion)
        targetSdkVersion Integer.parseInt(rootProject.ext.targetSdkVersion)
        versionCode 1
        versionName "1.0"
    }

    compileOptions {
        sourceCompatibility rootProject.javaCompileVersion
        targetCompatibility rootProject.javaCompileVersion
    }

    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            java.srcDirs = ['src']
            resources.srcDirs = ['src']
            aidl.srcDirs = ['src']
            renderscript.srcDirs = ['src']
            res.srcDirs = ['res']
            assets.srcDirs = ['assets']
            jniLibs.srcDirs = ['libs']
        }

        // Move the tests to tests/java, tests/res, etc...
        androidTest.setRoot('tests')

        // Move the build types to build-types/<type>
        // For instance, build-types/debug/java, build-types/debug/AndroidManifest.xml, ...
        // This moves them out of them default location under src/<type>/... which would
        // conflict with src/ being used by the main source set.
        // Adding new build types or product flavors should be accompanied
        // by a similar customization.
        debug.setRoot('build-types/debug')
        release.setRoot('build-types/release')
    }

    lintOptions {
        quiet true
        abortOnError false
        checkReleaseBuilds false
    }

    buildTypes {
        release {
            buildConfigField "String", "HOST_PACKAGE_NAME", "\"" + project.rootProject.ext.hostPackageName + "\""
            buildConfigField "String", "XMEDIA_REPO_VERSION", "\"${XMEDIA_REPO_VERSION}\""
        }
        debug {
            buildConfigField "String", "HOST_PACKAGE_NAME", "\"" + project.rootProject.ext.hostPackageName + "\""
            buildConfigField "String", "XMEDIA_REPO_VERSION", "\"${XMEDIA_REPO_VERSION}\""
        }
    }
}