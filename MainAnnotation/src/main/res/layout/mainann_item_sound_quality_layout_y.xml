<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_cl_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="74dp"
    android:layout_marginStart="16dp"
    android:layout_marginEnd="16dp"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingBottom="13dp"
    android:paddingTop="13dp"
    android:layout_marginTop="4dp"
    android:layout_marginBottom="4dp">

    <ImageView
        android:id="@+id/main_iv_atoms"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@+id/main_tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/main_tv_title"
        app:layout_constraintStart_toStartOf="parent"
        android:src="@drawable/mainann_ic_sound_quality_atoms"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/main_tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@+id/main_iv_atoms"
        app:layout_constraintBottom_toTopOf="@+id/main_tv_subtitle"
        app:layout_constraintEnd_toStartOf="@+id/main_tv_select"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constrainedWidth="true"
        android:layout_marginStart="2dp"
        app:layout_goneMarginStart="0dp"
        android:textColor="@color/host_color_131313_ffffff"
        android:textSize="15sp"
        android:fontFamily="PingFangSC-Regular"
        android:textFontWeight="500"
        tools:text="智能选择" />

    <ImageView
        android:id="@+id/main_iv_dolby_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="20px"
        app:layout_constraintTop_toTopOf="@+id/main_tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/main_tv_title"
        app:layout_constraintStart_toEndOf="@+id/main_tv_title"
        android:src="@drawable/mainann_ic_track_quality_dolby_tag"
        android:visibility="gone"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/main_iv_vip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@+id/main_tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/main_tv_title"
        app:layout_constraintStart_toEndOf="@+id/main_iv_dolby_tag"
        android:src="@drawable/main_ic_track_quality_vip_new"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/main_iv_free"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@+id/main_tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/main_tv_title"
        app:layout_constraintStart_toEndOf="@+id/main_iv_vip"
        android:src="@drawable/main_ic_track_sound_effect_free_new"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/main_tv_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/main_tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/main_tv_select"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constrainedWidth="true"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="20dp"
        android:ellipsize="end"
        android:textColor="@color/host_color_acacaf_66666b"
        android:textSize="12sp"
        tools:text="WIFI下声音品质更高，移动数据收听时更省流量" />

    <TextView
        android:id="@+id/main_tv_select"
        android:layout_width="48dp"
        android:layout_height="24dp"
        android:background="@drawable/main_bg_quality_use"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="使用"
        app:layout_constraintEnd_toEndOf="parent"
        android:textColor="@color/main_color_ff4444"
        android:textSize="12dp"
        android:visibility="visible"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>
