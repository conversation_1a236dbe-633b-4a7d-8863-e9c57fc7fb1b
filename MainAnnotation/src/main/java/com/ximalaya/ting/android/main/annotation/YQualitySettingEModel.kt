package com.ximalaya.ting.android.main.annotation

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyHolder
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.ximalaya.ting.android.host.manager.play.TrackPlayQualityManager
import com.ximalaya.ting.android.host.model.play.TrackQualities
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil

@EpoxyModelClass
abstract class YQualitySettingEModel: EpoxyModelWithHolder<YQualitySettingEModel.Holder>() {
    @EpoxyAttribute lateinit var quality: TrackQualities
    @EpoxyAttribute var playQuality: Int = -1
    @EpoxyAttribute var first: Boolean = false
    @EpoxyAttribute var last: Boolean = false

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var clickListener: View.OnClickListener

    override fun getDefaultLayout() = R.layout.mainann_item_setting_sound_quality_layout_y

    override fun bind(holder: Holder) {
        val isAtoms = TrackPlayQualityManager.getInstance().isFullDepth(quality.qualityLevel)
        if (isAtoms) {
            ViewStatusUtil.setVisible(View.VISIBLE, holder.atomsIcon)
            holder.titleTextView?.setTextColor(0xffF09068.toInt())
        } else {
            holder.titleTextView?.also {
                it.setTextColor(it.context.resources.getColor(com.ximalaya.ting.android.host.R.color.host_color_titleColor))
            }
            ViewStatusUtil.setVisible(View.GONE, holder.atomsIcon)
        }

        holder.itemView?.setOnClickListener(clickListener)
        holder.titleTextView?.text = quality.qualityName
        holder.subTitleTextView?.text = quality.desc?: ""

        if (quality.needVip) {
            ViewStatusUtil.setVisible(View.VISIBLE, holder.vipIcon)
        } else {
            ViewStatusUtil.setVisible(View.INVISIBLE, holder.vipIcon)
        }

        if (quality.qualityLevel == playQuality ||
            TrackPlayQualityManager.getInstance().isFullDepth(playQuality)
            && TrackPlayQualityManager.getInstance().isFullDepth(quality.qualityLevel)
        ) {
            ViewStatusUtil.setVisible(View.VISIBLE, holder.selectedIcon)
        } else {
            ViewStatusUtil.setVisible(View.INVISIBLE, holder.selectedIcon)
        }

        holder.itemView?.also {
            if (first) {
                it.setBackgroundResource(R.drawable.mainann_bg_rect_ffffff_1b1b1b_top_radius_8)
            } else if (last) {
                it.setBackgroundResource(R.drawable.mainann_bg_rect_ffffff_1b1b1b_bottom_radius_8)
            } else {
                it.setBackgroundColor(
                    it.context.resources.getColor(com.ximalaya.ting.android.host.R.color.host_color_ffffff_1b1b1b)
                )
            }
        }

        if (last) {
            ViewStatusUtil.setVisible(View.INVISIBLE, holder.bottomDivider)
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, holder.bottomDivider)
        }
    }

    class Holder: EpoxyHolder() {
        var itemView: View? = null
        var titleTextView: TextView? = null
        var subTitleTextView: TextView? = null
        var vipIcon: ImageView? = null
        var atomsIcon: ImageView? = null
        var selectedIcon: View? = null
        var bottomDivider: View? = null

        override fun bindView(itemView: View) {
            this.itemView = itemView
            titleTextView = itemView.findViewById(R.id.main_tv_title)
            subTitleTextView = itemView.findViewById(R.id.main_tv_subtitle)
            atomsIcon = itemView.findViewById(R.id.main_iv_atoms)
            vipIcon = itemView.findViewById(R.id.main_iv_vip)
            selectedIcon = itemView.findViewById(R.id.main_ic_selected)
            bottomDivider = itemView.findViewById(R.id.main_divider)
        }
    }
}