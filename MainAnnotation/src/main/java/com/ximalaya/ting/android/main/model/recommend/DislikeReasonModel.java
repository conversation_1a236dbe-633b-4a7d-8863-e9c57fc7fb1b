package com.ximalaya.ting.android.main.model.recommend;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.typeadapter.annotation.GenerateGsonTypeAdapter;
import com.ximalaya.ting.android.typeadapter.annotation.GenerateGsonTypeAdapterFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by WolfXu on 2020-02-12.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
@GenerateGsonTypeAdapterFactory(classes = {DislikeReasonModel.class, DislikeReasonNew.class})
@GenerateGsonTypeAdapter
public class DislikeReasonModel {
    @SerializedName("default")
    private List<DislikeReasonNew> defaultReasons;
    @SerializedName("traits")
    private List<DislikeReasonNew> traitsReasons;

    public List<DislikeReasonNew> getDefaultReasons() {
        return defaultReasons;
    }

    public void setDefaultReasons(List<DislikeReasonNew> defaultReasons) {
        this.defaultReasons = defaultReasons;
    }

    public List<DislikeReasonNew> getTraitsReasons() {
        return traitsReasons;
    }

    public void setTraitsReasons(List<DislikeReasonNew> traitsReasons) {
        this.traitsReasons = traitsReasons;
    }

    public boolean hasDislikeReason() {
        return !ToolUtil.isEmptyCollects(defaultReasons) || !ToolUtil.isEmptyCollects(traitsReasons);
    }

    public static DislikeReasonModel parse(Map<String, String> defaultReasons, Map<String, String> traitsReasons) {
        DislikeReasonModel result = new DislikeReasonModel();
        if (!ToolUtil.isEmptyMap(defaultReasons)) {
            result.defaultReasons = new ArrayList<>();
            for (String key : defaultReasons.keySet()) {
                if (null == key) {
                    continue;
                }
                DislikeReasonNew reason = new DislikeReasonNew();
                reason.setName(key);
                reason.setCodeType(defaultReasons.get(key));
                result.defaultReasons.add(reason);
            }
        }
        if (!ToolUtil.isEmptyMap(traitsReasons)) {
            result.traitsReasons = new ArrayList<>();
            for (String key : traitsReasons.keySet()) {
                if (null == key) {
                    continue;
                }
                DislikeReasonNew reason = new DislikeReasonNew();
                reason.setName(key);
                reason.setCodeType(traitsReasons.get(key));
                result.traitsReasons.add(reason);
            }
        }
        return result;
    }
}
