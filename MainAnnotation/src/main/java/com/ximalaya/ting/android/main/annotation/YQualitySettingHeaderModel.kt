package com.ximalaya.ting.android.main.annotation

import android.view.Gravity
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.updatePadding
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithView
import com.ximalaya.ting.android.host.util.extension.dp

@EpoxyModelClass
abstract class YQualitySettingHeaderModel: EpoxyModelWithView<TextView>() {
    @EpoxyAttribute lateinit var tipsText: String

    override fun buildView(parent: ViewGroup) = TextView(parent.context).apply {
        textSize = 15f
        updatePadding(left = 20.dp, right = 20.dp, bottom = 8.dp)
        maxLines = 1
        setTextColor(parent.context.resources.getColor(com.ximalaya.ting.android.host.R.color.host_color_111111_8d8d91))
        layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        gravity = Gravity.START
    }

    override fun bind(view: TextView) {
        view.text = tipsText
    }
}
